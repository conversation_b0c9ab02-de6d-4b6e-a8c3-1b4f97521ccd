{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/sms(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/sms/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "XxC-Hj707IIbaFQ13s9K-", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "iYkbc89o/xNp94OHE0zcAwu4I9iQljrb1v3upuhYeL0=", "__NEXT_PREVIEW_MODE_ID": "5b43207baf27ee5955c9f7fd249c1b17", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e25e380f736e4b41d68d692eda3ce5e367dfae91383ba92270aeb8751bab9b06", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6f0bb160cbed2dd0cf2db8c0a716310629984f9c03da1ca49334e56597ca83b3"}}}, "functions": {}, "sortedMiddleware": ["/"]}