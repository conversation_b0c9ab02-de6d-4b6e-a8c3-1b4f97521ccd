"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3164],{5623:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5845:(e,t,n)=>{n.d(t,{i:()=>u});var r,o=n(12115),i=n(52712),l=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function u({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,u,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),u=o.useRef(t);return l(()=>{u.current=t},[t]),o.useEffect(()=>{i.current!==n&&(u.current?.(n),i.current=n)},[n,i]),[n,r,u]}({defaultProp:t,onChange:n}),c=void 0!==e,s=c?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,r])}return[s,o.useCallback(t=>{if(c){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else u(t)},[c,e,u,a])]}Symbol("RADIX:SYNC_STATE")},28905:(e,t,n)=>{n.d(t,{C:()=>l});var r=n(12115),o=n(6101),i=n(52712),l=e=>{let{present:t,children:n}=e,l=function(e){var t,n;let[o,l]=r.useState(),a=r.useRef(null),c=r.useRef(e),s=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=u(a.current);s.current="mounted"===d?e:"none"},[d]),(0,i.N)(()=>{let t=a.current,n=c.current;if(n!==e){let r=s.current,o=u(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=u(a.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(s.current=u(a.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{a.current=e?getComputedStyle(e):null,l(e)},[])}}(t),a="function"==typeof n?n({present:l.isPresent}):r.Children.only(n),c=(0,o.s)(l.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||l.isPresent?r.cloneElement(a,{ref:c}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},39033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(12115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},46081:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(12115),o=n(95155);function i(e,t=[]){let n=[],l=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return l.scopeName=e,[function(t,i){let l=r.createContext(i),u=n.length;n=[...n,i];let a=t=>{let{scope:n,children:i,...a}=t,c=n?.[e]?.[u]||l,s=r.useMemo(()=>a,Object.values(a));return(0,o.jsx)(c.Provider,{value:s,children:i})};return a.displayName=t+"Provider",[a,function(n,o){let a=o?.[e]?.[u]||l,c=r.useContext(a);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(l,...t)]}},51154:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},52712:(e,t,n)=>{n.d(t,{N:()=>o});var r=n(12115),o=globalThis?.document?r.useLayoutEffect:()=>{}},61285:(e,t,n)=>{n.d(t,{B:()=>a});var r,o=n(12115),i=n(52712),l=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function a(e){let[t,n]=o.useState(l());return(0,i.N)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},62525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63655:(e,t,n)=>{n.d(t,{hO:()=>a,sG:()=>u});var r=n(12115),o=n(47650),i=n(99708),l=n(95155),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function a(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},85185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},89613:(e,t,n)=>{n.d(t,{Kq:()=>B,UC:()=>z,bL:()=>F,l9:()=>W});var r=n(12115),o=n(85185),i=n(6101),l=n(46081),u=n(19178),a=n(61285),c=n(35152),s=(n(34378),n(28905)),d=n(63655),f=n(99708),p=n(5845),m=n(2564),v=n(95155),[y,h]=(0,l.A)("Tooltip",[c.Bk]),x=(0,c.Bk)(),g="TooltipProvider",w="tooltip.open",[b,T]=y(g),C=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:o=300,disableHoverableContent:i=!1,children:l}=e,u=r.useRef(!0),a=r.useRef(!1),c=r.useRef(0);return r.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,v.jsx)(b,{scope:t,isOpenDelayedRef:u,delayDuration:n,onOpen:r.useCallback(()=>{window.clearTimeout(c.current),u.current=!1},[]),onClose:r.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>u.current=!0,o)},[o]),isPointerInTransitRef:a,onPointerInTransitChange:r.useCallback(e=>{a.current=e},[]),disableHoverableContent:i,children:l})};C.displayName=g;var E="Tooltip",[N,k]=y(E),M=e=>{let{__scopeTooltip:t,children:n,open:o,defaultOpen:i,onOpenChange:l,disableHoverableContent:u,delayDuration:s}=e,d=T(E,e.__scopeTooltip),f=x(t),[m,y]=r.useState(null),h=(0,a.B)(),g=r.useRef(0),b=null!=u?u:d.disableHoverableContent,C=null!=s?s:d.delayDuration,k=r.useRef(!1),[M,R]=(0,p.i)({prop:o,defaultProp:null!=i&&i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(w))):d.onClose(),null==l||l(e)},caller:E}),L=r.useMemo(()=>M?k.current?"delayed-open":"instant-open":"closed",[M]),O=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,k.current=!1,R(!0)},[R]),_=r.useCallback(()=>{window.clearTimeout(g.current),g.current=0,R(!1)},[R]),A=r.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{k.current=!0,R(!0),g.current=0},C)},[C,R]);return r.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),(0,v.jsx)(c.bL,{...f,children:(0,v.jsx)(N,{scope:t,contentId:h,open:M,stateAttribute:L,trigger:m,onTriggerChange:y,onTriggerEnter:r.useCallback(()=>{d.isOpenDelayedRef.current?A():O()},[d.isOpenDelayedRef,A,O]),onTriggerLeave:r.useCallback(()=>{b?_():(window.clearTimeout(g.current),g.current=0)},[_,b]),onOpen:O,onClose:_,disableHoverableContent:b,children:n})})};M.displayName=E;var R="TooltipTrigger",L=r.forwardRef((e,t)=>{let{__scopeTooltip:n,...l}=e,u=k(R,n),a=T(R,n),s=x(n),f=r.useRef(null),p=(0,i.s)(t,f,u.onTriggerChange),m=r.useRef(!1),y=r.useRef(!1),h=r.useCallback(()=>m.current=!1,[]);return r.useEffect(()=>()=>document.removeEventListener("pointerup",h),[h]),(0,v.jsx)(c.Mz,{asChild:!0,...s,children:(0,v.jsx)(d.sG.button,{"aria-describedby":u.open?u.contentId:void 0,"data-state":u.stateAttribute,...l,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(y.current||a.isPointerInTransitRef.current||(u.onTriggerEnter(),y.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{u.onTriggerLeave(),y.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{u.open&&u.onClose(),m.current=!0,document.addEventListener("pointerup",h,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{m.current||u.onOpen()}),onBlur:(0,o.m)(e.onBlur,u.onClose),onClick:(0,o.m)(e.onClick,u.onClose)})})});L.displayName=R;var[O,_]=y("TooltipPortal",{forceMount:void 0}),A="TooltipContent",P=r.forwardRef((e,t)=>{let n=_(A,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=k(A,e.__scopeTooltip);return(0,v.jsx)(s.C,{present:r||l.open,children:l.disableHoverableContent?(0,v.jsx)(U,{side:o,...i,ref:t}):(0,v.jsx)(j,{side:o,...i,ref:t})})}),j=r.forwardRef((e,t)=>{let n=k(A,e.__scopeTooltip),o=T(A,e.__scopeTooltip),l=r.useRef(null),u=(0,i.s)(t,l),[a,c]=r.useState(null),{trigger:s,onClose:d}=n,f=l.current,{onPointerInTransitChange:p}=o,m=r.useCallback(()=>{c(null),p(!1)},[p]),y=r.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());c(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),p(!0)},[p]);return r.useEffect(()=>()=>m(),[m]),r.useEffect(()=>{if(s&&f){let e=e=>y(e,f),t=e=>y(e,s);return s.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{s.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[s,f,y,m]),r.useEffect(()=>{if(a){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==s?void 0:s.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e],u=t[i],a=l.x,c=l.y,s=u.x,d=u.y;c>r!=d>r&&n<(s-a)*(r-c)/(d-c)+a&&(o=!o)}return o}(n,a);r?m():o&&(m(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[s,f,a,d,m]),(0,v.jsx)(U,{...e,ref:u})}),[I,S]=y(E,{isInside:!1}),D=(0,f.Dc)("TooltipContent"),U=r.forwardRef((e,t)=>{let{__scopeTooltip:n,children:o,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:a,...s}=e,d=k(A,n),f=x(n),{onClose:p}=d;return r.useEffect(()=>(document.addEventListener(w,p),()=>document.removeEventListener(w,p)),[p]),r.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,p]),(0,v.jsx)(u.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,v.jsxs)(c.UC,{"data-state":d.stateAttribute,...f,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,v.jsx)(D,{children:o}),(0,v.jsx)(I,{scope:n,isInside:!0,children:(0,v.jsx)(m.bL,{id:d.contentId,role:"tooltip",children:i||o})})]})})});P.displayName=A;var $="TooltipArrow";r.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=x(n);return S($,n).isInside?null:(0,v.jsx)(c.i3,{...o,...r,ref:t})}).displayName=$;var B=C,F=M,W=L,z=P},92657:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);