(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7639],{6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>l,t:()=>a});var n=t(12115);function s(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,n=e.map(e=>{let n=s(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():s(e[r],null)}}}}function l(...e){return n.useCallback(a(...e),e)}},13711:(e,r,t)=>{Promise.resolve().then(t.bind(t,81284))},40968:(e,r,t)=>{"use strict";t.d(r,{b:()=>i});var n=t(12115),s=t(63655),a=t(95155),l=n.forwardRef((e,r)=>(0,a.jsx)(s.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));l.displayName="Label";var i=l},53999:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var n=t(52596),s=t(39688);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,n.$)(r))}},63655:(e,r,t)=>{"use strict";t.d(r,{hO:()=>o,sG:()=>i});var n=t(12115),s=t(47650),a=t(99708),l=t(95155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,a.TL)(`Primitive.${r}`),s=n.forwardRef((e,n)=>{let{asChild:s,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(s?t:r,{...a,ref:n})});return s.displayName=`Primitive.${r}`,{...e,[r]:s}},{});function o(e,r){e&&s.flushSync(()=>e.dispatchEvent(r))}},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>l});var n=t(52596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,l=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:l,defaultVariants:i}=r,o=Object.keys(l).map(e=>{let r=null==t?void 0:t[e],n=null==i?void 0:i[e];if(null===r)return null;let a=s(r)||s(n);return l[e][a]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return a(e,o,null==r||null==(n=r.compoundVariants)?void 0:n.reduce((e,r)=>{let{class:t,className:n,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...d}[r]):({...i,...d})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},81284:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var n=t(95155),s=t(12115),a=t(97168),l=t(89852),i=t(82714),o=t(88482);function d(){let[e,r]=(0,s.useState)({CityId:"-999",CountryID:"1",EmailAddress:"<EMAIL>",FirstName:"Test",LastName:"User",MobileNo:"+905654646466",Password:"Test1234",StateProvinceId:"-999",AddressLineOne:"-999",PostalCode:"-999"}),[t,d]=(0,s.useState)(!1),[u,c]=(0,s.useState)(null),[f,m]=(0,s.useState)(null),p=async r=>{r.preventDefault(),d(!0),m(null),c(null);try{let r=await fetch("https://localhost:7149/api/v1/dynamic/dataoperation/signup-user",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({requestParameters:e})}),t=await r.json();if(!r.ok)throw Error(t.message||"Registration failed");c(t)}catch(e){m(e.message||"An error occurred"),console.error("Error:",e)}finally{d(!1)}},b=e=>{let{name:t,value:n}=e.target;r(e=>({...e,[t]:n}))};return(0,n.jsx)("div",{className:"container mx-auto p-4 max-w-2xl",children:(0,n.jsxs)(o.Zp,{className:"p-6",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Test Registration"}),(0,n.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(i.J,{htmlFor:"FirstName",children:"First Name"}),(0,n.jsx)(l.p,{id:"FirstName",name:"FirstName",value:e.FirstName,onChange:b,disabled:t})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(i.J,{htmlFor:"LastName",children:"Last Name"}),(0,n.jsx)(l.p,{id:"LastName",name:"LastName",value:e.LastName,onChange:b,disabled:t})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(i.J,{htmlFor:"EmailAddress",children:"Email"}),(0,n.jsx)(l.p,{id:"EmailAddress",name:"EmailAddress",type:"email",value:e.EmailAddress,onChange:b,disabled:t})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(i.J,{htmlFor:"MobileNo",children:"Mobile Number"}),(0,n.jsx)(l.p,{id:"MobileNo",name:"MobileNo",value:e.MobileNo,onChange:b,disabled:t})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(i.J,{htmlFor:"Password",children:"Password"}),(0,n.jsx)(l.p,{id:"Password",name:"Password",type:"password",value:e.Password,onChange:b,disabled:t})]}),(0,n.jsx)(a.$,{type:"submit",disabled:t,className:"w-full",children:t?"Processing...":"Test Registration"})]}),f&&(0,n.jsx)("div",{className:"mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded",children:(0,n.jsxs)("p",{children:["Error: ",f]})}),u&&(0,n.jsxs)("div",{className:"mt-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded",children:[(0,n.jsx)("h3",{className:"font-bold",children:"Success!"}),(0,n.jsx)("pre",{className:"whitespace-pre-wrap mt-2",children:JSON.stringify(u,null,2)})]})]})})}},82714:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var n=t(95155),s=t(12115),a=t(40968),l=t(74466),i=t(53999);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)(a.b,{ref:r,className:(0,i.cn)(o(),t),...s})});d.displayName=a.b.displayName},88482:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>u,ZB:()=>o,Zp:()=>l,aR:()=>i,wL:()=>c});var n=t(95155),s=t(12115),a=t(53999);let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});l.displayName="Card";let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",t),...s})});i.displayName="CardHeader";let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});o.displayName="CardTitle";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",t),...s})});d.displayName="CardDescription";let u=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",t),...s})});u.displayName="CardContent";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",t),...s})});c.displayName="CardFooter"},89852:(e,r,t)=>{"use strict";t.d(r,{p:()=>l});var n=t(95155),s=t(12115),a=t(53999);let l=s.forwardRef((e,r)=>{let{className:t,type:s,...l}=e;return(0,n.jsx)("input",{type:s,className:(0,a.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...l})});l.displayName="Input"},97168:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>o});var n=t(95155),s=t(12115),a=t(99708),l=t(74466),i=t(53999);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,r)=>{let{className:t,variant:s,size:l,asChild:d=!1,...u}=e,c=d?a.DX:"button";return(0,n.jsx)(c,{className:(0,i.cn)(o({variant:s,size:l,className:t})),ref:r,...u})});d.displayName="Button"},99708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>i,Dc:()=>d,TL:()=>l});var n=t(12115),s=t(6101),a=t(95155);function l(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...a}=e;if(n.isValidElement(t)){var l;let e,i,o=(l=t,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),d=function(e,r){let t={...r};for(let n in r){let s=e[n],a=r[n];/^on[A-Z]/.test(n)?s&&a?t[n]=(...e)=>{let r=a(...e);return s(...e),r}:s&&(t[n]=s):"style"===n?t[n]={...s,...a}:"className"===n&&(t[n]=[s,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==n.Fragment&&(d.ref=r?(0,s.t)(r,o):o),n.cloneElement(t,d)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:s,...l}=e,i=n.Children.toArray(s),o=i.find(u);if(o){let e=o.props.children,s=i.map(r=>r!==o?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,a.jsx)(r,{...l,ref:t,children:s})});return t.displayName=`${e}.Slot`,t}var i=l("Slot"),o=Symbol("radix.slottable");function d(e){let r=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=o,r}function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{e.O(0,[4277,8441,5964,7358],()=>e(e.s=13711)),_N_E=e.O()}]);