"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/product-details-client.tsx":
/*!*****************************************************!*\
  !*** ./app/product/[id]/product-details-client.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_modern_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/modern-toast */ \"(app-pages-browser)/./components/ui/modern-toast.tsx\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _contexts_currency_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/currency-context */ \"(app-pages-browser)/./contexts/currency-context.tsx\");\n/* harmony import */ var _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/settings-context */ \"(app-pages-browser)/./contexts/settings-context.tsx\");\n/* harmony import */ var _components_products_product_specifications__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/products/product-specifications */ \"(app-pages-browser)/./components/products/product-specifications.tsx\");\n/* harmony import */ var _components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/products/product-media-gallery */ \"(app-pages-browser)/./components/products/product-media-gallery.tsx\");\n/* harmony import */ var _components_ui_countdown__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/countdown */ \"(app-pages-browser)/./components/ui/countdown.tsx\");\n/* harmony import */ var _product_loading__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./product-loading */ \"(app-pages-browser)/./app/product/[id]/product-loading.tsx\");\n/* harmony import */ var _product_error__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./product-error */ \"(app-pages-browser)/./app/product/[id]/product-error.tsx\");\n/* harmony import */ var _components_ui_product_reviews_display__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/product-reviews-display */ \"(app-pages-browser)/./components/ui/product-reviews-display.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to construct image URL\nconst constructImageUrl = (attachmentUrl)=>{\n    if (!attachmentUrl) return \"/placeholder.svg?height=400&width=400\";\n    if (attachmentUrl.startsWith(\"http\")) {\n        return attachmentUrl;\n    }\n    const baseUrl = \"https://admin.codemedicalapps.com\";\n    // Normalize path (ensure it starts with exactly one slash)\n    let normalizedPath = attachmentUrl.startsWith(\"/\") ? attachmentUrl : \"/\".concat(attachmentUrl);\n    // Remove any double slashes in the path\n    normalizedPath = normalizedPath.replace(/\\/+/g, '/');\n    return \"\".concat(baseUrl).concat(normalizedPath);\n};\nfunction ProductDetailsClient(param) {\n    let { productId } = param;\n    var _product_Rating;\n    _s();\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__.useWishlist)();\n    const { rate } = (0,_contexts_currency_context__WEBPACK_IMPORTED_MODULE_11__.useCurrency)();\n    const { primaryColor, primaryTextColor } = (0,_contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings)();\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [activeImage, setActiveImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoLinks, setVideoLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideoIndex, setSelectedVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingToWishlist, setAddingToWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationType, setAnimationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ProductDetailsClient.useState\": ()=>{\n            // Initialize with first option selected for each attribute if none selected\n            const initial = {};\n            if (product === null || product === void 0 ? void 0 : product.AttributesJson) {\n                product.AttributesJson.forEach({\n                    \"ProductDetailsClient.useState\": (attr)=>{\n                        const key = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        initial[key] = true; // Select first option by default\n                    }\n                }[\"ProductDetailsClient.useState\"]);\n            }\n            return initial;\n        }\n    }[\"ProductDetailsClient.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetailsClient.useEffect\": ()=>{\n            fetchProduct();\n        }\n    }[\"ProductDetailsClient.useEffect\"], [\n        productId\n    ]);\n    const fetchProduct = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Try direct API call first, then fallback to proxy if CORS issues\n            const requestBody = {\n                requestParameters: {\n                    ProductId: Number.parseInt(productId, 10),\n                    recordValueJson: \"[]\"\n                }\n            };\n            console.log(\"Fetching product with ID:\", productId, \"Request body:\", requestBody);\n            let response;\n            try {\n                // Import MakeApiCallAsync for JWT token handling\n                const { MakeApiCallAsync } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api-helper */ \"(app-pages-browser)/./lib/api-helper.ts\"));\n                // Use API helper which automatically handles JWT tokens and removes UserID\n                response = await MakeApiCallAsync(\"get-product_detail\", null, requestBody, {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                }, \"POST\", true);\n                console.log(\"API helper response:\", response.data);\n            } catch (apiHelperError) {\n                console.log(\"API helper failed, trying proxy route:\", apiHelperError);\n                // Fallback to proxy route\n                response = await axios__WEBPACK_IMPORTED_MODULE_19__[\"default\"].post(\"/api/product-detail\", requestBody, {\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                console.log(\"Proxy API response:\", response.data);\n            }\n            if (response.data) {\n                // Check if response.data has a nested data property (from API proxy)\n                const responseData = response.data.data ? response.data : response.data;\n                if (responseData && responseData.data) {\n                    try {\n                        // Parse the response data\n                        const parsedData = JSON.parse(responseData.data);\n                        console.log(\"Parsed product data:\", parsedData);\n                        if (parsedData) {\n                            // The API might return an array with one item or a single object\n                            const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;\n                            if (productData) {\n                                // Ensure AttributesJson is properly parsed if it's a string\n                                if (productData.AttributesJson && typeof productData.AttributesJson === \"string\") {\n                                    try {\n                                        productData.AttributesJson = JSON.parse(productData.AttributesJson);\n                                    } catch (e) {\n                                        console.error(\"Error parsing AttributesJson:\", e);\n                                        productData.AttributesJson = [];\n                                    }\n                                } else if (!productData.AttributesJson) {\n                                    productData.AttributesJson = [];\n                                }\n                                console.log(\"Product data with attributes:\", productData);\n                                console.log(\"CategoryName in product data:\", productData.CategoryName);\n                                console.log(\"CategoryID in product data:\", productData.CategoryID);\n                                setProduct(productData);\n                                // Set active image\n                                if (productData.ProductImagesJson && productData.ProductImagesJson.length > 0) {\n                                    const primaryImage = productData.ProductImagesJson.find((img)=>img.IsPrimary) || productData.ProductImagesJson[0];\n                                    setActiveImage(constructImageUrl(primaryImage.AttachmentURL));\n                                }\n                                // Handle comma-separated video links\n                                if (productData.VideoLink) {\n                                    console.log(\"Video links found:\", productData.VideoLink);\n                                    const links = productData.VideoLink.split(\",\").map((link)=>link.trim());\n                                    const processedLinks = links.map((link)=>constructVideoUrl(link));\n                                    setVideoLinks(processedLinks);\n                                    setSelectedVideoIndex(0);\n                                }\n                                // Set initial quantity based on product minimum order quantity\n                                if (productData.OrderMinimumQuantity > 0) {\n                                    setQuantity(productData.OrderMinimumQuantity);\n                                }\n                            } else {\n                                console.error(\"No product data found in parsed response\");\n                                setError(\"Product with ID \".concat(productId, \" not found. Please check if this product exists.\"));\n                            }\n                        } else {\n                            console.error(\"Invalid product data format - parsedData is null/undefined\");\n                            setError(\"Invalid product data format\");\n                        }\n                    } catch (parseError) {\n                        console.error(\"Error parsing product data:\", parseError, \"Raw data:\", responseData.data);\n                        setError(\"Error parsing product data\");\n                    }\n                } else {\n                    console.error(\"No data property in API response:\", response.data);\n                    setError(\"No data in API response\");\n                }\n            } else {\n                console.error(\"Empty response from API\");\n                setError(\"Empty response from server\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching product:\", error);\n            // More detailed error handling\n            if (error.response) {\n                var _error_response_data;\n                // Server responded with error status\n                console.error(\"Server error:\", error.response.status, error.response.data);\n                setError(\"Server error: \".concat(error.response.status, \" - \").concat(((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Unknown error\"));\n            } else if (error.request) {\n                // Request was made but no response received\n                console.error(\"Network error:\", error.request);\n                setError(\"Network error - please check your connection\");\n            } else {\n                // Something else happened\n                console.error(\"Request setup error:\", error.message);\n                setError(\"Error: \".concat(error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const constructVideoUrl = (videoLink)=>{\n        if (!videoLink) return \"\";\n        if (videoLink.includes(\"youtube.com\") || videoLink.includes(\"youtu.be\")) {\n            return videoLink;\n        }\n        // For MP4 videos, use a proxy URL to handle CORS\n        if (videoLink.startsWith(\"http\")) {\n            return \"/api/video-proxy?url=\".concat(encodeURIComponent(videoLink));\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedVideoLink = videoLink.startsWith(\"/\") ? videoLink : \"/\".concat(videoLink);\n        return \"/api/video-proxy?url=\".concat(encodeURIComponent(\"\".concat(baseUrl).concat(normalizedVideoLink)));\n    };\n    // Group attributes by ProductAttributeID\n    const groupedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[groupedAttributes]\": ()=>{\n            if (!(product === null || product === void 0 ? void 0 : product.AttributesJson)) return {};\n            return product.AttributesJson.reduce({\n                \"ProductDetailsClient.useMemo[groupedAttributes]\": (groups, attr)=>{\n                    const groupId = attr.ProductAttributeID;\n                    if (!groups[groupId]) {\n                        groups[groupId] = [];\n                    }\n                    groups[groupId].push(attr);\n                    return groups;\n                }\n            }[\"ProductDetailsClient.useMemo[groupedAttributes]\"], {});\n        }\n    }[\"ProductDetailsClient.useMemo[groupedAttributes]\"], [\n        product === null || product === void 0 ? void 0 : product.AttributesJson\n    ]);\n    // Handle attribute selection with conditional behavior\n    const handleAttributeChange = (attr, isChecked, isRadioGroup)=>{\n        setSelectedAttributes((prev)=>{\n            const newState = {\n                ...prev\n            };\n            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n            // For radio groups, uncheck all other attributes in the same group\n            if (isRadioGroup && isChecked) {\n                Object.keys(prev).forEach((key)=>{\n                    if (key.startsWith(\"\".concat(attr.ProductAttributeID, \"_\")) && key !== attrKey) {\n                        newState[key] = false;\n                    }\n                });\n            }\n            // Set the selected attribute\n            // For checkboxes, toggle the state\n            // For radio buttons, always set to true (since we already unset others if needed)\n            newState[attrKey] = isRadioGroup ? true : !prev[attrKey];\n            return newState;\n        });\n    };\n    // Render price with all price-related information\n    const renderPrice = ()=>{\n        if (!product) return null;\n        const showDiscount = product.DiscountPrice && product.DiscountPrice < product.Price;\n        const adjustedPrice = calculateAdjustedPrice();\n        const showAdjustedPrice = adjustedPrice !== product.Price && adjustedPrice !== (product.DiscountPrice || product.Price);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-baseline gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-3xl font-bold text-primary\",\n                            children: [\n                                \"$\",\n                                showDiscount ? (product.DiscountPrice || 0).toFixed(2) : adjustedPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 13\n                        }, this),\n                        showAdjustedPrice && !showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 13\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded\",\n                            children: [\n                                Math.round((product.Price - (product.DiscountPrice || 0)) / product.Price * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 9\n                }, this),\n                product.PriceIQD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-lg font-medium text-gray-600\",\n                    children: [\n                        product.PriceIQD.toLocaleString(),\n                        \" IQD\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 472,\n                    columnNumber: 11\n                }, this),\n                product.PointNo && product.PointNo > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                        children: [\n                            \"Buy & Earn \",\n                            product.PointNo,\n                            \" $ credit\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 479,\n                    columnNumber: 11\n                }, this),\n                product.OldPrice && product.OldPrice > product.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"line-through\",\n                            children: [\n                                \"$\",\n                                product.OldPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-green-600\",\n                            children: [\n                                Math.round((product.OldPrice - (product.DiscountPrice || product.Price)) / product.OldPrice * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 433,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate adjusted price based on selected attributes\n    const calculateAdjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProductDetailsClient.useCallback[calculateAdjustedPrice]\": ()=>{\n            if (!product) return 0;\n            let adjustedPrice = product.Price;\n            if (product.AttributesJson && product.AttributesJson.length > 0) {\n                product.AttributesJson.forEach({\n                    \"ProductDetailsClient.useCallback[calculateAdjustedPrice]\": (attr)=>{\n                        const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        if (selectedAttributes[attrKey] && typeof attr.PriceAdjustment === \"number\" && typeof attr.PriceAdjustmentType === \"number\") {\n                            switch(attr.PriceAdjustmentType){\n                                case 1:\n                                    adjustedPrice += attr.PriceAdjustment;\n                                    break;\n                                case 2:\n                                    adjustedPrice += product.Price * attr.PriceAdjustment / 100;\n                                    break;\n                            }\n                        }\n                    }\n                }[\"ProductDetailsClient.useCallback[calculateAdjustedPrice]\"]);\n            }\n            return Math.max(0, adjustedPrice); // Ensure price doesn't go below 0\n        }\n    }[\"ProductDetailsClient.useCallback[calculateAdjustedPrice]\"], [\n        product,\n        selectedAttributes\n    ]);\n    const adjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[adjustedPrice]\": ()=>calculateAdjustedPrice()\n    }[\"ProductDetailsClient.useMemo[adjustedPrice]\"], [\n        calculateAdjustedPrice\n    ]);\n    // Render product badges\n    const renderBadges = ()=>{\n        if (!product) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute top-4 left-4 z-10 flex flex-col gap-2\",\n            children: [\n                product.IsDiscountAllowed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-red-500 hover:bg-red-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"SALE\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 545,\n                    columnNumber: 11\n                }, this),\n                product.MarkAsNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-green-500 hover:bg-green-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"NEW\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 550,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 543,\n            columnNumber: 7\n        }, this);\n    };\n    // Combine images and videos into a single media array for the gallery\n    const mediaItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[mediaItems]\": ()=>{\n            var _product_ProductImagesJson;\n            const items = [];\n            // Add product images\n            if (product === null || product === void 0 ? void 0 : (_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : _product_ProductImagesJson.length) {\n                product.ProductImagesJson.forEach({\n                    \"ProductDetailsClient.useMemo[mediaItems]\": (img)=>{\n                        items.push({\n                            type: \"image\",\n                            url: constructImageUrl(img.AttachmentURL),\n                            alt: (product === null || product === void 0 ? void 0 : product.ProductName) || \"Product image\",\n                            thumbnail: constructImageUrl(img.AttachmentURL)\n                        });\n                    }\n                }[\"ProductDetailsClient.useMemo[mediaItems]\"]);\n            }\n            // Add videos\n            videoLinks.forEach({\n                \"ProductDetailsClient.useMemo[mediaItems]\": (videoUrl, index)=>{\n                    items.push({\n                        type: \"video\",\n                        url: videoUrl,\n                        alt: \"\".concat((product === null || product === void 0 ? void 0 : product.ProductName) || \"Product\", \" - Video \").concat(index + 1),\n                        thumbnail: activeImage || \"\"\n                    });\n                }\n            }[\"ProductDetailsClient.useMemo[mediaItems]\"]);\n            return items;\n        }\n    }[\"ProductDetailsClient.useMemo[mediaItems]\"], [\n        product,\n        videoLinks,\n        activeImage\n    ]);\n    const animateCounter = (type)=>{\n        setAnimationType(type);\n        setIsAnimating(true);\n        setTimeout(()=>setIsAnimating(false), 300);\n    };\n    const incrementQuantity = ()=>{\n        if (product) {\n            const maxQuantity = product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity;\n            if (quantity < maxQuantity) {\n                setQuantity((prev)=>prev + 1);\n                animateCounter(\"increment\");\n            } else {\n                // Visual feedback when max quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Maximum quantity of \".concat(maxQuantity, \" reached\"));\n            }\n        }\n    };\n    const decrementQuantity = ()=>{\n        if (product) {\n            const minQuantity = product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1;\n            if (quantity > minQuantity) {\n                setQuantity((prev)=>prev - 1);\n                animateCounter(\"decrement\");\n            } else {\n                // Visual feedback when min quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Minimum quantity is \".concat(minQuantity));\n            }\n        }\n    };\n    // Dynamic button styles based on state\n    const getButtonStyles = (type)=>{\n        const baseStyles = \"flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 rounded-full transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-offset-1\";\n        const disabledStyles = \"bg-gray-100 text-gray-400 cursor-not-allowed\";\n        if (type === \"increment\") {\n            const isMax = product && quantity >= (product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity);\n            return \"\".concat(baseStyles, \" \").concat(isMax ? disabledStyles : \"bg-primary text-white hover:bg-primary/90 focus:ring-primary/50\");\n        } else {\n            const isMin = product && quantity <= (product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1);\n            return \"\".concat(baseStyles, \" \").concat(isMin ? disabledStyles : \"bg-primary text-white hover:bg-primary/90 focus:ring-primary/50\");\n        }\n    };\n    // Counter display with animation\n    const CounterDisplay = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center justify-center w-10 sm:w-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm sm:text-base font-medium transition-all duration-200 \".concat(isAnimating ? \"scale-125 text-primary\" : \"scale-100\"),\n                    children: quantity\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 657,\n                    columnNumber: 7\n                }, this),\n                isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"absolute text-xs font-bold text-primary transition-all duration-200 \".concat(animationType === \"increment\" ? \"-top-4 sm:-top-5\" : \"top-4 sm:top-5\"),\n                    children: animationType === \"increment\" ? \"+1\" : \"-1\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 665,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 656,\n            columnNumber: 5\n        }, this);\n    // Early return if product is not loaded yet\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_loading__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 680,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            error: error,\n            retry: fetchProduct\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 684,\n            columnNumber: 12\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Product Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 690,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: [\n                        'The product with ID \"',\n                        productId,\n                        '\" could not be found. It may not exist in the database.'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 691,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/products\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"View All Products\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 696,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Check the products list to find available product IDs\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 702,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 695,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 689,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.Breadcrumb, {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbList, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 716,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 720,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products?category=\".concat(product.CategoryID || \"all\"),\n                                    children: product.CategoryName || \"Medical Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 722,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 728,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 734,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbPage, {\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 735,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 714,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 713,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden mb-6\",\n                children: (()=>{\n                    // Only show countdown if both sale start and end dates are provided\n                    if (!product.SellStartDatetimeUTC || !product.SellEndDatetimeUTC) {\n                        return null;\n                    }\n                    const now = new Date();\n                    const saleStart = new Date(product.SellStartDatetimeUTC);\n                    const saleEnd = new Date(product.SellEndDatetimeUTC);\n                    // Check if sale is currently active or upcoming\n                    const isSaleActive = now >= saleStart && now <= saleEnd;\n                    const isSaleUpcoming = now < saleStart;\n                    const isSaleExpired = now > saleEnd;\n                    // Don't show timer if sale has expired\n                    if (isSaleExpired) {\n                        return null;\n                    }\n                    // Determine the countdown target date\n                    const countdownEndDate = isSaleUpcoming ? product.SellStartDatetimeUTC : product.SellEndDatetimeUTC;\n                    // Determine the message based on sale state\n                    let timerMessage = \"🔥 Limited Time Sale!\";\n                    let urgencyMessage = \"Sale ends soon - grab yours now!\";\n                    if (isSaleUpcoming) {\n                        timerMessage = \"⏰ Sale Starts Soon!\";\n                        urgencyMessage = \"Get ready for an amazing deal!\";\n                    } else if (isSaleActive) {\n                        timerMessage = \"🔥 Limited Time Sale!\";\n                        urgencyMessage = \"Sale ends soon - don't miss out!\";\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-2 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 text-red-500 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-bold text-red-600\",\n                                        children: timerMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 780,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_countdown__WEBPACK_IMPORTED_MODULE_15__.Countdown, {\n                                    endDate: countdownEndDate,\n                                    className: \"transform hover:scale-105 transition-transform duration-200\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 788,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 787,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-xs font-medium text-gray-700\",\n                                children: urgencyMessage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 794,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 779,\n                        columnNumber: 13\n                    }, this);\n                })()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 742,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_14__.ProductMediaGallery, {\n                            media: mediaItems,\n                            className: \"w-full rounded-lg overflow-hidden\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 805,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 804,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:w-1/2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-2\",\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 813,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            ...Array(5)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(i < Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 817,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 ml-2\",\n                                        children: [\n                                            \"(\",\n                                            product.Rating || 0,\n                                            \") \",\n                                            product.TotalReviews || 0,\n                                            \" reviews\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 11\n                            }, this),\n                            renderPrice(),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:block\",\n                                children: (()=>{\n                                    // Only show countdown if both sale start and end dates are provided\n                                    if (!product.SellStartDatetimeUTC || !product.SellEndDatetimeUTC) {\n                                        return null;\n                                    }\n                                    const now = new Date();\n                                    const saleStart = new Date(product.SellStartDatetimeUTC);\n                                    const saleEnd = new Date(product.SellEndDatetimeUTC);\n                                    // Check if sale is currently active or upcoming\n                                    const isSaleActive = now >= saleStart && now <= saleEnd;\n                                    const isSaleUpcoming = now < saleStart;\n                                    const isSaleExpired = now > saleEnd;\n                                    // Don't show timer if sale has expired\n                                    if (isSaleExpired) {\n                                        return null;\n                                    }\n                                    // Determine the countdown target date\n                                    const countdownEndDate = isSaleUpcoming ? product.SellStartDatetimeUTC : product.SellEndDatetimeUTC;\n                                    // Determine the message based on sale state\n                                    let timerMessage = \"🔥 Limited Time Sale!\";\n                                    let urgencyMessage = \"Sale ends soon - grab yours now!\";\n                                    if (isSaleUpcoming) {\n                                        timerMessage = \"⏰ Sale Starts Soon!\";\n                                        urgencyMessage = \"Get ready for an amazing deal!\";\n                                    } else if (isSaleActive) {\n                                        timerMessage = \"🔥 Limited Time Sale!\";\n                                        urgencyMessage = \"Sale ends soon - don't miss out!\";\n                                    }\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 p-4 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center gap-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-6 w-6 text-red-500 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 877,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-red-600\",\n                                                        children: timerMessage\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 878,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_countdown__WEBPACK_IMPORTED_MODULE_15__.Countdown, {\n                                                    endDate: countdownEndDate,\n                                                    className: \"transform hover:scale-105 transition-transform duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 884,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 883,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm font-medium text-gray-700\",\n                                                children: urgencyMessage\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 890,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 17\n                                    }, this);\n                                })()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 11\n                            }, this),\n                            product.ShortDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-sm max-w-none mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: product.ShortDescription\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 900,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 border-t border-gray-200 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 909,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Choose your preferences from the options below.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 912,\n                                        columnNumber: 13\n                                    }, this),\n                                    Object.entries(groupedAttributes).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: Object.entries(groupedAttributes).map((param)=>{\n                                            let [groupId, attributes] = param;\n                                            var _attributes_, _attributes_1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            ((_attributes_ = attributes[0]) === null || _attributes_ === void 0 ? void 0 : _attributes_.DisplayName) || ((_attributes_1 = attributes[0]) === null || _attributes_1 === void 0 ? void 0 : _attributes_1.AttributeName),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 pl-4\",\n                                                        children: attributes.map((attr)=>{\n                                                            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                                                            const isSelected = !!selectedAttributes[attrKey];\n                                                            const isRadioGroup = attributes.length > 1;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center h-5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: isRadioGroup ? \"radio\" : \"checkbox\",\n                                                                            id: \"attr-\".concat(attrKey),\n                                                                            name: \"attr-group-\".concat(groupId),\n                                                                            className: \"h-4 w-4 \".concat(isRadioGroup ? \"rounded-full\" : \"rounded\", \" border-gray-300 text-primary focus:ring-primary\"),\n                                                                            checked: isSelected,\n                                                                            onChange: (e)=>handleAttributeChange(attr, e.target.checked, isRadioGroup)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                            lineNumber: 934,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 933,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-3 text-sm\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"attr-\".concat(attrKey),\n                                                                            className: \"font-medium \".concat(isSelected ? \"text-primary\" : \"text-gray-700\"),\n                                                                            children: [\n                                                                                attr.AttributeValueText,\n                                                                                (attr.PriceAdjustment || attr.PriceAdjustment === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-2 text-sm font-normal text-green-600\",\n                                                                                    children: [\n                                                                                        \"(\",\n                                                                                        attr.PriceAdjustmentType === 1 ? \"+\" : \"\",\n                                                                                        \"$\",\n                                                                                        attr.PriceAdjustment,\n                                                                                        \" \",\n                                                                                        attr.PriceAdjustmentType === 2 ? \"%\" : \"\",\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                                    lineNumber: 963,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                            lineNumber: 952,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 951,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, attrKey, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 932,\n                                                                columnNumber: 29\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 925,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, \"attr-group-\".concat(groupId), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 916,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No additional product details available.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 986,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 908,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Quantity:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between sm:justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: decrementQuantity,\n                                                            className: getButtonStyles(\"decrement\"),\n                                                            disabled: quantity <= (product.OrderMinimumQuantity || 1),\n                                                            \"aria-label\": \"Decrease quantity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"h-4 w-4 sm:h-5 sm:w-5\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                fill: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                    lineNumber: 1010,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1004,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 998,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CounterDisplay, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1018,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: incrementQuantity,\n                                                            className: getButtonStyles(\"increment\"),\n                                                            disabled: product.OrderMaximumQuantity > 0 ? quantity >= Math.min(product.OrderMaximumQuantity, product.StockQuantity) : quantity >= product.StockQuantity,\n                                                            \"aria-label\": \"Increase quantity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"h-4 w-4 sm:h-5 sm:w-5\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                fill: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                    lineNumber: 1040,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1034,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1020,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 997,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row sm:items-center gap-2 sm:ml-4\",\n                                                    children: [\n                                                        product.OrderMinimumQuantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Min: \",\n                                                                product.OrderMinimumQuantity\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1051,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.OrderMaximumQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Max:\",\n                                                                \" \",\n                                                                Math.min(product.OrderMaximumQuantity, product.StockQuantity)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1057,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1049,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 996,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 994,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 993,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-3 sm:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none text-sm sm:text-base\",\n                                        disabled: product.StockQuantity <= 0 || addingToCart,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToCart(true);\n                                            try {\n                                                var _product_ProductImagesJson_, _product_ProductImagesJson;\n                                                // Get the first product image or use a placeholder\n                                                const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0 ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL) : \"/placeholder.jpg\";\n                                                // Get selected attributes\n                                                const selectedAttrs = (product.AttributesJson || []).filter((attr)=>selectedAttributes[\"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID)]);\n                                                // Add to cart using the cart context with attributes and adjusted price\n                                                cart.addToCart({\n                                                    id: product.ProductId,\n                                                    name: product.ProductName,\n                                                    price: product.DiscountPrice || product.Price,\n                                                    discountPrice: product.DiscountPrice,\n                                                    image: productImage,\n                                                    originalPrice: product.Price\n                                                }, quantity, selectedAttrs, product.PriceIQD, rate // Pass currency rate as the fifth parameter\n                                                );\n                                                // Show modern toast notification\n                                                (0,_components_ui_modern_toast__WEBPACK_IMPORTED_MODULE_8__.showModernAddToCartToast)({\n                                                    productName: product.ProductName,\n                                                    quantity,\n                                                    productImage: ((_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : (_product_ProductImagesJson_ = _product_ProductImagesJson[0]) === null || _product_ProductImagesJson_ === void 0 ? void 0 : _product_ProductImagesJson_.AttachmentURL) || \"/placeholder.svg\",\n                                                    onViewCart: ()=>{\n                                                        window.location.href = \"/cart\";\n                                                    }\n                                                });\n                                            } catch (error) {\n                                                console.error(\"Error adding to cart:\", error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Failed to add product to cart. Please try again.\");\n                                            } finally{\n                                                setAddingToCart(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1137,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1139,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: addingToCart ? \"Adding...\" : \"Add to Cart\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1141,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1073,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none text-sm sm:text-base sm:flex-initial sm:min-w-[120px]\",\n                                        disabled: addingToWishlist,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToWishlist(true);\n                                            try {\n                                                // Check if product is already in wishlist\n                                                const isAlreadyInWishlist = wishlist.isInWishlist(product.ProductId);\n                                                if (isAlreadyInWishlist) {\n                                                    // Remove from wishlist if already there\n                                                    wishlist.removeFromWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(product.ProductName, \" removed from wishlist\"));\n                                                } else {\n                                                    var _product_ProductImagesJson_, _product_ProductImagesJson;\n                                                    // Add to wishlist\n                                                    const productUrl = \"/product/\".concat(product.ProductId);\n                                                    const imageUrl = ((_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : (_product_ProductImagesJson_ = _product_ProductImagesJson[0]) === null || _product_ProductImagesJson_ === void 0 ? void 0 : _product_ProductImagesJson_.AttachmentURL) || \"/placeholder.svg\";\n                                                    const price = product.DiscountPrice || product.Price;\n                                                    wishlist.addToWishlist(product.ProductId, product.ProductName, productUrl, imageUrl, price);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(product.ProductName, \" added to wishlist\"));\n                                                }\n                                            } catch (error) {\n                                                console.error(\"Error updating wishlist:\", error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Failed to update wishlist. Please try again.\");\n                                            } finally{\n                                                setAddingToWishlist(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1189,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-5 w-5\",\n                                                fill: product && wishlist.isInWishlist(product.ProductId) ? \"currentColor\" : \"none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: addingToWishlist ? \"Updating...\" : product && wishlist.isInWishlist(product.ProductId) ? \"Remove\" : \"Wishlist\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1200,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground text-sm sm:text-base sm:flex-initial sm:min-w-[100px]\",\n                                        onClick: ()=>{\n                                            if (navigator.share) {\n                                                navigator.share({\n                                                    title: (product === null || product === void 0 ? void 0 : product.MetaTitle) || (product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    text: (product === null || product === void 0 ? void 0 : product.MetaDescription) || \"Check out this product: \".concat(product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    url: window.location.href\n                                                }).catch((err)=>console.error(\"Error sharing:\", err));\n                                            } else {\n                                                // Fallback - copy to clipboard\n                                                navigator.clipboard.writeText(window.location.href);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Product link copied to clipboard\");\n                                            }\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1231,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: \"Share\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1232,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1210,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1071,\n                                columnNumber: 11\n                            }, this),\n                            product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-3\",\n                                        children: \"Product Tags\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs bg-white/70 hover:bg-white transition-colors\",\n                                                children: keyword.trim()\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1242,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1240,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1238,\n                                columnNumber: 13\n                            }, this),\n                            product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1258,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"About This Product\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 leading-relaxed\",\n                                        children: product.MetaDescription\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1256,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 812,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 802,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                    defaultValue: \"description\",\n                    className: \"w-full\",\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                            className: \"grid w-full grid-cols-3 mb-6 gap-2 bg-transparent p-0 h-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"description\",\n                                    className: \"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === \"description\" ? primaryColor : \"rgb(209 213 219)\",\n                                        color: activeTab === \"description\" ? primaryTextColor : \"rgb(55 65 81)\",\n                                        transform: activeTab === \"description\" ? \"scale(1.05)\" : \"scale(1)\",\n                                        boxShadow: activeTab === \"description\" ? \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\" : \"none\",\n                                        borderColor: activeTab === \"description\" ? primaryColor : \"transparent\"\n                                    },\n                                    children: \"Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"reviews\",\n                                    className: \"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === \"reviews\" ? primaryColor : \"rgb(209 213 219)\",\n                                        color: activeTab === \"reviews\" ? primaryTextColor : \"rgb(55 65 81)\",\n                                        transform: activeTab === \"reviews\" ? \"scale(1.05)\" : \"scale(1)\",\n                                        boxShadow: activeTab === \"reviews\" ? \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\" : \"none\",\n                                        borderColor: activeTab === \"reviews\" ? primaryColor : \"transparent\"\n                                    },\n                                    children: \"Specifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"shipping\",\n                                    className: \"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === \"shipping\" ? primaryColor : \"rgb(209 213 219)\",\n                                        color: activeTab === \"shipping\" ? primaryTextColor : \"rgb(55 65 81)\",\n                                        transform: activeTab === \"shipping\" ? \"scale(1.05)\" : \"scale(1)\",\n                                        boxShadow: activeTab === \"shipping\" ? \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\" : \"none\",\n                                        borderColor: activeTab === \"shipping\" ? primaryColor : \"transparent\"\n                                    },\n                                    children: \"Reviews\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1321,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"description\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Product Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1348,\n                                        columnNumber: 15\n                                    }, this),\n                                    product.FullDescription ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none\",\n                                        dangerouslySetInnerHTML: {\n                                            __html: product.FullDescription\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1350,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: product.ShortDescription || \"This is a high-quality medical product designed to meet professional standards.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1356,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900 mb-2\",\n                                                                children: \"Key Features\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1362,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-sm text-gray-700 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Professional grade quality\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1366,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Durable construction\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1367,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Easy to use\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1368,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Reliable performance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1369,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1365,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1361,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900 mb-2\",\n                                                                children: \"Benefits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1373,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-sm text-gray-700 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Enhanced efficiency\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1377,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Cost-effective solution\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1378,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Long-lasting durability\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1379,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Professional results\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1380,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1376,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1372,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1360,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1355,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1347,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1343,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"reviews\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Product Specifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1394,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_specifications__WEBPACK_IMPORTED_MODULE_13__.ProductSpecifications, {\n                                        attributes: product.AttributesJson || []\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1397,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1393,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1389,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"shipping\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Customer Reviews\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1408,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            1,\n                                                            2,\n                                                            3,\n                                                            4,\n                                                            5\n                                                        ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"w-6 h-6 \".concat(star <= Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                            }, star, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1413,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1411,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: ((_product_Rating = product.Rating) === null || _product_Rating === void 0 ? void 0 : _product_Rating.toFixed(1)) || \"0.0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1424,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            \"out of 5\",\n                                                            product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \" \",\n                                                                    \"• \",\n                                                                    product.TotalReviews,\n                                                                    \" review\",\n                                                                    product.TotalReviews !== 1 ? \"s\" : \"\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1429,\n                                                                columnNumber: 23\n                                                            }, this) : null\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1423,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1410,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_product_reviews_display__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    productId: product.ProductId,\n                                                    showTitle: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1439,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1438,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1409,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1407,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1403,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 1271,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 1270,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n        lineNumber: 711,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDetailsClient, \"fhF87DY2zm3u0DvUJErcoKedGAA=\", false, function() {\n    return [\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__.useWishlist,\n        _contexts_currency_context__WEBPACK_IMPORTED_MODULE_11__.useCurrency,\n        _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings\n    ];\n});\n_c = ProductDetailsClient;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductDetailsClient);\nvar _c;\n$RefreshReg$(_c, \"ProductDetailsClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/product-details-client.tsx\n"));

/***/ })

});