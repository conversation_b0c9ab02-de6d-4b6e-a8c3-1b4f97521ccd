(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5409,6763,7790],{5623:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},13052:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},30024:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(95155),o=r(12115),a=r(35695),n=r(84995),l=r(88482),i=r(6874),c=r.n(i),d=r(79891),u=r(65409);function m(){let{categoryId:e}=(0,a.useParams)(),{t,primaryColor:r}=(0,d.t)(),[i,m]=(0,o.useState)([]),[g,p]=(0,o.useState)(!0),[f,h]=(0,o.useState)("");return((0,o.useEffect)(()=>{(async()=>{try{var t;let r={"Content-Type":"application/json",Accept:"application/json",Authorization:"Bearer "+localStorage.getItem("token")},s=await (0,u.MakeApiCallAsync)("get-products-list",null,{PageNumber:1,PageSize:100,SortColumn:"Name",SortOrder:"ASC",CategoryId:e},r,"POST",!0);if(null==s||null==(t=s.data)?void 0:t.data){let e=JSON.parse(s.data.data),t=e.map(e=>{var t,r;return{id:e.ProductId,name:e.ProductName,price:e.Price,discountPrice:e.DiscountedPrice||void 0,image:null==(r=e.ProductImagesJson)||null==(t=r[0])?void 0:t.AttachmentURL,description:e.ShortDescription||void 0,categoryId:e.CategoryID}});m(t),e.length>0&&h(e[0].CategoryName||"")}}catch(e){console.error("Error fetching products:",e)}finally{p(!1)}})()},[e]),g)?(0,s.jsx)("div",{className:"container mx-auto py-8 px-4",children:"Loading..."}):(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,s.jsx)(n.Qp,{className:"mb-6",children:(0,s.jsxs)(n.AB,{children:[(0,s.jsx)(n.J5,{children:(0,s.jsx)(n.w1,{asChild:!0,children:(0,s.jsx)(c(),{href:"/",children:t("home")})})}),(0,s.jsx)(n.tH,{}),(0,s.jsx)(n.J5,{children:(0,s.jsx)(n.tJ,{children:f})})]})}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-8",children:f}),0===i.length?(0,s.jsx)(l.Zp,{className:"p-6 text-center",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No products found in this category."})}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:i.map(e=>(0,s.jsx)(l.Zp,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:(0,s.jsxs)(c(),{href:"/product/".concat(e.id),children:[(0,s.jsx)("div",{className:"aspect-[3/4] relative bg-muted",children:(0,s.jsx)("img",{src:e.image?e.image.startsWith("http")?e.image:e.image.startsWith("/")?"".concat(u.Config.ADMIN_BASE_URL.replace(/\/$/,"")).concat(e.image):"".concat(u.Config.ADMIN_BASE_URL.replace(/\/$/,""),"/").concat(e.image):"/products/book".concat(e.id,".jpg"),alt:e.name,className:"object-cover w-full h-full",onError:t=>{let r=t.target;r.onerror=null,r.src="/products/book".concat(e.id,".jpg")}})}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h3",{className:"font-medium mb-2 line-clamp-2",children:e.name}),(0,s.jsx)("div",{className:"flex items-baseline gap-2",children:e.discountPrice?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("span",{className:"text-lg font-bold",style:{color:r},children:["$",e.discountPrice.toFixed(2)]}),(0,s.jsxs)("span",{className:"text-sm text-muted-foreground line-through",children:["$",e.price.toFixed(2)]})]}):(0,s.jsxs)("span",{className:"text-lg font-bold",style:{color:r},children:["$",e.price.toFixed(2)]})})]})]})},e.id))})]})]})}},35695:(e,t,r)=>{"use strict";var s=r(18999);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(52596),o=r(39688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,s.$)(t))}},58890:(e,t,r)=>{Promise.resolve().then(r.bind(r,30024))},61204:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});let s={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",COMMON_CONTROLLER_SUB_URL:"api/v1/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile"}}},65409:(e,t,r)=>{"use strict";r.d(t,{$g:()=>u,Config:()=>a,MakeApiCallAsync:()=>i,XX:()=>d,k6:()=>c});var s=r(23464),o=r(61204);s.A.defaults.timeout=3e4,"https:"===window.location.protocol&&o.T.ADMIN_BASE_URL.includes("localhost")&&(s.A.defaults.httpsAgent={rejectUnauthorized:!1});let a={ADMIN_BASE_URL:o.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...o.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",RESET_PASSWORD_FIREBASE:"reset-password-firebase",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile",INSERT_PRODUCT_REVIEW:"Insert-Product-Review",GET_PRODUCT_REVIEWS:"get-product-reviews"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},n=async()=>{try{{try{let e=await fetch("/api/auth/get-token",{method:"GET",credentials:"include"});if(e.ok){let t=await e.json();if(t.token)return console.log("\uD83D\uDD10 Retrieved JWT token from secure HttpOnly cookie"),t.token}}catch(e){console.log("API token retrieval failed, trying client-side cookies:",e)}for(let e of document.cookie.split(";")){let[t,r]=e.trim().split("=");if("auth_token"===t)return console.log("\uD83D\uDD10 Retrieved JWT token from client-side cookie"),decodeURIComponent(r)}let e=localStorage.getItem("token")||localStorage.getItem("authToken");if(e)return console.log("⚠️ Using token from localStorage (migrating to secure cookies)"),localStorage.removeItem("token"),localStorage.removeItem("authToken"),e}return null}catch(e){return console.error("Error getting token for header:",e),null}},l=async()=>{try{{for(let r of document.cookie.split(";")){let[s,o]=r.trim().split("=");if("auth_user"===s)try{var e,t;let r=JSON.parse(decodeURIComponent(o)),s=(null==(e=r.UserId)?void 0:e.toString())||(null==(t=r.UserID)?void 0:t.toString());if(s)return console.log("\uD83D\uDD10 Retrieved User ID from secure cookie"),s}catch(e){console.warn("Failed to parse user data from cookie:",e)}}let r=localStorage.getItem("userId")||localStorage.getItem("userID");if(r)return console.log("⚠️ Using User ID from localStorage (migrating to secure cookies)"),localStorage.removeItem("userId"),localStorage.removeItem("userID"),r}return null}catch(e){return console.error("Error getting user ID for header:",e),null}},i=async function(e,t,r,o,i){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let c=(e=>{if(!e)return e;let t=JSON.parse(JSON.stringify(e));return t.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from request body (will use JWT token instead)"),delete t.UserId),t.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from request body (will use JWT token instead)"),delete t.UserID),t.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from request body (will use JWT token instead)"),delete t.user_id),t.requestParameters&&(t.requestParameters.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from requestParameters (will use JWT token instead)"),delete t.requestParameters.UserId),t.requestParameters.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from requestParameters (will use JWT token instead)"),delete t.requestParameters.UserID),t.requestParameters.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from requestParameters (will use JWT token instead)"),delete t.requestParameters.user_id)),t})(r),d={...o};if(!d.hasOwnProperty("Authorization")){let e=await n();e&&(d.Authorization="Bearer "+e,console.log("\uD83D\uDD10 Added JWT token to Authorization header"))}if(!d.hasOwnProperty("Token")){let e=await n();d.Token=null!=e?e:"",e&&console.log("\uD83D\uDD10 Added JWT token to Token header (backward compatibility)")}if(!d.hasOwnProperty("UserID")){let e=await l();d.UserID=null!=e?e:""}d.hasOwnProperty("Accept")||(d.Accept="application/json"),d.hasOwnProperty("Content-Type")||(d["Content-Type"]="application/json");let u=a.ADMIN_BASE_URL+(null===t||void 0==t?a.DYNAMIC_METHOD_SUB_URL:t)+e;i=null!=i?i:"POST";let m={headers:d,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===i)return await s.A.post(u,c,m);if("GET"==i)return m.params=c,await s.A.get(u,m);return{data:{errorMessage:"Unsupported method type: ".concat(i),status:"method_not_supported"}}}catch(t){console.error("API call failed:",t);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(t&&"object"==typeof t&&"response"in t&&t.response){var c,d;let r=null==(c=t.response)?void 0:c.data;e.data={errorMessage:(null==r?void 0:r.errorMessage)||"An error occurred while processing your request.",status:null==(d=t.response)?void 0:d.status}}else if(t&&"object"==typeof t&&"request"in t){let r="Network error: No response received from server.";t.message&&t.message.includes("Network Error")&&(r="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+a.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:r,status:"network_error"}}else e.data={errorMessage:t&&"object"==typeof t&&"message"in t?t.message:"An unexpected error occurred",status:"request_error"};return e}},c=async()=>{try{return console.log("Using default currency rate (1430) - API endpoint not available"),1430}catch(e){return console.error("Error fetching currency rate:",e),1430}},d=(e,t)=>Math.round(e*t),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return null==e||isNaN(e)?"IQD"===t?"0 IQD":"$0.00":"IQD"===t?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},79891:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i,t:()=>c});var s=r(95155),o=r(12115);let a={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us",searchProducts:"Search products..."},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",searchProducts:"ابحث عن المنتجات...",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}};var n=r(94213);let l=(0,o.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,o.useState)("light"),[c,d]=(0,o.useState)("en"),[u,m]=(0,o.useState)("#0074b2"),[g,p]=(0,o.useState)("#ffffff");return(0,o.useEffect)(()=>{let e=(0,n.N)(u);p(e),document.documentElement.style.setProperty("--primary",u),document.documentElement.style.setProperty("--primary-foreground",e)},[u]),(0,s.jsx)(l.Provider,{value:{theme:r,language:c,primaryColor:u,primaryTextColor:g,toggleTheme:()=>{i("light"===r?"dark":"light")},setLanguage:e=>{d(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{m(e);let t=(0,n.N)(e);p(t),document.documentElement.style.setProperty("--primary",e),document.documentElement.style.setProperty("--primary-foreground",t)},t:e=>(function(e,t){let r=a[t];return e in r?r[e]:"en"!==t&&e in a.en?a.en[e]:e})(e,c)},children:t})}function c(){let e=(0,o.useContext)(l);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},84995:(e,t,r)=>{"use strict";r.d(t,{AB:()=>c,J5:()=>d,Qp:()=>i,tH:()=>g,tJ:()=>m,w1:()=>u});var s=r(95155),o=r(12115),a=r(99708),n=r(13052),l=(r(5623),r(53999));let i=o.forwardRef((e,t)=>{let{...r}=e;return(0,s.jsx)("nav",{ref:t,"aria-label":"breadcrumb",...r})});i.displayName="Breadcrumb";let c=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("ol",{ref:t,className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",r),...o})});c.displayName="BreadcrumbList";let d=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("li",{ref:t,className:(0,l.cn)("inline-flex items-center gap-1.5",r),...o})});d.displayName="BreadcrumbItem";let u=o.forwardRef((e,t)=>{let{asChild:r,className:o,...n}=e,i=r?a.DX:"a";return(0,s.jsx)(i,{ref:t,className:(0,l.cn)("transition-colors hover:text-foreground",o),...n})});u.displayName="BreadcrumbLink";let m=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,l.cn)("font-normal text-foreground",r),...o})});m.displayName="BreadcrumbPage";let g=e=>{let{children:t,className:r,...o}=e;return(0,s.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,l.cn)("[&>svg]:size-3.5",r),...o,children:null!=t?t:(0,s.jsx)(n.A,{})})};g.displayName="BreadcrumbSeparator"},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>i,Zp:()=>n,aR:()=>l,wL:()=>u});var s=r(95155),o=r(12115),a=r(53999);let n=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...o})});n.displayName="Card";let l=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",r),...o})});l.displayName="CardHeader";let i=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",r),...o})});i.displayName="CardTitle";let c=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",r),...o})});c.displayName="CardDescription";let d=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",r),...o})});d.displayName="CardContent";let u=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",r),...o})});u.displayName="CardFooter"},94213:(e,t,r)=>{"use strict";function s(e,t){let r=e=>{let t=e.replace("#",""),r=parseInt(t.slice(0,2),16)/255,s=[r,parseInt(t.slice(2,4),16)/255,parseInt(t.slice(4,6),16)/255].map(e=>e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4));return .2126*s[0]+.7152*s[1]+.0722*s[2]},s=r(e),o=r(t);return(Math.max(s,o)+.05)/(Math.min(s,o)+.05)}function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"AA",r=s(e,"#ffffff"),o=s(e,"#000000"),a="AAA"===t?7:4.5;return r>=a&&o>=a?r>o?"#ffffff":"#000000":r>=a?"#ffffff":o>=a?"#000000":r>o?"#ffffff":"#000000"}r.d(t,{N:()=>o})}},e=>{e.O(0,[4277,3464,4706,8441,5964,7358],()=>e(e.s=58890)),_N_E=e.O()}]);