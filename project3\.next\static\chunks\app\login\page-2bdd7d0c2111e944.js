(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{5896:(e,t,s)=>{Promise.resolve().then(s.bind(s,35014))},35014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>P});var r=s(95155),a=s(12115),i=s(88482),l=s(97168),o=s(89852),n=s(82714),d=s(1978),c=s(28883),u=s(32919),m=s(53904),h=s(75525),p=s(6874),x=s.n(p),g=s(35695),f=s(79891),v=s(98816),b=s(49223),w=s(53999),j=s(78749),N=s(92657);let y=a.forwardRef((e,t)=>{let{className:s,...i}=e,[n,d]=a.useState(!1);return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.p,{type:n?"text":"password",className:(0,w.cn)("pr-10",s),ref:t,...i}),(0,r.jsx)(l.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>d(!n),"aria-label":n?"Hide password":"Show password",children:n?(0,r.jsx)(j.A,{className:"h-4 w-4","aria-hidden":"true"}):(0,r.jsx)(N.A,{className:"h-4 w-4","aria-hidden":"true"})})]})});y.displayName="PasswordInput";var A=s(53580),k=s(30242);function P(){let{isAuthorized:e,isLoading:t}=(0,b.zK)({requireAuth:!1,redirectIfAuthenticated:!0}),[s,p]=(0,a.useState)(""),[j,N]=(0,a.useState)({email:"",password:""}),{t:P}=(0,f.t)(),{login:S,isLoading:C,isLoggedIn:R}=(0,v.J)(),{toast:E}=(0,A.dj)(),L=(0,g.useRouter)(),{executeRecaptcha:T}=(0,k._Y)(),[U,_]=(0,a.useState)({email:"",password:""}),[z,G]=(0,a.useState)(!1),I=()=>{let e={email:"",password:""},t=!0;return j.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(j.email)||(e.email="Please enter a valid email address",t=!1):(e.email="Email is required",t=!1),j.password.trim()||(e.password="Password is required",t=!1),_(e),t};if((0,a.useEffect)(()=>{if(R){let e=new URLSearchParams(window.location.search).get("redirect");if(e){let t=decodeURIComponent(e);if(t.startsWith("/")&&!t.startsWith("//")){console.log("\uD83D\uDD04 Already logged in, redirecting to stored URL:",t),L.push(t);return}}L.push("/account")}},[R,L]),t||null===e)return(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Checking authentication..."})]})});if(!e)return null;let q=async e=>{if(e.preventDefault(),I()){p("");try{if(!T)return void p("reCAPTCHA not available. Please refresh the page.");G(!0);let e=await T("login");if(!e){p("reCAPTCHA verification failed. Please try again."),G(!1);return}console.log("reCAPTCHA token obtained:",e.substring(0,20)+"..."),G(!1),await fetch("/api/auth/clear-cookies",{method:"POST",credentials:"include"});let t=await S(j.email,j.password,e);if(t.success){E({title:"Login Successful",description:t.message,variant:"default"});try{(await fetch("/api/auth/get-token",{method:"GET",credentials:"include"})).ok||console.warn("Failed to refresh auth token after login")}catch(e){console.error("Error refreshing token:",e)}let e=new URLSearchParams(window.location.search).get("redirect");if(await new Promise(e=>setTimeout(e,300)),e){let t=decodeURIComponent(e);if(t.startsWith("/")&&!t.startsWith("//")){console.log("\uD83D\uDD04 Hard redirecting to stored URL after login:",t),window.location.href=t;return}}console.log("\uD83C\uDFE0 No valid redirect URL, going to account page"),window.location.href="/account"}else p(t.message),E({title:"Login Failed",description:t.message,variant:"destructive"})}catch(t){let e=t.message||"Failed to login";p(e),E({title:"Login Error",description:e,variant:"destructive"}),console.error("Error:",t),G(!1)}}};return(0,r.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"mt-6 text-3xl font-extrabold",children:"Welcome Back"}),(0,r.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:"Sign in to your account"})]}),(0,r.jsx)(i.Zp,{className:"mt-8 p-8 shadow-xl bg-card",children:(0,r.jsx)(d.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:(0,r.jsxs)("form",{onSubmit:q,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.J,{className:"block text-sm font-medium mb-2",children:"Email"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(o.p,{type:"email",value:j.email,onChange:e=>{N({...j,email:e.target.value}),U.email&&I()},className:(0,w.cn)("pl-10",U.email&&"border-red-500"),required:!0,disabled:C}),(0,r.jsx)(c.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]}),U.email&&(0,r.jsx)("p",{className:"mt-1 text-sm text-destructive",children:U.email})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(n.J,{className:"block text-sm font-medium mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(y,{value:j.password,onChange:e=>{N({...j,password:e.target.value}),U.password&&I()},className:(0,w.cn)("pl-10",U.password&&"border-red-500"),required:!0,disabled:C}),(0,r.jsx)(u.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]}),U.password&&(0,r.jsx)("p",{className:"mt-1 text-sm text-destructive",children:U.password})]}),s&&(0,r.jsx)("div",{className:"text-red-500 text-sm text-center",children:s}),(0,r.jsx)(l.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors",disabled:C||z,children:C||z?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"w-4 h-4 animate-spin"}),z?"Verifying...":"Signing In..."]}):(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),"Sign In"]})}),(0,r.jsxs)("div",{className:"text-xs text-center text-muted-foreground",children:["This site is protected by reCAPTCHA and the Google"," ",(0,r.jsx)("a",{href:"https://policies.google.com/privacy",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"Privacy Policy"})," ","and"," ",(0,r.jsx)("a",{href:"https://policies.google.com/terms",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"Terms of Service"})," ","apply."]}),(0,r.jsxs)("div",{className:"text-center text-sm space-y-2",children:[(0,r.jsx)("div",{children:(0,r.jsx)(x(),{href:"/forgot-password",className:"text-primary hover:text-primary/80 hover:underline transition-colors",children:"Forgot your password?"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Don't have an account? "}),(0,r.jsx)(x(),{href:"/signup",className:"text-primary hover:text-primary/80 hover:underline transition-colors",children:"Sign up"})]})]})]})})})]})})}},49223:(e,t,s)=>{"use strict";s.d(t,{zK:()=>i}),s(95155);var r=s(35695),a=s(98816);function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{redirectTo:t="/login",requireAuth:s=!0,redirectIfAuthenticated:i=!1}=e,{isLoggedIn:l,isLoading:o}=(0,a.J)();(0,r.useRouter)(),(0,r.usePathname)(),console.log("\uD83D\uDD27 useAuthGuard: Auth state check",{isLoggedIn:l,isLoading:o,requireAuth:s,redirectIfAuthenticated:i});let n=null;return o?(console.log("\uD83D\uDD27 useAuthGuard: Still loading, isAuthorized = null"),n=null):s&&!l?(console.log("\uD83D\uDD12 useAuthGuard: Authentication required but user not logged in, isAuthorized = false"),n=!1):i&&l?(console.log("\uD83D\uDD13 useAuthGuard: User authenticated but should be redirected, isAuthorized = false"),n=!1):(console.log("✅ useAuthGuard: User is authorized, isAuthorized = true"),n=!0),console.log("\uD83D\uDD27 useAuthGuard: Final result",{isAuthorized:n,isLoading:o,isLoggedIn:l}),{isAuthorized:n,isLoading:o,isLoggedIn:l}}},82714:(e,t,s)=>{"use strict";s.d(t,{J:()=>d});var r=s(95155),a=s(12115),i=s(40968),l=s(74466),o=s(53999);let n=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(i.b,{ref:t,className:(0,o.cn)(n(),s),...a})});d.displayName=i.b.displayName},89852:(e,t,s)=>{"use strict";s.d(t,{p:()=>l});var r=s(95155),a=s(12115),i=s(53999);let l=a.forwardRef((e,t)=>{let{className:s,type:a,...l}=e;return(0,r.jsx)("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...l})});l.displayName="Input"}},e=>{e.O(0,[4277,3464,4706,4042,817,8816,2616,8441,5964,7358],()=>e(e.s=5896)),_N_E=e.O()}]);