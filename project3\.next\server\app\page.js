(()=>{var a={};a.id=8974,a.ids=[8974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6375:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>P});var d=c(60687),e=c(43210);c(40529);var f=c(47033),g=c(14952),h=c(24934),i=c(96241);function j({images:a,autoPlayInterval:b=5e3,className:c,onSlideChange:j,initialIndex:k=0}){let[l,m]=(0,e.useState)(k),[n,o]=(0,e.useState)(!0),[p,q]=(0,e.useState)(!1),r=(0,e.useCallback)(()=>{if(p)return;q(!0);let b=l===a.length-1?0:l+1;m(b),j&&j(b),setTimeout(()=>q(!1),500)},[a.length,l,j,p]);return(0,d.jsxs)("div",{className:(0,i.cn)("relative w-full overflow-hidden rounded-xl",c),onMouseEnter:()=>o(!1),onMouseLeave:()=>o(!0),children:[(0,d.jsx)("div",{className:"flex transition-transform duration-500 ease-out",style:{transform:`translateX(-${100*l}%)`},children:a.map((a,b)=>(0,d.jsx)("div",{className:"w-full h-full flex-shrink-0 relative",children:(0,d.jsx)("div",{className:"w-full h-full",children:(0,d.jsx)("img",{src:a.url,alt:a.alt,className:"w-full h-full object-cover",style:{objectFit:"cover",width:"100%",height:"100%"}})})},b))}),(0,d.jsx)(h.$,{variant:"ghost",size:"icon",className:"absolute left-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 z-20",onClick:()=>{if(p)return;q(!0);let b=0===l?a.length-1:l-1;m(b),j&&j(b),setTimeout(()=>q(!1),500)},disabled:p,children:(0,d.jsx)(f.A,{className:"h-6 w-6"})}),(0,d.jsx)(h.$,{variant:"ghost",size:"icon",className:"absolute right-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 z-20",onClick:r,disabled:p,children:(0,d.jsx)(g.A,{className:"h-6 w-6"})}),(0,d.jsx)("div",{className:"absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-3 z-20",children:a.map((a,b)=>(0,d.jsx)("button",{className:(0,i.cn)("w-3 h-3 rounded-full transition-all",l===b?"bg-white scale-125":"bg-white/50 hover:bg-white/75"),onClick:()=>{p||(q(!0),m(b),j&&j(b),setTimeout(()=>q(!1),500))},disabled:p},b))})]})}function k({className:a}){let[b,c]=(0,e.useState)([]),[f,g]=(0,e.useState)(!0),[i,k]=(0,e.useState)(null),[l,m]=(0,e.useState)(0),[n,o]=(0,e.useState)({transform:"translateX(0%) translateY(0%)"});if(f)return(0,d.jsx)("div",{className:`relative w-full h-[250px] md:h-[386px] overflow-hidden rounded-xl bg-accent/10 animate-pulse ${a}`,children:(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,d.jsx)("div",{className:"w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"})})});if((i||0===b.length)&&(c([{BannerID:1,TopTitle:"TOP TITLE",MainTitle:"main title",BottomTitle:"bottom title",LeftButtonText:"test",RightButtonText:"test right",BannerImgUrl:"https://placehold.co/1200x450/333333/FFFFFF?text=Banner+Image",LeftButtonUrl:"#",ThemeTypeID:1}]),i))return(0,d.jsx)("div",{className:`relative w-full h-[250px] md:h-[386px] overflow-hidden rounded-xl bg-accent/5 flex items-center justify-center ${a}`,children:(0,d.jsxs)("div",{className:"text-center p-8",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Failed to load banners"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:i}),(0,d.jsx)(h.$,{onClick:()=>window.location.reload(),children:"Retry"})]})});let p=b.map(a=>({url:a.BannerImgUrl,alt:a.MainTitle||"Banner",id:a.BannerID,leftButtonUrl:a.LeftButtonUrl}));return(0,d.jsx)("div",{className:"relative",children:(0,d.jsx)("div",{className:"relative w-full h-full",children:(0,d.jsx)(j,{images:p,className:`h-[250px] md:h-[386px] ${a}`,onSlideChange:a=>{m(a)},initialIndex:l,autoPlayInterval:5e3})})})}var l=c(12941),m=c(19080),n=c(11860),o=c(77080),p=c(85814),q=c.n(p),r=c(97905),s=c(12157),t=c(72789),u=c(21279),v=c(32582);class w extends e.Component{getSnapshotBeforeUpdate(a){let b=this.props.childRef.current;if(b&&a.isPresent&&!this.props.isPresent){let a=this.props.sizeRef.current;a.height=b.offsetHeight||0,a.width=b.offsetWidth||0,a.top=b.offsetTop,a.left=b.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function x({children:a,isPresent:b}){let c=(0,e.useId)(),f=(0,e.useRef)(null),g=(0,e.useRef)({width:0,height:0,top:0,left:0}),{nonce:h}=(0,e.useContext)(v.Q);return(0,e.useInsertionEffect)(()=>{let{width:a,height:d,top:e,left:i}=g.current;if(b||!f.current||!a||!d)return;f.current.dataset.motionPopId=c;let j=document.createElement("style");return h&&(j.nonce=h),document.head.appendChild(j),j.sheet&&j.sheet.insertRule(`
          [data-motion-pop-id="${c}"] {
            position: absolute !important;
            width: ${a}px !important;
            height: ${d}px !important;
            top: ${e}px !important;
            left: ${i}px !important;
          }
        `),()=>{document.head.removeChild(j)}},[b]),(0,d.jsx)(w,{isPresent:b,childRef:f,sizeRef:g,children:e.cloneElement(a,{ref:f})})}let y=({children:a,initial:b,isPresent:c,onExitComplete:f,custom:g,presenceAffectsLayout:h,mode:i})=>{let j=(0,t.M)(z),k=(0,e.useId)(),l=(0,e.useCallback)(a=>{for(let b of(j.set(a,!0),j.values()))if(!b)return;f&&f()},[j,f]),m=(0,e.useMemo)(()=>({id:k,initial:b,isPresent:c,custom:g,onExitComplete:l,register:a=>(j.set(a,!1),()=>j.delete(a))}),h?[Math.random(),l]:[c,l]);return(0,e.useMemo)(()=>{j.forEach((a,b)=>j.set(b,!1))},[c]),e.useEffect(()=>{c||j.size||!f||f()},[c]),"popLayout"===i&&(a=(0,d.jsx)(x,{isPresent:c,children:a})),(0,d.jsx)(u.t.Provider,{value:m,children:a})};function z(){return new Map}var A=c(86044);let B=a=>a.key||"";function C(a){let b=[];return e.Children.forEach(a,a=>{(0,e.isValidElement)(a)&&b.push(a)}),b}var D=c(15124);let E=({children:a,custom:b,initial:c=!0,onExitComplete:f,presenceAffectsLayout:g=!0,mode:h="sync",propagate:i=!1})=>{let[j,k]=(0,A.xQ)(i),l=(0,e.useMemo)(()=>C(a),[a]),m=i&&!j?[]:l.map(B),n=(0,e.useRef)(!0),o=(0,e.useRef)(l),p=(0,t.M)(()=>new Map),[q,r]=(0,e.useState)(l),[u,v]=(0,e.useState)(l);(0,D.E)(()=>{n.current=!1,o.current=l;for(let a=0;a<u.length;a++){let b=B(u[a]);m.includes(b)?p.delete(b):!0!==p.get(b)&&p.set(b,!1)}},[u,m.length,m.join("-")]);let w=[];if(l!==q){let a=[...l];for(let b=0;b<u.length;b++){let c=u[b],d=B(c);m.includes(d)||(a.splice(b,0,c),w.push(c))}"wait"===h&&w.length&&(a=w),v(C(a)),r(l);return}let{forceRender:x}=(0,e.useContext)(s.L);return(0,d.jsx)(d.Fragment,{children:u.map(a=>{let e=B(a),q=(!i||!!j)&&(l===u||m.includes(e));return(0,d.jsx)(y,{isPresent:q,initial:(!n.current||!!c)&&void 0,custom:q?void 0:b,presenceAffectsLayout:g,mode:h,onExitComplete:q?void 0:()=>{if(!p.has(e))return;p.set(e,!0);let a=!0;p.forEach(b=>{b||(a=!1)}),a&&(null==x||x(),v(o.current),i&&(null==k||k()),f&&f())},children:a},e)})})};function F(){let[a,b]=(0,e.useState)([]),[c,f]=(0,e.useState)(!0),[j,k]=(0,e.useState)([]),[p,s]=(0,e.useState)(!0),[t,u]=(0,e.useState)(null),{t:v,primaryColor:w}=(0,o.t)();return c?(0,d.jsx)("div",{className:"p-6 space-y-4 bg-gradient-to-b from-background to-accent/5 min-h-screen border-r",children:[void 0,void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("div",{className:"h-8 bg-accent/10 animate-pulse rounded-lg w-3/4"}),(0,d.jsx)("div",{className:"h-6 bg-accent/5 animate-pulse rounded-lg w-1/2 ml-4"})]},b))}):(0,d.jsxs)("div",{className:"hidden md:block",children:[(0,d.jsx)(h.$,{variant:"ghost",size:"icon",className:"absolute left-4 top-4 z-50 md:hidden",onClick:()=>s(!p),children:(0,d.jsx)(l.A,{className:"h-6 w-6"})}),(0,d.jsx)(r.P.div,{className:"bg-background md:relative w-64 shadow-sm",style:{background:`linear-gradient(135deg, ${w}30, ${w}20, ${w}10)`},initial:{x:0},animate:{x:p?0:"-100%"},transition:{duration:.3,ease:"easeInOut"},children:(0,d.jsxs)("div",{className:"p-6 space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)(m.A,{className:"h-6 w-6",style:{color:w}}),(0,d.jsx)("h2",{className:"text-xl font-semibold",style:{color:w},children:v("categories")})]}),t&&(0,d.jsx)(h.$,{variant:"ghost",size:"icon",onClick:()=>{u(null)},className:"hover:bg-accent/80",children:(0,d.jsx)(n.A,{className:"h-5 w-5"})})]}),(0,d.jsx)(E,{mode:"wait",children:t?(0,d.jsx)(r.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.2},className:"space-y-2",children:t.subcategories.map((a,b)=>(0,d.jsx)(r.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.05*b},children:(0,d.jsx)(q(),{href:`/products?category=${a.id}`,className:(0,i.cn)("block px-4 py-3 text-sm transition-all duration-200","bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] rounded-lg","relative overflow-hidden group"),children:(0,d.jsxs)("span",{className:"relative z-10 flex items-center gap-2",children:[(0,d.jsx)(g.A,{className:"h-4 w-4 text-muted-foreground"}),a.name]})})},a.id))},"subcategory-list"):(0,d.jsx)(r.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.2},className:"space-y-2",children:[...a].reverse().map(a=>(0,d.jsx)(r.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.2},children:a.subcategories.length>0?(0,d.jsxs)("button",{onClick:()=>{u(a)},className:(0,i.cn)("w-full flex items-center justify-between p-4 transition-all duration-200 rounded-lg","bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] relative overflow-hidden"),children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:a.name}),(0,d.jsx)(g.A,{className:"h-4 w-4"})]}):(0,d.jsx)(q(),{href:`/products?category=${a.id}`,className:(0,i.cn)("w-full flex items-center justify-between p-4 transition-all duration-200 rounded-lg","bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] relative overflow-hidden"),children:(0,d.jsx)("span",{className:"text-sm font-medium",children:a.name})})},a.id))},"parent-list")})]})})]})}function G(){let a=a=>({popularCategories:"Popular Categories"})[a]||a,[b,c]=(0,e.useState)([]),[h,i]=(0,e.useState)(!0),[j,k]=(0,e.useState)(0),l=(0,e.useRef)(null),[m,n]=(0,e.useState)(!0),[o]=(0,e.useState)(6e3),p=(0,e.useRef)(null),q=Math.ceil(b.length/8),r=()=>{j<q-1?k(j+1):k(0)},s=()=>{p.current&&clearInterval(p.current),p.current=setInterval(()=>{r()},o)},t=()=>{p.current&&(clearInterval(p.current),p.current=null)},u=()=>{t(),setTimeout(()=>{m&&s()},1e4)};return h?(0,d.jsx)("section",{className:"py-8",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-6",children:a("popularCategories")}),(0,d.jsx)("div",{className:"w-full flex justify-center items-center py-12",children:(0,d.jsx)("div",{className:"animate-pulse text-lg",children:"Loading popular categories..."})})]})}):b.length?(0,d.jsx)("section",{className:"py-8",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsx)("h2",{className:"text-2xl font-bold text-center",children:a("popularCategories")})}),(0,d.jsxs)("div",{className:"w-full relative overflow-hidden",onMouseEnter:()=>t(),onMouseLeave:()=>m&&s(),children:[(0,d.jsx)("button",{onClick:()=>{j>0?k(j-1):k(q-1),u()},disabled:q<=1,className:"hidden sm:block absolute left-2 top-1/2 -translate-y-1/2 z-10 p-3 rounded-full bg-white shadow-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:scale-105","aria-label":"Previous categories",children:(0,d.jsx)(f.A,{className:"h-6 w-6 text-gray-600"})}),(0,d.jsx)("button",{onClick:()=>{r(),u()},disabled:q<=1,className:"hidden sm:block absolute right-2 top-1/2 -translate-y-1/2 z-10 p-3 rounded-full bg-white shadow-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:scale-105","aria-label":"Next categories",children:(0,d.jsx)(g.A,{className:"h-6 w-6 text-gray-600"})}),(0,d.jsx)("div",{ref:l,className:"w-full transition-transform duration-700 ease-in-out md:duration-1000",style:{transform:`translateX(-${100*j}%)`},children:(0,d.jsx)("div",{className:"flex flex-nowrap",style:{width:`${100*q}%`},children:Array.from({length:q}).map((a,c)=>(0,d.jsx)("div",{className:"w-full flex-shrink-0 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 sm:gap-6 md:gap-8",style:{width:`${100/q}%`},children:b.slice(8*c,(c+1)*8).map(a=>(0,d.jsx)("div",{className:"flex flex-col items-center px-2",children:(0,d.jsxs)("a",{href:`/products?category=${a.id}`,className:"group flex flex-col items-center cursor-pointer transition-all duration-300 hover:scale-105",children:[(0,d.jsx)("div",{className:"w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-32 lg:h-32 xl:w-36 xl:h-36 mb-3 relative",children:(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full p-1 group-hover:shadow-lg transition-all duration-300",children:(0,d.jsx)("div",{className:"w-full h-full rounded-full overflow-hidden border-2 border-white bg-white",children:(0,d.jsx)("img",{src:a.image||"/placeholder.svg?height=150&width=150",alt:a.title,width:144,height:144,className:"w-full h-full object-cover",onError:b=>{let c=b.target;if(console.error("Image load error for:",a.image),"https://admin.codemedicalapps.com/images/no-image.jpg"===a.image||a.image.includes("no-image"))c.src="/placeholder.svg?height=150&width=150";else{let a="https://admin.codemedicalapps.com/images/no-image.jpg";console.log("Trying fallback URL:",a),c.src=a,c.onerror=()=>{console.error("Fallback URL also failed, using simple placeholder"),c.src="/placeholder.svg?height=150&width=150",c.onerror=null}}}})})})}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h3",{className:"text-xs sm:text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2",children:a.title}),a.parentName&&(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:a.parentName})]})]})},a.id))},`page-${c}`))})})]}),(0,d.jsx)("div",{className:"flex justify-center mt-4 space-x-2",children:Array.from({length:q}).map((a,b)=>(0,d.jsx)("button",{className:`h-2 rounded-full transition-all ${j===b?"w-6 bg-primary":"w-2 bg-gray-300"}`,onClick:()=>{k(b),u()},"aria-label":`Go to page ${b+1}`},`indicator-${b}`))})]})}):(0,d.jsx)("section",{className:"py-8",children:(0,d.jsxs)("div",{className:"container mx-auto px-4",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-6",children:a("popularCategories")}),(0,d.jsx)("div",{className:"w-full flex justify-center items-center py-12",children:(0,d.jsx)("div",{className:"text-lg text-gray-500",children:"No categories available at the moment. Please check back later."})})]})})}var H=c(71463),I=c(51499),J=c(60796);function K({effect:a}){let{t:b}=(0,o.t)(),[f,g]=(0,e.useState)([]),[h,i]=(0,e.useState)(!0),[j,k]=(0,e.useState)(null),l=async()=>{i(!0);try{let{MakeApiCallAsync:a}=await Promise.resolve().then(c.bind(c,40529)),b=await a("get-recents-products-list",null,{requestParameters:{PageNo:1,PageSize:100,recordValueJson:"[]"}},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(b?.data?.data)try{let a=JSON.parse(b.data.data);if(console.log("New products data:",a),Array.isArray(a)){let b=a.map(a=>{let b=a.ProductImagesUrl||a.ProductImageUrl,c=null;try{if(b){let a=b;if("string"==typeof b&&(b.startsWith("[")||b.startsWith('"')))try{let c=JSON.parse(b);Array.isArray(c)&&c.length>0?a=c[0].AttachmentURL||c[0]:"string"==typeof c&&(a=c)}catch(c){a=b.replace(/^"|"/g,"")}if("string"==typeof a&&""!==a.trim()&&(a=a.replace(/^"|"$/g,"").trim())){let b=decodeURIComponent(a),d=b.startsWith("/")||b.startsWith("http")?b:`/${b}`;c=d.startsWith("http")?d:`${J.T.ADMIN_BASE_URL}${d}`}}}catch(b){console.error("Error processing URL for product",a.ProductId,":",b)}return{ProductId:a.ProductId||a.ProductID||0,ProductName:a.ProductName||"Unnamed Product",Price:parseFloat(a.Price)||0,OldPrice:a.OldPrice?parseFloat(a.OldPrice):void 0,DiscountPrice:a.DiscountPrice?parseFloat(a.DiscountPrice):void 0,Rating:parseFloat(a.Rating)||0,ProductImageUrl:c||void 0,CategoryName:a.CategoryName||"Uncategorized",StockQuantity:parseInt(a.StockQuantity,10)||0,ProductTypeName:a.ProductTypeName,IQDPrice:parseFloat(a.IQDPrice)||void 0,IsDiscountAllowed:!!a.IsDiscountAllowed,MarkAsNew:!!a.MarkAsNew,SellStartDatetimeUTC:a.SellStartDatetimeUTC,SellEndDatetimeUTC:a.SellEndDatetimeUTC}});g(b)}else console.error("Products data is not an array:",a),g([]),k("Invalid data format received from server")}catch(a){console.error("Error parsing products data:",a),g([]),k("Error processing product data")}else b?.data?.errorMessage?(console.error("API Error:",b.data.errorMessage),g([]),k(b.data.errorMessage||"An error occurred while fetching products")):(console.error("Invalid or empty response from API"),g([]),k("No data received from server"))}catch(a){console.error("Error fetching products:",a),g([]),a&&"object"==typeof a&&"message"in a?k(a.message):k("An unexpected error occurred while fetching products")}finally{i(!1)}};return j?(0,d.jsx)("div",{className:"w-full p-4 mb-4 text-center",children:(0,d.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-md",children:[(0,d.jsx)("p",{className:"text-red-600",children:j}),(0,d.jsx)("button",{onClick:()=>{k(null),l()},className:"mt-2 px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm transition-colors",children:b("tryAgain")})]})}):(0,d.jsx)("div",{className:"w-full",children:(0,d.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6",children:h?Array.from({length:6}).map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,d.jsx)("div",{className:"aspect-square",children:(0,d.jsx)(H.E,{className:"h-full w-full"})}),(0,d.jsxs)("div",{className:"p-4 space-y-2",children:[(0,d.jsx)(H.E,{className:"h-4 w-full"}),(0,d.jsx)(H.E,{className:"h-4 w-3/4"}),(0,d.jsx)(H.E,{className:"h-6 w-1/3"})]}),(0,d.jsx)("div",{className:"p-4 pt-0",children:(0,d.jsxs)("div",{className:"flex w-full gap-2",children:[(0,d.jsx)(H.E,{className:"h-10 flex-1"}),(0,d.jsx)(H.E,{className:"h-10 w-10"})]})})]},b)):f.length>0?f.map(a=>(0,d.jsx)(I.A,{product:a},a.ProductId)):(0,d.jsx)("div",{className:"col-span-full text-center py-8",children:(0,d.jsx)("p",{className:"text-muted-foreground",children:b("noProductsFound")})})})})}var L=c(30474);let M=()=>{let[a,b]=(0,e.useState)([]),[f]=(0,e.useState)("https://admin.codemedicalapps.com/");return(0,e.useEffect)(()=>{(async()=>{try{let{MakeApiCallAsync:a}=await Promise.resolve().then(c.bind(c,40529)),d=await a("get-web-campaign-list",null,{requestParameters:{PageNo:1,PageSize:12,recordValueJson:"[]"}},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(d?.data?.data)try{let a=JSON.parse(d.data.data);if(console.log("Campaign data:",a),Array.isArray(a)&&a.length>0){let c=a.map(a=>({CampaignId:a.CampaignId?.toString()||"",MainTitle:a.MainTitle||a.Title||"",DiscountTitle:a.DiscountTitle||a.SubTitle||"",CoverPictureUrl:a.CoverPictureUrl||a.ImageUrl||"/images/campaign/placeholder.jpg"}));b(c)}else b([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"},{CampaignId:"4",MainTitle:"Medical Courses",DiscountTitle:"Best Prices",CoverPictureUrl:"/images/campaign/medical.jpg"},{CampaignId:"5",MainTitle:"E-Books Collection",DiscountTitle:"Digital Library",CoverPictureUrl:"/images/campaign/ebooks.jpg"},{CampaignId:"6",MainTitle:"Printed Books",DiscountTitle:"Physical Copies",CoverPictureUrl:"/images/campaign/books.jpg"},{CampaignId:"7",MainTitle:"Cardiology Special",DiscountTitle:"Heart Health",CoverPictureUrl:"/images/campaign/cardiology.jpg"},{CampaignId:"8",MainTitle:"Surgery Guides",DiscountTitle:"Expert Techniques",CoverPictureUrl:"/images/campaign/surgery.jpg"},{CampaignId:"9",MainTitle:"Pediatrics Course",DiscountTitle:"Child Care",CoverPictureUrl:"/images/campaign/pediatrics.jpg"}])}catch(a){console.error("Error parsing campaign data:",a),b([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"},{CampaignId:"4",MainTitle:"Medical Courses",DiscountTitle:"Best Prices",CoverPictureUrl:"/images/campaign/medical.jpg"},{CampaignId:"5",MainTitle:"E-Books Collection",DiscountTitle:"Digital Library",CoverPictureUrl:"/images/campaign/ebooks.jpg"},{CampaignId:"6",MainTitle:"Printed Books",DiscountTitle:"Physical Copies",CoverPictureUrl:"/images/campaign/books.jpg"}])}else console.error("Invalid or empty response from API"),b([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"},{CampaignId:"4",MainTitle:"Medical Courses",DiscountTitle:"Best Prices",CoverPictureUrl:"/images/campaign/medical.jpg"},{CampaignId:"5",MainTitle:"E-Books Collection",DiscountTitle:"Digital Library",CoverPictureUrl:"/images/campaign/ebooks.jpg"},{CampaignId:"6",MainTitle:"Printed Books",DiscountTitle:"Physical Copies",CoverPictureUrl:"/images/campaign/books.jpg"}])}catch(a){console.error("Error fetching campaign data:",a),b([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"},{CampaignId:"4",MainTitle:"Medical Courses",DiscountTitle:"Best Prices",CoverPictureUrl:"/images/campaign/medical.jpg"},{CampaignId:"5",MainTitle:"E-Books Collection",DiscountTitle:"Digital Library",CoverPictureUrl:"/images/campaign/ebooks.jpg"},{CampaignId:"6",MainTitle:"Printed Books",DiscountTitle:"Physical Copies",CoverPictureUrl:"/images/campaign/books.jpg"}])}})()},[]),(0,d.jsx)("section",{className:"py-12",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:a&&a.map((a,b)=>(0,d.jsx)(q(),{href:`/en/campaign/${a.CampaignId}/${a.MainTitle}`,className:"relative overflow-hidden rounded-lg group block shadow-md hover:shadow-lg transition-shadow duration-300",children:(0,d.jsxs)("div",{className:"aspect-[4/3] relative",children:[(0,d.jsx)(L.default,{src:f+a.CoverPictureUrl,className:"object-cover transition-transform duration-300 group-hover:scale-105",alt:a.MainTitle,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw",onError:a=>{a.target.src="https://placehold.co/400x300/cccccc/666666?text=Campaign+Image"}}),(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,d.jsxs)("div",{className:"absolute bottom-4 left-4 right-4 text-white",children:[(0,d.jsx)("h3",{className:"text-lg font-bold mb-1",children:a.MainTitle}),(0,d.jsx)("p",{className:"text-sm opacity-90",children:a.DiscountTitle})]})})]})},a.CampaignId||b))})})})};var N=c(41923);function O(){let[a,b]=(0,e.useState)([]),[c,f]=(0,e.useState)(!0);return c?(0,d.jsxs)("div",{className:"w-full",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold",children:"Today's Hot Deals"}),(0,d.jsx)(N.A,{className:"h-6 w-6 text-red-500"})]}),(0,d.jsx)("div",{className:"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-4",children:Array.from({length:4}).map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,d.jsx)("div",{className:"aspect-square",children:(0,d.jsx)(H.E,{className:"h-full w-full"})}),(0,d.jsxs)("div",{className:"p-4 space-y-2",children:[(0,d.jsx)(H.E,{className:"h-4 w-full"}),(0,d.jsx)(H.E,{className:"h-4 w-3/4"}),(0,d.jsx)(H.E,{className:"h-6 w-1/3"})]}),(0,d.jsx)("div",{className:"p-4 pt-0",children:(0,d.jsxs)("div",{className:"flex w-full gap-2",children:[(0,d.jsx)(H.E,{className:"h-10 flex-1"}),(0,d.jsx)(H.E,{className:"h-10 w-10"})]})})]},b))})]}):0===a.length?(0,d.jsxs)("div",{className:"w-full text-center py-8",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold",children:"Today's Hot Deals"}),(0,d.jsx)(N.A,{className:"h-6 w-6 text-red-500"})]}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"No hot deals available at the moment"})]}):(0,d.jsxs)("div",{className:"w-full",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold",children:"Today's Hot Deals"}),(0,d.jsx)(N.A,{className:"h-6 w-6 text-red-500"})]}),(0,d.jsx)("div",{className:"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-4",children:a.slice(0,4).map(a=>(0,d.jsx)(I.A,{product:a},a.ProductId))})]})}function P(){return(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row min-h-screen bg-background",children:[(0,d.jsx)("div",{className:"w-full lg:w-64 lg:flex-shrink-0 px-4 sm:px-6 lg:px-0 py-4 lg:py-0",children:(0,d.jsx)(F,{})}),(0,d.jsxs)("div",{className:"flex-1 p-4 sm:p-6",children:[(0,d.jsx)(k,{}),(0,d.jsx)(G,{}),(0,d.jsxs)("div",{className:"space-y-12 mt-8",children:[(0,d.jsxs)("section",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"New Products"}),(0,d.jsx)(K,{effect:"icon-inline"})]}),(0,d.jsx)("section",{children:(0,d.jsx)(M,{})}),(0,d.jsx)("section",{children:(0,d.jsx)(O,{})})]})]})]})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31757:(a,b,c)=>{Promise.resolve().then(c.bind(c,90597))},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41923:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]])},47033:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},79021:(a,b,c)=>{Promise.resolve().then(c.bind(c,6375))},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},90597:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\page.tsx","default")},94567:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,90597)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}],H=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,1697,4773,5361,9822,5861],()=>b(b.s=94567));module.exports=c})();