(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9025],{4516:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5623:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},13052:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},27737:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var r=t(95155),a=t(53999);function d(e){let{className:s,...t}=e;return(0,r.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",s),...t})}},28883:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},35169:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},37108:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},40968:(e,s,t)=>{"use strict";t.d(s,{b:()=>l});var r=t(12115),a=t(63655),d=t(95155),n=r.forwardRef((e,s)=>(0,d.jsx)(a.sG.label,{...e,ref:s,onMouseDown:s=>{var t;s.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=n},53999:(e,s,t)=>{"use strict";t.d(s,{cn:()=>d});var r=t(52596),a=t(39688);function d(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.QP)((0,r.$)(s))}},57340:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},62525:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},63357:(e,s,t)=>{Promise.resolve().then(t.bind(t,88646))},74466:(e,s,t)=>{"use strict";t.d(s,{F:()=>n});var r=t(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,d=r.$,n=(e,s)=>t=>{var r;if((null==s?void 0:s.variants)==null)return d(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:n,defaultVariants:l}=s,i=Object.keys(n).map(e=>{let s=null==t?void 0:t[e],r=null==l?void 0:l[e];if(null===s)return null;let d=a(s)||a(r);return n[e][d]}),o=t&&Object.entries(t).reduce((e,s)=>{let[t,r]=s;return void 0===r||(e[t]=r),e},{});return d(e,i,null==s||null==(r=s.compoundVariants)?void 0:r.reduce((e,s)=>{let{class:t,className:r,...a}=s;return Object.entries(a).every(e=>{let[s,t]=e;return Array.isArray(t)?t.includes({...l,...o}[s]):({...l,...o})[s]===t})?[...e,t,r]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},81586:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},82714:(e,s,t)=>{"use strict";t.d(s,{J:()=>o});var r=t(95155),a=t(12115),d=t(40968),n=t(74466),l=t(53999);let i=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(d.b,{ref:s,className:(0,l.cn)(i(),t),...a})});o.displayName=d.b.displayName},84616:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},84995:(e,s,t)=>{"use strict";t.d(s,{AB:()=>o,J5:()=>c,Qp:()=>i,tH:()=>h,tJ:()=>m,w1:()=>u});var r=t(95155),a=t(12115),d=t(99708),n=t(13052),l=(t(5623),t(53999));let i=a.forwardRef((e,s)=>{let{...t}=e;return(0,r.jsx)("nav",{ref:s,"aria-label":"breadcrumb",...t})});i.displayName="Breadcrumb";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("ol",{ref:s,className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",t),...a})});o.displayName="BreadcrumbList";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("li",{ref:s,className:(0,l.cn)("inline-flex items-center gap-1.5",t),...a})});c.displayName="BreadcrumbItem";let u=a.forwardRef((e,s)=>{let{asChild:t,className:a,...n}=e,i=t?d.DX:"a";return(0,r.jsx)(i,{ref:s,className:(0,l.cn)("transition-colors hover:text-foreground",a),...n})});u.displayName="BreadcrumbLink";let m=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("span",{ref:s,role:"link","aria-disabled":"true","aria-current":"page",className:(0,l.cn)("font-normal text-foreground",t),...a})});m.displayName="BreadcrumbPage";let h=e=>{let{children:s,className:t,...a}=e;return(0,r.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,l.cn)("[&>svg]:size-3.5",t),...a,children:null!=s?s:(0,r.jsx)(n.A,{})})};h.displayName="BreadcrumbSeparator"},88482:(e,s,t)=>{"use strict";t.d(s,{BT:()=>o,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>l,wL:()=>u});var r=t(95155),a=t(12115),d=t(53999);let n=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,d.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});n.displayName="Card";let l=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,d.cn)("flex flex-col space-y-1.5 p-6",t),...a})});l.displayName="CardHeader";let i=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h3",{ref:s,className:(0,d.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});i.displayName="CardTitle";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("p",{ref:s,className:(0,d.cn)("text-sm text-muted-foreground",t),...a})});o.displayName="CardDescription";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,d.cn)("p-6 pt-0",t),...a})});c.displayName="CardContent";let u=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,d.cn)("flex items-center p-6 pt-0",t),...a})});u.displayName="CardFooter"},88646:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var r=t(95155),a=t(12115),d=t(35695),n=t(84995),l=t(88482),i=t(97168),o=t(89852),c=t(82714),u=t(95784),m=t(27737),h=t(6874),x=t.n(h),p=t(98816),f=t(57340),y=t(81586),g=t(37108),j=t(28883),v=t(4516),N=t(35169),b=t(84616);let w=(0,t(19946).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);var A=t(62525);function C(){let{user:e,isLoggedIn:s,isLoading:h,token:C}=(0,p.J)(),I=(0,d.useRouter)(),[D,k]=(0,a.useState)([]),[P,S]=(0,a.useState)([]),[T,L]=(0,a.useState)([]),[O,J]=(0,a.useState)(!0),[R,B]=(0,a.useState)(!1),[E,M]=(0,a.useState)(!1),[q,z]=(0,a.useState)(!1),[F,V]=(0,a.useState)(null),[H,$]=(0,a.useState)({AddressLineOne:"",AddressLineTwo:"",CountryID:"107",CityID:"",StateProvinceID:"",PostalCode:"",AddressTypeID:1}),U=async()=>{console.log("\uD83C\uDFE0 Fetching addresses with token:",C?"exists":"missing");try{let e=await fetch("/api/addresses/get-user-addresses",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(C)},body:JSON.stringify({requestParameters:{recordValueJson:"[]"}})});console.log("\uD83C\uDFE0 Fetch response status:",e.status);let s=await e.json();if(console.log("\uD83C\uDFE0 Addresses API Response:",s),"TOKEN_EXPIRED"===s.code||"Token is expired"===s.error){console.log("\uD83C\uDFE0 Token expired, redirecting to login"),I.push("/login?redirect=/addresses&reason=token_expired");return}if(200===s.statusCode&&s.data){let e="string"==typeof s.data?JSON.parse(s.data):s.data;console.log("\uD83C\uDFE0 Parsed addresses data:",e),k(e||[])}else console.log("\uD83C\uDFE0 No addresses data or error:",s),k([])}catch(e){console.error("Error fetching addresses:",e)}finally{J(!1)}},Z=async()=>{B(!0);try{let e=await fetch("/api/countries",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({requestParameters:{recordValueJson:"[]"}})}),s=await e.json();if(s&&s.data){let e=JSON.parse(s.data);Array.isArray(e)&&S(e)}}catch(e){console.error("Error fetching countries:",e)}finally{B(!1)}},_=async e=>{M(!0),L([]);try{let s=await fetch("/api/cities",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({requestParameters:{StateProvinceId:null,CountryId:e,recordValueJson:"[]"}})}),t=await s.json();if(t&&t.data){let e=JSON.parse(t.data);Array.isArray(e)&&L(e)}}catch(e){console.error("Error fetching cities:",e)}finally{M(!1)}};if((0,a.useEffect)(()=>{h||s||I.push("/login?redirect=/addresses")},[h,s,I]),(0,a.useEffect)(()=>{C&&(U(),Z(),_(107))},[C]),h)return(0,r.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,r.jsxs)("div",{className:"flex justify-center items-center min-h-[400px]",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"}),(0,r.jsx)("p",{className:"text-lg text-muted-foreground mt-4",children:"Loading..."})]})});if(!s)return(0,r.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,r.jsx)("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("p",{className:"text-lg text-muted-foreground",children:"Redirecting to login..."})})})});let Y=e=>{let{name:s,value:t}=e.target;$(e=>({...e,[s]:t}))},Q=(e,s)=>{$(t=>({...t,[e]:s})),"CountryID"===e&&s&&(_(parseInt(s)),$(e=>({...e,CityID:""})))},X=async s=>{s.preventDefault();try{let s=F?"/api/addresses/update-address":"/api/addresses/insert-address",r={requestParameters:{...H,CountryID:parseInt(H.CountryID),CityID:H.CityID?parseInt(H.CityID):1,StateProvinceID:H.StateProvinceID?parseInt(H.StateProvinceID):1,AddressTypeID:parseInt(H.AddressTypeID.toString()),...F&&{AddressID:F.AddressID,ModifiedBy:(null==e?void 0:e.UserId)||(null==e?void 0:e.UserID)||1},recordValueJson:"[]"}};console.log("\uD83C\uDFE0 Submitting address with type:",r.requestParameters.AddressTypeID,G(r.requestParameters.AddressTypeID));let a=await fetch(s,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(C)},body:JSON.stringify(r)}),d=await a.json();if(200===d.statusCode){let e=(await t.e(8320).then(t.t.bind(t,24752,23))).default;await e.fire({icon:"success",title:F?"Address Updated!":"Address Added!",text:"Your address has been ".concat(F?"updated":"added"," successfully"),timer:2e3,showConfirmButton:!1}),$({AddressLineOne:"",AddressLineTwo:"",CountryID:"107",CityID:"",StateProvinceID:"",PostalCode:"",AddressTypeID:1}),_(107),z(!1),V(null),U()}}catch(s){console.error("Error saving address:",s);let e=(await t.e(8320).then(t.t.bind(t,24752,23))).default;await e.fire({icon:"error",title:"Error",text:"Failed to save address. Please try again.",timer:3e3,showConfirmButton:!1})}},W=async e=>{let s=(await t.e(8320).then(t.t.bind(t,24752,23))).default;if((await s.fire({title:"Delete Address?",text:"Are you sure you want to delete this address?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, delete it!"})).isConfirmed)try{await s.fire({icon:"info",title:"Delete functionality",text:"Delete endpoint needs to be implemented",timer:2e3,showConfirmButton:!1})}catch(e){console.error("Error deleting address:",e)}},G=e=>{switch(e){case 1:return"Home";case 2:return"Billing";case 3:return"Shipping";case 4:return"Mailing";default:return"Other"}};return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,r.jsx)(n.Qp,{className:"mb-6",children:(0,r.jsxs)(n.AB,{children:[(0,r.jsx)(n.J5,{children:(0,r.jsx)(n.w1,{asChild:!0,children:(0,r.jsx)(x(),{href:"/",children:"Home"})})}),(0,r.jsx)(n.tH,{}),(0,r.jsx)(n.J5,{children:(0,r.jsx)(n.w1,{asChild:!0,children:(0,r.jsx)(x(),{href:"/account",children:"Account"})})}),(0,r.jsx)(n.tH,{}),(0,r.jsx)(n.J5,{children:(0,r.jsx)(n.tJ,{children:"Addresses"})})]})}),(0,r.jsx)(i.$,{variant:"outline",className:"mb-6",asChild:!0,children:(0,r.jsxs)(x(),{href:"/account",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Back to Account"]})}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"My Addresses"}),(0,r.jsxs)(i.$,{onClick:()=>{z(!0),V(null),$({AddressLineOne:"",AddressLineTwo:"",CountryID:"107",CityID:"",StateProvinceID:"",PostalCode:"",AddressTypeID:1}),_(107)},className:"flex items-center gap-2",children:[(0,r.jsx)(b.A,{className:"h-4 w-4"}),"Add New Address"]})]}),q?(0,r.jsx)(l.Zp,{className:"mb-6",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:F?"Edit Address":"Add New Address"}),(0,r.jsxs)("form",{onSubmit:X,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"AddressLineOne",children:"Address Line 1 *"}),(0,r.jsx)(o.p,{id:"AddressLineOne",name:"AddressLineOne",value:H.AddressLineOne,onChange:Y,placeholder:"Enter street address",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"AddressLineTwo",children:"Address Line 2"}),(0,r.jsx)(o.p,{id:"AddressLineTwo",name:"AddressLineTwo",value:H.AddressLineTwo,onChange:Y,placeholder:"Apartment, suite, etc."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"CountryID",children:"Country *"}),(0,r.jsxs)(u.l6,{value:H.CountryID,onValueChange:e=>Q("CountryID",e),disabled:R,children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{placeholder:R?"Loading countries...":"Select country"})}),(0,r.jsx)(u.gC,{className:"max-h-[200px] bg-white",children:P.length>0?P.map(e=>(0,r.jsx)(u.eb,{value:e.CountryID.toString(),className:"text-black hover:bg-gray-100 focus:bg-gray-100 data-[highlighted]:bg-gray-100",children:e.CountryName},e.CountryID)):(0,r.jsx)("div",{className:"p-2 text-sm text-gray-500",children:R?"Loading countries...":"No countries found"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"CityID",children:"City"}),(0,r.jsxs)(u.l6,{value:H.CityID,onValueChange:e=>Q("CityID",e),disabled:!H.CountryID||E,children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{placeholder:H.CountryID?E?"Loading cities...":"Select city":"Select country first"})}),(0,r.jsx)(u.gC,{className:"max-h-[200px] bg-white",children:H.CountryID?T.length>0?T.map(e=>(0,r.jsx)(u.eb,{value:e.CityID.toString(),className:"text-black hover:bg-gray-100 focus:bg-gray-100 data-[highlighted]:bg-gray-100",children:e.CityName||e.Name},e.CityID)):(0,r.jsx)("div",{className:"p-2 text-sm text-gray-500",children:E?"Loading cities...":"No cities found"}):(0,r.jsx)("div",{className:"p-2 text-sm text-gray-500",children:"Select country first"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"PostalCode",children:"Postal Code"}),(0,r.jsx)(o.p,{id:"PostalCode",name:"PostalCode",value:H.PostalCode,onChange:Y,placeholder:"Enter postal code"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(c.J,{htmlFor:"AddressTypeID",children:"Address Type"}),(0,r.jsxs)(u.l6,{value:H.AddressTypeID.toString(),onValueChange:e=>Q("AddressTypeID",e),children:[(0,r.jsx)(u.bq,{children:(0,r.jsx)(u.yv,{})}),(0,r.jsxs)(u.gC,{children:[(0,r.jsx)(u.eb,{value:"1",children:"Home"}),(0,r.jsx)(u.eb,{value:"2",children:"Billing"}),(0,r.jsx)(u.eb,{value:"3",children:"Shipping"}),(0,r.jsx)(u.eb,{value:"4",children:"Mailing"})]})]})]})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsx)(i.$,{type:"submit",children:F?"Update Address":"Add Address"}),(0,r.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>{z(!1),V(null)},children:"Cancel"})]})]})]})}):null,O?(0,r.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,s)=>(0,r.jsx)(l.Zp,{children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)(m.E,{className:"h-6 w-32 mb-2"}),(0,r.jsx)(m.E,{className:"h-4 w-full mb-1"}),(0,r.jsx)(m.E,{className:"h-4 w-3/4"})]})},s))}):D.length>0?(0,r.jsx)("div",{className:"space-y-4",children:D.map(e=>(0,r.jsx)(l.Zp,{children:(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(e=>{switch(e){case 1:return(0,r.jsx)(f.A,{className:"h-4 w-4"});case 2:return(0,r.jsx)(y.A,{className:"h-4 w-4"});case 3:return(0,r.jsx)(g.A,{className:"h-4 w-4"});case 4:return(0,r.jsx)(j.A,{className:"h-4 w-4"});default:return(0,r.jsx)(v.A,{className:"h-4 w-4"})}})(e.AddressTypeID),(0,r.jsx)("span",{className:"font-medium text-primary",children:e.AddressTypeName||G(e.AddressTypeID)})]}),(0,r.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,r.jsx)("p",{className:"font-medium",children:e.AddressLineOne}),e.AddressLineTwo&&(0,r.jsx)("p",{className:"text-muted-foreground",children:e.AddressLineTwo}),(0,r.jsxs)("p",{className:"text-muted-foreground",children:[e.CountryName,e.PostalCode&&" - ".concat(e.PostalCode)]})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>{var s,t;console.log("\uD83C\uDFE0 Editing address with type:",e.AddressTypeID,G(e.AddressTypeID)),V(e),$({AddressLineOne:e.AddressLineOne,AddressLineTwo:e.AddressLineTwo,CountryID:e.CountryID.toString(),CityID:(null==(s=e.CityID)?void 0:s.toString())||"",StateProvinceID:(null==(t=e.StateProvinceID)?void 0:t.toString())||"",PostalCode:e.PostalCode||"",AddressTypeID:e.AddressTypeID}),e.CountryID&&_(e.CountryID),z(!0)},children:(0,r.jsx)(w,{className:"h-4 w-4"})}),(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>W(e.AddressID),className:"text-red-600 hover:text-red-700",children:(0,r.jsx)(A.A,{className:"h-4 w-4"})})]})]})})},e.AddressID))}):(0,r.jsx)(l.Zp,{children:(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(v.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No Addresses Found"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"You haven't added any addresses yet. Add your first address to get started."}),(0,r.jsxs)(i.$,{onClick:()=>{z(!0),V(null),$({AddressLineOne:"",AddressLineTwo:"",CountryID:"107",CityID:"",StateProvinceID:"",PostalCode:"",AddressTypeID:1}),_(107)},children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Add Your First Address"]})]})})]})]})}},89852:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var r=t(95155),a=t(12115),d=t(53999);let n=a.forwardRef((e,s)=>{let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,className:(0,d.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...n})});n.displayName="Input"},95784:(e,s,t)=>{"use strict";t.d(s,{bq:()=>m,eb:()=>f,gC:()=>p,l6:()=>c,yv:()=>u});var r=t(95155),a=t(12115),d=t(14582),n=t(66474),l=t(47863),i=t(5196),o=t(53999);let c=d.bL;d.YJ;let u=d.WT,m=a.forwardRef((e,s)=>{let{className:t,children:a,...l}=e;return(0,r.jsxs)(d.l9,{ref:s,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...l,children:[a,(0,r.jsx)(d.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=d.l9.displayName;let h=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(d.PP,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})})});h.displayName=d.PP.displayName;let x=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(d.wn,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})});x.displayName=d.wn.displayName;let p=a.forwardRef((e,s)=>{let{className:t,children:a,position:n="popper",...l}=e;return(0,r.jsx)(d.ZL,{children:(0,r.jsxs)(d.UC,{ref:s,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-white text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...l,children:[(0,r.jsx)(h,{}),(0,r.jsx)(d.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,r.jsx)(x,{})]})})});p.displayName=d.UC.displayName,a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(d.JU,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...a})}).displayName=d.JU.displayName;let f=a.forwardRef((e,s)=>{let{className:t,children:a,...n}=e;return(0,r.jsxs)(d.q7,{ref:s,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(d.VF,{children:(0,r.jsx)(i.A,{className:"h-4 w-4"})})}),(0,r.jsx)(d.p4,{children:a})]})});f.displayName=d.q7.displayName,a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(d.wv,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),...a})}).displayName=d.wv.displayName},97168:(e,s,t)=>{"use strict";t.d(s,{$:()=>o,r:()=>i});var r=t(95155),a=t(12115),d=t(99708),n=t(74466),l=t(53999);let i=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,s)=>{let{className:t,variant:a,size:n,asChild:o=!1,...c}=e,u=o?d.DX:"button";return(0,r.jsx)(u,{className:(0,l.cn)(i({variant:a,size:n,className:t})),ref:s,...c})});o.displayName="Button"}},e=>{e.O(0,[4277,3464,4706,3942,5725,5145,655,8816,8441,5964,7358],()=>e(e.s=63357)),_N_E=e.O()}]);