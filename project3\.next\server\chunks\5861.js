"use strict";exports.id=5861,exports.ids=[5861],exports.modules={2445:(a,b,c)=>{c.d(b,{S:()=>k,k:()=>j});var d=c(60687),e=c(52581),f=c(5336),g=c(28561),h=c(11860),i=c(24934);let j=({productName:a,quantity:b,productImage:c,onViewCart:j})=>e.oR.custom(k=>(0,d.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-md w-full",children:(0,d.jsxs)("div",{className:"flex items-start gap-3",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,d.jsx)(f.A,{className:"w-5 h-5 text-green-600"})})}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900 mb-1",children:"Added to cart"}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 line-clamp-2",children:[b," \xd7 ",a]})]}),c&&(0,d.jsx)("div",{className:"flex-shrink-0 ml-3",children:(0,d.jsx)("img",{src:(a=>{if(!a)return"/placeholder.svg";if(a.startsWith("http://")||a.startsWith("https://")||a.startsWith("/placeholder")||a.startsWith("/images/")||a.startsWith("/assets/"))return a;let b="https://admin.codemedicalapps.com/".replace(/\/$/,""),c=a.startsWith("/")?a:`/${a}`;return c=c.replace(/\/+/g,"/"),`${b}${c}`})(c),alt:a,className:"w-12 h-12 rounded-md object-cover border border-gray-200",onError:a=>{let b=a.target;b.onerror=null,b.src="/placeholder.svg"}})})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mt-3",children:[(0,d.jsxs)(i.$,{size:"sm",variant:"outline",onClick:()=>{e.oR.dismiss(k),j?.()},className:"h-8 text-xs",children:[(0,d.jsx)(g.A,{className:"w-3 h-3 mr-1"}),"View Cart"]}),(0,d.jsx)(i.$,{size:"sm",variant:"ghost",onClick:()=>e.oR.dismiss(k),className:"h-8 text-xs text-gray-500 hover:text-gray-700",children:"Continue Shopping"})]})]}),(0,d.jsx)("button",{onClick:()=>e.oR.dismiss(k),className:"flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 transition-colors",children:(0,d.jsx)(h.A,{className:"w-4 h-4"})})]})}),{duration:5e3,position:"top-right"}),k=({productName:a,quantity:b})=>e.oR.success((0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(f.A,{className:"w-4 h-4 text-green-600"}),(0,d.jsxs)("span",{children:[b," \xd7 ",a," added to cart"]})]}),{duration:3e3})},5336:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48730:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},51499:(a,b,c)=>{c.d(b,{A:()=>u});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(30474),i=c(48730),j=c(64398),k=c(13861),l=c(67760),m=c(24934),n=c(55192),o=c(59821),p=c(93283),q=c(18868),r=c(71702);c(2445);var s=c(77080);function t({endDate:a}){let[b,c]=(0,e.useState)(null),f=({value:a,label:b})=>(0,d.jsxs)("div",{className:"flex flex-col items-center mx-0.5",children:[(0,d.jsx)("div",{className:"relative w-8 h-8 sm:w-9 sm:h-9 flex items-center justify-center bg-white rounded-md shadow-sm border border-red-500",children:(0,d.jsx)("span",{className:"text-red-500 font-bold text-xs sm:text-sm",children:String(a).padStart(2,"0")})}),(0,d.jsx)("span",{className:"text-[10px] sm:text-xs text-red-500 mt-0.5 font-medium",children:b})]});return b?(0,d.jsx)("div",{className:"bg-white/90 backdrop-blur-sm p-1.5 sm:p-2 rounded-lg shadow-md border border-gray-200 max-w-[90%] mx-auto",children:(0,d.jsxs)("div",{className:"flex justify-center items-center space-x-1",children:[b.days>0&&(0,d.jsx)(d.Fragment,{children:(0,d.jsx)(f,{value:b.days,label:"Days"})}),(0,d.jsx)(f,{value:b.hours,label:"Hrs"}),(0,d.jsx)(f,{value:b.minutes,label:"Min"}),(0,d.jsx)(f,{value:b.seconds,label:"Sec"})]})}):(0,d.jsxs)("div",{className:"px-2 py-1 bg-white/90 backdrop-blur-sm rounded-lg border border-gray-300 flex items-center justify-center shadow-md max-w-[90%] mx-auto",children:[(0,d.jsx)(i.A,{className:"w-3 h-3 mr-1 text-gray-500 animate-pulse"}),(0,d.jsx)("span",{className:"text-xs font-semibold text-gray-500",children:"Sale Ended"})]})}function u({product:a}){(0,p._)();let b=(0,q.n)(),{toast:c}=(0,r.dj)(),{primaryColor:f}=(0,s.t)(),[i,u]=(0,e.useState)(!1),[v,w]=(0,e.useState)(!1),x=(a,b="USD")=>null==a||isNaN(a)?"IQD"===b?"IQD 0":"$0.00":"IQD"===b?`IQD ${a.toLocaleString()}`:`$${a.toFixed(2)}`;a.SellStartDatetimeUTC&&a.SellEndDatetimeUTC;let y=new Date,z=a.SellStartDatetimeUTC?new Date(a.SellStartDatetimeUTC):null,A=a.SellEndDatetimeUTC?new Date(a.SellEndDatetimeUTC):null,B=z&&A&&y>=z&&y<=A;return(0,d.jsxs)(n.Zp,{className:"overflow-hidden flex flex-col h-full relative",children:[(0,d.jsxs)("div",{className:"absolute top-2 left-2 z-10 flex flex-col gap-1",children:[a.MarkAsNew&&(0,d.jsx)(o.E,{variant:"secondary",className:"bg-blue-500 text-white text-xs",children:"New"}),a.DiscountPrice&&a.DiscountPrice>0&&(0,d.jsx)(o.E,{variant:"destructive",className:"bg-red-500 text-white text-xs",children:"Sale"}),B&&!a.DiscountPrice&&(0,d.jsx)(o.E,{variant:"destructive",className:"bg-red-500 text-white text-xs",children:"Sale"})]}),(0,d.jsx)(g(),{href:`/product/${a.ProductId}`,children:(0,d.jsxs)("div",{className:"aspect-square overflow-hidden relative",children:[(0,d.jsx)("div",{className:"h-full w-full relative",children:(0,d.jsx)(h.default,{src:a.ProductImageUrl||"/placeholder.svg?height=300&width=300",alt:a.ProductName||"Product",fill:!0,className:"object-cover transition-transform hover:scale-105",onError:a=>{a.target.src="/placeholder.svg?height=300&width=300"},priority:!1,loading:"lazy"})}),B&&a.SellEndDatetimeUTC&&(0,d.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-2 flex justify-center",children:(0,d.jsx)(t,{endDate:a.SellEndDatetimeUTC})})]})}),(0,d.jsxs)(n.Wu,{className:"pt-4 flex-grow",children:[(0,d.jsxs)("div",{className:"flex items-center mb-2",children:[(0,d.jsx)("div",{className:"flex",children:[void 0,void 0,void 0,void 0,void 0].map((b,c)=>(0,d.jsx)(j.A,{className:`w-4 h-4 ${c<Math.floor(a.Rating||0)?"text-yellow-400 fill-yellow-400":"text-gray-300"}`},c))}),(0,d.jsxs)("span",{className:"text-xs text-gray-500 ml-1",children:["(",a.Rating||0,")"]})]}),(0,d.jsx)(g(),{href:`/product/${a.ProductId}`,className:"hover:underline",children:(0,d.jsx)("h3",{className:"font-semibold text-lg line-clamp-2 mb-2",children:a.ProductName||"Unnamed Product"})}),a.ProductTypeName&&(0,d.jsxs)("p",{className:"text-sm text-gray-500 mb-2",children:["Type: ",a.ProductTypeName]}),(0,d.jsxs)("div",{className:"flex flex-col",children:[(0,d.jsx)("div",{className:"flex items-center gap-2",children:a.DiscountPrice?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{className:"text-lg font-bold text-red-500",children:x(a.DiscountPrice)}),(0,d.jsx)("span",{className:"text-xs text-gray-500 line-through",children:x(a.Price||0)})]}):a.OldPrice&&a.OldPrice>a.Price?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{className:"text-lg font-bold text-red-500",children:x(a.Price||0)}),(0,d.jsx)("span",{className:"text-xs text-gray-500 line-through",children:x(a.OldPrice)})]}):(0,d.jsx)("span",{className:"text-lg font-bold text-primary",children:x(a.Price||0)})}),a.IQDPrice&&(0,d.jsx)("span",{className:"text-sm font-medium text-green-600 mt-0.5",children:x(a.IQDPrice,"IQD")})]})]}),(0,d.jsx)(n.wL,{className:"p-3 pt-1 mt-auto",children:(0,d.jsx)("div",{className:"w-full",children:(0,d.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,d.jsx)(m.$,{variant:"ghost",size:"sm",className:"h-8 px-2 text-xs font-medium flex-1 gap-1.5 text-white hover:opacity-90",style:{backgroundColor:f},asChild:!0,children:(0,d.jsxs)(g(),{href:`/product/${a.ProductId}`,children:[(0,d.jsx)(k.A,{className:"h-3.5 w-3.5"}),(0,d.jsx)("span",{children:"View"})]})}),(0,d.jsx)(m.$,{variant:"ghost",size:"icon",className:"h-8 w-8 rounded-full",onClick:()=>{if(b.isHydrated){w(!0);try{if(b.isInWishlist(a.ProductId))b.removeFromWishlist(a.ProductId),c.success(`${a.ProductName} removed from wishlist`);else{let d=`/product/${a.ProductId}`,e=a.ProductImageUrl||"/placeholder.svg",f=a.DiscountPrice||a.Price;b.addToWishlist(a.ProductId,a.ProductName,d,e,f),c.success(`${a.ProductName} added to wishlist`)}}catch(a){console.error("Error updating wishlist:",a),c.error("Failed to update wishlist")}finally{setTimeout(()=>{w(!1)},500)}}},disabled:v,children:(0,d.jsx)(l.A,{className:`h-4 w-4 ${b.isInWishlist(a.ProductId)?"fill-red-500 text-red-500":""}`})})]})})})]})}},55192:(a,b,c)=>{c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h,wL:()=>l});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b}));l.displayName="CardFooter"},59821:(a,b,c)=>{c.d(b,{E:()=>h});var d=c(60687),e=c(24224),f=c(96241);let g=(0,e.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}},64398:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},71463:(a,b,c)=>{c.d(b,{E:()=>f});var d=c(60687),e=c(96241);function f({className:a,...b}){return(0,d.jsx)("div",{className:(0,e.cn)("animate-pulse rounded-md bg-muted",a),...b})}},71702:(a,b,c)=>{c.d(b,{dj:()=>l});var d=c(43210);let e=0,f=new Map,g=a=>{if(f.has(a))return;let b=setTimeout(()=>{f.delete(a),j({type:"REMOVE_TOAST",toastId:a})},1e6);f.set(a,b)},h=[],i={toasts:[]};function j(a){i=((a,b)=>{switch(b.type){case"ADD_TOAST":return{...a,toasts:[b.toast,...a.toasts].slice(0,1)};case"UPDATE_TOAST":return{...a,toasts:a.toasts.map(a=>a.id===b.toast.id?{...a,...b.toast}:a)};case"DISMISS_TOAST":{let{toastId:c}=b;return c?g(c):a.toasts.forEach(a=>{g(a.id)}),{...a,toasts:a.toasts.map(a=>a.id===c||void 0===c?{...a,open:!1}:a)}}case"REMOVE_TOAST":if(void 0===b.toastId)return{...a,toasts:[]};return{...a,toasts:a.toasts.filter(a=>a.id!==b.toastId)}}})(i,a),h.forEach(a=>{a(i)})}function k({duration:a=2e3,...b}){let c=(e=(e+1)%Number.MAX_SAFE_INTEGER).toString(),d=()=>j({type:"DISMISS_TOAST",toastId:c});return j({type:"ADD_TOAST",toast:{...b,id:c,duration:a,open:!0,onOpenChange:a=>{a||d()}}}),setTimeout(()=>{d()},a),{id:c,dismiss:d,update:a=>j({type:"UPDATE_TOAST",toast:{...a,id:c}})}}function l(){let[a,b]=d.useState(i);return d.useEffect(()=>(h.push(b),()=>{let a=h.indexOf(b);a>-1&&h.splice(a,1)}),[a]),{...a,toast:k,dismiss:a=>j({type:"DISMISS_TOAST",toastId:a})}}k.success=(a,b)=>k({description:a,type:"success",duration:2e3,...b}),k.error=(a,b)=>k({description:a,type:"error",duration:2e3,...b}),k.warning=(a,b)=>k({description:a,type:"warning",duration:2e3,...b}),k.info=(a,b)=>k({description:a,type:"info",duration:2e3,...b})}};