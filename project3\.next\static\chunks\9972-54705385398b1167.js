"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9972],{12486:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},27737:(e,t,r)=>{r.d(t,{E:()=>i});var s=r(95155),a=r(53999);function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",t),...r})}},38564:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},53999:(e,t,r)=>{r.d(t,{cn:()=>i});var s=r(52596),a=r(39688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},69026:(e,t,r)=>{r.d(t,{A:()=>x});var s=r(95155),a=r(12115),i=r(97168),n=r(89852),o=r(99474),l=r(88482),d=r(38564),c=r(12486),m=r(98816),u=r(24752),f=r.n(u);function x(e){let{productId:t,productName:r,onReviewSubmitted:u}=e,{user:x,token:g}=(0,m.J)(),[p,v]=(0,a.useState)(0),[h,b]=(0,a.useState)(0),[w,y]=(0,a.useState)(""),[N,j]=(0,a.useState)(""),[R,k]=(0,a.useState)(!1),[A,C]=(0,a.useState)(null),[S,E]=(0,a.useState)(!0),[T,P]=(0,a.useState)(!1),B=()=>{b(0)};(0,a.useEffect)(()=>{(async()=>{if(!x||!g||!t)return E(!1);try{console.log("\uD83D\uDD0D Checking for existing review for product:",t);let e=await fetch("/api/reviews/check-user-review",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json",Authorization:"Bearer ".concat(g)},body:JSON.stringify({requestParameters:{ProductId:t,recordValueJson:"[]"}})}),r=await e.json();if(console.log("\uD83D\uDD0D Check existing review response:",r),e.ok&&r&&r.data){let e;if("string"==typeof r.data)try{e=JSON.parse(r.data)}catch(t){e=[]}else e=r.data;if(console.log("\uD83D\uDD0D Parsed review data:",e),Array.isArray(e)&&e.length>0){let t=e[0];C(t),P(!0),console.log("✅ Found existing review:",t)}else P(!1),console.log("ℹ️ No existing review found")}else P(!1),console.log("ℹ️ No existing review found or API error")}catch(e){console.error("❌ Error checking existing review:",e),P(!1)}finally{E(!1)}})()},[x,g,t]);let I=async()=>{if(!p||!w.trim()||!N.trim())return void f().fire({icon:"warning",title:"Missing Information",text:"Please provide a rating, title, and review text.",confirmButtonColor:"#3085d6"});if(!(null==x?void 0:x.UserID)&&!(null==x?void 0:x.UserId)||!g)return void f().fire({icon:"error",title:"Authentication Required",text:"Please log in to submit a review.",confirmButtonColor:"#3085d6"});k(!0);try{let e={requestParameters:{ProductId:t,ReviewTitle:w.trim(),ReviewBody:N.trim(),ReviewRating:p,ReviewerName:"".concat(x.FirstName||""," ").concat(x.LastName||"").trim()||x.Email||"Anonymous",ReviewerEmail:x.Email||x.EmailAddress||""}};console.log("\uD83D\uDD0D Review submission: Sending review data:",e),console.log("\uD83D\uDD0D Review submission: User data:",{userId:x.UserID||x.UserId,email:x.Email||x.EmailAddress,name:"".concat(x.FirstName||""," ").concat(x.LastName||"").trim()});let r=await fetch("/api/reviews/insert",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json",Authorization:"Bearer ".concat(g)},body:JSON.stringify(e)}),s=await r.json();if(console.log("\uD83D\uDD0D Review submission: API response status:",r.status),console.log("\uD83D\uDD0D Review submission: Response data:",s),r.ok&&s&&!s.errorMessage)await f().fire({icon:"success",title:"Review Submitted!",text:"Thank you for your review. It will be visible after approval.",confirmButtonColor:"#10b981"}),v(0),y(""),j(""),u&&u();else throw Error((null==s?void 0:s.errorMessage)||(null==s?void 0:s.message)||"Failed to submit review")}catch(e){console.error("Error submitting review:",e),f().fire({icon:"error",title:"Submission Failed",text:e instanceof Error?e.message:"Failed to submit review. Please try again.",confirmButtonColor:"#ef4444"})}finally{k(!1)}};return S?(0,s.jsx)(l.Zp,{className:"p-6",children:(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/3 mb-2"}),(0,s.jsx)("div",{className:"h-3 bg-gray-200 rounded w-2/3"})]})})}):T&&A?(0,s.jsx)(l.Zp,{className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Your Review"}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["You have already reviewed ",r]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("div",{className:"flex items-center",children:[1,2,3,4,5].map(e=>(0,s.jsx)(d.A,{className:"h-4 w-4 ".concat(e<=(A.ReviewRating||A.Rating||0)?"fill-yellow-400 text-yellow-400":"text-gray-300")},e))}),(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:[A.ReviewRating||A.Rating||0,"/5"]})]}),A.ReviewTitle&&(0,s.jsx)("h4",{className:"font-medium mb-2",children:A.ReviewTitle}),A.ReviewBody&&(0,s.jsx)("p",{className:"text-gray-700 text-sm",children:A.ReviewBody}),(0,s.jsxs)("div",{className:"mt-3 text-xs text-gray-500",children:["Reviewed by ",A.ReviewerName||"You",(A.ReviewDate||A.CreatedOn)&&(0,s.jsxs)("span",{children:[" on ",new Date(A.ReviewDate||A.CreatedOn).toLocaleDateString()]})]})]})]})}):(0,s.jsx)(l.Zp,{className:"p-6",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Write a Review"}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Share your experience with ",r]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Rating *"}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[[1,2,3,4,5].map(e=>(0,s.jsx)("button",{type:"button",onClick:()=>{v(e)},onMouseEnter:()=>{b(e)},onMouseLeave:B,className:"p-1 transition-colors",disabled:R,children:(0,s.jsx)(d.A,{className:"h-6 w-6 ".concat(e<=(h||p)?"fill-yellow-400 text-yellow-400":"text-gray-300")})},e)),(0,s.jsx)("span",{className:"ml-2 text-sm text-muted-foreground",children:p>0&&"".concat(p," star").concat(1!==p?"s":"")})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Review Title *"}),(0,s.jsx)(n.p,{type:"text",value:w,onChange:e=>y(e.target.value),placeholder:"Summarize your experience",disabled:R,maxLength:100}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:[w.length,"/100 characters"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Your Review *"}),(0,s.jsx)(o.T,{value:N,onChange:e=>j(e.target.value),placeholder:"Tell others about your experience with this product...",disabled:R,rows:4,maxLength:500}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:[N.length,"/500 characters"]})]}),(0,s.jsx)(i.$,{onClick:I,disabled:R||!p||!w.trim()||!N.trim(),className:"w-full",children:R?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Submitting..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Submit Review"]})}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground text-center",children:"Reviews are moderated and will be published after approval."})]})})}},71007:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},88482:(e,t,r)=>{r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>m});var s=r(95155),a=r(12115),i=r(53999);let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});n.displayName="Card";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...a})});o.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});l.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",r),...a})});c.displayName="CardContent";let m=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",r),...a})});m.displayName="CardFooter"},89852:(e,t,r)=>{r.d(t,{p:()=>n});var s=r(95155),a=r(12115),i=r(53999);let n=a.forwardRef((e,t)=>{let{className:r,type:a,...n}=e;return(0,s.jsx)("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...n})});n.displayName="Input"},97168:(e,t,r)=>{r.d(t,{$:()=>d,r:()=>l});var s=r(95155),a=r(12115),i=r(99708),n=r(74466),o=r(53999);let l=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:r,variant:a,size:n,asChild:d=!1,...c}=e,m=d?i.DX:"button";return(0,s.jsx)(m,{className:(0,o.cn)(l({variant:a,size:n,className:r})),ref:t,...c})});d.displayName="Button"},99474:(e,t,r)=>{r.d(t,{T:()=>n});var s=r(95155),a=r(12115),i=r(53999);let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...a})});n.displayName="Textarea"}}]);