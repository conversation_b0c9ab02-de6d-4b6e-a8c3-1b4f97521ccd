(()=>{var a={};a.id=3571,a.ids=[3571],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4876:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\products\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\products\\page.tsx","default")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},22151:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f,metadata:()=>e});var d=c(37413);let e={title:"All Products - Medical Store",description:"Browse our complete collection of medical products and equipment"};function f({children:a}){return(0,d.jsx)("section",{className:"min-h-screen bg-background",children:a})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28848:(a,b,c)=>{Promise.resolve().then(c.bind(c,4876))},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33246:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>A});var d=c(60687),e=c(43210),f=c(16189),g=c(19080),h=c(93613);let i=(0,c(62688).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);var j=c(24934),k=c(71463),l=c(63974),m=c(71702),n=c(51499),o=c(47033),p=c(14952),q=c(93661),r=c(96241);let s=({className:a,...b})=>(0,d.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,r.cn)("mx-auto flex w-full justify-center",a),...b});s.displayName="Pagination";let t=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("ul",{ref:c,className:(0,r.cn)("flex flex-row items-center gap-1",a),...b}));t.displayName="PaginationContent";let u=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("li",{ref:c,className:(0,r.cn)("",a),...b}));u.displayName="PaginationItem";let v=({className:a,isActive:b,size:c="icon",...e})=>(0,d.jsx)("a",{"aria-current":b?"page":void 0,className:(0,r.cn)((0,j.r)({variant:b?"outline":"ghost",size:c}),a),...e});v.displayName="PaginationLink";let w=({className:a,...b})=>(0,d.jsxs)(v,{"aria-label":"Go to previous page",size:"default",className:(0,r.cn)("gap-1 pl-2.5",a),...b,children:[(0,d.jsx)(o.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:"Previous"})]});w.displayName="PaginationPrevious";let x=({className:a,...b})=>(0,d.jsxs)(v,{"aria-label":"Go to next page",size:"default",className:(0,r.cn)("gap-1 pr-2.5",a),...b,children:[(0,d.jsx)("span",{children:"Next"}),(0,d.jsx)(p.A,{className:"h-4 w-4"})]});x.displayName="PaginationNext";let y=({className:a,...b})=>(0,d.jsxs)("span",{"aria-hidden":!0,className:(0,r.cn)("flex h-9 w-9 items-center justify-center",a),...b,children:[(0,d.jsx)(q.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"sr-only",children:"More pages"})]});function z({children:a}){let b=(0,f.useSearchParams)(),c=b.get("search")||"",e=b.get("category")||"all",g=b.get("productType")||"all";return(0,d.jsx)(d.Fragment,{children:a({searchTerm:c,categoryId:e,productTypeId:g})})}function A(){return(0,d.jsx)(e.Suspense,{fallback:(0,d.jsx)(B,{}),children:(0,d.jsx)(z,{children:({searchTerm:a,categoryId:b,productTypeId:c})=>(0,d.jsx)(C,{initialSearchTerm:a,initialCategoryId:b,initialProductTypeId:c})})})}function B(){return(0,d.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,d.jsx)("div",{className:"h-8 w-64 bg-gray-200 rounded mb-4 animate-pulse"}),(0,d.jsx)("div",{className:"h-4 w-96 bg-gray-200 rounded mb-6 animate-pulse"}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,d.jsx)("div",{className:"lg:w-1/4",children:(0,d.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,d.jsx)("div",{className:"h-6 w-32 bg-gray-200 rounded mb-4 animate-pulse"}),(0,d.jsx)("div",{className:"space-y-4",children:[,,,,].fill(0).map((a,b)=>(0,d.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"},b))})]})}),(0,d.jsx)("div",{className:"lg:w-3/4",children:(0,d.jsx)("div",{className:"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-4",children:Array(12).fill(0).map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,d.jsx)("div",{className:"aspect-square bg-gray-200 animate-pulse"}),(0,d.jsxs)("div",{className:"p-4 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-4 w-3/4 bg-gray-200 rounded animate-pulse"}),(0,d.jsx)("div",{className:"h-6 w-1/3 bg-gray-200 rounded animate-pulse"})]})]},b))})})]})]})}function C({initialSearchTerm:a,initialCategoryId:b,initialProductTypeId:c}){var f;let o,{toast:p}=(0,m.dj)(),[q,r]=(0,e.useState)([]),[z,A]=(0,e.useState)([]),[B,C]=(0,e.useState)(!0),[D,E]=(0,e.useState)(1),[F,G]=(0,e.useState)(1),[H,I]=(0,e.useState)("Newest"),[J,K]=(0,e.useState)("all"!==b?Number.parseInt(b):null),[L,M]=(0,e.useState)("all"!==c?Number.parseInt(c):null),[N,O]=(0,e.useState)(a),[P,Q]=(0,e.useState)(a),[R,S]=(0,e.useState)([]),[T,U]=(0,e.useState)([]),[V,W]=(0,e.useState)({min:null,max:null}),[X,Y]=(0,e.useState)(!1),[Z,$]=(0,e.useState)(null),[_,aa]=(0,e.useState)([]),[ab,ac]=(0,e.useState)({}),[ad,ae]=(0,e.useState)({});(0,e.useCallback)((f=a=>{O(a),E(1)},(...a)=>{clearTimeout(o),o=setTimeout(()=>{f(...a)},500)}),[]);let af=async()=>{C(1===D),Y(D>1),$(null);try{let a={requestParameters:{SearchTerm:N||"",SizeID:null,ColorID:null,CategoryID:J,TagID:null,ManufacturerID:null,producttypeId:L,MinPrice:V.min,MaxPrice:V.max,Rating:null,OrderBy:(a=>{switch(a){case"Newest":default:return 5;case"Price ASC":return 1;case"Price DESC":return 0;case"ProductName ASC":return 3;case"ProductName DESC":return 2;case"Rating DESC":return 4}})(H),PageNo:1,PageSize:1e3,recordValueJson:"[]"}};console.log("Sending request to Next.js API route:",a);let b=await fetch("/api/products/get-products",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(console.log("API route response status:",b.status),!b.ok){let a=await b.json();throw Error(a.error||`HTTP error! status: ${b.status}`)}let c=await b.json();if(console.log("API route response data:",c),c&&c.data){let a=[];try{if("string"==typeof c.data?(console.log("Parsing string data:",c.data),a=JSON.parse(c.data)):Array.isArray(c.data)?(console.log("Using array data directly"),a=c.data):(console.log("Converting object to array:",c.data),a=[c.data]),console.log("Processed products data:",a),!Array.isArray(a))throw Error("Parsed data is not an array");if(0===a.length){console.log("No products found in API response"),r([]),A([]),G(0);return}console.log("First product in response:",a[0]),console.log("Available fields:",Object.keys(a[0]));let b=a.map(a=>{let b=a.ProductImagesUrl||a.ProductImageUrl,c=null;try{if(b){let a=b;if("string"==typeof b&&(b.startsWith("[")||b.startsWith('"')))try{let c=JSON.parse(b);Array.isArray(c)&&c.length>0?a=c[0].AttachmentURL||c[0]:"string"==typeof c&&(a=c)}catch(c){a=b.replace(/^"|"/g,"")}if("string"==typeof a&&""!==a.trim()&&(a=a.replace(/^"|"$/g,"").trim())){let b=decodeURIComponent(a);if(b.startsWith("http"))c=b;else{let a=b.startsWith("/")?b:`/${b}`;a=a.replace(/\/+/g,"/"),c=`https://admin.codemedicalapps.com${a}`}}}}catch(b){console.error("Error processing URL for product",a.ProductID||a.ProductId,":",b)}let d=a.ProductID||a.ProductId||a.Id||a.ID||a.id;return{...a,ProductId:d,ProductID:d,ProductName:a.ProductName||"Unnamed Product",Price:Number.parseFloat(a.Price)||0,OldPrice:a.OldPrice?Number.parseFloat(a.OldPrice):void 0,IQDPrice:Number.parseFloat(a.IQDPrice)||0,ProductTypeID:a.ProductTypeID,ProductTypeName:a.ProductTypeName,CategoryName:a.CategoryName||"Uncategorized",Rating:Number.parseFloat(a.Rating)||0,StockQuantity:Number.parseInt(a.StockQuantity,10)||0,ProductImageUrl:c,IsDiscountAllowed:!!a.IsDiscountAllowed,MarkAsNew:!!a.MarkAsNew,SellStartDatetimeUTC:a.SellStartDatetimeUTC||void 0,SellEndDatetimeUTC:a.SellEndDatetimeUTC||void 0,Attributes:a.Attributes||[],...a.DiscountPrice&&{DiscountPrice:Number.parseFloat(a.DiscountPrice)}}});console.log("Processed products:",b),A(b),r(b);let d=new Map;b.forEach(a=>{a.Attributes&&a.Attributes.length>0&&a.Attributes.forEach(a=>{let b=`${a.AttributeName}|${a.DisplayName}`;d.has(b)||d.set(b,new Map);let c=d.get(b),e=c.get(a.AttributeValueID);e?e.count++:c.set(a.AttributeValueID,{text:a.AttributeValueText,count:1})})});let e=[];if(d.forEach((a,b)=>{let[c,d]=b.split("|"),f=Array.from(a.entries()).map(([a,b])=>({id:a,text:b.text,count:b.count}));e.push({attributeName:c,displayName:d,values:f.sort((a,b)=>a.text.localeCompare(b.text))})}),aa(e.sort((a,b)=>a.displayName.localeCompare(b.displayName))),a.length>0&&(a[0].TotalRecords||a[0].totalRecords)){let b=a[0].TotalRecords||a[0].totalRecords;G(Math.ceil(Number.parseInt(b,10)/12))}}catch(a){throw console.error("Error parsing product data:",a),console.error("Raw response data:",c),$(`Error parsing data: ${a.message||"Unknown"}`),a}}else console.warn("No data field in API response:",c),$("API response missing data field"),r([]),A([])}catch(a){console.error("Error fetching products:",a),$(`API Error: ${a.message||"Unknown"}`),p({description:"Failed to load products. Please try again.",type:"error"}),r([]),A([])}finally{Y(!1),C(!1)}},ag=()=>{let a=new URLSearchParams;N&&a.append("search",N),null!==J&&a.append("category",J.toString()),null!==L&&a.append("productType",L.toString()),window.history.pushState({},"",`${window.location.pathname}?${a.toString()}`)},ah=a=>{E(a),window.scrollTo(0,0)};return(0,d.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold mb-6",children:null!==J&&R.length>0?`${R.find(a=>a.id===J)?.name||"Category"} Products`:"All Products"}),(0,d.jsx)("div",{className:"bg-white p-6 rounded-lg shadow mb-6",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-4",children:[(0,d.jsx)(g.A,{className:"h-5 w-5 text-gray-600"}),(0,d.jsx)("span",{className:"font-medium text-gray-700",children:"Filter by Product Type"})]}),(0,d.jsxs)("div",{className:"flex justify-center gap-3 flex-wrap",children:[(0,d.jsx)(j.$,{variant:null===L?"default":"outline",size:"lg",onClick:()=>{M(null),E(1),ag()},className:"min-w-[120px]",children:"All Types"}),(0,d.jsx)(j.$,{variant:1===L?"default":"outline",size:"lg",onClick:()=>{M(1),E(1),ag()},className:"min-w-[120px]",children:"Courses"}),(0,d.jsx)(j.$,{variant:2===L?"default":"outline",size:"lg",onClick:()=>{M(2),E(1),ag()},className:"min-w-[120px]",children:"Books"}),(0,d.jsx)(j.$,{variant:3===L?"default":"outline",size:"lg",onClick:()=>{M(3),E(1),ag()},className:"min-w-[120px]",children:"Journals"}),(0,d.jsx)(j.$,{variant:4===L?"default":"outline",size:"lg",onClick:()=>{M(4),E(1),ag()},className:"min-w-[120px]",children:"Medical Apps"})]})]})}),Z&&(0,d.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 p-4 mb-6 rounded-md",children:[(0,d.jsxs)("div",{className:"flex items-center mb-2",children:[(0,d.jsx)(h.A,{className:"h-5 w-5 mr-2"}),(0,d.jsx)("h3",{className:"font-semibold",children:"API Notice"})]}),(0,d.jsx)("p",{className:"text-sm",children:Z}),(0,d.jsx)("div",{className:"mt-3 flex gap-2",children:(0,d.jsx)(j.$,{size:"sm",onClick:()=>af(),children:"Retry API Call"})})]}),(0,d.jsxs)("div",{className:"mb-6",children:[N&&(0,d.jsxs)("p",{className:"text-lg",children:["Search results for:"," ",(0,d.jsxs)("span",{className:"font-semibold",children:['"',N,'"']}),null!==J&&R.length>0&&(0,d.jsxs)("span",{children:[" ","in"," ",R.find(a=>a.id===J)?.name||"selected category"]}),null!==L&&T.length>0&&(0,d.jsxs)("span",{children:[" ","-"," ",T.find(a=>a.producttypeID===L)?.Name||"selected type"]})]}),!N&&(null!==J||null!==L)&&(0,d.jsxs)("p",{className:"text-lg",children:["Browsing:",null!==J&&R.length>0&&(0,d.jsx)("span",{className:"font-semibold ml-1",children:R.find(a=>a.id===J)?.name||"selected category"}),null!==L&&T.length>0&&(0,d.jsxs)("span",{className:"font-semibold ml-1",children:[null!==J?" - ":" ",T.find(a=>a.producttypeID===L)?.Name||"selected type"]})]})]}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,d.jsx)("div",{className:"lg:w-1/4 space-y-6",children:(0,d.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,d.jsxs)("h2",{className:"text-lg font-semibold mb-4 flex items-center",children:[(0,d.jsx)(i,{className:"mr-2 h-5 w-5"}),"Filters"]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium mb-2",children:"Category"}),(0,d.jsxs)(l.l6,{value:J?.toString()||"all",onValueChange:a=>{K("all"===a?null:Number(a)),E(1),ag()},children:[(0,d.jsx)(l.bq,{children:(0,d.jsx)(l.yv,{placeholder:"All Categories"})}),(0,d.jsxs)(l.gC,{children:[(0,d.jsx)(l.eb,{value:"all",children:"All Categories"}),R.map(a=>(0,d.jsx)(l.eb,{value:a.id.toString(),children:a.name},a.id))]})]})]}),(0,d.jsxs)("div",{className:"hidden",children:[(0,d.jsx)("h3",{className:"font-medium mb-2",children:"Price Range"}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"number",placeholder:"Min",className:"w-full p-2 border rounded",onChange:a=>W({...V,min:a.target.value?Number.parseFloat(a.target.value):null})}),(0,d.jsx)("span",{children:"-"}),(0,d.jsx)("input",{type:"number",placeholder:"Max",className:"w-full p-2 border rounded",onChange:a=>W({...V,max:a.target.value?Number.parseFloat(a.target.value):null})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium mb-2",children:"Sort By"}),(0,d.jsxs)(l.l6,{value:H,onValueChange:I,children:[(0,d.jsx)(l.bq,{children:(0,d.jsx)(l.yv,{})}),(0,d.jsxs)(l.gC,{children:[(0,d.jsx)(l.eb,{value:"Newest",children:"Newest"}),(0,d.jsx)(l.eb,{value:"ProductName ASC",children:"Name: A to Z"}),(0,d.jsx)(l.eb,{value:"ProductName DESC",children:"Name: Z to A"}),(0,d.jsx)(l.eb,{value:"Rating DESC",children:"Rating: High to Low"})]})]})]}),(0,d.jsx)(j.$,{className:"w-full",onClick:()=>{K(null),M(null),W({min:null,max:null}),I("Newest"),E(1),O(""),Q(""),ac({}),ae({}),window.history.pushState({},"",window.location.pathname)},disabled:X,children:"Reset Filters"})]})]})}),(0,d.jsxs)("div",{className:"lg:w-3/4",children:[(0,d.jsx)("div",{className:"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-4",children:B?Array(12).fill(0).map((a,b)=>(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,d.jsx)("div",{className:"aspect-square",children:(0,d.jsx)(k.E,{className:"h-full w-full"})}),(0,d.jsxs)("div",{className:"p-4 space-y-2",children:[(0,d.jsx)(k.E,{className:"h-4 w-full"}),(0,d.jsx)(k.E,{className:"h-4 w-3/4"}),(0,d.jsx)(k.E,{className:"h-6 w-1/3"})]}),(0,d.jsx)("div",{className:"p-4 pt-0",children:(0,d.jsxs)("div",{className:"flex w-full gap-2",children:[(0,d.jsx)(k.E,{className:"h-10 flex-1"}),(0,d.jsx)(k.E,{className:"h-10 w-10"})]})})]},`skeleton-${b}`)):(()=>{let a=((a,b)=>{let c=[...a];switch(b){case"Newest":default:return c.sort((a,b)=>b.ProductId-a.ProductId);case"ProductName ASC":return c.sort((a,b)=>a.ProductName.localeCompare(b.ProductName));case"ProductName DESC":return c.sort((a,b)=>b.ProductName.localeCompare(a.ProductName));case"Price ASC":return c.sort((a,b)=>a.Price-b.Price);case"Price DESC":return c.sort((a,b)=>b.Price-a.Price);case"Rating DESC":return c.sort((a,b)=>b.Rating-a.Rating)}})(q,H),b=(D-1)*12;return a.slice(b,b+12)})().map(a=>a.ProductId?(0,d.jsx)(n.A,{product:a},a.ProductId):null)}),!B&&0===q.length&&(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold mb-2",children:Z?"Failed to load products":"No products found"}),(0,d.jsx)("p",{className:"text-gray-500",children:Z?"Please check your connection and try again":"Try adjusting your filters or search criteria"}),Z&&(0,d.jsx)(j.$,{className:"mt-4",onClick:()=>af(),children:"Retry"})]}),!B&&q.length>0&&(()=>{let a=[],b=Math.max(1,D-Math.floor(2.5)),c=Math.min(F,b+5-1);c-b+1<5&&(b=Math.max(1,c-5+1));for(let e=b;e<=c;e++)a.push((0,d.jsx)(u,{children:(0,d.jsx)(v,{onClick:()=>ah(e),isActive:D===e,children:e})},e));return(0,d.jsx)(s,{className:"mt-8",children:(0,d.jsxs)(t,{children:[(0,d.jsx)(u,{children:(0,d.jsx)(w,{onClick:()=>D>1&&ah(D-1),className:1===D?"pointer-events-none opacity-50":"cursor-pointer"})}),b>1&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(u,{children:(0,d.jsx)(v,{onClick:()=>ah(1),children:"1"})}),b>2&&(0,d.jsx)(y,{})]}),a,c<F&&(0,d.jsxs)(d.Fragment,{children:[c<F-1&&(0,d.jsx)(y,{}),(0,d.jsx)(u,{children:(0,d.jsx)(v,{onClick:()=>ah(F),children:F})})]}),(0,d.jsx)(u,{children:(0,d.jsx)(x,{onClick:()=>D<F&&ah(D+1),className:D===F?"pointer-events-none opacity-50":"cursor-pointer"})})]})})})()]})]})]})}y.displayName="PaginationEllipsis"},33873:a=>{"use strict";a.exports=require("path")},36849:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["products",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,4876)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\products\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,22151)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\products\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\products\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/products/page",pathname:"/products",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/products/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},47033:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(a,b,c)=>{"use strict";c.d(b,{bq:()=>m,eb:()=>q,gC:()=>p,l6:()=>k,yv:()=>l});var d=c(60687),e=c(43210),f=c(72951),g=c(78272),h=c(3589),i=c(13964),j=c(96241);let k=f.bL;f.YJ;let l=f.WT,m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.l9,{ref:e,className:(0,j.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[b,(0,d.jsx)(f.In,{asChild:!0,children:(0,d.jsx)(g.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=f.l9.displayName;let n=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.PP,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(h.A,{className:"h-4 w-4"})}));n.displayName=f.PP.displayName;let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wn,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(g.A,{className:"h-4 w-4"})}));o.displayName=f.wn.displayName;let p=e.forwardRef(({className:a,children:b,position:c="popper",...e},g)=>(0,d.jsx)(f.ZL,{children:(0,d.jsxs)(f.UC,{ref:g,className:(0,j.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-white text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...e,children:[(0,d.jsx)(n,{}),(0,d.jsx)(f.LM,{className:(0,j.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:b}),(0,d.jsx)(o,{})]})}));p.displayName=f.UC.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.JU,{ref:c,className:(0,j.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...b})).displayName=f.JU.displayName;let q=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.q7,{ref:e,className:(0,j.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})}),(0,d.jsx)(f.p4,{children:b})]}));q.displayName=f.q7.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wv,{ref:c,className:(0,j.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=f.wv.displayName},72123:(a,b,c)=>{Promise.resolve().then(c.bind(c,33246))},74075:a=>{"use strict";a.exports=require("zlib")},78335:()=>{},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93613:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},93661:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,1697,5361,2978,9822,5861],()=>b(b.s=36849));module.exports=c})();