(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5409,6927,7790],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>a,t:()=>o});var s=r(12115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,s=e.map(e=>{let s=n(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():n(e[t],null)}}}}function a(...e){return s.useCallback(o(...e),e)}},13172:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(95155),n=r(12115),o=r(97168),a=r(89852),l=r(88482),i=r(65409);function d(){let[e,t]=(0,n.useState)("1000"),[r,d]=(0,n.useState)(""),[c,u]=(0,n.useState)(""),[p,m]=(0,n.useState)(""),[f,g]=(0,n.useState)("-999"),[h,v]=(0,n.useState)("-999"),[x,y]=(0,n.useState)(""),[_,N]=(0,n.useState)("1024"),[I,b]=(0,n.useState)(null),[E,S]=(0,n.useState)(!1),T=async()=>{S(!0);try{let e={requestParameters:{FirstName:r,LastName:c,AddressLineOne:p,CityId:f,StateProvinceId:h,PostalCode:x,CategoryId:_}};console.log("Sending update profile request:",e);let t=await (0,i.MakeApiCallAsync)(i.Config.END_POINT_NAMES.UPDATE_PROFILE,null,e,{Accept:"application/json","Content-Type":"application/json"},"POST",!0);console.log("Raw API Response:",t),b(t)}catch(e){console.error("Profile update test error:",e),b({error:e instanceof Error?e.message:"An unknown error occurred"})}finally{S(!1)}};return(0,s.jsx)("div",{className:"container mx-auto p-8",children:(0,s.jsxs)(l.Zp,{className:"max-w-2xl mx-auto p-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Profile Update API Test"}),(0,s.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"User ID:"}),(0,s.jsx)(a.p,{type:"number",value:e,onChange:e=>t(e.target.value),placeholder:"Enter user ID"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"First Name:"}),(0,s.jsx)(a.p,{type:"text",value:r,onChange:e=>d(e.target.value),placeholder:"Enter first name"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Last Name:"}),(0,s.jsx)(a.p,{type:"text",value:c,onChange:e=>u(e.target.value),placeholder:"Enter last name"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Address:"}),(0,s.jsx)(a.p,{type:"text",value:p,onChange:e=>m(e.target.value),placeholder:"Enter address"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"City ID:"}),(0,s.jsx)(a.p,{type:"text",value:f,onChange:e=>g(e.target.value),placeholder:"City ID (-999 for none)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"State ID:"}),(0,s.jsx)(a.p,{type:"text",value:h,onChange:e=>v(e.target.value),placeholder:"State ID (-999 for none)"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Postal Code:"}),(0,s.jsx)(a.p,{type:"text",value:x,onChange:e=>y(e.target.value),placeholder:"Enter postal code"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Category ID:"}),(0,s.jsx)(a.p,{type:"text",value:_,onChange:e=>N(e.target.value),placeholder:"Category ID (1024 default)"})]})]}),(0,s.jsx)(o.$,{onClick:T,disabled:E||!e,children:E?"Testing...":"Test Update Profile API"})]}),I&&(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"API Response:"}),(0,s.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96",children:JSON.stringify(I,null,2)})]}),(0,s.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:"Instructions:"})}),(0,s.jsxs)("ol",{className:"list-decimal list-inside space-y-1",children:[(0,s.jsx)("li",{children:"Enter a valid User ID (get from login test)"}),(0,s.jsx)("li",{children:"Fill in the profile fields you want to update"}),(0,s.jsx)("li",{children:'Click "Test Update Profile API"'}),(0,s.jsx)("li",{children:"Check the response structure below"})]}),(0,s.jsxs)("div",{className:"mt-4 p-3 bg-blue-50 rounded",children:[(0,s.jsx)("p",{children:(0,s.jsx)("strong",{children:"API Endpoint:"})}),(0,s.jsx)("p",{className:"font-mono text-sm",children:"POST /api/v1/dynamic/dataoperation/update-profile"}),(0,s.jsx)("p",{className:"mt-2",children:(0,s.jsx)("strong",{children:"Expected Payload:"})}),(0,s.jsx)("pre",{className:"text-xs mt-1",children:'{\n  "requestParameters": {\n    "UserID": 1000,\n    "FirstName": "John",\n    "LastName": "Doe",\n    "AddressLineOne": "123 Main St",\n    "CityId": "-999",\n    "StateProvinceId": "-999",\n    "PostalCode": "12345",\n    "CategoryId": "1024"\n  }\n}'})]})]})]})})}},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(52596),n=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,s.$)(t))}},61204:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});let s={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",COMMON_CONTROLLER_SUB_URL:"api/v1/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile"}}},65409:(e,t,r)=>{"use strict";r.d(t,{$g:()=>u,Config:()=>o,MakeApiCallAsync:()=>i,XX:()=>c,k6:()=>d});var s=r(23464),n=r(61204);s.A.defaults.timeout=3e4,"https:"===window.location.protocol&&n.T.ADMIN_BASE_URL.includes("localhost")&&(s.A.defaults.httpsAgent={rejectUnauthorized:!1});let o={ADMIN_BASE_URL:n.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...n.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",RESET_PASSWORD_FIREBASE:"reset-password-firebase",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile",INSERT_PRODUCT_REVIEW:"Insert-Product-Review",GET_PRODUCT_REVIEWS:"get-product-reviews"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},a=async()=>{try{{try{let e=await fetch("/api/auth/get-token",{method:"GET",credentials:"include"});if(e.ok){let t=await e.json();if(t.token)return console.log("\uD83D\uDD10 Retrieved JWT token from secure HttpOnly cookie"),t.token}}catch(e){console.log("API token retrieval failed, trying client-side cookies:",e)}for(let e of document.cookie.split(";")){let[t,r]=e.trim().split("=");if("auth_token"===t)return console.log("\uD83D\uDD10 Retrieved JWT token from client-side cookie"),decodeURIComponent(r)}let e=localStorage.getItem("token")||localStorage.getItem("authToken");if(e)return console.log("⚠️ Using token from localStorage (migrating to secure cookies)"),localStorage.removeItem("token"),localStorage.removeItem("authToken"),e}return null}catch(e){return console.error("Error getting token for header:",e),null}},l=async()=>{try{{for(let r of document.cookie.split(";")){let[s,n]=r.trim().split("=");if("auth_user"===s)try{var e,t;let r=JSON.parse(decodeURIComponent(n)),s=(null==(e=r.UserId)?void 0:e.toString())||(null==(t=r.UserID)?void 0:t.toString());if(s)return console.log("\uD83D\uDD10 Retrieved User ID from secure cookie"),s}catch(e){console.warn("Failed to parse user data from cookie:",e)}}let r=localStorage.getItem("userId")||localStorage.getItem("userID");if(r)return console.log("⚠️ Using User ID from localStorage (migrating to secure cookies)"),localStorage.removeItem("userId"),localStorage.removeItem("userID"),r}return null}catch(e){return console.error("Error getting user ID for header:",e),null}},i=async function(e,t,r,n,i){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let d=(e=>{if(!e)return e;let t=JSON.parse(JSON.stringify(e));return t.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from request body (will use JWT token instead)"),delete t.UserId),t.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from request body (will use JWT token instead)"),delete t.UserID),t.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from request body (will use JWT token instead)"),delete t.user_id),t.requestParameters&&(t.requestParameters.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from requestParameters (will use JWT token instead)"),delete t.requestParameters.UserId),t.requestParameters.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from requestParameters (will use JWT token instead)"),delete t.requestParameters.UserID),t.requestParameters.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from requestParameters (will use JWT token instead)"),delete t.requestParameters.user_id)),t})(r),c={...n};if(!c.hasOwnProperty("Authorization")){let e=await a();e&&(c.Authorization="Bearer "+e,console.log("\uD83D\uDD10 Added JWT token to Authorization header"))}if(!c.hasOwnProperty("Token")){let e=await a();c.Token=null!=e?e:"",e&&console.log("\uD83D\uDD10 Added JWT token to Token header (backward compatibility)")}if(!c.hasOwnProperty("UserID")){let e=await l();c.UserID=null!=e?e:""}c.hasOwnProperty("Accept")||(c.Accept="application/json"),c.hasOwnProperty("Content-Type")||(c["Content-Type"]="application/json");let u=o.ADMIN_BASE_URL+(null===t||void 0==t?o.DYNAMIC_METHOD_SUB_URL:t)+e;i=null!=i?i:"POST";let p={headers:c,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===i)return await s.A.post(u,d,p);if("GET"==i)return p.params=d,await s.A.get(u,p);return{data:{errorMessage:"Unsupported method type: ".concat(i),status:"method_not_supported"}}}catch(t){console.error("API call failed:",t);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(t&&"object"==typeof t&&"response"in t&&t.response){var d,c;let r=null==(d=t.response)?void 0:d.data;e.data={errorMessage:(null==r?void 0:r.errorMessage)||"An error occurred while processing your request.",status:null==(c=t.response)?void 0:c.status}}else if(t&&"object"==typeof t&&"request"in t){let r="Network error: No response received from server.";t.message&&t.message.includes("Network Error")&&(r="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+o.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:r,status:"network_error"}}else e.data={errorMessage:t&&"object"==typeof t&&"message"in t?t.message:"An unexpected error occurred",status:"request_error"};return e}},d=async()=>{try{return console.log("Using default currency rate (1430) - API endpoint not available"),1430}catch(e){return console.error("Error fetching currency rate:",e),1430}},c=(e,t)=>Math.round(e*t),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return null==e||isNaN(e)?"IQD"===t?"0 IQD":"$0.00":"IQD"===t?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var s=r(52596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=s.$,a=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:l}=t,i=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],s=null==l?void 0:l[e];if(null===t)return null;let o=n(t)||n(s);return a[e][o]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return o(e,i,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...d}[t]):({...l,...d})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>a,aR:()=>l,wL:()=>u});var s=r(95155),n=r(12115),o=r(53999);let a=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...n})});a.displayName="Card";let l=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...n})});l.displayName="CardHeader";let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",r),...n})});i.displayName="CardTitle";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...n})});d.displayName="CardDescription";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...n})});c.displayName="CardContent";let u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...n})});u.displayName="CardFooter"},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>a});var s=r(95155),n=r(12115),o=r(53999);let a=n.forwardRef((e,t)=>{let{className:r,type:n,...a}=e;return(0,s.jsx)("input",{type:n,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...a})});a.displayName="Input"},95811:(e,t,r)=>{Promise.resolve().then(r.bind(r,13172))},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>i});var s=r(95155),n=r(12115),o=r(99708),a=r(74466),l=r(53999);let i=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,t)=>{let{className:r,variant:n,size:a,asChild:d=!1,...c}=e,u=d?o.DX:"button";return(0,s.jsx)(u,{className:(0,l.cn)(i({variant:n,size:a,className:r})),ref:t,...c})});d.displayName="Button"},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>d,TL:()=>a});var s=r(12115),n=r(6101),o=r(95155);function a(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...o}=e;if(s.isValidElement(r)){var a;let e,l,i=(a=r,(l=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(l=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),d=function(e,t){let r={...t};for(let s in t){let n=e[s],o=t[s];/^on[A-Z]/.test(s)?n&&o?r[s]=(...e)=>{let t=o(...e);return n(...e),t}:n&&(r[s]=n):"style"===s?r[s]={...n,...o}:"className"===s&&(r[s]=[n,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==s.Fragment&&(d.ref=t?(0,n.t)(t,i):i),s.cloneElement(r,d)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:n,...a}=e,l=s.Children.toArray(n),i=l.find(c);if(i){let e=i.props.children,n=l.map(t=>t!==i?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...a,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,o.jsx)(t,{...a,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var l=a("Slot"),i=Symbol("radix.slottable");function d(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=i,t}function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}}},e=>{e.O(0,[4277,3464,8441,5964,7358],()=>e(e.s=95811)),_N_E=e.O()}]);