exports.id=9822,exports.ids=[9822],exports.modules={832:(a,b,c)=>{"use strict";c.d(b,{J:()=>i,v:()=>h});var d=c(60687),e=c(43210),f=c(40529);let g=(0,e.createContext)(void 0);function h({children:a}){let[b,c]=(0,e.useState)(null),[h,i]=(0,e.useState)(null),[j,k]=(0,e.useState)(!0),l=async(a,b,d)=>{try{k(!0);let e={requestParameters:{Email:a,Password:b,...d&&{RecaptchaToken:d}}},g=await (0,f.MakeApiCallAsync)(f.Config.END_POINT_NAMES.GET_USER_LOGIN,null,e,{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(console.log("=== LOGIN API RESPONSE DEBUG ==="),console.log("Full response:",g),console.log("response.data:",g.data),console.log("response.data.data:",g.data.data),console.log("isAuthorized:",g.data?.isAuthorized),console.log("errorMessage:",g.data?.errorMessage),!g||!g.data)return{success:!1,message:"Login failed. Please try again."};{let a;if(g.data.errorMessage)return{success:!1,message:g.data.errorMessage||"Login failed. Please try again."};try{if("string"==typeof g.data.data){let b=JSON.parse(g.data.data);if(console.log("Outer data:",b),Array.isArray(b)&&b.length>0&&b[0].DATA){let c=b[0].DATA;console.log("Inner DATA string:",c);let d=JSON.parse(c);if(console.log("Parsed inner data:",d),Array.isArray(d)&&0===d.length)return console.log("Login failed - empty DATA array"),{success:!1,message:"Invalid email or password. Please check your credentials and try again."};a=d}else a=b}else a=g.data.data;if(console.log("Final userData:",a),!Array.isArray(a)||!(a.length>0))return console.log("Empty user data - login failed"),{success:!1,message:"Invalid email or password. Please check your credentials and try again."};{let b=a[0];if(console.log("Checking userInfo:",b),console.log("Available fields in userInfo:",b?Object.keys(b):"null"),console.log("UserInfo Gender:",b?.Gender),console.log("UserInfo CategoryId:",b?.CategoryId),console.log("UserInfo CategoryID:",b?.CategoryID),!b||!b.UserID&&!b.UserId&&!b.ID&&!b.Id)return{success:!1,message:b.ResponseMsg||"Invalid credentials. Please try again."};{if(!1===b.IsActive||0===b.IsActive||"false"===b.IsActive||"0"===b.IsActive)return console.log("Login failed - account is inactive"),{success:!1,message:"Your account is inactive. Please contact support for assistance."};let a=b.UserID||b.UserId||b.ID||b.Id;console.log("Found user ID:",a);let d={...b,UserId:a,UserID:a,UserName:((b.FirstName||"")+" "+(b.LastName||"")).trim()||b.UserName||b.Name,Email:b.EmailAddress||b.Email||b.email,EmailAddress:b.EmailAddress||b.Email||b.email,FirstName:b.FirstName||b.firstname,LastName:b.LastName||b.lastname,PhoneNumber:b.PhoneNo||b.MobileNo||b.PhoneNumber,PhoneNo:b.PhoneNo||b.MobileNo,MobileNo:b.MobileNo||b.PhoneNo,ResponseMsg:b.ResponseMsg||"Login successful!",Pointno:b.Pointno||b.Points||0,Gender:b.Gender||b.gender||"",CategoryID:b.CategoryID||b.CategoryId||b.category_id||b.categoryId||"",CategoryId:b.CategoryId||b.CategoryID||b.category_id||b.categoryId||"",SpecialistId:b.SpecialistId||b.specialist_id||b.CategoryId||b.CategoryID||"",CatID:b.CatID||b.CategoryId||b.CategoryID||"",IsActive:b.IsActive,CreatedOn:b.CreatedOn||b.createdOn||b.created_on};console.log("Mapped user:",d);try{c(d);let a=g.data.token||null;console.log("Extracted token:",a?`${a.substring(0,20)}...`:"No token found"),a?i(a):(console.warn("No JWT token found in login response"),i(null));try{(await fetch("/api/auth/set-cookies",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({user:d,token:a||null})})).ok?console.log("\uD83D\uDD10 Secure HttpOnly cookies set successfully"):console.warn("Failed to set secure cookies")}catch(a){console.warn("Cookie API failed:",a)}return console.log("User data saved successfully to secure cookies"),{success:!0,message:"Login successful!"}}catch(a){return console.error("Error saving user data:",a),{success:!1,message:"Login successful but failed to save user data"}}}}}catch(a){return console.error("Error parsing login response:",a),{success:!1,message:"Error processing login response!"}}}}catch(a){return console.error("Login error:",a),{success:!1,message:"An error occurred. Please try again!"}}finally{k(!1)}},m=async()=>{c(null),i(null);try{(await fetch("/api/auth/clear-cookies",{method:"POST",credentials:"include"})).ok?console.log("\uD83D\uDD10 Secure cookies cleared successfully"):console.warn("Failed to clear secure cookies via API")}catch(a){console.warn("Cookie clearing API failed:",a)}},n=async a=>{if(b){c({...b,...a});try{(await fetch("/api/auth/update-user-cookies",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(a)})).ok?console.log("✅ User context: Profile updated in cookies successfully"):console.warn("⚠️ User context: Failed to update profile in cookies")}catch(a){console.warn("⚠️ User context: Cookie update API failed:",a)}}},o=null!==b&&(b.UserId&&b.UserId>0||b.UserID&&b.UserID>0);return(0,d.jsx)(g.Provider,{value:{user:b,isLoggedIn:o,isLoading:j,login:l,logout:m,token:h,updateProfile:n},children:a})}function i(){let a=(0,e.useContext)(g);if(void 0===a)throw Error("useUser must be used within a UserProvider");return a}},15991:(a,b,c)=>{"use strict";c.d(b,{U:()=>h,Y:()=>i});var d=c(60687),e=c(43210),f=c(40529);let g=(0,e.createContext)(void 0);function h({children:a}){let[b,c]=(0,e.useState)(null),[h,i]=(0,e.useState)(!1),j=async(a,b,d)=>{if(!a.trim())return{valid:!1,message:"Please enter a coupon code",discount:0};i(!0);try{let e=d?.map(a=>({ProductId:a.id,ProductName:a.name,Price:a.adjustedPrice||a.price,Quantity:a.quantity,IsDiscountAllowed:!0}))||[],g=JSON.stringify(e),h={requestParameters:{CouponCode:a.toUpperCase(),cartJsonData:g}},i=await (0,f.MakeApiCallAsync)(f.Config.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT,f.Config.DYNAMIC_METHOD_SUB_URL,h,{"Content-Type":"application/json",Accept:"application/json"},"POST");if(!i||!i.data||i.data.errorMessage)return{valid:!1,message:i.data?.errorMessage||"Failed to validate coupon",discount:0};{let d;if((d="string"==typeof i.data.data?JSON.parse(i.data.data):i.data.data)&&d.DiscountValueAfterCouponAppliedWithQuantity>0){let b=d.DiscountValueAfterCouponAppliedWithQuantity,e=1===d.DiscountValueType?"percentage":"fixed",f={code:a.toUpperCase(),discount:b,type:e,discountTypeId:d.DiscountTypeId||1};return c(f),{valid:!0,message:"Coupon applied successfully!",discount:b}}if(d&&Array.isArray(d)&&d.length>0){let e=d[0];if(e&&e.DiscountValue>0&&e.IsActive){let d=0,f=e.DiscountTypeId||1;switch(f){case 1:case 2:1===e.DiscountValueType?d=e.DiscountValue*b/100:2===e.DiscountValueType&&(d=e.DiscountValue);break;case 3:case 4:case 5:case 6:case 7:d=1===e.DiscountValueType?e.DiscountValue*b/100:e.DiscountValue;break;default:d=0}if(d>0){let b={code:a.toUpperCase(),discount:d,type:1===e.DiscountValueType?"percentage":"fixed",discountTypeId:f,title:e.Title,discountId:e.DiscountId,maxQuantity:e.MaxQuantity,productId:e.ProductId,categoryId:e.CategoryID};return c(b),{valid:!0,message:`Coupon "${e.Title}" applied successfully! Discount applied on ${({1:"order total",2:"order subtotal",3:"products",4:"categories",5:"manufacturers",6:"cities",7:"shipping"})[f]||"order"}.`,discount:d}}}}return{valid:!1,message:"Invalid coupon code or coupon not applicable to your cart",discount:0}}}catch(a){return console.error("Coupon validation error:",a),{valid:!1,message:"Error validating coupon. Please try again.",discount:0}}finally{i(!1)}};return(0,d.jsx)(g.Provider,{value:{appliedCoupon:b,validateCoupon:j,clearCoupon:()=>{c(null)},isLoading:h},children:a})}function i(){let a=(0,e.useContext)(g);if(void 0===a)throw Error("useCoupon must be used within a CouponProvider");return a}},18094:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},18868:(a,b,c)=>{"use strict";c.d(b,{Z:()=>g,n:()=>h});var d=c(60687),e=c(43210);let f=(0,e.createContext)(void 0);function g({children:a}){let[b,c]=(0,e.useState)([]),[g,h]=(0,e.useState)(!1);return(0,d.jsx)(f.Provider,{value:{wishlistItems:b,addToWishlist:(a,d,e,f,g)=>{b.some(b=>b.productId===a)||c([...b,{productId:a,productName:d,productUrl:e,imageUrl:f,price:g,addedAt:new Date().toISOString()}])},removeFromWishlist:a=>{c(b.filter(b=>b.productId!==a))},isInWishlist:a=>b.some(b=>b.productId===a),getWishlistItem:a=>b.find(b=>b.productId===a),totalItems:b.length,isHydrated:g},children:a})}function h(){let a=(0,e.useContext)(f);if(void 0===a)throw Error("useWishlist must be used within a WishlistProvider");return a}},24934:(a,b,c)=>{"use strict";c.d(b,{$:()=>j,r:()=>i});var d=c(60687),e=c(43210),f=c(8730),g=c(24224),h=c(96241);let i=(0,g.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),j=e.forwardRef(({className:a,variant:b,size:c,asChild:e=!1,...g},j)=>{let k=e?f.DX:"button";return(0,d.jsx)(k,{className:(0,h.cn)(i({variant:b,size:c,className:a})),ref:j,...g})});j.displayName="Button"},27790:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(60687);c(43210);let e=({className:a,...b})=>(0,d.jsxs)("svg",{viewBox:"-1 0 226 226",xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",className:a,...b,children:[(0,d.jsx)("path",{d:"M41.255 185.52v40.2l37.589-21.37c10.478 3.02 21.616 4.65 33.156 4.65 61.86 0 112-46.79 112-104.5 0-57.714-50.14-104.5-112-104.5-61.856 0-112 46.786-112 104.5 0 32.68 16.078 61.86 41.255 81.02z"}),(0,d.jsx)("path",{d:"m100.04 75.878-60.401 63.952 54.97-30.16 28.721 30.16 60.06-63.952-54.36 29.632-28.99-29.632z",fill:"#ffffff"})]})},37534:(a,b,c)=>{Promise.resolve().then(c.bind(c,58014))},40529:(a,b,c)=>{"use strict";c.d(b,{$g:()=>l,Config:()=>f,MakeApiCallAsync:()=>i,XX:()=>k,k6:()=>j});var d=c(51060),e=c(60796);d.A.defaults.timeout=3e4;let f={ADMIN_BASE_URL:e.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...e.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",RESET_PASSWORD_FIREBASE:"reset-password-firebase",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile",INSERT_PRODUCT_REVIEW:"Insert-Product-Review",GET_PRODUCT_REVIEWS:"get-product-reviews"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},g=async()=>{try{return null}catch(a){return console.error("Error getting token for header:",a),null}},h=async()=>{try{return null}catch(a){return console.error("Error getting user ID for header:",a),null}},i=async(a,b,c,e,i,j=!0)=>{try{let j=(a=>{if(!a)return a;let b=JSON.parse(JSON.stringify(a));return b.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from request body (will use JWT token instead)"),delete b.UserId),b.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from request body (will use JWT token instead)"),delete b.UserID),b.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from request body (will use JWT token instead)"),delete b.user_id),b.requestParameters&&(b.requestParameters.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from requestParameters (will use JWT token instead)"),delete b.requestParameters.UserId),b.requestParameters.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from requestParameters (will use JWT token instead)"),delete b.requestParameters.UserID),b.requestParameters.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from requestParameters (will use JWT token instead)"),delete b.requestParameters.user_id)),b})(c),k={...e};if(!k.hasOwnProperty("Authorization")){let a=await g();a&&(k.Authorization="Bearer "+a,console.log("\uD83D\uDD10 Added JWT token to Authorization header"))}if(!k.hasOwnProperty("Token")){let a=await g();k.Token=a??"",a&&console.log("\uD83D\uDD10 Added JWT token to Token header (backward compatibility)")}k.hasOwnProperty("UserID")||(k.UserID=await h()??""),k.hasOwnProperty("Accept")||(k.Accept="application/json"),k.hasOwnProperty("Content-Type")||(k["Content-Type"]="application/json");let l=f.ADMIN_BASE_URL+(null===b||void 0==b?f.DYNAMIC_METHOD_SUB_URL:b)+a;i=i??"POST";let m={headers:k,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===i)return await d.A.post(l,j,m);if("GET"==i)return m.params=j,await d.A.get(l,m);return{data:{errorMessage:`Unsupported method type: ${i}`,status:"method_not_supported"}}}catch(b){console.error("API call failed:",b);let a={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(b&&"object"==typeof b&&"response"in b&&b.response){let c=b.response?.data;a.data={errorMessage:c?.errorMessage||"An error occurred while processing your request.",status:b.response?.status}}else if(b&&"object"==typeof b&&"request"in b){let c="Network error: No response received from server.";b.message&&b.message.includes("Network Error")&&(c="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+f.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),a.data={errorMessage:c,status:"network_error"}}else a.data={errorMessage:b&&"object"==typeof b&&"message"in b?b.message:"An unexpected error occurred",status:"request_error"};return a}},j=async()=>{try{return console.log("Using default currency rate (1430) - API endpoint not available"),1430}catch(a){return console.error("Error fetching currency rate:",a),1430}},k=(a,b)=>Math.round(a*b),l=(a,b="USD")=>null==a||isNaN(a)?"IQD"===b?"0 IQD":"$0.00":"IQD"===b?`${a.toLocaleString()} IQD`:`$${a.toFixed(2)}`},44761:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>aA});var d=c(60687);c(76080);var e=c(43210),f=c(8685),g=c.n(f),h=c(48340),i=c(41550),j=c(11437),k=c(33872),l=c(58869),m=c(78272),n=c(19080),o=c(97992),p=c(67760),q=c(40083),r=c(23026),s=c(99270),t=c(28561),u=c(11860),v=c(12941),w=c(16189),x=c(85814),y=c.n(x),z=c(93283),A=c(18868),B=c(52581),C=c(40529),D=c(38460),E=c(24224),F=c(96241);let G=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(D.bL,{ref:e,className:(0,F.cn)("relative z-10 flex max-w-max flex-1 items-center justify-center",a),...c,children:[b,(0,d.jsx)(L,{})]}));G.displayName=D.bL.displayName;let H=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(D.B8,{ref:c,className:(0,F.cn)("group flex flex-1 list-none items-center justify-center space-x-1 bg-white",a),...b}));H.displayName=D.B8.displayName;let I=D.q7,J=(0,E.F)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-white px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50");e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(D.l9,{ref:e,className:(0,F.cn)(J(),"group",a),...c,children:[b," ",(0,d.jsx)(m.A,{className:"relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180","aria-hidden":"true"})]})).displayName=D.l9.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(D.UC,{ref:c,className:(0,F.cn)("left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto ",a),...b})).displayName=D.UC.displayName;let K=D.N_,L=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{className:(0,F.cn)("absolute left-0 top-full flex justify-center z-50"),children:(0,d.jsx)(D.LM,{className:(0,F.cn)("origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-white text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)] !bg-white",a),ref:c,...b})}));L.displayName=D.LM.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(D.C1,{ref:c,className:(0,F.cn)("top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in",a),...b,children:(0,d.jsx)("div",{className:"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md"})})).displayName=D.C1.displayName;var M=c(40599);let N=M.bL,O=M.l9,P=e.forwardRef(({className:a,align:b="center",sideOffset:c=4,...e},f)=>(0,d.jsx)(M.ZL,{children:(0,d.jsx)(M.UC,{ref:f,align:b,sideOffset:c,className:(0,F.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...e})}));P.displayName=M.UC.displayName;var Q=c(24934),R=c(77080),S=c(832),T=c(80063);let U=["#0074b2","#194234","#2a9d8f","#81d4fa","#f295ce","#fce4ec","#b39ddb","#bcaaa4","#ffccbc","#b2dfdb","#6c9bcf","#ffd552","#39b1df","#7986cb","#003554"];function V({onColorSelect:a,onClose:b}){let[c,f]=(0,e.useState)("#0074b2");return(0,d.jsx)("div",{className:"fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"bg-card border rounded-lg shadow-lg p-6 w-[320px] space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Choose a Color"}),(0,d.jsx)(Q.$,{variant:"ghost",size:"icon",onClick:b,children:(0,d.jsx)(u.A,{className:"h-4 w-4"})})]}),(0,d.jsx)("div",{className:"grid grid-cols-3 gap-2",children:U.map(b=>(0,d.jsx)("button",{className:`w-full aspect-square rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-lg ${c===b?"ring-2 ring-primary":""}`,style:{backgroundColor:b,color:(0,T.N)(b)},onClick:()=>(b=>{f(b);let c=(0,T.N)(b);document.documentElement.style.setProperty("--primary",b),document.documentElement.style.setProperty("--primary-foreground",c),a(b)})(b),children:c===b&&"✓"},b))})]})})}function W(){let a=(0,w.useRouter)(),[b,c]=(0,e.useState)([]),[f,g]=(0,e.useState)(!0),[x,C]=(0,e.useState)(!1),[D,E]=(0,e.useState)(null),[J,L]=(0,e.useState)(null),[M,T]=(0,e.useState)(null),[U,W]=(0,e.useState)(!1),[X,Y]=(0,e.useState)(!1),[Z,$]=(0,e.useState)(""),[_,aa]=(0,e.useState)(0),[ab,ac]=(0,e.useState)(0);(0,z._)(),(0,A.n)();let{user:ad,isLoggedIn:ae,logout:af}=(0,S.J)(),{theme:ag,language:ah,primaryColor:ai,primaryTextColor:aj,toggleTheme:ak,setLanguage:al,setPrimaryColor:am,t:an}=(0,R.t)(),ao=()=>{let b=new URLSearchParams;Z&&b.append("search",Z),J&&b.append("category",J.toString()),a.push(`/products?${b.toString()}`)};return(0,d.jsxs)("header",{className:"w-full",children:[(0,d.jsx)(Q.$,{variant:"ghost",size:"sm",className:"fixed bottom-36 right-4 md:bottom-24 md:right-6 md:left-auto z-50 bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 shadow-2xl rounded-2xl border-2 border-white/50 flex hover:scale-110 hover:-rotate-6 transition-all duration-300 hover:shadow-purple-500/50 group items-center justify-center w-14 h-14 md:w-16 md:h-16 hover:rounded-full",onClick:()=>C(!0),children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"h-8 w-8 md:h-10 md:w-10 rounded-xl group-hover:rounded-full ring-2 ring-white/80 group-hover:ring-4 transition-all duration-300 shadow-inner",style:{backgroundColor:ai}}),(0,d.jsx)("div",{className:"absolute -top-2 -right-2 w-5 h-5 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full shadow-lg animate-spin group-hover:animate-pulse"}),(0,d.jsx)("div",{className:"absolute -bottom-1 -left-1 w-3 h-3 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full animate-bounce"})]})}),(0,d.jsx)("div",{className:"hidden md:block py-2.5",style:{backgroundColor:ai,color:aj},children:(0,d.jsxs)("div",{className:"container mx-auto flex flex-col md:flex-row justify-between items-center text-sm px-4",children:[(0,d.jsxs)("div",{className:"flex md:flex-row items-start justify-start gap-4 md:gap-8",children:[(0,d.jsxs)(y(),{href:"tel:***************",className:"flex items-center gap-2 hover:text-white/80",children:[(0,d.jsx)(h.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-xs md:text-sm",children:an("phone")})]}),(0,d.jsxs)(y(),{href:"mailto:<EMAIL>",className:"flex items-center gap-2 hover:text-white/80",children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-xs md:text-sm",children:an("email")})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 md:gap-4",children:[(0,d.jsxs)(Q.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",onClick:()=>al("en"===ah?"ar":"en"),children:[(0,d.jsx)(j.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-sm",children:"en"===ah?"العربية":"English"})]}),(0,d.jsxs)(Q.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 flex items-center gap-2",onClick:()=>window.open(`https://wa.me/*************?text=${encodeURIComponent("Hello! I would like to chat with you regarding your services.")}`,"_blank"),children:[(0,d.jsx)(k.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-sm",children:an("liveChat")})]}),ae?(0,d.jsxs)(N,{children:[(0,d.jsx)(O,{asChild:!0,children:(0,d.jsxs)(Q.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",children:[(0,d.jsx)(l.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{className:"text-sm",children:["Welcome, ",ad?.FirstName||ad?.UserName]}),(0,d.jsx)(m.A,{className:"h-3 w-3"})]})}),(0,d.jsx)(P,{className:"w-48 p-2 bg-white border border-gray-200 shadow-lg",align:"end",children:(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)(y(),{href:"/account",children:(0,d.jsxs)(Q.$,{variant:"ghost",size:"sm",className:"w-full justify-start hover:bg-gray-50",children:[(0,d.jsx)(l.A,{className:"h-4 w-4 mr-2"}),"My Account"]})}),(0,d.jsx)(y(),{href:"/orders",children:(0,d.jsxs)(Q.$,{variant:"ghost",size:"sm",className:"w-full justify-start hover:bg-gray-50",children:[(0,d.jsx)(n.A,{className:"h-4 w-4 mr-2"}),"My Orders"]})}),(0,d.jsx)(y(),{href:"/addresses",children:(0,d.jsxs)(Q.$,{variant:"ghost",size:"sm",className:"w-full justify-start hover:bg-gray-50",children:[(0,d.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"My Addresses"]})}),(0,d.jsx)(y(),{href:"/wishlist",children:(0,d.jsxs)(Q.$,{variant:"ghost",size:"sm",className:"w-full justify-start hover:bg-gray-50",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Wishlist"]})}),(0,d.jsx)("div",{className:"border-t border-gray-100 my-1"}),(0,d.jsxs)(Q.$,{variant:"ghost",size:"sm",className:"w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50",onClick:()=>{af(),a.push("/"),B.oR.success("Logged out successfully")},children:[(0,d.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Logout"]})]})})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(y(),{href:"/login",children:(0,d.jsxs)(Q.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",children:[(0,d.jsx)(l.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-sm",children:an("login")})]})}),(0,d.jsx)(y(),{href:"/signup",children:(0,d.jsxs)(Q.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",children:[(0,d.jsx)(r.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{className:"text-sm",children:an("signUp")})]})})]})]})]})}),(0,d.jsxs)("div",{className:"container mx-auto py-4 px-4",children:[(0,d.jsxs)("div",{className:"md:hidden",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,d.jsx)("div",{className:"w-20"}),(0,d.jsx)(y(),{href:"/",className:"flex items-center gap-2",children:(0,d.jsx)("div",{className:"text-[#1B3764] flex items-center gap-2",children:(0,d.jsx)("img",{src:"https://admin.codemedicalapps.com/content/commonImages/otherImages/18b_logo2x.png",alt:"Logo",className:"h-12 w-auto"})})}),(0,d.jsxs)(Q.$,{variant:"ghost",size:"sm",className:"flex items-center gap-2 px-3 py-2 rounded-full border border-gray-200 hover:bg-gray-50",onClick:()=>al("en"===ah?"ar":"en"),children:[(0,d.jsx)("span",{className:"text-lg",children:"en"===ah?"\uD83C\uDDFA\uD83C\uDDF8":"\uD83C\uDDEE\uD83C\uDDF6"}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"en"===ah?"EN":"AR"})]})]}),(0,d.jsx)("div",{className:"w-full",children:(0,d.jsxs)("div",{className:"flex items-center gap-2 border rounded-full px-4 py-3 bg-background shadow-sm",children:[(0,d.jsx)("input",{type:"text",placeholder:an("products")||"البحث عن المنتجات...",className:"bg-transparent border-none focus:outline-none text-sm flex-1 placeholder:text-gray-400",value:Z,onChange:a=>$(a.target.value),onKeyDown:a=>"Enter"===a.key&&ao()}),(0,d.jsx)(Q.$,{variant:"ghost",className:"h-8 w-8 p-0 hover:bg-accent/80 transition-colors",style:{color:ai},onClick:ao,children:(0,d.jsx)(s.A,{className:"h-4 w-4"})})]})})]}),(0,d.jsxs)("div",{className:"hidden md:flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4 flex-1",children:[(0,d.jsx)(y(),{href:"/",className:"flex items-center gap-2",children:(0,d.jsx)("div",{className:"text-[#1B3764] flex items-center gap-2",children:(0,d.jsx)("img",{src:"https://admin.codemedicalapps.com/content/commonImages/otherImages/18b_logo2x.png",alt:"Logo",className:"h-16 w-auto"})})}),(0,d.jsxs)("div",{className:"flex items-center gap-2 border rounded-full px-3 py-1.5 flex-1 max-w-md ml-4",children:[(0,d.jsxs)(N,{children:[(0,d.jsx)(O,{asChild:!0,children:(0,d.jsxs)(Q.$,{variant:"ghost",className:"h-8 flex items-center gap-1 px-2",children:[(0,d.jsx)("span",{className:"text-muted-foreground text-sm",children:D||M||an("category")}),(0,d.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]})}),(0,d.jsx)(P,{className:"w-64 p-0 bg-white border border-gray-200 shadow-lg",align:"start",children:(0,d.jsx)("div",{className:"max-h-[300px] overflow-auto",children:f?(0,d.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:an("loadingCategories")}):(0,d.jsx)("div",{className:"grid",children:b.map(a=>(0,d.jsxs)("div",{className:"group",children:[(0,d.jsx)("button",{className:"w-full px-4 py-2 text-left hover:bg-gray-50",onClick:()=>{E(a.name),L(a.id),T(null)},children:a.name}),(0,d.jsx)("div",{className:"hidden group-hover:block absolute left-full top-0 w-48 bg-white shadow-lg rounded-md border border-gray-200",children:a.subcategories.map((b,c)=>(0,d.jsx)("button",{className:"w-full px-4 py-2 text-left hover:bg-gray-50",onClick:()=>{T(b),E(null),L(a.id)},children:b},c))})]},a.id))})})})]}),(0,d.jsx)("div",{className:"h-5 w-px bg-border mx-2"}),(0,d.jsx)("input",{type:"text",placeholder:an("products"),className:"bg-transparent border-none focus:outline-none text-sm flex-1",value:Z,onChange:a=>$(a.target.value),onKeyDown:a=>"Enter"===a.key&&ao()}),(0,d.jsx)(Q.$,{variant:"ghost",className:"h-8 w-8 p-0",onClick:ao,children:(0,d.jsx)(s.A,{className:"h-4 w-4"})})]})]}),(0,d.jsx)("div",{className:"hidden md:block",children:(0,d.jsx)(G,{children:(0,d.jsxs)(H,{children:[(0,d.jsx)(I,{children:(0,d.jsx)(K,{asChild:!0,children:(0,d.jsx)(y(),{href:"/",className:(0,F.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:an("home")})})}),(0,d.jsx)(I,{children:(0,d.jsx)(K,{asChild:!0,children:(0,d.jsx)(y(),{href:"/hot-deals",className:(0,F.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:an("hotDeals")})})}),(0,d.jsx)(I,{children:(0,d.jsx)(K,{asChild:!0,children:(0,d.jsx)(y(),{href:"/products",className:(0,F.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:an("products")||"Products"})})}),(0,d.jsx)(I,{children:(0,d.jsx)(K,{asChild:!0,children:(0,d.jsx)(y(),{href:"/payment-methods",className:(0,F.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:an("paymentMethods")})})}),(0,d.jsx)(I,{children:(0,d.jsx)(K,{asChild:!0,children:(0,d.jsx)(y(),{href:"/follow-us",className:(0,F.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:an("followUs")})})}),(0,d.jsx)(I,{children:(0,d.jsx)(K,{asChild:!0,children:(0,d.jsx)(y(),{href:"/about",className:(0,F.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:an("aboutUs")})})}),(0,d.jsx)(I,{children:(0,d.jsx)(K,{asChild:!0,children:(0,d.jsx)(y(),{href:"/contact",className:(0,F.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:an("contactUs")})})})]})})}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsxs)("div",{className:"hidden md:flex items-center gap-4",children:[(0,d.jsx)(y(),{href:"/wishlist",children:(0,d.jsxs)(Q.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,d.jsx)(p.A,{className:"h-5 w-5",style:{color:ai}}),(0,d.jsx)("span",{className:"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center",children:ab})]})}),(0,d.jsx)(y(),{href:"/cart",children:(0,d.jsxs)(Q.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,d.jsx)(t.A,{className:"h-5 w-5",style:{color:ai}}),(0,d.jsx)("span",{className:"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center",children:_})]})})]}),(0,d.jsx)(Q.$,{variant:"ghost",size:"icon",className:"md:hidden",onClick:()=>W(!U),"aria-label":"Toggle menu",children:U?(0,d.jsx)(u.A,{className:"h-6 w-6"}):(0,d.jsx)(v.A,{className:"h-6 w-6"})})]})]})]}),U&&(0,d.jsx)("div",{className:"md:hidden bg-white border-t border-gray-200 shadow-lg",children:(0,d.jsxs)("nav",{className:"px-4 py-4 space-y-2",children:[(0,d.jsx)(y(),{href:"/",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>W(!1),children:an("home")}),(0,d.jsx)(y(),{href:"/hot-deals",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>W(!1),children:an("hotDeals")}),(0,d.jsx)(y(),{href:"/products",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>W(!1),children:an("products")||"Products"}),(0,d.jsx)(y(),{href:"/payment-methods",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>W(!1),children:an("paymentMethods")}),(0,d.jsx)(y(),{href:"/follow-us",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>W(!1),children:an("followUs")}),(0,d.jsx)(y(),{href:"/about",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>W(!1),children:an("aboutUs")}),(0,d.jsx)(y(),{href:"/contact",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>W(!1),children:an("contactUs")}),(0,d.jsxs)("div",{className:"border-t border-gray-200 pt-4 mt-4",children:[(0,d.jsxs)(y(),{href:"/wishlist",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>W(!1),children:[(0,d.jsx)(p.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"Wishlist"}),(0,d.jsx)("span",{className:"ml-auto bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:ab})]}),(0,d.jsxs)(y(),{href:"/cart",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>W(!1),children:[(0,d.jsx)(t.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"Cart"}),(0,d.jsx)("span",{className:"ml-auto bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:_})]})]}),(0,d.jsx)("div",{className:"border-t border-gray-200 pt-4 mt-4",children:ae?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-500",children:["Welcome, ",ad?.FirstName||ad?.UserName]}),(0,d.jsxs)(y(),{href:"/account",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>W(!1),children:[(0,d.jsx)(l.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"My Account"})]}),(0,d.jsxs)(y(),{href:"/orders",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>W(!1),children:[(0,d.jsx)(n.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"My Orders"})]}),(0,d.jsxs)(y(),{href:"/addresses",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>W(!1),children:[(0,d.jsx)(o.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"My Addresses"})]}),(0,d.jsxs)("button",{className:"flex items-center gap-3 w-full px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors",onClick:()=>{af(),W(!1),a.push("/"),B.oR.success("Logged out successfully")},children:[(0,d.jsx)(q.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:"Logout"})]})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)(y(),{href:"/login",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>W(!1),children:[(0,d.jsx)(l.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:an("login")})]}),(0,d.jsxs)(y(),{href:"/signup",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>W(!1),children:[(0,d.jsx)(r.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:an("signUp")})]})]})})]})}),x&&(0,d.jsx)(V,{onColorSelect:a=>{am(a),C(!1)},onClose:()=>C(!1)})]})}var X=c(19526),Y=c(68575),Z=c(27790),$=c(90452),_=c(15488);function aa(){let{primaryColor:a,primaryTextColor:b,t:c}=(0,R.t)(),[f,g]=(0,e.useState)(""),[h,i]=(0,e.useState)(!1),[j,k]=(0,e.useState)({type:"",text:""}),{executeRecaptcha:l}=(0,_._Y)(),m=async a=>{if(a.preventDefault(),!f||!f.trim())return void k({type:"error",text:"Please enter a valid email address."});if(!l){k({type:"error",text:"reCAPTCHA not available. Please refresh the page and try again."}),console.error("Execute recaptcha not yet available");return}try{i(!0),k({type:"",text:""});try{if(!await l("subscribe"))throw Error("reCAPTCHA verification failed")}catch(a){console.error("reCAPTCHA error:",a),k({type:"error",text:"reCAPTCHA verification failed. Please try again."});return}let a="https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/insert-subscriber";console.log("Making request to:",a);let b=await fetch(a,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({requestParameters:{SubscriberEmail:f.trim()}})});if(console.log("Response status:",b.status),!b.ok)throw Error(`HTTP error! status: ${b.status}`);let c=await b.json();console.log("Response data:",c),200===c.statusCode||b.ok?(k({type:"success",text:"Successfully subscribed to our newsletter!"}),g("")):k({type:"error",text:c.message||c.errorMessage||"Failed to subscribe. Please try again."})}catch(a){console.error("Subscription error:",a),a instanceof TypeError&&a.message.includes("fetch")?k({type:"error",text:"Network error. Please check your connection and try again."}):k({type:"error",text:`Subscription failed: ${a instanceof Error?a.message:"Unknown error"}`})}finally{i(!1)}};return(0,d.jsx)("footer",{className:"w-full",children:(0,d.jsxs)("div",{className:"py-8 sm:py-12",style:{backgroundColor:a,color:b},children:[(0,d.jsxs)("div",{className:"container mx-auto px-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8",children:[(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)("div",{className:"flex items-center gap-2",children:(0,d.jsx)("img",{src:"https://admin.codemedicalapps.com/content/commonImages/otherImages/18b_logo2x.png".replace(/\//g,"/"),alt:"Logo",className:"h-12 sm:h-16 w-auto bg-white p-2 rounded-md"})}),(0,d.jsx)("p",{className:"text-sm sm:text-base opacity-90",children:"We are professional team specialized in providing well known valuable medical courses, Ebooks, Printed books and popular medical accounts for all medical field staff all around the world with low cost and in short time."}),(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsx)(y(),{href:"https://www.facebook.com/codemedicalapps/",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-70 transition-opacity",children:(0,d.jsx)(X.A,{className:"h-5 w-5"})}),(0,d.jsx)(y(),{href:"https://t.me/codemedicalapps",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-70 transition-opacity",children:(0,d.jsx)($.A,{className:"h-5 w-5"})}),(0,d.jsx)(y(),{href:"https://wa.me/*************",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-70 transition-opacity",children:(0,d.jsx)(Y.A,{className:"h-5 w-5"})}),(0,d.jsx)(y(),{href:"https://m.me/***************",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-70 transition-opacity",children:(0,d.jsx)(Z.A,{className:"h-5 w-5",style:{color:"#00B2FF"}})})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-6 sm:gap-8 col-span-2 sm:col-span-1 md:col-span-2",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:c("quickLinks")}),(0,d.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,d.jsx)("li",{children:(0,d.jsx)(y(),{href:"/about",className:"hover:opacity-70 transition-opacity",children:c("about")})}),(0,d.jsx)("li",{children:(0,d.jsx)(y(),{href:"/contact",className:"hover:opacity-70 transition-opacity",children:c("contact")})}),(0,d.jsx)("li",{children:(0,d.jsx)(y(),{href:"/hot-deals",className:"hover:opacity-70 transition-opacity",children:c("hotDeals")})}),(0,d.jsx)("li",{children:(0,d.jsx)(y(),{href:"/login",className:"hover:opacity-70 transition-opacity",children:c("login")})}),(0,d.jsx)("li",{children:(0,d.jsx)(y(),{href:"/signup",className:"hover:opacity-70 transition-opacity",children:c("signup")})})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:c("customerArea")}),(0,d.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,d.jsx)("li",{children:(0,d.jsx)(y(),{href:"/account",className:"hover:opacity-70 transition-opacity",children:c("myAccount")})}),(0,d.jsx)("li",{children:(0,d.jsx)(y(),{href:"/orders",className:"hover:opacity-70 transition-opacity",children:c("orders")})}),(0,d.jsx)("li",{children:(0,d.jsx)(y(),{href:"/cart",className:"hover:opacity-70 transition-opacity",children:c("cart")})}),(0,d.jsx)("li",{children:(0,d.jsx)(y(),{href:"/wishlist",className:"hover:opacity-70 transition-opacity",children:c("wishlist")})}),(0,d.jsx)("li",{children:(0,d.jsx)(y(),{href:"/payment-methods",className:"hover:opacity-70 transition-opacity",children:c("paymentMethods")})})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:c("contact")}),(0,d.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,d.jsxs)("li",{className:"flex items-center gap-2",children:[(0,d.jsxs)("span",{className:"opacity-75",children:[c("location"),":"]}),(0,d.jsx)("span",{children:"Iraq"})]}),(0,d.jsxs)("li",{className:"flex items-center gap-2",children:[(0,d.jsxs)("span",{className:"opacity-75",children:[c("callUs"),":"]}),(0,d.jsx)("a",{href:"tel:+*************",className:"hover:opacity-70 transition-opacity",children:"+964 ************"})]}),(0,d.jsxs)("li",{className:"flex items-center gap-2",children:[(0,d.jsxs)("span",{className:"opacity-75",children:[c("emailUs"),":"]}),(0,d.jsx)("a",{href:"mailto:<EMAIL>",className:"hover:opacity-70 transition-opacity",children:"<EMAIL>"})]})]}),(0,d.jsxs)("div",{className:"mt-6",children:[(0,d.jsx)("h3",{className:"text-base sm:text-lg font-semibold mb-4",children:c("newsletter")}),(0,d.jsxs)("form",{onSubmit:m,className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,d.jsx)("input",{type:"email",value:f,onChange:a=>g(a.target.value),placeholder:c("enterEmail"),required:!0,className:"w-full px-4 py-2 text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-opacity-50",style:{outlineColor:a}}),(0,d.jsx)("button",{type:"submit",disabled:h,className:" px-2 py-2 border border-white rounded-md",style:{backgroundColor:a,color:b,borderColor:b},children:h?(0,d.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):(0,d.jsx)(d.Fragment,{children:(0,d.jsx)("span",{children:c("subscribe")})})})]}),j.text&&(0,d.jsx)("p",{className:`text-sm text-center ${"success"===j.type?"text-green-400":"text-red-400"}`,children:j.text}),(0,d.jsx)("p",{className:"text-xs opacity-75 text-center",children:c("newsletterDisclaimer")})]})]})]})]}),(0,d.jsx)("div",{className:"mt-6 pt-6 border-t text-center opacity-75 text-sm sm:text-base px-4",style:{borderColor:b},children:(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-4",children:[(0,d.jsx)("p",{children:"\xa9 2024 Code Medical. All rights reserved."}),(0,d.jsx)("span",{className:"hidden sm:inline",children:"•"}),(0,d.jsxs)("p",{children:["Powered by"," ",(0,d.jsx)("a",{href:"https://perfectjobline.com/",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-70 transition-opacity underline",children:"perfectjobline"})]})]})})]})})}function ab({className:a=""}){return(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:a,fill:"currentColor",children:(0,d.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413Z"})})}function ac(){return(0,d.jsx)("button",{className:"fixed bottom-20 right-4 md:bottom-6 md:right-6 z-50 flex h-12 w-12 md:h-14 md:w-14 items-center justify-center rounded-full bg-[#25D366] text-white shadow-lg transition-all duration-300 hover:bg-[#128C7E] hover:scale-110 active:scale-95",onClick:()=>{window.open("https://wa.me/+*************","_blank")},"aria-label":"Chat on WhatsApp",children:(0,d.jsx)(ab,{className:"h-6 w-6 md:h-7 md:w-7"})})}var ad=c(32192),ae=c(6943),af=c(3589);function ag(){let[a,b]=(0,e.useState)(!1),[c,f]=(0,e.useState)(!1),[g,h]=(0,e.useState)([]),[i,j]=(0,e.useState)([]),[k,o]=(0,e.useState)({}),[q,r]=(0,e.useState)(""),[u,v]=(0,e.useState)(!1),x=(0,w.useRouter)(),B=(0,w.usePathname)(),{totalItems:D}=(0,z._)(),{totalItems:E}=(0,A.n)(),{user:F,isLoggedIn:G}=(0,S.J)(),{primaryColor:H,t:I}=(0,R.t)(),J=async()=>{if(i.length>0)return void f(!0);v(!0);try{let a={"Content-Type":"application/json",Accept:"application/json",Authorization:"Bearer "+localStorage.getItem("token")},b=await (0,C.MakeApiCallAsync)(C.Config.END_POINT_NAMES.GET_CATEGORIES_LIST,null,{PageNumber:1,PageSize:100,SortColumn:"Name",SortOrder:"ASC"},a,"POST",!0);if(b?.data?.data)try{let a=JSON.parse(b.data.data);if(Array.isArray(a)){let b=a.filter(a=>!a.ParentCategoryID).sort((a,b)=>a.Name.localeCompare(b.Name));j(b),h(a),f(!0)}}catch(a){console.error("Error parsing categories data:",a)}}catch(a){console.error("Error fetching categories:",a)}finally{v(!1)}};if(!a)return null;let K=[{href:"/",icon:ad.A,label:I("home")||"Home",isActive:"/"===B,onClick:null},{href:"#",icon:ae.A,label:I("categories")||"Categories",isActive:!1,onClick:J},{href:"/cart",icon:t.A,label:I("cart")||"Cart",isActive:"/cart"===B,badge:D||0,onClick:null},{href:"/wishlist",icon:p.A,label:I("wishlist")||"Wishlist",isActive:"/wishlist"===B,badge:E||0,onClick:null},{href:G?"/account":"/login",icon:l.A,label:G?F?.FirstName||I("myAccount")||"My Account":I("login")||"Login",isActive:"/login"===B||"/signup"===B||"/profile"===B,onClick:null}];return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-pb",children:(0,d.jsx)("div",{className:"flex items-center justify-end py-2 px-4",children:K.map(a=>{let b=a.icon,c=!!a.onClick,e="flex flex-col items-center justify-center min-w-0 py-2 px-3 ml-2"+(c?" bg-transparent border-none":""),f=(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(b,{className:"h-6 w-6 mb-1",style:{color:a.isActive?H:"#6B7280"}}),void 0!==a.badge&&a.badge>0&&(0,d.jsx)("span",{className:"absolute -top-1 -right-1 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-medium shadow-md",style:{backgroundColor:H},children:a.badge>99?"99+":a.badge})]}),(0,d.jsx)("span",{className:"text-xs font-medium text-center leading-tight mt-1",style:{color:a.isActive?H:"#6B7280"},children:a.label})]});return c?(0,d.jsx)("button",{onClick:a.onClick||void 0,className:e,type:"button",children:f},a.href):(0,d.jsx)(y(),{href:a.href,className:e,children:f},a.href)})})}),c&&(0,d.jsx)("div",{className:"md:hidden fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-end animate-in fade-in duration-300",children:(0,d.jsxs)("div",{className:"bg-white w-full max-h-[75vh] rounded-t-2xl overflow-hidden shadow-2xl animate-in slide-in-from-bottom duration-500",children:[(0,d.jsxs)("div",{className:"relative overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 opacity-10",style:{background:`linear-gradient(135deg, ${H}, ${H}80)`}}),(0,d.jsxs)("div",{className:"relative flex items-center justify-between p-5 border-b border-gray-100",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 rounded-xl flex items-center justify-center animate-pulse",style:{backgroundColor:`${H}20`},children:(0,d.jsx)(ae.A,{className:"h-5 w-5",style:{color:H}})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-xl font-bold",style:{color:H},children:I("categories")||"Categories"}),(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Choose the right category for you"})]})]}),(0,d.jsx)("button",{onClick:()=>f(!1),className:"w-10 h-10 rounded-xl bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-all duration-300 hover:scale-110 hover:rotate-90 group",children:(0,d.jsx)("span",{className:"text-gray-500 group-hover:text-gray-700 font-bold text-lg",children:"✕"})})]}),(0,d.jsx)("div",{className:"h-1 w-full",style:{background:`linear-gradient(90deg, transparent, ${H}, transparent)`}})]}),(0,d.jsx)("div",{className:"overflow-y-auto max-h-[calc(75vh-120px)] scrollbar-hide",children:(()=>{if(u)return(0,d.jsxs)("div",{className:"p-8 text-center",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-4 border-t-transparent mx-auto mb-4",style:{borderColor:`${H}30`,borderTopColor:H}}),(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,d.jsx)("div",{className:"w-3 h-3 rounded-full animate-pulse",style:{backgroundColor:H}})})]}),(0,d.jsx)("p",{className:"text-gray-600 font-medium animate-pulse",children:"Loading..."})]});let a=""===q.trim()?i.sort((a,b)=>a.Name.localeCompare(b.Name)):i.filter(a=>a.Name.toLowerCase().includes(q.toLowerCase())||g.some(b=>b.ParentCategoryID===a.CategoryID&&b.Name.toLowerCase().includes(q.toLowerCase()))).sort((a,b)=>a.Name.localeCompare(b.Name));return(0,d.jsxs)("div",{className:"h-full flex flex-col bg-gradient-to-b from-gray-50 to-white",children:[(0,d.jsx)("div",{className:"p-4 bg-white shadow-sm",children:(0,d.jsxs)("div",{className:"relative group",children:[(0,d.jsx)("input",{type:"text",placeholder:"Search for category...",className:"w-full p-3 pr-12 border-2 rounded-xl focus:outline-none focus:ring-0 transition-all duration-300 bg-gray-50 focus:bg-white focus:shadow-lg",style:{borderColor:q?H:"#e5e7eb",paddingRight:"3rem"},value:q,onChange:a=>r(a.target.value)}),(0,d.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,d.jsx)(s.A,{className:"h-5 w-5 transition-colors duration-300",style:{color:q?H:"#9ca3af"}})}),q&&(0,d.jsx)("button",{onClick:()=>r(""),className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors",children:"✕"})]})}),(0,d.jsx)("div",{className:"flex-1 overflow-y-auto px-2",children:0===a.length?(0,d.jsxs)("div",{className:"p-8 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center",children:(0,d.jsx)(s.A,{className:"h-8 w-8 text-gray-400"})}),(0,d.jsx)("p",{className:"text-gray-500 font-medium",children:"No matching categories found"}),(0,d.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Try searching with different keywords"})]}):(0,d.jsx)("div",{className:"space-y-2 py-2",children:a.map((a,b)=>{let c=g.some(b=>b.ParentCategoryID===a.CategoryID),e=k[a.CategoryID],h=c?g.filter(b=>b.ParentCategoryID===a.CategoryID).sort((a,b)=>a.Name.localeCompare(b.Name)):[],i=""===q.trim()?h:h.filter(a=>a.Name.toLowerCase().includes(q.toLowerCase())).sort((a,b)=>a.Name.localeCompare(b.Name));return(0,d.jsxs)("div",{className:"group animate-in slide-in-from-right duration-300",style:{animationDelay:`${100*b}ms`},children:[(0,d.jsx)("div",{className:"relative overflow-hidden rounded-xl bg-white shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-300 hover:scale-[1.02] hover:border-gray-200",children:(0,d.jsxs)("button",{onClick:()=>{g.filter(b=>b.ParentCategoryID===a.CategoryID).length>0?o(b=>({...b,[a.CategoryID]:!b[a.CategoryID]})):(x.push(`/products?category=${a.CategoryID}`),f(!1))},className:"w-full text-right p-4 flex justify-between items-center relative z-10",children:[(0,d.jsx)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300",style:{background:`linear-gradient(135deg, ${H}, ${H}80)`}}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-300 group-hover:scale-110",style:{backgroundColor:`${H}15`},children:(0,d.jsx)(n.A,{className:"h-5 w-5 transition-colors duration-300",style:{color:H}})}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsx)("span",{className:"font-semibold text-gray-800 group-hover:text-gray-900 transition-colors duration-300",children:a.Name}),c&&(0,d.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[h.length," subcategories"]})]})]}),c&&(0,d.jsx)("div",{className:"flex items-center",children:(0,d.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-110",style:{backgroundColor:`${H}10`},children:e?(0,d.jsx)(af.A,{className:"h-4 w-4 transition-all duration-300",style:{color:H}}):(0,d.jsx)(m.A,{className:"h-4 w-4 transition-all duration-300",style:{color:H}})})})]})}),c&&(0,d.jsx)("div",{className:`overflow-hidden transition-all duration-500 ease-in-out ${e?"max-h-80 opacity-100 mt-2":"max-h-0 opacity-0"}`,children:(0,d.jsxs)("div",{className:"bg-gradient-to-r from-gray-50 to-white rounded-xl p-3 border border-gray-100",children:[(0,d.jsx)("div",{className:"max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100",children:(0,d.jsx)("div",{className:"grid grid-cols-2 gap-2 mb-3 pr-2",children:i.map((a,b)=>(0,d.jsx)("button",{onClick:b=>{b.stopPropagation(),x.push(`/products?category=${a.CategoryID}`),f(!1)},className:"group/child p-2 text-xs text-right rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-md border border-transparent hover:border-gray-200 bg-white hover:bg-gradient-to-r",style:{animationDelay:`${100*b}ms`,"--tw-gradient-from":`${H}05`,"--tw-gradient-to":`${H}10`},children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{className:"font-medium transition-colors duration-300 group-hover/child:font-semibold text-xs leading-tight",style:{color:H},children:a.Name}),(0,d.jsx)("div",{className:"w-1.5 h-1.5 rounded-full transition-all duration-300 group-hover/child:scale-150 flex-shrink-0",style:{backgroundColor:`${H}40`}})]})},a.CategoryID))})}),(0,d.jsxs)("button",{onClick:()=>{x.push(`/products?category=${a.CategoryID}`),f(!1)},className:"w-full py-3 text-center text-sm font-semibold rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg relative overflow-hidden group/all",style:{backgroundColor:H,color:"white"},children:[(0,d.jsx)("div",{className:"absolute inset-0 -translate-x-full group-hover/all:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 to-transparent"}),(0,d.jsxs)("span",{className:"relative z-10 flex items-center justify-center gap-2",children:["View All in ",a.Name,(0,d.jsx)(m.A,{className:"h-4 w-4 rotate-[-90deg] transition-transform duration-300 group-hover/all:translate-x-1"})]})]})]})})]},a.CategoryID)})})})]})})()})]})})]})}var ah=c(70333);let ai=e.forwardRef(({className:a,title:b,description:c,action:e,type:f,...g},h)=>(0,d.jsxs)("div",{ref:h,className:(0,F.cn)("group relative flex items-center justify-between p-4 pr-8 space-x-4 overflow-hidden rounded-md border shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none",(()=>{switch(f){case"success":return"bg-green-500 text-white border-green-600";case"error":return"bg-red-500 text-white border-red-600";case"warning":return"bg-yellow-500 text-white border-yellow-600";case"info":return"bg-blue-500 text-white border-blue-600";default:return"bg-background text-foreground border-border"}})(),a),...g,children:[(0,d.jsxs)("div",{className:"grid gap-1",children:[b&&(0,d.jsx)("div",{className:"text-sm font-semibold",children:b}),c&&(0,d.jsx)("div",{className:"text-sm opacity-90",children:c})]}),e,(0,d.jsx)("button",{className:"absolute right-2 top-2 rounded-md p-1 text-white/70 opacity-0 transition-opacity hover:text-white focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100",children:(0,d.jsx)(u.A,{className:"h-4 w-4"})})]}));ai.displayName="Toast";let aj=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,F.cn)("text-sm font-semibold",a),...b}));aj.displayName="ToastTitle";let ak=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,F.cn)("text-sm opacity-90",a),...b}));ak.displayName="ToastDescription";let al=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("button",{ref:c,className:(0,F.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100",a),...b,children:(0,d.jsx)(u.A,{className:"h-4 w-4"})}));al.displayName="ToastClose";let am=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,F.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",a),...b}));function an({children:a,...b}){return(0,d.jsx)(d.Fragment,{children:a})}function ao(){let{toasts:a}=(0,ah.dj)();return(0,d.jsxs)(an,{children:[a.map(function({id:a,title:b,description:c,action:e,type:f,...g}){return(0,d.jsxs)(ai,{type:f,...g,children:[(0,d.jsxs)("div",{className:"grid gap-1",children:[b&&(0,d.jsx)(aj,{children:b}),c&&(0,d.jsx)(ak,{children:c})]}),e,(0,d.jsx)(al,{})]},a)}),(0,d.jsx)(am,{})]})}am.displayName="ToastViewport",an.displayName="ToastProvider";let ap={whatsappNumber:"+**********",phoneNumber:"+**********",whatsappLink:"https://wa.me/**********"},aq=(0,e.createContext)(ap);function ar({children:a}){return(0,d.jsx)(aq.Provider,{value:ap,children:a})}var as=c(15991),at=c(79936);let au=[{primary:"#0074b2",primaryForeground:"#ffffff",name:"Default Blue"},{primary:"#dc2626",primaryForeground:"#ffffff",name:"Red"},{primary:"#16a34a",primaryForeground:"#ffffff",name:"Green"},{primary:"#ca8a04",primaryForeground:"#ffffff",name:"Yellow"},{primary:"#9333ea",primaryForeground:"#ffffff",name:"Purple"},{primary:"#ea580c",primaryForeground:"#ffffff",name:"Orange"},{primary:"#0891b2",primaryForeground:"#ffffff",name:"Cyan"},{primary:"#be185d",primaryForeground:"#ffffff",name:"Pink"}],av=365,aw=(0,e.createContext)(void 0);function ax({children:a}){let b=function(){let[a,b]=(0,e.useState)(au[0]),[c,d]=(0,e.useState)(!0);(0,e.useCallback)(a=>{if("undefined"==typeof document)return null;let b=a+"=";for(let a of document.cookie.split(";")){let c=a.trim();if(0===c.indexOf(b))return decodeURIComponent(c.substring(b.length))}return null},[]);let f=(0,e.useCallback)((a,b,c=av)=>{if("undefined"==typeof document)return;let d=new Date;d.setTime(d.getTime()+24*c*36e5);let e=`${a}=${encodeURIComponent(b)}; expires=${d.toUTCString()}; path=/; SameSite=strict`;e+="; Secure",document.cookie=e},[]),g=(0,e.useCallback)(a=>{if("undefined"==typeof document)return;let b=document.documentElement,c=a=>{let b=parseInt(a.slice(1,3),16)/255,c=parseInt(a.slice(3,5),16)/255,d=parseInt(a.slice(5,7),16)/255,e=Math.max(b,c,d),f=Math.min(b,c,d),g=0,h=0,i=(e+f)/2;if(e!==f){let a=e-f;switch(h=i>.5?a/(2-e-f):a/(e+f),e){case b:g=(c-d)/a+6*(c<d);break;case c:g=(d-b)/a+2;break;case d:g=(b-c)/a+4}g/=6}return`${Math.round(360*g)} ${Math.round(100*h)}% ${Math.round(100*i)}%`},d=c(a.primary),e=c(a.primaryForeground);b.style.setProperty("--primary",d),b.style.setProperty("--primary-foreground",e);let f=document.querySelector('meta[name="theme-color"]');f&&f.setAttribute("content",a.primary)},[]),h=(0,e.useCallback)(a=>{b(a),g(a),f("color_theme",JSON.stringify(a))},[g,f]),i=(0,e.useCallback)((a,b="Custom")=>{let c={primary:a,primaryForeground:"#ffffff",name:b};return h(c),c},[h]);return{currentTheme:a,availableThemes:au,changeTheme:h,createCustomTheme:i,isLoading:c}}();return(0,d.jsx)(aw.Provider,{value:b,children:a})}function ay({children:a}){return(0,d.jsx)(R.Z,{children:(0,d.jsx)(ax,{children:(0,d.jsx)(S.v,{children:(0,d.jsx)(at.B,{children:(0,d.jsx)(z.e,{children:(0,d.jsx)(ar,{children:(0,d.jsx)(as.U,{children:(0,d.jsx)(A.Z,{children:a})})})})})})})})}function az({data:a}){return(0,d.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(a,null,2)}})}function aA({children:a}){return(0,d.jsxs)("html",{lang:"en",children:[(0,d.jsxs)("head",{children:[(0,d.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),(0,d.jsx)("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),(0,d.jsx)("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),(0,d.jsx)("link",{rel:"manifest",href:"/site.webmanifest"}),(0,d.jsx)("link",{rel:"shortcut icon",href:"/favicon.ico"}),(0,d.jsx)("meta",{name:"theme-color",content:"#0074b2"}),(0,d.jsx)("meta",{name:"msapplication-TileColor",content:"#0074b2"}),(0,d.jsx)("title",{children:"Code Medical Website - Medical Courses & Resources"}),(0,d.jsx)("meta",{name:"description",content:"Professional medical courses, ebooks, printed books and medical accounts for medical field staff worldwide."}),(0,d.jsx)(az,{data:{"@context":"https://schema.org","@type":"Organization",name:"Code Medical",url:"https://codemedicalapps.com",logo:"https://admin.codemedicalapps.com/content/commonImages/otherImages/18b_logo2x.png",description:"Professional team specialized in providing well known valuable medical courses, Ebooks, Printed books and popular medical accounts for all medical field staff all around the world with low cost and in short time.",contactPoint:{"@type":"ContactPoint",telephone:"+964-************",contactType:"customer service",email:"<EMAIL>"},sameAs:["https://www.facebook.com/codemedicalapps/","https://t.me/codemedicalapps","https://wa.me/*************"]}}),(0,d.jsx)(az,{data:{"@context":"https://schema.org","@type":"WebSite",name:"Code Medical",url:"https://codemedicalapps.com",description:"Medical courses, ebooks, printed books and medical accounts for medical professionals worldwide.",potentialAction:{"@type":"SearchAction",target:{"@type":"EntryPoint",urlTemplate:"https://codemedicalapps.com/products?search={search_term_string}"},"query-input":"required name=search_term_string"}}})]}),(0,d.jsx)("body",{className:g().className,suppressHydrationWarning:!0,children:(0,d.jsx)(_.G3,{reCaptchaKey:"6LfS7D0rAAAAAAybL-FSr2N1pWStAiKxN_EapgHJ",scriptProps:{async:!0,defer:!0,appendTo:"body",nonce:void 0},container:{parameters:{badge:"inline",theme:"light"}},children:(0,d.jsxs)(ay,{children:[(0,d.jsx)(W,{}),(0,d.jsx)("main",{className:"min-h-screen pb-16 md:pb-0",children:a}),(0,d.jsx)(aa,{}),(0,d.jsx)(ac,{}),(0,d.jsx)(ag,{}),(0,d.jsx)(ao,{}),(0,d.jsx)(B.l$,{position:"top-right"})]})})})]})}},58014:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx","default")},60796:(a,b,c)=>{"use strict";c.d(b,{T:()=>d});let d={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",COMMON_CONTROLLER_SUB_URL:"api/v1/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile"}}},68575:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(60687);c(43210);let e=({className:a,...b})=>(0,d.jsxs)("svg",{viewBox:"0 0 308 308",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",className:a,...b,children:[(0,d.jsx)("path",{d:"M227.904 176.981c-.6-.288-23.054-11.345-27.044-12.781-1.629-.585-3.374-1.156-5.23-1.156-3.032 0-5.579 1.511-7.563 4.479-2.243 3.334-9.033 11.271-11.131 13.642-.274.313-.648.687-.872.687-.201 0-3.676-1.431-4.728-1.888-24.087-10.463-42.37-35.624-44.877-39.867-.358-.61-.373-.887-.376-.887.088-.323.898-1.135 1.316-1.554 1.223-1.21 2.548-2.805 3.83-4.348.607-.731 1.215-1.463 1.812-2.153 1.86-2.164 2.688-3.844 3.648-5.79l.503-1.011c2.344-4.657.342-8.587-.305-9.856-.531-1.062-10.012-23.944-11.02-26.348-2.424-5.801-5.627-8.502-10.078-8.502-.413 0 0 0-1.732.073-2.109.089-13.594 1.601-18.672 4.802-5.385 3.395-14.495 14.217-14.495 33.249 0 17.129 10.87 33.302 15.537 39.453.116.155.329.47.638.922 17.873 26.102 40.154 45.446 62.741 54.469 21.745 8.686 32.042 9.69 37.896 9.69h.001c2.46 0 4.429-.193 6.166-.364l1.102-.105c7.512-.666 24.02-9.22 27.775-19.655 2.958-8.219 3.738-17.199 1.77-20.458-1.418-2.472-3.741-3.587-6.682-4.999z"}),(0,d.jsx)("path",{d:"M156.734 0C73.318 0 5.454 67.354 5.454 150.143c0 26.777 7.166 52.988 20.741 75.928L.212 302.716a3.999 3.999 0 0 0 .933 4.085A3.99 3.99 0 0 0 4 308c.405 0 .813-.061 1.211-.188l79.92-25.396c21.87 11.685 46.588 17.853 71.604 17.853 83.409 0 151.266-67.347 151.266-150.127C308 67.354 240.143 0 156.734 0zM156.734 268.994c-23.539 0-46.338-6.797-65.936-19.657a4.005 4.005 0 0 0-3.406-.655 4.018 4.018 0 0 0-1.212.188l-40.035 12.726 12.924-38.129a4.001 4.001 0 0 0-.561-3.647C43.584 199.428 35.695 175.335 35.695 150.143c0-65.543 53.754-118.867 119.826-118.867 66.064 0 119.812 53.324 119.812 118.867 0 65.535-53.747 118.851-119.599 118.851z"})]})},70333:(a,b,c)=>{"use strict";c.d(b,{dj:()=>l,oR:()=>k});var d=c(43210);let e=0,f=new Map,g=a=>{if(f.has(a))return;let b=setTimeout(()=>{f.delete(a),j({type:"REMOVE_TOAST",toastId:a})},1e6);f.set(a,b)},h=[],i={toasts:[]};function j(a){i=((a,b)=>{switch(b.type){case"ADD_TOAST":return{...a,toasts:[b.toast,...a.toasts].slice(0,1)};case"UPDATE_TOAST":return{...a,toasts:a.toasts.map(a=>a.id===b.toast.id?{...a,...b.toast}:a)};case"DISMISS_TOAST":{let{toastId:c}=b;return c?g(c):a.toasts.forEach(a=>{g(a.id)}),{...a,toasts:a.toasts.map(a=>a.id===c||void 0===c?{...a,open:!1}:a)}}case"REMOVE_TOAST":if(void 0===b.toastId)return{...a,toasts:[]};return{...a,toasts:a.toasts.filter(a=>a.id!==b.toastId)}}})(i,a),h.forEach(a=>{a(i)})}function k({duration:a=2e3,...b}){let c=(e=(e+1)%100).toString(),d=()=>j({type:"DISMISS_TOAST",toastId:c});return j({type:"ADD_TOAST",toast:{...b,id:c,duration:a,open:!0,onOpenChange:a=>{a||d()}}}),setTimeout(()=>{d()},a),{id:c,dismiss:d,update:a=>j({type:"UPDATE_TOAST",toast:{...a,id:c}})}}function l(){let[a,b]=d.useState(i);return d.useEffect(()=>(h.push(b),()=>{let a=h.indexOf(b);a>-1&&h.splice(a,1)}),[a]),{...a,toast:k,dismiss:a=>j({type:"DISMISS_TOAST",toastId:a})}}k.success=(a,b)=>k({description:a,type:"success",duration:2e3,...b}),k.error=(a,b)=>k({description:a,type:"error",duration:2e3,...b}),k.warning=(a,b)=>k({description:a,type:"warning",duration:2e3,...b}),k.info=(a,b)=>k({description:a,type:"info",duration:2e3,...b})},76080:()=>{},77080:(a,b,c)=>{"use strict";c.d(b,{Z:()=>i,t:()=>j});var d=c(60687),e=c(43210);let f={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us",searchProducts:"Search products..."},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",searchProducts:"ابحث عن المنتجات...",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}};var g=c(80063);let h=(0,e.createContext)(void 0);function i({children:a}){let[b,c]=(0,e.useState)("light"),[i,j]=(0,e.useState)("en"),[k,l]=(0,e.useState)("#0074b2"),[m,n]=(0,e.useState)("#ffffff");return(0,d.jsx)(h.Provider,{value:{theme:b,language:i,primaryColor:k,primaryTextColor:m,toggleTheme:()=>{c("light"===b?"dark":"light")},setLanguage:a=>{j(a),document.documentElement.dir="ar"===a?"rtl":"ltr"},setPrimaryColor:a=>{l(a);let b=(0,g.N)(a);n(b),document.documentElement.style.setProperty("--primary",a),document.documentElement.style.setProperty("--primary-foreground",b)},t:a=>(function(a,b){let c=f[b];return a in c?c[a]:"en"!==b&&a in f.en?f.en[a]:a})(a,i)},children:a})}function j(){let a=(0,e.useContext)(h);if(void 0===a)throw Error("useSettings must be used within a SettingsProvider");return a}},77702:(a,b,c)=>{Promise.resolve().then(c.bind(c,44761))},79936:(a,b,c)=>{"use strict";c.d(b,{B:()=>h,H:()=>i});var d=c(60687),e=c(43210),f=c(40529);let g=(0,e.createContext)(void 0);function h({children:a}){let[b,c]=(0,e.useState)(1500),[h,i]=(0,e.useState)(!0),j=async()=>{i(!0);try{let a=await (0,f.k6)();c(a)}catch(a){console.error("Failed to load currency rate:",a)}finally{i(!1)}},k=async()=>{await j()};return(0,d.jsx)(g.Provider,{value:{rate:b,isLoading:h,convertToIQD:a=>(0,f.XX)(a,b),formatUSD:a=>(0,f.$g)(a,"USD"),formatIQD:a=>(0,f.$g)(a,"IQD"),refreshRate:k},children:a})}function i(){let a=(0,e.useContext)(g);if(void 0===a)throw Error("useCurrency must be used within a CurrencyProvider");return a}},80063:(a,b,c)=>{"use strict";function d(a,b){let c=a=>{let b=a.replace("#",""),c=parseInt(b.slice(0,2),16)/255,d=[c,parseInt(b.slice(2,4),16)/255,parseInt(b.slice(4,6),16)/255].map(a=>a<=.03928?a/12.92:Math.pow((a+.055)/1.055,2.4));return .2126*d[0]+.7152*d[1]+.0722*d[2]},d=c(a),e=c(b);return(Math.max(d,e)+.05)/(Math.min(d,e)+.05)}function e(a,b="AA"){let c=d(a,"#ffffff"),f=d(a,"#000000"),g="AAA"===b?7:4.5;return c>=g&&f>=g?c>f?"#ffffff":"#000000":c>=g?"#ffffff":f>=g?"#000000":c>f?"#ffffff":"#000000"}c.d(b,{N:()=>e})},81142:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},90452:(a,b,c)=>{"use strict";c.d(b,{A:()=>e});var d=c(60687);c(43210);let e=({className:a,...b})=>(0,d.jsxs)("svg",{viewBox:"0 0 455 455",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",className:a,...b,children:[(0,d.jsx)("path",{d:"M0 0v455h455V0H0zm384.814 100.68l-53.458 257.136a8.001 8.001 0 0 1-13.401 5.172l-72.975-52.981a11.996 11.996 0 0 0-11.942-.744l-40.46 32.981c-4.695 3.84-11.771 1.7-13.569-4.083l-28.094-90.351-72.583-27.089c-7.373-2.762-7.436-13.171-.084-16.003L373.36 90.959c6.315-2.442 12.83 3.09 11.454 9.721z"}),(0,d.jsx)("path",{d:"M313.567 147.179l-141.854 87.367c-5.437 3.355-7.996 9.921-6.242 16.068l15.337 53.891a4.002 4.002 0 0 0 7.162-.517l3.986-29.553a20.016 20.016 0 0 1 7.522-14.522l117.069-108.822c2.729-2.53.105-5.977-2.653-4.912z",fill:"#ffffff"})]})},93283:(a,b,c)=>{"use strict";c.d(b,{_:()=>h,e:()=>g});var d=c(60687),e=c(43210);let f=(0,e.createContext)(void 0);function g({children:a}){let[b,c]=(0,e.useState)([]),[g,h]=(0,e.useState)(!1),i=a=>{c(b=>b.filter(b=>b.id!==a))},j=b.reduce((a,b)=>a+b.quantity,0),k=b.reduce((a,b)=>a+(b.discountPrice?Math.min(b.discountPrice,b.adjustedPrice):b.adjustedPrice)*b.quantity,0),l=b.reduce((a,b)=>a+(b.adjustedIqdPrice||b.iqdPrice||0)*b.quantity,0);return(0,d.jsx)(f.Provider,{value:{items:b,addToCart:(a,b,d=[],e,f=1500)=>{c(c=>{let g=a.price,h=e||Math.round(a.price*f),i=h;d.forEach(b=>{if(b.PriceAdjustment&&b.PriceAdjustmentType){let c=a.originalPrice||a.price;switch(b.PriceAdjustmentType){case 1:g+=b.PriceAdjustment,i+=Math.round(b.PriceAdjustment*f);break;case 2:let d=c*b.PriceAdjustment/100;g+=d,i+=Math.round(d*f)}}});let j=c.findIndex(b=>b.id===a.id&&JSON.stringify(b.attributes?.sort((a,b)=>a.ProductAttributeID-b.ProductAttributeID))===JSON.stringify(d?.sort((a,b)=>a.ProductAttributeID-b.ProductAttributeID)));if(!(j>=0))return[...c,{...a,iqdPrice:h,adjustedIqdPrice:Math.max(0,i),quantity:b,attributes:d,adjustedPrice:Math.max(0,g),originalPrice:a.originalPrice}];{let a=[...c];return a[j].quantity+=b,a}})},removeFromCart:i,updateQuantity:(a,b)=>{if(b<=0)return void i(a);c(c=>c.map(c=>c.id===a?{...c,quantity:b}:c))},clearCart:()=>{c([])},totalItems:j,subtotal:k,subtotalIQD:l,total:k,totalIQD:l,isHydrated:g},children:a})}function h(){let a=(0,e.useContext)(f);if(void 0===a)throw Error("useCart must be used within a CartProvider");return a}},96241:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}}};