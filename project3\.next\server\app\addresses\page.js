(()=>{var a={};a.id=9025,a.ids=[9025],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8963:(a,b,c)=>{Promise.resolve().then(c.bind(c,58297))},9643:(a,b,c)=>{Promise.resolve().then(c.bind(c,79614))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},14952:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28559:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34993:(a,b,c)=>{"use strict";c.d(b,{AB:()=>j,J5:()=>k,Qp:()=>i,tH:()=>n,tJ:()=>m,w1:()=>l});var d=c(60687),e=c(43210),f=c(8730),g=c(14952),h=(c(93661),c(96241));let i=e.forwardRef(({...a},b)=>(0,d.jsx)("nav",{ref:b,"aria-label":"breadcrumb",...a}));i.displayName="Breadcrumb";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("ol",{ref:c,className:(0,h.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",a),...b}));j.displayName="BreadcrumbList";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("li",{ref:c,className:(0,h.cn)("inline-flex items-center gap-1.5",a),...b}));k.displayName="BreadcrumbItem";let l=e.forwardRef(({asChild:a,className:b,...c},e)=>{let g=a?f.DX:"a";return(0,d.jsx)(g,{ref:e,className:(0,h.cn)("transition-colors hover:text-foreground",b),...c})});l.displayName="BreadcrumbLink";let m=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("span",{ref:c,role:"link","aria-disabled":"true","aria-current":"page",className:(0,h.cn)("font-normal text-foreground",a),...b}));m.displayName="BreadcrumbPage";let n=({children:a,className:b,...c})=>(0,d.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,h.cn)("[&>svg]:size-3.5",b),...c,children:a??(0,d.jsx)(g.A,{})});n.displayName="BreadcrumbSeparator"},39390:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(96241);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55192:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h,wL:()=>l});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b}));l.displayName="CardFooter"},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},58297:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>z});var d=c(60687),e=c(43210),f=c(16189),g=c(34993),h=c(55192),i=c(24934),j=c(68988),k=c(39390),l=c(63974),m=c(71463),n=c(85814),o=c.n(n),p=c(832),q=c(32192),r=c(85778),s=c(19080),t=c(41550),u=c(97992),v=c(28559),w=c(96474);let x=(0,c(62688).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);var y=c(88233);function z(){let{user:a,isLoggedIn:b,isLoading:n,token:z}=(0,p.J)(),A=(0,f.useRouter)(),[B,C]=(0,e.useState)([]),[D,E]=(0,e.useState)([]),[F,G]=(0,e.useState)([]),[H,I]=(0,e.useState)(!0),[J,K]=(0,e.useState)(!1),[L,M]=(0,e.useState)(!1),[N,O]=(0,e.useState)(!1),[P,Q]=(0,e.useState)(null),[R,S]=(0,e.useState)({AddressLineOne:"",AddressLineTwo:"",CountryID:"107",CityID:"",StateProvinceID:"",PostalCode:"",AddressTypeID:1}),T=async()=>{console.log("\uD83C\uDFE0 Fetching addresses with token:",z?"exists":"missing");try{let a=await fetch("/api/addresses/get-user-addresses",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${z}`},body:JSON.stringify({requestParameters:{recordValueJson:"[]"}})});console.log("\uD83C\uDFE0 Fetch response status:",a.status);let b=await a.json();if(console.log("\uD83C\uDFE0 Addresses API Response:",b),"TOKEN_EXPIRED"===b.code||"Token is expired"===b.error){console.log("\uD83C\uDFE0 Token expired, redirecting to login"),A.push("/login?redirect=/addresses&reason=token_expired");return}if(200===b.statusCode&&b.data){let a="string"==typeof b.data?JSON.parse(b.data):b.data;console.log("\uD83C\uDFE0 Parsed addresses data:",a),C(a||[])}else console.log("\uD83C\uDFE0 No addresses data or error:",b),C([])}catch(a){console.error("Error fetching addresses:",a)}finally{I(!1)}},U=async a=>{M(!0),G([]);try{let b=await fetch("/api/cities",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({requestParameters:{StateProvinceId:null,CountryId:a,recordValueJson:"[]"}})}),c=await b.json();if(c&&c.data){let a=JSON.parse(c.data);Array.isArray(a)&&G(a)}}catch(a){console.error("Error fetching cities:",a)}finally{M(!1)}};if(n)return(0,d.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,d.jsxs)("div",{className:"flex justify-center items-center min-h-[400px]",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"}),(0,d.jsx)("p",{className:"text-lg text-muted-foreground mt-4",children:"Loading..."})]})});if(!b)return(0,d.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,d.jsx)("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,d.jsx)("div",{className:"text-center",children:(0,d.jsx)("p",{className:"text-lg text-muted-foreground",children:"Redirecting to login..."})})})});let V=a=>{let{name:b,value:c}=a.target;S(a=>({...a,[b]:c}))},W=(a,b)=>{S(c=>({...c,[a]:b})),"CountryID"===a&&b&&(U(parseInt(b)),S(a=>({...a,CityID:""})))},X=async b=>{b.preventDefault();try{let b=P?"/api/addresses/update-address":"/api/addresses/insert-address",d={requestParameters:{...R,CountryID:parseInt(R.CountryID),CityID:R.CityID?parseInt(R.CityID):1,StateProvinceID:R.StateProvinceID?parseInt(R.StateProvinceID):1,AddressTypeID:parseInt(R.AddressTypeID.toString()),...P&&{AddressID:P.AddressID,ModifiedBy:a?.UserId||a?.UserID||1},recordValueJson:"[]"}};console.log("\uD83C\uDFE0 Submitting address with type:",d.requestParameters.AddressTypeID,Z(d.requestParameters.AddressTypeID));let e=await fetch(b,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${z}`},body:JSON.stringify(d)}),f=await e.json();if(200===f.statusCode){let a=(await c.e(7567).then(c.bind(c,77567))).default;await a.fire({icon:"success",title:P?"Address Updated!":"Address Added!",text:`Your address has been ${P?"updated":"added"} successfully`,timer:2e3,showConfirmButton:!1}),S({AddressLineOne:"",AddressLineTwo:"",CountryID:"107",CityID:"",StateProvinceID:"",PostalCode:"",AddressTypeID:1}),U(107),O(!1),Q(null),T()}}catch(b){console.error("Error saving address:",b);let a=(await c.e(7567).then(c.bind(c,77567))).default;await a.fire({icon:"error",title:"Error",text:"Failed to save address. Please try again.",timer:3e3,showConfirmButton:!1})}},Y=async a=>{let b=(await c.e(7567).then(c.bind(c,77567))).default;if((await b.fire({title:"Delete Address?",text:"Are you sure you want to delete this address?",icon:"warning",showCancelButton:!0,confirmButtonColor:"#ef4444",cancelButtonColor:"#6b7280",confirmButtonText:"Yes, delete it!"})).isConfirmed)try{await b.fire({icon:"info",title:"Delete functionality",text:"Delete endpoint needs to be implemented",timer:2e3,showConfirmButton:!1})}catch(a){console.error("Error deleting address:",a)}},Z=a=>{switch(a){case 1:return"Home";case 2:return"Billing";case 3:return"Shipping";case 4:return"Mailing";default:return"Other"}};return(0,d.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,d.jsx)(g.Qp,{className:"mb-6",children:(0,d.jsxs)(g.AB,{children:[(0,d.jsx)(g.J5,{children:(0,d.jsx)(g.w1,{asChild:!0,children:(0,d.jsx)(o(),{href:"/",children:"Home"})})}),(0,d.jsx)(g.tH,{}),(0,d.jsx)(g.J5,{children:(0,d.jsx)(g.w1,{asChild:!0,children:(0,d.jsx)(o(),{href:"/account",children:"Account"})})}),(0,d.jsx)(g.tH,{}),(0,d.jsx)(g.J5,{children:(0,d.jsx)(g.tJ,{children:"Addresses"})})]})}),(0,d.jsx)(i.$,{variant:"outline",className:"mb-6",asChild:!0,children:(0,d.jsxs)(o(),{href:"/account",children:[(0,d.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Back to Account"]})}),(0,d.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"My Addresses"}),(0,d.jsxs)(i.$,{onClick:()=>{O(!0),Q(null),S({AddressLineOne:"",AddressLineTwo:"",CountryID:"107",CityID:"",StateProvinceID:"",PostalCode:"",AddressTypeID:1}),U(107)},className:"flex items-center gap-2",children:[(0,d.jsx)(w.A,{className:"h-4 w-4"}),"Add New Address"]})]}),N?(0,d.jsx)(h.Zp,{className:"mb-6",children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold mb-4",children:P?"Edit Address":"Add New Address"}),(0,d.jsxs)("form",{onSubmit:X,className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(k.J,{htmlFor:"AddressLineOne",children:"Address Line 1 *"}),(0,d.jsx)(j.p,{id:"AddressLineOne",name:"AddressLineOne",value:R.AddressLineOne,onChange:V,placeholder:"Enter street address",required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(k.J,{htmlFor:"AddressLineTwo",children:"Address Line 2"}),(0,d.jsx)(j.p,{id:"AddressLineTwo",name:"AddressLineTwo",value:R.AddressLineTwo,onChange:V,placeholder:"Apartment, suite, etc."})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(k.J,{htmlFor:"CountryID",children:"Country *"}),(0,d.jsxs)(l.l6,{value:R.CountryID,onValueChange:a=>W("CountryID",a),disabled:J,children:[(0,d.jsx)(l.bq,{children:(0,d.jsx)(l.yv,{placeholder:J?"Loading countries...":"Select country"})}),(0,d.jsx)(l.gC,{className:"max-h-[200px] bg-white",children:D.length>0?D.map(a=>(0,d.jsx)(l.eb,{value:a.CountryID.toString(),className:"text-black hover:bg-gray-100 focus:bg-gray-100 data-[highlighted]:bg-gray-100",children:a.CountryName},a.CountryID)):(0,d.jsx)("div",{className:"p-2 text-sm text-gray-500",children:J?"Loading countries...":"No countries found"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(k.J,{htmlFor:"CityID",children:"City"}),(0,d.jsxs)(l.l6,{value:R.CityID,onValueChange:a=>W("CityID",a),disabled:!R.CountryID||L,children:[(0,d.jsx)(l.bq,{children:(0,d.jsx)(l.yv,{placeholder:R.CountryID?L?"Loading cities...":"Select city":"Select country first"})}),(0,d.jsx)(l.gC,{className:"max-h-[200px] bg-white",children:R.CountryID?F.length>0?F.map(a=>(0,d.jsx)(l.eb,{value:a.CityID.toString(),className:"text-black hover:bg-gray-100 focus:bg-gray-100 data-[highlighted]:bg-gray-100",children:a.CityName||a.Name},a.CityID)):(0,d.jsx)("div",{className:"p-2 text-sm text-gray-500",children:L?"Loading cities...":"No cities found"}):(0,d.jsx)("div",{className:"p-2 text-sm text-gray-500",children:"Select country first"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(k.J,{htmlFor:"PostalCode",children:"Postal Code"}),(0,d.jsx)(j.p,{id:"PostalCode",name:"PostalCode",value:R.PostalCode,onChange:V,placeholder:"Enter postal code"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(k.J,{htmlFor:"AddressTypeID",children:"Address Type"}),(0,d.jsxs)(l.l6,{value:R.AddressTypeID.toString(),onValueChange:a=>W("AddressTypeID",a),children:[(0,d.jsx)(l.bq,{children:(0,d.jsx)(l.yv,{})}),(0,d.jsxs)(l.gC,{children:[(0,d.jsx)(l.eb,{value:"1",children:"Home"}),(0,d.jsx)(l.eb,{value:"2",children:"Billing"}),(0,d.jsx)(l.eb,{value:"3",children:"Shipping"}),(0,d.jsx)(l.eb,{value:"4",children:"Mailing"})]})]})]})]}),(0,d.jsxs)("div",{className:"flex gap-3",children:[(0,d.jsx)(i.$,{type:"submit",children:P?"Update Address":"Add Address"}),(0,d.jsx)(i.$,{type:"button",variant:"outline",onClick:()=>{O(!1),Q(null)},children:"Cancel"})]})]})]})}):null,H?(0,d.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((a,b)=>(0,d.jsx)(h.Zp,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)(m.E,{className:"h-6 w-32 mb-2"}),(0,d.jsx)(m.E,{className:"h-4 w-full mb-1"}),(0,d.jsx)(m.E,{className:"h-4 w-3/4"})]})},b))}):B.length>0?(0,d.jsx)("div",{className:"space-y-4",children:B.map(a=>(0,d.jsx)(h.Zp,{children:(0,d.jsx)("div",{className:"p-6",children:(0,d.jsxs)("div",{className:"flex justify-between items-start",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(a=>{switch(a){case 1:return(0,d.jsx)(q.A,{className:"h-4 w-4"});case 2:return(0,d.jsx)(r.A,{className:"h-4 w-4"});case 3:return(0,d.jsx)(s.A,{className:"h-4 w-4"});case 4:return(0,d.jsx)(t.A,{className:"h-4 w-4"});default:return(0,d.jsx)(u.A,{className:"h-4 w-4"})}})(a.AddressTypeID),(0,d.jsx)("span",{className:"font-medium text-primary",children:a.AddressTypeName||Z(a.AddressTypeID)})]}),(0,d.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,d.jsx)("p",{className:"font-medium",children:a.AddressLineOne}),a.AddressLineTwo&&(0,d.jsx)("p",{className:"text-muted-foreground",children:a.AddressLineTwo}),(0,d.jsxs)("p",{className:"text-muted-foreground",children:[a.CountryName,a.PostalCode&&` - ${a.PostalCode}`]})]})]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>{console.log("\uD83C\uDFE0 Editing address with type:",a.AddressTypeID,Z(a.AddressTypeID)),Q(a),S({AddressLineOne:a.AddressLineOne,AddressLineTwo:a.AddressLineTwo,CountryID:a.CountryID.toString(),CityID:a.CityID?.toString()||"",StateProvinceID:a.StateProvinceID?.toString()||"",PostalCode:a.PostalCode||"",AddressTypeID:a.AddressTypeID}),a.CountryID&&U(a.CountryID),O(!0)},children:(0,d.jsx)(x,{className:"h-4 w-4"})}),(0,d.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>Y(a.AddressID),className:"text-red-600 hover:text-red-700",children:(0,d.jsx)(y.A,{className:"h-4 w-4"})})]})]})})},a.AddressID))}):(0,d.jsx)(h.Zp,{children:(0,d.jsxs)("div",{className:"p-8 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(u.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No Addresses Found"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:"You haven't added any addresses yet. Add your first address to get started."}),(0,d.jsxs)(i.$,{onClick:()=>{O(!0),Q(null),S({AddressLineOne:"",AddressLineTwo:"",CountryID:"107",CityID:"",StateProvinceID:"",PostalCode:"",AddressTypeID:1}),U(107)},children:[(0,d.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Add Your First Address"]})]})})]})]})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(a,b,c)=>{"use strict";c.d(b,{bq:()=>m,eb:()=>q,gC:()=>p,l6:()=>k,yv:()=>l});var d=c(60687),e=c(43210),f=c(72951),g=c(78272),h=c(3589),i=c(13964),j=c(96241);let k=f.bL;f.YJ;let l=f.WT,m=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.l9,{ref:e,className:(0,j.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[b,(0,d.jsx)(f.In,{asChild:!0,children:(0,d.jsx)(g.A,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=f.l9.displayName;let n=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.PP,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(h.A,{className:"h-4 w-4"})}));n.displayName=f.PP.displayName;let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wn,{ref:c,className:(0,j.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(g.A,{className:"h-4 w-4"})}));o.displayName=f.wn.displayName;let p=e.forwardRef(({className:a,children:b,position:c="popper",...e},g)=>(0,d.jsx)(f.ZL,{children:(0,d.jsxs)(f.UC,{ref:g,className:(0,j.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-white text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...e,children:[(0,d.jsx)(n,{}),(0,d.jsx)(f.LM,{className:(0,j.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:b}),(0,d.jsx)(o,{})]})}));p.displayName=f.UC.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.JU,{ref:c,className:(0,j.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...b})).displayName=f.JU.displayName;let q=e.forwardRef(({className:a,children:b,...c},e)=>(0,d.jsxs)(f.q7,{ref:e,className:(0,j.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...c,children:[(0,d.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,d.jsx)(f.VF,{children:(0,d.jsx)(i.A,{className:"h-4 w-4"})})}),(0,d.jsx)(f.p4,{children:b})]}));q.displayName=f.q7.displayName,e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.wv,{ref:c,className:(0,j.cn)("-mx-1 my-1 h-px bg-muted",a),...b})).displayName=f.wv.displayName},68988:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"},71463:(a,b,c)=>{"use strict";c.d(b,{E:()=>f});var d=c(60687),e=c(96241);function f({className:a,...b}){return(0,d.jsx)("div",{className:(0,e.cn)("animate-pulse rounded-md bg-muted",a),...b})}},74075:a=>{"use strict";a.exports=require("zlib")},78148:(a,b,c)=>{"use strict";c.d(b,{b:()=>h});var d=c(43210),e=c(14163),f=c(60687),g=d.forwardRef((a,b)=>(0,f.jsx)(e.sG.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));g.displayName="Label";var h=g},79551:a=>{"use strict";a.exports=require("url")},79614:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\addresses\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\addresses\\page.tsx","default")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},85778:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88233:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},88363:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["addresses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,79614)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\addresses\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\addresses\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/addresses/page",pathname:"/addresses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/addresses/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},93661:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:a=>{"use strict";a.exports=require("events")},96474:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,1697,2978,9822],()=>b(b.s=88363));module.exports=c})();