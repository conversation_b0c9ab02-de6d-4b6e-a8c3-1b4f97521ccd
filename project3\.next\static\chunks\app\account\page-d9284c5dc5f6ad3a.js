(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1298],{4516:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},19420:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},28883:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},34835:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},34964:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>d,av:()=>c,j7:()=>n,tU:()=>i});var a=s(95155),r=s(12115),l=s(30064),o=s(53999);let i=l.bL,n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.B8,{ref:t,className:(0,o.cn)("inline-flex h-8 items-center justify-center rounded-md bg-muted p-0.5 text-muted-foreground",s),...r})});n.displayName=l.B8.displayName;let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.l9,{ref:t,className:(0,o.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-2 py-1 text-xs font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...r})});d.displayName=l.l9.displayName;let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.UC,{ref:t,className:(0,o.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...r})});c.displayName=l.UC.displayName},35695:(e,t,s)=>{"use strict";var a=s(18999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"usePathname")&&s.d(t,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},37108:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},38564:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},40968:(e,t,s)=>{"use strict";s.d(t,{b:()=>i});var a=s(12115),r=s(63655),l=s(95155),o=a.forwardRef((e,t)=>(0,l.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var s;t.target.closest("button, input, select, textarea")||(null==(s=e.onMouseDown)||s.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var i=o},44922:(e,t,s)=>{Promise.resolve().then(s.bind(s,85627))},51976:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},54653:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]])},71007:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},74466:(e,t,s)=>{"use strict";s.d(t,{F:()=>o});var a=s(52596);let r=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=a.$,o=(e,t)=>s=>{var a;if((null==t?void 0:t.variants)==null)return l(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:o,defaultVariants:i}=t,n=Object.keys(o).map(e=>{let t=null==s?void 0:s[e],a=null==i?void 0:i[e];if(null===t)return null;let l=r(t)||r(a);return o[e][l]}),d=s&&Object.entries(s).reduce((e,t)=>{let[s,a]=t;return void 0===a||(e[s]=a),e},{});return l(e,n,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:s,className:a,...r}=t;return Object.entries(r).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...i,...d}[t]):({...i,...d})[t]===s})?[...e,s,a]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}},78749:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},81586:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},82714:(e,t,s)=>{"use strict";s.d(t,{J:()=>d});var a=s(95155),r=s(12115),l=s(40968),o=s(74466),i=s(53999);let n=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.b,{ref:t,className:(0,i.cn)(n(),s),...r})});d.displayName=l.b.displayName},84995:(e,t,s)=>{"use strict";s.d(t,{AB:()=>d,J5:()=>c,Qp:()=>n,tH:()=>g,tJ:()=>u,w1:()=>m});var a=s(95155),r=s(12115),l=s(99708),o=s(13052),i=(s(5623),s(53999));let n=r.forwardRef((e,t)=>{let{...s}=e;return(0,a.jsx)("nav",{ref:t,"aria-label":"breadcrumb",...s})});n.displayName="Breadcrumb";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("ol",{ref:t,className:(0,i.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",s),...r})});d.displayName="BreadcrumbList";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("li",{ref:t,className:(0,i.cn)("inline-flex items-center gap-1.5",s),...r})});c.displayName="BreadcrumbItem";let m=r.forwardRef((e,t)=>{let{asChild:s,className:r,...o}=e,n=s?l.DX:"a";return(0,a.jsx)(n,{ref:t,className:(0,i.cn)("transition-colors hover:text-foreground",r),...o})});m.displayName="BreadcrumbLink";let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,i.cn)("font-normal text-foreground",s),...r})});u.displayName="BreadcrumbPage";let g=e=>{let{children:t,className:s,...r}=e;return(0,a.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,i.cn)("[&>svg]:size-3.5",s),...r,children:null!=t?t:(0,a.jsx)(o.A,{})})};g.displayName="BreadcrumbSeparator"},85627:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>O});var a=s(95155),r=s(12115),l=s(35695),o=s(84995),i=s(88482),n=s(97168),d=s(89852),c=s(82714),m=s(34964),u=s(6874),g=s.n(u),h=s(79891),x=s(98816),p=s(65409),f=s(53580),y=s(71007),v=s(19946);let N=(0,v.A)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);var b=s(38564),j=s(37108),w=s(4516),A=s(81586),C=s(51976),P=s(34835),k=s(19420),S=s(28883),I=s(54653);let M=(0,v.A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var E=s(78749),D=s(92657);function O(){var e,t;let{t:s,primaryColor:u,primaryTextColor:v}=(0,h.t)(),{user:O,isLoggedIn:F,isLoading:L,logout:J,updateProfile:R}=(0,x.J)(),{toast:_}=(0,f.dj)(),z=(0,l.useRouter)(),[B,G]=(0,r.useState)(!1),[T,U]=(0,r.useState)(!1),[$,H]=(0,r.useState)(!1),[q,V]=(0,r.useState)([]),[Z,X]=(0,r.useState)(!0),[W,Q]=(0,r.useState)(""),[Y,K]=(0,r.useState)(!1),[ee,et]=(0,r.useState)([]),[es,ea]=(0,r.useState)({firstName:"",lastName:"",email:"",phone:"",gender:"",category:"",newPassword:"",confirmPassword:"",currentPassword:""}),[er,el]=(0,r.useState)("");(0,r.useEffect)(()=>{console.log("\uD83D\uDD0D Account Page Debug - Full user object:",O),console.log("\uD83D\uDD0D Account Page Debug - IsActive:",null==O?void 0:O.IsActive,"(type:",typeof(null==O?void 0:O.IsActive),")"),console.log("\uD83D\uDD0D Account Page Debug - CreatedOn:",null==O?void 0:O.CreatedOn,"(type:",typeof(null==O?void 0:O.CreatedOn),")"),O&&console.log("\uD83D\uDD0D Account Page Debug - All user keys:",Object.keys(O))},[O]),(0,r.useEffect)(()=>{(async()=>{try{var e;let t=await (0,p.MakeApiCallAsync)(p.Config.END_POINT_NAMES.GET_CATEGORIES_LIST,null,{PageNumber:1,PageSize:100,SortColumn:"Name",SortOrder:"ASC"},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(null==t||null==(e=t.data)?void 0:e.data)try{let e=JSON.parse(t.data.data);V(e),et(e)}catch(e){console.error("Error parsing categories data:",e),V([]),et([])}else V([]),et([])}catch(e){console.error("Error fetching categories:",e),V([]),et([])}finally{X(!1)}})()},[]),(0,r.useEffect)(()=>{if(O){console.log("\uD83D\uDD0D Account page: User data received:",O),console.log("\uD83D\uDD0D Account page: Gender field:",O.Gender||O.gender),console.log("\uD83D\uDD0D Account page: Category fields:",{CategoryID:O.CategoryID,CategoryId:O.CategoryId,SpecialistId:O.SpecialistId,CatID:O.CatID});let e={firstName:O.FirstName||O.firstname||O.first_name||"",lastName:O.LastName||O.lastname||O.last_name||"",email:O.Email||O.EmailAddress||O.email||O.email_address||"",phone:O.PhoneNumber||O.PhoneNo||O.MobileNo||O.phone||O.mobile||"",gender:O.Gender||O.gender||"",category:O.CategoryID||O.CategoryId||O.category_id||O.categoryId||O.SpecialistId||O.specialist_id||O.CatID||""};console.log("\uD83D\uDD0D Account page: Mapped profile data:",e),setTimeout(()=>{ea(t=>({...t,...e}));let t=document.getElementById("gender");t&&e.gender&&(console.log("\uD83D\uDD0D Account page: Directly setting gender select to:",e.gender),t.value=e.gender)},100)}},[O]),(0,r.useEffect)(()=>{if(q.length>0&&es.category){console.log("\uD83D\uDD0D Account page: Setting up category search"),console.log("\uD83D\uDD0D Account page: Profile category:",es.category),console.log("\uD83D\uDD0D Account page: Available categories:",q.length);let e=parseInt(es.category.toString()),t=q.find(t=>t.CategoryID===e||t.CategoryId===e);if(console.log("\uD83D\uDD0D Account page: Found user category:",t),t){let e=t.CategoryName||t.Name||"";console.log("\uD83D\uDD0D Account page: Setting category search to:",e),Q(e),et(q)}}},[q,es.category]),(0,r.useEffect)(()=>{let e=document.getElementById("gender");if(e&&es.gender&&(console.log("\uD83D\uDD0D Account page: Setting gender select to:",es.gender),e.value=es.gender),q.length>0&&es.category){let e=parseInt(es.category.toString()),t=q.find(t=>t.CategoryID===e||t.CategoryId===e);if(t){let e=t.CategoryName||t.Name||"";console.log("\uD83D\uDD0D Account page: Setting category search to:",e),Q(e)}}},[es.gender,es.category,q]),(0,r.useEffect)(()=>{let e=()=>{document.querySelectorAll("[data-active-bg]").forEach(e=>{"active"===e.getAttribute("data-state")?(e.style.setProperty("--state-active",u),e.style.setProperty("--state-active-text",v),e.style.setProperty("--state-active-scale","scale(1.05)"),e.style.setProperty("--state-active-shadow","0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"),e.style.setProperty("--state-active-border",u),e.style.backgroundColor=u,e.style.color=v,e.style.transform="scale(1.05)",e.style.boxShadow="0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",e.style.borderColor=u):(e.style.setProperty("--state-active","rgb(209 213 219)"),e.style.setProperty("--state-active-text","rgb(55 65 81)"),e.style.setProperty("--state-active-scale","scale(1)"),e.style.setProperty("--state-active-shadow","none"),e.style.setProperty("--state-active-border","transparent"),e.style.backgroundColor="rgb(209 213 219)",e.style.color="rgb(55 65 81)",e.style.transform="scale(1)",e.style.boxShadow="none",e.style.borderColor="transparent")})};e();let t=new MutationObserver(e),s=document.querySelector('[role="tablist"]');return s&&t.observe(s,{attributes:!0,subtree:!0,attributeFilter:["data-state"]}),()=>t.disconnect()},[u,v]),(0,r.useEffect)(()=>{console.log("\uD83D\uDD0D Account page auth check:",{isLoading:L,isLoggedIn:F,user:!!O}),L||F||(console.log("\uD83D\uDEA8 Account page: Redirecting to login"),z.push("/login?redirect=/account"))},[L,F,z,O]);let eo=e=>{let{name:t,value:s}=e.target;ea(e=>({...e,[t]:s})),el(""),"gender"===t&&(console.log("\uD83D\uDD0D Account page: Gender changed to:",s),setTimeout(()=>{let e=document.getElementById("gender");e&&(e.value=s,console.log("\uD83D\uDD0D Account page: Gender select updated to:",s))},10))};if(L)return console.log("\uD83D\uDD04 Account page: Showing loading state"),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground",children:"Loading..."})]})});if(!L&&!F)return console.log("\uD83D\uDEA8 Account page: Not logged in, should redirect"),(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground",children:"Redirecting to login..."})]})});let ei=async e=>{if(e.preventDefault(),U(!0),el(""),es.newPassword||es.confirmPassword||es.currentPassword){if(es.newPassword!==es.confirmPassword){el("New password and confirm password do not match"),U(!1);return}if(es.newPassword&&es.newPassword.length<8){el("Password must be at least 8 characters long"),U(!1);return}el("Password update functionality is not yet implemented"),U(!1);return}try{var t,s;let e={requestParameters:{FirstName:es.firstName,LastName:es.lastName,Gender:es.gender||"Male",CategoryId:es.category||"1024"}};console.log("Sending update profile request:",e);let a=await (0,p.MakeApiCallAsync)(p.Config.END_POINT_NAMES.UPDATE_PROFILE,null,e,{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(console.log("Profile update response:",a),(null==a?void 0:a.data)&&!a.data.errorMessage){let e;if(e="string"==typeof a.data.data?JSON.parse(a.data.data):a.data.data,Array.isArray(e)&&e.length>0&&"Saved Successfully"===e[0].ResponseMsg){H(!0);let e={FirstName:es.firstName,LastName:es.lastName,UserName:"".concat(es.firstName," ").concat(es.lastName).trim(),Gender:es.gender,CategoryID:es.category,CategoryId:es.category,SpecialistId:es.category};await R(e);try{(await fetch("/api/auth/update-user-cookies",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({FirstName:es.firstName,LastName:es.lastName,UserName:"".concat(es.firstName," ").concat(es.lastName).trim(),Gender:es.gender,CategoryID:es.category,CategoryId:es.category,SpecialistId:es.category})})).ok&&(console.log("User cookies updated successfully with all profile data"),setTimeout(()=>{let e=document.getElementById("gender");if(e&&(e.value=es.gender),es.category&&q.length>0){let e=q.find(e=>e.CategoryID===parseInt(es.category.toString()));e&&Q(e.Name||e.CategoryName||"")}},100))}catch(e){console.warn("Failed to update user cookies:",e)}_({title:"Success!",description:"Profile updated successfully!"}),setTimeout(()=>H(!1),3e3)}else throw Error((null==e||null==(t=e[0])?void 0:t.ResponseMsg)||"Failed to update profile")}else throw Error((null==a||null==(s=a.data)?void 0:s.errorMessage)||"Failed to update profile")}catch(t){console.error("Profile update error:",t);let e=t instanceof Error?t.message:"Failed to update profile. Please try again.";el(e),_({title:"Error",description:e,variant:"destructive"})}finally{U(!1)}};return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,a.jsx)(o.Qp,{className:"mb-6",children:(0,a.jsxs)(o.AB,{children:[(0,a.jsx)(o.J5,{children:(0,a.jsx)(o.w1,{asChild:!0,children:(0,a.jsx)(g(),{href:"/",children:s("home")})})}),(0,a.jsx)(o.tH,{}),(0,a.jsx)(o.J5,{children:(0,a.jsx)(o.tJ,{children:s("myAccount")})})]})}),(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold mb-6",children:s("myAccount")}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-[250px_1fr] gap-6",children:[(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)(i.Zp,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center text-center mb-6",children:[(0,a.jsxs)("div",{className:"w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center mb-4 relative",children:[(0,a.jsx)(y.A,{className:"h-10 w-10 text-primary"}),(null==O?void 0:O.IsVerified)&&(0,a.jsx)("div",{className:"absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center",children:(0,a.jsx)(N,{className:"h-3 w-3 text-white"})})]}),(0,a.jsxs)("h3",{className:"font-medium",children:[null==O?void 0:O.FirstName," ",null==O?void 0:O.LastName]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:(null==O?void 0:O.Email)||(null==O?void 0:O.EmailAddress)}),(null==O?void 0:O.Pointno)!==void 0&&(0,a.jsxs)("div",{className:"flex items-center gap-1 mt-2 px-2 py-1 bg-yellow-100 rounded-full",children:[(0,a.jsx)(b.A,{className:"h-3 w-3 text-yellow-600"}),(0,a.jsxs)("span",{className:"text-xs font-medium text-yellow-700",children:[O.Pointno," Credit"]})]})]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(n.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(g(),{href:"/account",children:[(0,a.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Profile"]})}),(0,a.jsx)(n.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(g(),{href:"/orders",children:[(0,a.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Orders"]})}),(0,a.jsx)(n.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(g(),{href:"/addresses",children:[(0,a.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"Addresses"]})}),(0,a.jsx)(n.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(g(),{href:"/payment-methods",children:[(0,a.jsx)(A.A,{className:"mr-2 h-4 w-4"}),"Payment Methods"]})}),(0,a.jsx)(n.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,a.jsxs)(g(),{href:"/wishlist",children:[(0,a.jsx)(C.A,{className:"mr-2 h-4 w-4"}),"Wishlist"]})}),(0,a.jsxs)(n.$,{variant:"ghost",className:"w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50",onClick:()=>{J(),_({title:"Logged Out",description:"You have been successfully logged out."}),z.push("/")},children:[(0,a.jsx)(P.A,{className:"mr-2 h-4 w-4"}),"Logout"]})]})]})})}),(0,a.jsx)("div",{children:(0,a.jsxs)(m.tU,{defaultValue:"profile",children:[(0,a.jsxs)(m.j7,{className:"grid w-full grid-cols-3 mb-6 gap-1 sm:gap-2 bg-transparent p-0 h-auto",children:[(0,a.jsx)(m.Xi,{value:"profile",className:"rounded-lg px-1 py-1 sm:px-4 sm:py-2 text-xs sm:text-sm font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"var(--state-active, rgb(209 213 219))",color:"var(--state-active-text, rgb(55 65 81))",transform:"var(--state-active-scale, scale(1))",boxShadow:"var(--state-active-shadow, none)",borderColor:"var(--state-active-border, transparent)"},"data-active-bg":u,"data-active-text":v,children:"Personal Information"}),(0,a.jsx)(m.Xi,{value:"password",className:"rounded-lg px-1 py-1 sm:px-4 sm:py-2 text-xs sm:text-sm font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"var(--state-active, rgb(209 213 219))",color:"var(--state-active-text, rgb(55 65 81))",transform:"var(--state-active-scale, scale(1))",boxShadow:"var(--state-active-shadow, none)",borderColor:"var(--state-active-border, transparent)"},"data-active-bg":u,"data-active-text":v,children:"Security"}),(0,a.jsx)(m.Xi,{value:"overview",className:"rounded-lg px-1 py-1 sm:px-4 sm:py-2 text-xs sm:text-sm font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"var(--state-active, rgb(209 213 219))",color:"var(--state-active-text, rgb(55 65 81))",transform:"var(--state-active-scale, scale(1))",boxShadow:"var(--state-active-shadow, none)",borderColor:"var(--state-active-border, transparent)"},"data-active-bg":u,"data-active-text":v,children:"Account Details"})]}),(0,a.jsx)(m.av,{value:"overview",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,a.jsxs)("div",{className:"p-8",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-6",children:"Account Details"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(i.Zp,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 text-primary"}),"Account Information"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Phone Number"}),(0,a.jsx)("span",{className:"font-medium",children:(null==O?void 0:O.PhoneNo)||(null==O?void 0:O.MobileNo)||(null==O?void 0:O.PhoneNumber)||"Not specified"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Gender"}),(0,a.jsx)("span",{className:"font-medium",children:(null==O?void 0:O.Gender)||"Not specified"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Specialist"}),(0,a.jsx)("span",{className:"font-medium",children:(null==(e=q.find(e=>{var t;return e.CategoryID===parseInt((null==(t=es.category)?void 0:t.toString())||"0")}))?void 0:e.Name)||(null==(t=q.find(e=>e.CategoryID===((null==O?void 0:O.CategoryID)||(null==O?void 0:O.CategoryId)||(null==O?void 0:O.categoryId))))?void 0:t.Name)||"Not specified"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Account Status"}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(null==O?void 0:O.IsActive)===!0||(null==O?void 0:O.IsActive)===1||(null==O?void 0:O.IsActive)==="true"||(null==O?void 0:O.IsActive)==="1"||"string"==typeof(null==O?void 0:O.IsActive)&&(null==O?void 0:O.IsActive.toLowerCase())==="true"?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{className:"text-green-600 font-medium",children:"Active"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,a.jsxs)("span",{className:"text-red-600 font-medium",children:["Inactive (Debug: IsActive=",JSON.stringify(null==O?void 0:O.IsActive),", type=",typeof(null==O?void 0:O.IsActive),")"]})]})})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Member Since"}),(0,a.jsx)("span",{className:"font-medium",children:(null==O?void 0:O.CreatedOn)?new Date(O.CreatedOn).toLocaleDateString():"N/A (Debug: CreatedOn=".concat(JSON.stringify(null==O?void 0:O.CreatedOn),", type=").concat(typeof(null==O?void 0:O.CreatedOn),")")})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-2",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Credit Balance"}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 text-yellow-500"}),(0,a.jsx)("span",{className:"font-medium",children:(null==O?void 0:O.Pointno)||0})]})]})]})]})}),(0,a.jsx)(i.Zp,{children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center gap-2",children:[(0,a.jsx)(k.A,{className:"h-5 w-5 text-primary"}),"Contact Information"]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)(S.A,{className:"h-5 w-5 text-gray-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Email Address"}),(0,a.jsx)("p",{className:"font-medium",children:(null==O?void 0:O.EmailAddress)||(null==O?void 0:O.Email)})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)(k.A,{className:"h-5 w-5 text-gray-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Phone Number"}),(0,a.jsx)("p",{className:"font-medium",children:(null==O?void 0:O.PhoneNo)||(null==O?void 0:O.MobileNo)||(null==O?void 0:O.PhoneNumber)||"Not provided (Debug: PhoneNo="+JSON.stringify(null==O?void 0:O.PhoneNo)+", MobileNo="+JSON.stringify(null==O?void 0:O.MobileNo)+", PhoneNumber="+JSON.stringify(null==O?void 0:O.PhoneNumber)+")"})]})]}),(null==O?void 0:O.MobileNo)&&(null==O?void 0:O.PhoneNo)&&(null==O?void 0:O.MobileNo)!==(null==O?void 0:O.PhoneNo)&&(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[(0,a.jsx)(k.A,{className:"h-5 w-5 text-gray-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Mobile Number"}),(0,a.jsx)("p",{className:"font-medium",children:null==O?void 0:O.MobileNo})]})]})]})]})})]})]})}),(0,a.jsx)(m.av,{value:"profile",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,a.jsxs)("div",{className:"p-8",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-6",children:"Personal Information"}),(0,a.jsx)(i.Zp,{children:(0,a.jsxs)("form",{onSubmit:ei,className:"p-6",children:[$&&(0,a.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6",children:"Profile updated successfully!"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"firstName",children:"First Name"}),(0,a.jsx)(d.p,{id:"firstName",name:"firstName",value:es.firstName,onChange:eo,placeholder:"Enter your first name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"lastName",children:"Last Name"}),(0,a.jsx)(d.p,{id:"lastName",name:"lastName",value:es.lastName,onChange:eo,placeholder:"Enter your last name"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"gender",children:"Gender"}),(0,a.jsxs)("select",{id:"gender",name:"gender",value:es.gender||"",onChange:eo,className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm text-black ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:[(0,a.jsx)("option",{value:"",children:"Select Gender"}),(0,a.jsx)("option",{value:"Male",children:"Male"}),(0,a.jsx)("option",{value:"Female",children:"Female"})]},"gender-select-".concat(es.gender))]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.J,{htmlFor:"category",children:"Specialist"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(d.p,{id:"category",name:"category",value:W,onChange:e=>{Q(e.target.value),K(!0)},onFocus:()=>K(!0),placeholder:"Search and select specialist...",disabled:Z,className:"pr-10"},"category-input-".concat(es.category)),W&&(0,a.jsx)("button",{type:"button",onClick:()=>{Q(""),ea(e=>({...e,category:""})),K(!1)},className:"absolute right-8 top-3 h-4 w-4 text-muted-foreground hover:text-red-500",children:"\xd7"}),(0,a.jsx)(I.A,{className:"absolute right-3 top-3 h-4 w-4 text-muted-foreground"}),Y&&!Z&&(0,a.jsxs)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto",children:[q.filter(e=>e.Name.toLowerCase().includes(W.toLowerCase())).map(e=>{var t;return(0,a.jsxs)("div",{className:"px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm",onClick:()=>{ea(t=>({...t,category:e.CategoryID})),Q(e.Name),K(!1)},children:[(0,a.jsx)("div",{className:"font-medium",children:e.Name}),e.ParentCategoryID&&(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Subcategory of:"," ",null==(t=q.find(t=>t.CategoryID===e.ParentCategoryID))?void 0:t.Name]})]},e.CategoryID)}),0===q.filter(e=>e.Name.toLowerCase().includes(W.toLowerCase())).length&&(0,a.jsx)("div",{className:"px-3 py-2 text-sm text-muted-foreground",children:"No specialists found"})]})]}),Z&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Loading specialists..."}),Y&&(0,a.jsx)("div",{className:"fixed inset-0 z-5",onClick:()=>K(!1)})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"email",children:"Email Address"}),(0,a.jsx)(d.p,{id:"email",name:"email",type:"email",value:es.email,onChange:eo,disabled:!0,className:"bg-gray-50",placeholder:"Email address"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Email address cannot be changed"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"phone",children:"Phone Number"}),(0,a.jsx)(d.p,{id:"phone",name:"phone",value:es.phone,onChange:eo,disabled:!0,className:"bg-gray-50",placeholder:"Phone number"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Phone number cannot be changed"})]})]}),(0,a.jsx)(n.$,{type:"submit",disabled:T,children:T?(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Updating..."]}):(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(M,{className:"h-4 w-4"}),"Save Changes"]})})]})})]})}),(0,a.jsx)(m.av,{value:"password",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,a.jsxs)("div",{className:"p-8",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-6",children:"Security Settings"}),(0,a.jsx)(i.Zp,{children:(0,a.jsxs)("form",{onSubmit:ei,className:"p-6",children:[$&&(0,a.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6",children:"Password updated successfully!"}),(0,a.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"currentPassword",children:"Current Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(d.p,{id:"currentPassword",name:"currentPassword",type:B?"text":"password",value:es.currentPassword,onChange:eo}),(0,a.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>G(!B),children:B?(0,a.jsx)(E.A,{className:"h-4 w-4"}):(0,a.jsx)(D.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"newPassword",children:"New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(d.p,{id:"newPassword",name:"newPassword",type:B?"text":"password",value:es.newPassword,onChange:eo}),(0,a.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>G(!B),children:B?(0,a.jsx)(E.A,{className:"h-4 w-4"}):(0,a.jsx)(D.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(c.J,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(d.p,{id:"confirmPassword",name:"confirmPassword",type:B?"text":"password",value:es.confirmPassword,onChange:eo}),(0,a.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>G(!B),children:B?(0,a.jsx)(E.A,{className:"h-4 w-4"}):(0,a.jsx)(D.A,{className:"h-4 w-4"})})]})]})]}),(0,a.jsx)(n.$,{type:"submit",disabled:T||!es.currentPassword||!es.newPassword||!es.confirmPassword||es.newPassword!==es.confirmPassword,children:T?(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Updating..."]}):(0,a.jsxs)("span",{className:"flex items-center gap-2",children:[(0,a.jsx)(M,{className:"h-4 w-4"}),"Update Password"]})}),er&&(0,a.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6",children:er})]})})]})})]})})]})]})]})}},89852:(e,t,s)=>{"use strict";s.d(t,{p:()=>o});var a=s(95155),r=s(12115),l=s(53999);let o=r.forwardRef((e,t)=>{let{className:s,type:r,...o}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...o})});o.displayName="Input"},92657:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{e.O(0,[4277,3464,4706,3942,5371,8816,2616,8441,5964,7358],()=>e(e.s=44922)),_N_E=e.O()}]);