(()=>{var a={};a.id=879,a.ids=[879],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},12597:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},14719:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},17581:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},25902:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>x});var d=c(60687),e=c(43210),f=c(55192),g=c(24934),h=c(68988),i=c(39390),j=c(97461),k=c.n(j);c(90895);var l=c(97905),m=c(85814),n=c.n(m),o=c(17581),p=c(14719),q=c(58869),r=c(78122),s=c(70334),t=c(41550),u=c(64021),v=c(13861),w=c(12597);function x(){let[a,b]=(0,e.useState)("phone"),[j,m]=(0,e.useState)("964"),[x,y]=(0,e.useState)("iq"),[z,A]=(0,e.useState)(""),[B,C]=(0,e.useState)(null),[D,E]=(0,e.useState)(0),[F,G]=(0,e.useState)(0),[H,I]=(0,e.useState)(!1),[J,K]=(0,e.useState)(!1),[L,M]=(0,e.useState)(""),[N,O]=(0,e.useState)(!1),[P,Q]=(0,e.useState)({firstName:"",lastName:"",email:"",password:"",mobileNo:j,cityId:"-999",stateProvinceId:"-999",countryId:"1"}),R=async a=>{if(a.preventDefault(),K(!0),M(""),!j){M("Phone number is required"),K(!1);return}let d=j.replace(/[^+\d]/g,"");if(d.length<8){M("Phone number must be at least 8 digits"),K(!1);return}if(!/^\+?[1-9]\d{7,14}$/.test(d)){M("Please enter a valid phone number"),K(!1);return}let e=d.startsWith("+")?d:`+${d}`;try{console.log("Step 1: Checking if user already exists with phone number:",e);let{MakeApiCallAsync:a,Config:d}=await Promise.resolve().then(c.bind(c,40529)),f=await a(d.END_POINT_NAMES.GET_USER_BY_PHONE,null,{requestParameters:{PhoneNumber:e}},{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(console.log("User check response:",f),console.log("User check response data:",f.data),console.log("User check response data.data:",f.data?.data),console.log("User check response data.data type:",typeof f.data?.data),f.data&&!f.data.errorMessage&&!f.data.error&&f.data.data&&"[]"!==f.data.data){let a;if("string"==typeof f.data.data)try{a=JSON.parse(f.data.data)}catch(b){console.error("Error parsing user data:",b),a=[]}else a=f.data.data;if(Array.isArray(a)&&a.length>0){M("An account with this phone number already exists. Please use a different phone number or try logging in."),K(!1);return}}console.log("Step 2: User not found, proceeding with SMS verification for signup");let g=await fetch("/api/sms/send-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:e,useWhatsApp:!0})}),h=await g.json();if(g.ok)C({phoneNumber:e}),Q({...P,mobileNo:e}),b("verification"),G(180),I(!1),E(180);else throw Error(h.error||"Failed to send verification code")}catch(b){console.error("Error during phone verification:",b),console.error("Error code:",b.code),console.error("Error message:",b.message);let a="Failed to send verification code. Please try again.";"auth/captcha-check-failed"===b.code?a="reCAPTCHA verification failed. Please refresh the page and try again.":"auth/invalid-phone-number"===b.code?a="Invalid phone number format. Please check and try again.":"auth/too-many-requests"===b.code?a="Too many attempts. Please wait a few minutes before trying again.":"auth/network-request-failed"===b.code?a="Network error. Please check your internet connection and try again.":b.message&&(a=b.message),M(a)}finally{K(!1)}},S=async()=>{if(!(D>0)){K(!0),M("");try{let a=await fetch("/api/sms/send-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:B.phoneNumber})}),b=await a.json();if(a.ok)G(180),I(!1),E(180);else throw Error(b.error||"Failed to resend verification code")}catch(a){M("Failed to resend verification code"),console.error("Error:",a)}finally{K(!1)}}},T=async a=>{if(a.preventDefault(),H||0===F)return void M("Verification code has expired. Please request a new code.");K(!0),M("");try{let a=await fetch("/api/sms/verify-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:B.phoneNumber,code:z})}),c=await a.json();if(a.ok)G(0),I(!1),b("details");else throw Error(c.error||"Invalid verification code")}catch(a){M(a.message||"Invalid verification code"),console.error("Error:",a)}finally{K(!1)}},U=async a=>{if(a.preventDefault(),K(!0),M(""),!P.firstName.trim()){M("First name is required"),K(!1);return}if(!P.lastName.trim()){M("Last name is required"),K(!1);return}if(!P.email.trim()||!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(P.email)){M("Please enter a valid email address"),K(!1);return}if(!P.password||P.password.length<8){M("Password must be at least 8 characters long"),K(!1);return}try{let a={FirstName:P.firstName,LastName:P.lastName,EmailAddress:P.email,Password:P.password,MobileNo:P.mobileNo,CityId:P.cityId,StateProvinceId:P.stateProvinceId,CountryID:P.countryId,AddressLineOne:P.cityId,PostalCode:P.cityId};console.log("Request parameters:",a);let{MakeApiCallAsync:b,Config:d}=await Promise.resolve().then(c.bind(c,40529)),e=await b(d.END_POINT_NAMES.SIGNUP_USER,null,{requestParameters:a},{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(console.log("Create user response:",e),!e.data||e.data.errorMessage||e.data.error){let a=e.data?.errorMessage||e.data?.error||"Failed to create account";M(a)}else{console.log("Account created successfully");let a=(await c.e(7567).then(c.bind(c,77567))).default;await a.fire({title:"Success!",text:"Your account has been created successfully!",icon:"success",confirmButtonText:"Go to Login",confirmButtonColor:"#10b981",allowOutsideClick:!1,allowEscapeKey:!1}),window.location.href="/login"}}catch(a){M(a.message||"Failed to create account"),console.error("Error:",a)}finally{K(!1)}};return(0,d.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("h2",{className:"mt-6 text-3xl font-extrabold",children:["phone"===a&&"Get Started","verification"===a&&"Verify Your Phone","details"===a&&"Complete Your Profile"]}),(0,d.jsxs)("p",{className:"mt-2 text-sm text-muted-foreground",children:["phone"===a&&"Enter your WhatsApp number to create an account","verification"===a&&"Enter the code we sent to your WhatsApp","details"===a&&"Just a few more details to complete your account"]})]}),(0,d.jsxs)(f.Zp,{className:"mt-8 p-8 shadow-xl bg-card/100",children:[(0,d.jsx)("div",{className:"flex justify-center mb-8",children:(0,d.jsxs)("div",{className:"flex items-center",children:[(0,d.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"phone"===a?"bg-primary text-primary-foreground":"bg-primary/20 text-primary"}`,children:(0,d.jsx)(o.A,{className:"w-4 h-4"})}),(0,d.jsx)("div",{className:`w-16 h-1 ${"phone"===a?"bg-primary/20":"bg-primary"}`}),(0,d.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"verification"===a?"bg-primary text-primary-foreground":"details"===a?"bg-primary":"bg-primary/20 text-primary"}`,children:(0,d.jsx)(p.A,{className:"w-4 h-4"})}),(0,d.jsx)("div",{className:`w-16 h-1 ${"details"===a?"bg-primary":"bg-primary/20"}`}),(0,d.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center ${"details"===a?"bg-primary text-primary-foreground":"bg-primary/20 text-primary"}`,children:(0,d.jsx)(q.A,{className:"w-4 h-4"})})]})}),(0,d.jsxs)(l.P.div,{variants:{hidden:{opacity:0,x:-20},visible:{opacity:1,x:0},exit:{opacity:0,x:20}},initial:"hidden",animate:"visible",exit:"exit",transition:{duration:.3},children:["phone"===a&&(0,d.jsxs)("form",{onSubmit:R,className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{className:"block text-sm font-medium mb-2 text-center",children:"Phone Number (WhatsApp)"}),(0,d.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,d.jsx)("div",{className:"w-full max-w-[300px]",children:(0,d.jsx)(k(),{country:x,value:j,onChange:a=>{m(a),M("")},enableSearch:!0,searchPlaceholder:"Search country...",containerClass:"w-full",inputClass:`w-full p-4 border rounded-lg focus:ring-2 focus:ring-primary/50 ${L?"border-destructive":""}`,buttonClass:"!border-input !bg-background hover:!bg-accent",dropdownClass:"!bg-background !border-input",disabled:J,countryCodeEditable:!1,isValid:(a,b)=>!!a&&!(a.length<8)&&!!/^\+?[1-9]\d{1,14}$/.test(a)})}),L&&(0,d.jsx)("p",{className:"text-sm text-destructive",children:L})]})]}),(0,d.jsx)(g.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2",disabled:J,children:J?(0,d.jsx)(r.A,{className:"w-4 h-4 animate-spin"}):(0,d.jsxs)(d.Fragment,{children:["Continue ",(0,d.jsx)(s.A,{className:"w-4 h-4"})]})}),(0,d.jsxs)("div",{className:"text-center text-sm mt-4",children:[(0,d.jsx)("span",{className:"text-muted-foreground",children:"Already have an account? "}),(0,d.jsx)(n(),{href:"/login",className:"text-primary hover:text-primary/80 hover:underline transition-colors",children:"Login"})]})]}),"verification"===a&&(0,d.jsxs)("form",{onSubmit:T,className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{className:"block text-sm font-medium mb-4 text-center",children:"Verification Code"}),H&&(0,d.jsx)("p",{className:"text-sm text-red-600 mb-4 font-medium text-center",children:"Verification code has expired. Please request a new code."}),(0,d.jsx)("div",{className:"flex justify-center items-center gap-3",children:[...Array(6)].map((a,b)=>(0,d.jsx)(h.p,{type:"number",maxLength:1,className:"w-10 h-10 sm:w-12 sm:h-12 text-center text-lg sm:text-2xl font-semibold rounded-lg focus:ring-2 focus:ring-primary/50 transition-all",value:z[b]||"",onChange:a=>{let c=z.split("");c[b]=a.target.value,A(c.join("")),a.target.value&&a.target.nextElementSibling&&a.target.nextElementSibling.focus()},onPaste:a=>{if(0===b){a.preventDefault();let b=a.clipboardData.getData("text").replace(/\D/g,"").slice(0,6);if(b){A(b.padEnd(6,""));let c=Math.min(b.length-1,5),d=a.currentTarget.parentElement?.children[c];d&&setTimeout(()=>d.focus(),0)}}},onKeyDown:a=>{if("Backspace"===a.key&&!z[b]&&b>0){let c=z.split("");c[b-1]="",A(c.join(""));let d=a.currentTarget.previousElementSibling;d&&d.focus()}else if("Backspace"===a.key&&z[b]){let a=z.split("");a[b]="",A(a.join(""))}},disabled:J},b))}),(0,d.jsx)("div",{className:"mt-4 text-center",children:(0,d.jsxs)("div",{className:"flex justify-center items-center gap-4",children:[F>0&&(0,d.jsxs)("div",{className:"text-sm text-orange-600 font-medium",children:["Timer: ",Math.floor(F/60),":",(F%60).toString().padStart(2,"0")]}),(0,d.jsx)("button",{type:"button",onClick:S,className:`text-sm ${F>0?"text-muted-foreground":"text-primary hover:underline"}`,disabled:F>0||J,children:"Resend code"})]})})]}),(0,d.jsx)(g.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2",disabled:J||6!==z.length,children:J?(0,d.jsx)(r.A,{className:"w-4 h-4 animate-spin"}):(0,d.jsxs)(d.Fragment,{children:["Verify ",(0,d.jsx)(p.A,{className:"w-4 h-4"})]})})]}),"details"===a&&(0,d.jsxs)("form",{onSubmit:U,className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{className:"block text-sm font-medium mb-2",children:"First Name"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(h.p,{type:"text",value:P.firstName,onChange:a=>Q({...P,firstName:a.target.value}),className:"pl-10",required:!0,disabled:J}),(0,d.jsx)(q.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{className:"block text-sm font-medium mb-2",children:"Last Name"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(h.p,{type:"text",value:P.lastName,onChange:a=>Q({...P,lastName:a.target.value}),className:"pl-10",required:!0,disabled:J}),(0,d.jsx)(q.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{className:"block text-sm font-medium mb-2",children:"Email"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(h.p,{type:"email",value:P.email,onChange:a=>Q({...P,email:a.target.value}),className:"pl-10",required:!0,disabled:J}),(0,d.jsx)(t.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{className:"block text-sm font-medium mb-2",children:"Password"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(h.p,{type:N?"text":"password",value:P.password,onChange:a=>Q({...P,password:a.target.value}),className:"pl-10 pr-10",required:!0,minLength:8,disabled:J}),(0,d.jsx)(u.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"}),(0,d.jsxs)(g.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 hover:bg-transparent",onClick:()=>O(!N),disabled:J,children:[N?(0,d.jsx)(v.A,{className:"w-4 h-4 text-muted-foreground"}):(0,d.jsx)(w.A,{className:"w-4 h-4 text-muted-foreground"}),(0,d.jsx)("span",{className:"sr-only",children:N?"Hide password":"Show password"})]})]})]}),(0,d.jsx)(g.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors",disabled:J,children:J?(0,d.jsx)(r.A,{className:"w-4 h-4 animate-spin"}):"Create Account"})]})]},a)]})]})})}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30979:(a,b,c)=>{Promise.resolve().then(c.bind(c,25902))},31659:(a,b,c)=>{Promise.resolve().then(c.bind(c,46088))},33873:a=>{"use strict";a.exports=require("path")},39390:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(96241);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46088:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\signup\\page.tsx","default")},55192:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h,wL:()=>l});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b}));l.displayName="CardFooter"},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},68988:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"},70334:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},74075:a=>{"use strict";a.exports=require("zlib")},75773:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,46088)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\signup\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\signup\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/signup/page",pathname:"/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/signup/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},78122:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,1697,4773,3338,9822],()=>b(b.s=75773));module.exports=c})();