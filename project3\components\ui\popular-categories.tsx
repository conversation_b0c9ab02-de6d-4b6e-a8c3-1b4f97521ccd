"use client";

import { useEffect, useState, useRef } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface Category {
  id: number;
  title: string;
  image: string;
  parentId?: number;
  parentName?: string;
}

export default function PopularCategories() {
  // Replace the settings context with a simple translation function
  const t = (key: string) => {
    const translations: Record<string, string> = {
      popularCategories: "Popular Categories",
      // Add more translations as needed
    };
    return translations[key] || key;
  };

  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const carouselRef = useRef<HTMLDivElement>(null);

  const [autoplay, setAutoplay] = useState(true);
  // Slower autoplay speed for desktop (6 seconds), faster for mobile (4 seconds)
  const getAutoplaySpeed = () => {
    if (typeof window !== "undefined") {
      return window.innerWidth >= 768 ? 6000 : 4000; // 6s for desktop, 4s for mobile
    }
    return 6000; // Default for SSR
  };
  const [autoplaySpeed] = useState(getAutoplaySpeed());
  const autoplayTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Number of items to show per page based on screen size
  const itemsPerPage = {
    sm: 6, // Small screens - Show 6 items (3 per row)
    md: 6, // Medium screens - Show 6 items (2 per row)
    lg: 8, // Large screens - Show 8 items (4 per row)
    xl: 10, // Extra large screens - Show 10 items (5 per row)
  };

  // Calculate total pages based on number of categories and items per page
  // Use a responsive approach to determine items per page
  const getItemsPerPage = () => {
    // This is a client-side calculation, so we need to check if window is defined
    if (typeof window !== "undefined") {
      const width = window.innerWidth;
      if (width >= 1280) return itemsPerPage.xl; // xl screens
      if (width >= 1024) return itemsPerPage.lg; // lg screens
      if (width >= 768) return itemsPerPage.md; // md screens
      return itemsPerPage.sm; // sm screens
    }
    return itemsPerPage.lg; // Default for SSR
  };

  const currentItemsPerPage = getItemsPerPage();
  const totalPages = Math.ceil(categories.length / currentItemsPerPage);

  // Navigation functions for the carousel
  const nextPage = () => {
    if (currentPage < totalPages - 1) {
      setCurrentPage(currentPage + 1);
    } else {
      // Loop back to the first page
      setCurrentPage(0);
    }
  };

  const prevPage = () => {
    if (currentPage > 0) {
      setCurrentPage(currentPage - 1);
    } else {
      // Loop to the last page
      setCurrentPage(totalPages - 1);
    }
  };

  const startAutoplay = () => {
    if (autoplayTimerRef.current) {
      clearInterval(autoplayTimerRef.current);
    }

    autoplayTimerRef.current = setInterval(() => {
      nextPage();
    }, autoplaySpeed);
  };

  const stopAutoplay = () => {
    if (autoplayTimerRef.current) {
      clearInterval(autoplayTimerRef.current);
      autoplayTimerRef.current = null;
    }
  };

  const handleInteraction = () => {
    // Stop autoplay on user interaction
    stopAutoplay();

    // Resume autoplay after 10 seconds of inactivity
    setTimeout(() => {
      if (autoplay) {
        startAutoplay();
      }
    }, 10000);
  };

  const constructImageUrl = (attachmentUrl: string | null) => {
    if (!attachmentUrl)
      return `${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}images/no-image.jpg`;

    // Check if the URL already has the full domain
    if (attachmentUrl.startsWith("http")) {
      return attachmentUrl;
    }

    // Base URL from environment variable
    const baseUrl = process.env.NEXT_PUBLIC_ADMIN_BASE_URL || "";

    // Normalize path (ensure it starts with exactly one slash)
    let normalizedPath = attachmentUrl.startsWith("/")
      ? attachmentUrl
      : `/${attachmentUrl}`;
    // Remove any double slashes in the path
    normalizedPath = normalizedPath.replace(/\/+/g, '/');

    // Construct the full URL
    return `${baseUrl}${normalizedPath}`;
  };

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);

        // Direct axios implementation instead of using MakeApiCallAsync
        const axios = (await import("axios")).default;

        const param = {
          requestParameters: {
            recordValueJson: "[]",
          },
        };

        const config = {
          method: "post",
          url: `${process.env.NEXT_PUBLIC_ADMIN_BASE_URL}api/v1/dynamic/dataoperation/get-popular-categories`,
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          data: param,
        };

        // Make the API call
        const response = await axios(config);

        // Process the response
        if (response?.data?.data) {
          try {
            const parsedData = JSON.parse(response.data.data);

            if (Array.isArray(parsedData)) {
              // Map the data to our category format and sort alphabetically
              const popularCategories = parsedData
                .map((item) => ({
                  id: item.CategoryID,
                  title: item.Name,
                  image: constructImageUrl(item.AttachmentURL),
                  parentId: item.ParentCategoryID || undefined,
                  parentName: item.ParentCategoryName || undefined,
                }))
                .sort((a, b) => a.title.localeCompare(b.title)); // Sort A to Z

              setCategories(popularCategories);
            } else {
              console.error("Categories data is not an array:", parsedData);
              setCategories([]);
            }
          } catch (parseError) {
            console.error("Error parsing data:", parseError);
            setCategories([]);
          }
        } else {
          console.error("No data returned from API");
          setCategories([]);
        }
      } catch (error) {
        console.error("Error fetching categories:", error);
        setCategories([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Optimized window resize handler with debounce
  useEffect(() => {
    let resizeTimer: NodeJS.Timeout;

    const handleResize = () => {
      // Debounce the resize handler to prevent excessive recalculations
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(() => {
        const newItemsPerPage = getItemsPerPage();
        const newTotalPages = Math.ceil(categories.length / newItemsPerPage);

        // Only update state if the page needs to change
        if (currentPage >= newTotalPages) {
          setCurrentPage(Math.max(0, newTotalPages - 1));
        }
      }, 100); // 100ms debounce time
    };

    window.addEventListener("resize", handleResize);
    return () => {
      clearTimeout(resizeTimer);
      window.removeEventListener("resize", handleResize);
    };
  }, [categories.length, currentPage]);

  // Optimized autoplay effect with proper cleanup
  useEffect(() => {
    let autoplayTimer: NodeJS.Timeout;

    const startAutoplay = () => {
      stopAutoplay();
      autoplayTimer = setInterval(() => {
        setCurrentPage((prev) => (prev < totalPages - 1 ? prev + 1 : 0));
      }, autoplaySpeed);
    };

    const stopAutoplay = () => {
      if (autoplayTimer) {
        clearInterval(autoplayTimer);
      }
    };

    if (autoplay && totalPages > 1) {
      startAutoplay();
    }

    return () => {
      stopAutoplay();
    };
  }, [autoplay, autoplaySpeed, totalPages]);

  if (loading) {
    return (
      <section className="py-8">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-6">{t("popularCategories")}</h2>
          <div className="w-full flex justify-center items-center py-12">
            <div className="animate-pulse text-lg">
              Loading popular categories...
            </div>
          </div>
        </div>
      </section>
    );
  }

  if (!categories.length) {
    return (
      <section className="py-8">
        <div className="container mx-auto px-4">
          <h2 className="text-2xl font-bold mb-6">{t("popularCategories")}</h2>
          <div className="w-full flex justify-center items-center py-12">
            <div className="text-lg text-gray-500">
              No categories available at the moment. Please check back later.
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-8">
      <div className="container mx-auto px-4">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-center">
            {t("popularCategories")}
          </h2>
        </div>

        <div
          className="w-full relative overflow-hidden"
          onMouseEnter={() => stopAutoplay()}
          onMouseLeave={() => autoplay && startAutoplay()}
        >
          {/* Navigation arrows - hidden on mobile, visible on desktop */}
          <button
            onClick={() => {
              prevPage();
              handleInteraction();
            }}
            disabled={totalPages <= 1}
            className="hidden sm:block absolute left-2 top-1/2 -translate-y-1/2 z-10 p-3 rounded-full bg-white shadow-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:scale-105"
            aria-label="Previous categories"
          >
            <ChevronLeft className="h-6 w-6 text-gray-600" />
          </button>
          <button
            onClick={() => {
              nextPage();
              handleInteraction();
            }}
            disabled={totalPages <= 1}
            className="hidden sm:block absolute right-2 top-1/2 -translate-y-1/2 z-10 p-3 rounded-full bg-white shadow-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:scale-105"
            aria-label="Next categories"
          >
            <ChevronRight className="h-6 w-6 text-gray-600" />
          </button>
          <div
            ref={carouselRef}
            className="w-full transition-transform duration-700 ease-in-out md:duration-1000"
            style={{
              transform: `translateX(-${currentPage * 100}%)`,
            }}
          >
            <div
              className="flex flex-nowrap"
              style={{ width: `${totalPages * 100}%` }}
            >
              {/* Show all categories in groups */}
              {Array.from({ length: totalPages }).map((_, pageIndex) => (
                <div
                  key={`page-${pageIndex}`}
                  className="w-full flex-shrink-0 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 sm:gap-6 md:gap-8"
                  style={{ width: `${100 / totalPages}%` }}
                >
                  {categories
                    .slice(
                      pageIndex * currentItemsPerPage,
                      (pageIndex + 1) * currentItemsPerPage
                    )
                    .map((category) => (
                      <div
                        key={category.id}
                        className="flex flex-col items-center px-2"
                      >
                        <a
                          href={`/products?category=${category.id}`}
                          className="group flex flex-col items-center cursor-pointer transition-all duration-300 hover:scale-105"
                        >
                          <div className="w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-32 lg:h-32 xl:w-36 xl:h-36 mb-3 relative">
                            <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full p-1 group-hover:shadow-lg transition-all duration-300">
                              <div className="w-full h-full rounded-full overflow-hidden border-2 border-white bg-white">
                                <img
                                  src={
                                    category.image ||
                                    "/placeholder.svg?height=150&width=150"
                                  }
                                  alt={category.title}
                                  width={144}
                                  height={144}
                                  className="w-full h-full object-cover"
                                  onError={(e) => {
                                    const target = e.target as HTMLImageElement;
                                    console.error(
                                      "Image load error for:",
                                      category.image
                                    );

                                    if (
                                      category.image !==
                                        "https://admin.codemedicalapps.com/images/no-image.jpg" &&
                                      !category.image.includes("no-image")
                                    ) {
                                      const baseUrl =
                                        "https://admin.codemedicalapps.com";
                                      const directUrl = `${baseUrl}/images/no-image.jpg`;
                                      console.log(
                                        "Trying fallback URL:",
                                        directUrl
                                      );
                                      target.src = directUrl;

                                      target.onerror = () => {
                                        console.error(
                                          "Fallback URL also failed, using simple placeholder"
                                        );
                                        target.src =
                                          "/placeholder.svg?height=150&width=150";
                                        target.onerror = null;
                                      };
                                    } else {
                                      target.src =
                                        "/placeholder.svg?height=150&width=150";
                                    }
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                          <div className="text-center">
                            <h3 className="text-xs sm:text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2">
                              {category.title}
                            </h3>
                            {category.parentName && (
                              <p className="text-xs text-gray-500 mt-1">
                                {category.parentName}
                              </p>
                            )}
                          </div>
                        </a>
                      </div>
                    ))}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Pagination indicators */}
        <div className="flex justify-center mt-4 space-x-2">
          {Array.from({ length: totalPages }).map((_, index) => (
            <button
              key={`indicator-${index}`}
              className={`h-2 rounded-full transition-all ${
                currentPage === index ? "w-6 bg-primary" : "w-2 bg-gray-300"
              }`}
              onClick={() => {
                setCurrentPage(index);
                handleInteraction();
              }}
              aria-label={`Go to page ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
