(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8455],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>n});var s=r(12115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}function i(...e){return s.useCallback(n(...e),e)}},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(12115);let a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,s.forwardRef)((e,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:u,...m}=e;return(0,s.createElement)("svg",{ref:t,...n,width:i,height:i,stroke:r,strokeWidth:o?24*Number(l)/Number(i):l,className:a("lucide",d),...m},[...u.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),l=(e,t)=>{let r=(0,s.forwardRef)((r,n)=>{let{className:l,...o}=r;return(0,s.createElement)(i,{ref:n,iconNode:t,className:a("lucide-".concat(e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),l),...o})});return r.displayName="".concat(e),r}},27809:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},40646:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(52596),a=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},54416:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},73339:(e,t,r)=>{"use strict";r.d(t,{S:()=>c,k:()=>d});var s=r(95155),a=r(56671),n=r(40646),i=r(27809),l=r(54416),o=r(97168);let d=e=>{let{productName:t,quantity:r,productImage:d,onViewCart:c}=e;return a.oR.custom(e=>(0,s.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-md w-full",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)(n.A,{className:"w-5 h-5 text-green-600"})})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 mb-1",children:"Added to cart"}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 line-clamp-2",children:[r," \xd7 ",t]})]}),d&&(0,s.jsx)("div",{className:"flex-shrink-0 ml-3",children:(0,s.jsx)("img",{src:(e=>{if(!e)return"/placeholder.svg";if(e.startsWith("http://")||e.startsWith("https://")||e.startsWith("/placeholder")||e.startsWith("/images/")||e.startsWith("/assets/"))return e;let t="https://admin.codemedicalapps.com/".replace(/\/$/,""),r=e.startsWith("/")?e:"/".concat(e);return r=r.replace(/\/+/g,"/"),"".concat(t).concat(r)})(d),alt:t,className:"w-12 h-12 rounded-md object-cover border border-gray-200",onError:e=>{let t=e.target;t.onerror=null,t.src="/placeholder.svg"}})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-3",children:[(0,s.jsxs)(o.$,{size:"sm",variant:"outline",onClick:()=>{a.oR.dismiss(e),null==c||c()},className:"h-8 text-xs",children:[(0,s.jsx)(i.A,{className:"w-3 h-3 mr-1"}),"View Cart"]}),(0,s.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>a.oR.dismiss(e),className:"h-8 text-xs text-gray-500 hover:text-gray-700",children:"Continue Shopping"})]})]}),(0,s.jsx)("button",{onClick:()=>a.oR.dismiss(e),className:"flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)(l.A,{className:"w-4 h-4"})})]})}),{duration:5e3,position:"top-right"})},c=e=>{let{productName:t,quantity:r}=e;return a.oR.success((0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.A,{className:"w-4 h-4 text-green-600"}),(0,s.jsxs)("span",{children:[r," \xd7 ",t," added to cart"]})]}),{duration:3e3})}},77772:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(95155),a=r(97168),n=r(88482),i=r(73339);function l(){return(0,s.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Modern Toast Demo"}),(0,s.jsxs)(n.Zp,{className:"p-6 space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Modern Add to Cart Toast"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"This is the new modern toast that shows when a product is added to cart. It includes the product image, quantity, and action buttons."}),(0,s.jsx)(a.$,{onClick:()=>{(0,i.k)({productName:"Rutherford's Vascular Surgery and Endovascular Therapy",quantity:1,productImage:"/uploads/products/book1.jpg",onViewCart:()=>{alert("Navigate to cart")}})},children:"Show Modern Toast (5 seconds)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Quick Add to Cart Toast"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"This is a simpler version that shows for 3 seconds instead of 2."}),(0,s.jsx)(a.$,{variant:"outline",onClick:()=>{(0,i.S)({productName:"Rutherford's Vascular Surgery and Endovascular Therapy",quantity:1})},children:"Show Quick Toast (3 seconds)"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Multiple Products"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"Test with different quantities and product names."}),(0,s.jsxs)("div",{className:"flex gap-2 flex-wrap",children:[(0,s.jsx)(a.$,{size:"sm",onClick:()=>{(0,i.k)({productName:"Medical Textbook Volume 1",quantity:2,productImage:"/placeholder.svg",onViewCart:()=>{alert("Navigate to cart")}})},children:"2x Medical Book"}),(0,s.jsx)(a.$,{size:"sm",onClick:()=>{(0,i.k)({productName:"Surgical Instruments Set",quantity:5,productImage:"/placeholder.svg",onViewCart:()=>{alert("Navigate to cart")}})},children:"5x Instruments"})]})]})]}),(0,s.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"What's New:"}),(0,s.jsxs)("ul",{className:"text-blue-800 space-y-1 text-sm",children:[(0,s.jsx)("li",{children:"• Modern design with product image"}),(0,s.jsx)("li",{children:"• Shows for 5 seconds instead of 2 seconds"}),(0,s.jsx)("li",{children:'• Includes "View Cart" and "Continue Shopping" buttons'}),(0,s.jsx)("li",{children:"• Better visual feedback with success icon"}),(0,s.jsx)("li",{children:"• Positioned at top-right for better visibility"}),(0,s.jsx)("li",{children:"• Can be dismissed manually with close button"})]})]})]})})}},85243:(e,t,r)=>{Promise.resolve().then(r.bind(r,77772))},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>l,wL:()=>u});var s=r(95155),a=r(12115),n=r(53999);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});i.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});l.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});o.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});c.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>o});var s=r(95155),a=r(12115),n=r(99708),i=r(74466),l=r(53999);let o=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,asChild:d=!1,...c}=e,u=d?n.DX:"button";return(0,s.jsx)(u,{className:(0,l.cn)(o({variant:a,size:i,className:r})),ref:t,...c})});d.displayName="Button"},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>d,TL:()=>i});var s=r(12115),a=r(6101),n=r(95155);function i(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){var i;let e,l,o=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{let t=n(...e);return a(...e),t}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==s.Fragment&&(d.ref=t?(0,a.t)(t,o):o),s.cloneElement(r,d)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:a,...i}=e,l=s.Children.toArray(a),o=l.find(c);if(o){let e=o.props.children,a=l.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...i,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(t,{...i,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),o=Symbol("radix.slottable");function d(e){let t=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{e.O(0,[4277,6774,8441,5964,7358],()=>e(e.s=85243)),_N_E=e.O()}]);