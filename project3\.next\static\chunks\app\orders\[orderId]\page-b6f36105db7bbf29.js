(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4560],{1497:(e,t,s)=>{Promise.resolve().then(s.bind(s,98760))},5623:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},13052:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19420:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},28883:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},35169:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,s)=>{"use strict";var r=s(18999);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},50477:()=>{},54807:(e,t,s)=>{"use strict";s.d(t,{DW:()=>d,PA:()=>c,Rb:()=>n,pN:()=>o});var r=s(32109),a=s.n(r);let l=s(49509).env.NEXT_PUBLIC_ENCRYPTION_KEY||"your-secret-key-change-this-in-production";function n(e){try{let t=e.toString();return a().AES.encrypt(t,l).toString().replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}catch(t){return console.error("Encryption error:",t),e.toString()}}function o(e){try{let t=e.replace(/-/g,"+").replace(/_/g,"/");for(;t.length%4;)t+="=";let s=a().AES.decrypt(t,l).toString(a().enc.Utf8);if(!s)throw Error("Failed to decrypt value");return s}catch(t){return console.error("Decryption error:",t),e}}function c(e){return n(e)}function d(e){return o(e)}},70306:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]])},79891:(e,t,s)=>{"use strict";s.d(t,{Z:()=>c,t:()=>d});var r=s(95155),a=s(12115);let l={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us",searchProducts:"Search products..."},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",searchProducts:"ابحث عن المنتجات...",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}};var n=s(94213);let o=(0,a.createContext)(void 0);function c(e){let{children:t}=e,[s,c]=(0,a.useState)("light"),[d,i]=(0,a.useState)("en"),[m,u]=(0,a.useState)("#0074b2"),[h,x]=(0,a.useState)("#ffffff");return(0,a.useEffect)(()=>{let e=(0,n.N)(m);x(e),document.documentElement.style.setProperty("--primary",m),document.documentElement.style.setProperty("--primary-foreground",e)},[m]),(0,r.jsx)(o.Provider,{value:{theme:s,language:d,primaryColor:m,primaryTextColor:h,toggleTheme:()=>{c("light"===s?"dark":"light")},setLanguage:e=>{i(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{u(e);let t=(0,n.N)(e);x(t),document.documentElement.style.setProperty("--primary",e),document.documentElement.style.setProperty("--primary-foreground",t)},t:e=>(function(e,t){let s=l[t];return e in s?s[e]:"en"!==t&&e in l.en?l.en[e]:e})(e,d)},children:t})}function d(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},81586:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},84995:(e,t,s)=>{"use strict";s.d(t,{AB:()=>d,J5:()=>i,Qp:()=>c,tH:()=>h,tJ:()=>u,w1:()=>m});var r=s(95155),a=s(12115),l=s(99708),n=s(13052),o=(s(5623),s(53999));let c=a.forwardRef((e,t)=>{let{...s}=e;return(0,r.jsx)("nav",{ref:t,"aria-label":"breadcrumb",...s})});c.displayName="Breadcrumb";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("ol",{ref:t,className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",s),...a})});d.displayName="BreadcrumbList";let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("li",{ref:t,className:(0,o.cn)("inline-flex items-center gap-1.5",s),...a})});i.displayName="BreadcrumbItem";let m=a.forwardRef((e,t)=>{let{asChild:s,className:a,...n}=e,c=s?l.DX:"a";return(0,r.jsx)(c,{ref:t,className:(0,o.cn)("transition-colors hover:text-foreground",a),...n})});m.displayName="BreadcrumbLink";let u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,o.cn)("font-normal text-foreground",s),...a})});u.displayName="BreadcrumbPage";let h=e=>{let{children:t,className:s,...a}=e;return(0,r.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,o.cn)("[&>svg]:size-3.5",s),...a,children:null!=t?t:(0,r.jsx)(n.A,{})})};h.displayName="BreadcrumbSeparator"},94213:(e,t,s)=>{"use strict";function r(e,t){let s=e=>{let t=e.replace("#",""),s=parseInt(t.slice(0,2),16)/255,r=[s,parseInt(t.slice(2,4),16)/255,parseInt(t.slice(4,6),16)/255].map(e=>e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4));return .2126*r[0]+.7152*r[1]+.0722*r[2]},r=s(e),a=s(t);return(Math.max(r,a)+.05)/(Math.min(r,a)+.05)}function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"AA",s=r(e,"#ffffff"),a=r(e,"#000000"),l="AAA"===t?7:4.5;return s>=l&&a>=l?s>a?"#ffffff":"#000000":s>=l?"#ffffff":a>=l?"#000000":s>a?"#ffffff":"#000000"}s.d(t,{N:()=>a})},98760:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>I});var r=s(95155),a=s(12115),l=s(35695),n=s(84995),o=s(88482),c=s(97168),d=s(27737),i=s(6874),m=s.n(i),u=s(79891),h=s(98816),x=s(70306),g=s(35169);let p=(0,s(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var f=s(71007),y=s(28883),N=s(19420),j=s(81586),v=s(38564),b=s(37108),w=s(69026),A=s(54807);function I(){let e,t=(0,l.useParams)();(0,l.useRouter)();let s=(0,l.useSearchParams)(),{t:i}=(0,u.t)(),{user:I,isLoggedIn:P,isLoading:S,token:k}=(0,h.J)(),[C,D]=(0,a.useState)([]),[O,E]=(0,a.useState)(!0),[U,L]=(0,a.useState)(null),M=t.orderId,T=null,R=null;try{e=(0,A.DW)(M);let t=s.get("t"),r=s.get("d");if(t)try{T=(0,A.pN)(t),console.log("\uD83D\uDD10 Decrypted order total:",T)}catch(e){console.warn("⚠️ Failed to decrypt order total:",e)}if(r)try{R=(0,A.pN)(r),console.log("\uD83D\uDD10 Decrypted order date:",R)}catch(e){console.warn("⚠️ Failed to decrypt order date:",e)}console.log("\uD83D\uDD10 Order ID Decryption:",{encrypted:M,decrypted:e,isValidNumber:!isNaN(parseInt(e)),hasQuickTotal:!!T,hasQuickDate:!!R}),e===M&&(M.includes("-")||M.includes("_"))&&console.warn("⚠️ Decryption may have failed, encrypted and decrypted values are the same")}catch(t){console.error("❌ Order ID decryption failed:",t),e=M}let F=async()=>{console.log("fetchOrderDetails called - Starting API request for order:",e);let t=parseInt(e);if(isNaN(t)||t<=0){console.error("❌ Invalid order ID after decryption:",e),E(!1);return}E(!0);try{console.log("Making API call to /api/orders/details with OrderId:",t);let e={"Content-Type":"application/json",Accept:"application/json"};k&&(e.Authorization="Bearer ".concat(k),console.log("\uD83D\uDD10 Added JWT token to order details request"));let s=await fetch("/api/orders/details",{method:"POST",headers:e,body:JSON.stringify({requestParameters:{OrderId:t,recordValueJson:"[]"}})}),r=await s.json();if(console.log("Order details response:",r),r&&r.data){let e;e="string"==typeof r.data?JSON.parse(r.data):r.data,console.log("Parsed order details:",e),Array.isArray(e)&&e.length>0?(console.log("\uD83D\uDD0D Order Details: Available fields in first order:",Object.keys(e[0])),console.log("\uD83D\uDD0D Order Details: Status-related fields:",{StatusID:e[0].StatusID,OrderStatusId:e[0].OrderStatusId,StateId:e[0].StateId,LatestStatusID:e[0].LatestStatusID,LatestStatusId:e[0].LatestStatusId,LatestStatusName:e[0].LatestStatusName,StatusName:e[0].StatusName}),D(e)):(console.log("No order details found or invalid data structure"),D([]))}else console.error("Invalid response structure:",r),D([])}catch(e){console.error("Error fetching order details:",e),D([])}finally{E(!1)}};if((0,a.useEffect)(()=>{let t=(null==I?void 0:I.UserID)||(null==I?void 0:I.UserId);console.log("Order details page - User context:",{isLoggedIn:P,user:I,userID:null==I?void 0:I.UserID,userId:null==I?void 0:I.UserId,finalUserId:t,orderId:e}),P&&t&&e?(console.log("Order details page - Fetching order details for UserID:",t,"OrderID:",e),F()):console.log("Order details page - Not fetching details. isLoggedIn:",P,"userId:",t,"orderId:",e)},[P,I,e]),S)return(0,r.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,r.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)(o.Zp,{children:(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsx)(d.E,{className:"h-8 w-64"}),(0,r.jsx)(d.E,{className:"h-4 w-32"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(d.E,{className:"h-20"}),(0,r.jsx)(d.E,{className:"h-20"})]})]})})})})});if(!P)return(0,r.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,r.jsx)(o.Zp,{className:"max-w-md mx-auto",children:(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(x.A,{className:"h-8 w-8 text-primary"})}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Login Required"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please log in to view order details"}),(0,r.jsx)(c.$,{asChild:!0,className:"w-full",children:(0,r.jsx)(m(),{href:"/login?redirect=/orders/".concat(t.orderId),children:"Login to Continue"})})]})})});let B=C[0]||{},_=B.StatusID||B.OrderStatusId||B.StateId||B.LatestStatusID||B.LatestStatusId||1,J=3===parseInt(_.toString()),H=B.LatestStatusName||B.StatusName||null;console.log("\uD83D\uDD0D Order Details: Status debugging",{orderInfo:B,statusId:_,statusName:H,availableFields:Object.keys(B)});let z=()=>{L(null)};return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,r.jsx)(n.Qp,{className:"mb-6",children:(0,r.jsxs)(n.AB,{children:[(0,r.jsx)(n.J5,{children:(0,r.jsx)(n.w1,{asChild:!0,children:(0,r.jsx)(m(),{href:"/",children:"Home"})})}),(0,r.jsx)(n.tH,{}),(0,r.jsx)(n.J5,{children:(0,r.jsx)(n.w1,{asChild:!0,children:(0,r.jsx)(m(),{href:"/orders",children:"Orders"})})}),(0,r.jsx)(n.tH,{}),(0,r.jsx)(n.J5,{children:(0,r.jsxs)(n.tJ,{children:["Order #",e]})})]})}),(0,r.jsx)(c.$,{variant:"outline",className:"mb-6",asChild:!0,children:(0,r.jsxs)(m(),{href:"/orders",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Back to Orders"]})}),(0,r.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Order Details"}),O?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(o.Zp,{children:(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsx)(d.E,{className:"h-8 w-64"}),(0,r.jsx)(d.E,{className:"h-4 w-32"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(d.E,{className:"h-20"}),(0,r.jsx)(d.E,{className:"h-20"})]})]})}),(0,r.jsx)(o.Zp,{children:(0,r.jsxs)("div",{className:"p-6 space-y-4",children:[(0,r.jsx)(d.E,{className:"h-6 w-32"}),Array.from({length:3}).map((e,t)=>(0,r.jsx)(d.E,{className:"h-16"},t))]})})]}):C.length>0?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)(o.Zp,{children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h2",{className:"text-2xl font-bold",children:["Order #",B.OrderNumber||B.OrderID||e]}),(0,r.jsxs)("p",{className:"text-muted-foreground flex items-center gap-2 mt-1",children:[(0,r.jsx)(p,{className:"h-4 w-4"}),"Placed on ",O?R||"Loading...":B.OrderDate||B.CreatedOn||R||"N/A"]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"text-2xl font-bold",children:["$",O?T?"".concat(T):"Loading...":B.OrderTotal||B.TotalAmount||T||"0.00"]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:O?"Loading items...":"".concat(C.length," item(s)")})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-semibold mb-3 flex items-center gap-2",children:[(0,r.jsx)(f.A,{className:"h-4 w-4"}),"Customer Information"]}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Name:"})," ",null==I?void 0:I.FirstName," ",null==I?void 0:I.LastName]}),(0,r.jsxs)("p",{className:"flex items-center gap-2",children:[(0,r.jsx)(y.A,{className:"h-3 w-3"}),(null==I?void 0:I.Email)||(null==I?void 0:I.EmailAddress)]}),(0,r.jsxs)("p",{className:"flex items-center gap-2",children:[(0,r.jsx)(N.A,{className:"h-3 w-3"}),(null==I?void 0:I.PhoneNumber)||(null==I?void 0:I.PhoneNo)||"N/A"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h3",{className:"font-semibold mb-3 flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-4 w-4"}),"Payment Information"]}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Payment Method:"})," ",B.PaymentMethodName||(6===B.PaymentMethod?"Cash on Delivery":"Online Payment")]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Currency:"})," USD"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Status:"}),(()=>{if(H){let e="bg-gray-100 text-gray-800";switch(H.toLowerCase()){case"active":e="bg-blue-100 text-blue-800";break;case"in progress":case"inprogress":e="bg-orange-100 text-orange-800";break;case"completed":e="bg-green-100 text-green-800";break;case"returned":e="bg-purple-100 text-purple-800";break;case"refunded":e="bg-indigo-100 text-indigo-800";break;case"cancelled":e="bg-red-100 text-red-800"}return(0,r.jsx)("span",{className:"ml-1 px-2 py-1 rounded-full text-xs ".concat(e),children:H})}else{let e=(e=>{switch(parseInt((null==e?void 0:e.toString())||"1")){case 1:return{name:"Active",color:"bg-blue-100 text-blue-800"};case 2:return{name:"In Progress",color:"bg-orange-100 text-orange-800"};case 3:return{name:"Completed",color:"bg-green-100 text-green-800"};case 4:return{name:"Returned",color:"bg-purple-100 text-purple-800"};case 5:return{name:"Refunded",color:"bg-indigo-100 text-indigo-800"};case 6:return{name:"Cancelled",color:"bg-red-100 text-red-800"};default:return{name:"Unknown",color:"bg-gray-100 text-gray-800"}}})(_);return(0,r.jsx)("span",{className:"ml-1 px-2 py-1 rounded-full text-xs ".concat(e.color),children:e.name})}})()]}),J&&(0,r.jsxs)("p",{className:"text-sm text-green-600 mt-1 flex items-center gap-1",children:[(0,r.jsx)(v.A,{className:"h-3 w-3"}),"You can now write reviews for your products"]})]})]})]})]})}),(0,r.jsx)(o.Zp,{children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("h3",{className:"font-semibold mb-4 flex items-center gap-2",children:[(0,r.jsx)(b.A,{className:"h-4 w-4"}),"Order Items"]}),(0,r.jsx)("div",{className:"space-y-4",children:C.map((e,t)=>{let s=e.ProductImageUrl||e.ImageUrl||e.ProductImage;return(0,r.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-muted rounded-lg overflow-hidden flex items-center justify-center",children:s?(0,r.jsx)("img",{src:s.startsWith("http")?s:"".concat("https://admin.codemedicalapps.com/").concat(s),alt:e.ProductName||e.ItemName||"Product",className:"w-full h-full object-cover",onError:e=>{let t=e.target;t.style.display="none";let s=t.parentElement;s&&(s.innerHTML='<svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>')}}):(0,r.jsx)(b.A,{className:"h-8 w-8 text-gray-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:e.ProductName||e.ItemName||"Medical Product"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Quantity: ",e.Quantity||e.OrderQuantity||1]}),e.ProductDescription&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:e.ProductDescription})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("p",{className:"font-medium",children:["$",(e.UnitPrice||e.Price||e.ItemPrice||0).toFixed(2)]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Total: $",((e.UnitPrice||e.Price||0)*(e.Quantity||1)).toFixed(2)]}),J&&(0,r.jsxs)(c.$,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>L(U===e.ProductID?null:e.ProductID),children:[(0,r.jsx)(v.A,{className:"h-3 w-3 mr-1"}),U===e.ProductID?"Cancel":"Write Review"]})]})]}),J&&U===e.ProductID&&(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(w.A,{productId:e.ProductID,productName:e.ProductName||e.ItemName||"Medical Product",onReviewSubmitted:z})})]},t)})}),(0,r.jsx)("div",{className:"mt-6 pt-4 border-t",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-lg font-semibold",children:"Total Amount:"}),(0,r.jsxs)("span",{className:"text-2xl font-bold",children:["$",O?T?"".concat(T):"Loading...":B.OrderTotal||B.TotalAmount||T||"0.00"]})]})})]})})]}):(0,r.jsx)(o.Zp,{children:(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(b.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Order Not Found"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"The order you're looking for doesn't exist or you don't have permission to view it."}),(0,r.jsx)(c.$,{asChild:!0,children:(0,r.jsx)(m(),{href:"/orders",children:"Back to Orders"})})]})})]})]})}}},e=>{e.O(0,[8320,4277,3464,4706,9637,8816,9972,8441,5964,7358],()=>e(e.s=1497)),_N_E=e.O()}]);