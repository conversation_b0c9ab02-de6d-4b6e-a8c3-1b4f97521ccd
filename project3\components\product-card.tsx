"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { Star, ShoppingCart, Heart, Clock, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useCart } from "@/contexts/cart-context"
import { useWishlist } from "@/contexts/wishlist-context"
import { useToast } from "@/hooks/use-toast"
import { showModernAddToCartToast } from "@/components/ui/modern-toast"
import { useSettings } from "@/contexts/settings-context"

interface Product {
  ProductId: number
  ProductName: string
  Price: number
  OldPrice?: number
  DiscountPrice?: number
  Rating: number
  ProductImageUrl?: string
  CategoryName: string
  StockQuantity: number
  ProductTypeName?: string
  IQDPrice?: number
  IsDiscountAllowed?: boolean
  MarkAsNew?: boolean
  SellStartDatetimeUTC?: string
  SellEndDatetimeUTC?: string
}

interface ProductCardProps {
  product: Product
}

function CountdownTimer({ endDate }: { endDate: string }) {
  const [timeLeft, setTimeLeft] = useState<{
    days: number
    hours: number
    minutes: number
    seconds: number
  } | null>(null)

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date().getTime()
      const end = new Date(endDate).getTime()
      const difference = end - now

      if (difference > 0) {
        return {
          days: Math.floor(difference / (1000 * 60 * 60 * 24)),
          hours: Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
          minutes: Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60)),
          seconds: Math.floor((difference % (1000 * 60)) / 1000),
        }
      }
      return null
    }

    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft())
    }, 1000)

    // Initial calculation
    setTimeLeft(calculateTimeLeft())

    return () => clearInterval(timer)
  }, [endDate])

  const TimeBox = ({ value, label }: { value: number; label: string }) => (
    <div className="flex flex-col items-center mx-0.5">
      <div className="relative w-8 h-8 sm:w-9 sm:h-9 flex items-center justify-center bg-white rounded-md shadow-sm border border-red-500">
        <span className="text-red-500 font-bold text-xs sm:text-sm">
          {String(value).padStart(2, '0')}
        </span>
      </div>
      <span className="text-[10px] sm:text-xs text-red-500 mt-0.5 font-medium">
        {label}
      </span>
    </div>
  )

  if (!timeLeft) {
    return (
      <div className="px-2 py-1 bg-white/90 backdrop-blur-sm rounded-lg border border-gray-300 flex items-center justify-center shadow-md max-w-[90%] mx-auto">
        <Clock className="w-3 h-3 mr-1 text-gray-500 animate-pulse" />
        <span className="text-xs font-semibold text-gray-500">Sale Ended</span>
      </div>
    )
  }

  return (
    <div className="bg-white/90 backdrop-blur-sm p-1.5 sm:p-2 rounded-lg shadow-md border border-gray-200 max-w-[90%] mx-auto">
      <div className="flex justify-center items-center space-x-1">
        {timeLeft.days > 0 && (
          <>
            <TimeBox value={timeLeft.days} label="Days" />
          </>
        )}
        <TimeBox value={timeLeft.hours} label="Hrs" />
        <TimeBox value={timeLeft.minutes} label="Min" />
        <TimeBox value={timeLeft.seconds} label="Sec" />
      </div>
    </div>
  )
}

export default function ProductCard({ product }: ProductCardProps) {
  const cart = useCart()
  const wishlist = useWishlist()
  const { toast } = useToast()
  const { primaryColor } = useSettings()
  const [addingToCart, setAddingToCart] = useState(false)
  const [addingToWishlist, setAddingToWishlist] = useState(false)

  const handleAddToCart = () => {
    if (!cart.isHydrated) return

    setAddingToCart(true)
    try {
      const productImage = product.ProductImageUrl || "/placeholder.svg?height=300&width=300"
      cart.addToCart(
        {
          id: product.ProductId,
          name: product.ProductName,
          price: product.DiscountPrice || product.Price, // Use discount price if available
          discountPrice: product.DiscountPrice,
          image: productImage,
          originalPrice: product.Price, // Always store the original price
        },
        1,
        [], // No attributes by default
        product.IQDPrice // Pass IQD price if available
      )
      // Show modern toast notification
      showModernAddToCartToast({
        productName: product.ProductName,
        quantity: 1,
        productImage: product.ProductImageUrl || '/placeholder.svg',
        onViewCart: () => {
          window.location.href = '/cart';
        }
      })
    } catch (error) {
      console.error("Error adding to cart:", error)
      toast.error("Failed to add product to cart")
    } finally {
      setTimeout(() => {
        setAddingToCart(false)
      }, 500)
    }
  }

  const handleAddToWishlist = () => {
    if (!wishlist.isHydrated) return

    setAddingToWishlist(true)
    try {
      const isInWishlist = wishlist.isInWishlist(product.ProductId)
      if (isInWishlist) {
        wishlist.removeFromWishlist(product.ProductId)
        toast.success(`${product.ProductName} removed from wishlist`)
      } else {
        const productUrl = `/product/${product.ProductId}`
        const imageUrl = product.ProductImageUrl || "/placeholder.svg"
        const price = product.DiscountPrice || product.Price
        
        wishlist.addToWishlist(
          product.ProductId,
          product.ProductName,
          productUrl,
          imageUrl,
          price
        )
        toast.success(`${product.ProductName} added to wishlist`)
      }
    } catch (error) {
      console.error("Error updating wishlist:", error)
      toast.error("Failed to update wishlist")
    } finally {
      setTimeout(() => {
        setAddingToWishlist(false)
      }, 500)
    }
  }

  const formatPrice = (price: number | undefined | null, currency: "USD" | "IQD" = "USD") => {
    // Handle undefined, null, or invalid price values
    if (price === undefined || price === null || isNaN(price)) {
      if (currency === "IQD") {
        return 'IQD 0'
      }
      return '$0.00'
    }

    if (currency === "IQD") {
      return `IQD ${price.toLocaleString()}`
    }
    return `$${price.toFixed(2)}`
  }

  const isOnSale = product.SellStartDatetimeUTC && product.SellEndDatetimeUTC
  const currentDate = new Date()
  const saleStartDate = product.SellStartDatetimeUTC ? new Date(product.SellStartDatetimeUTC) : null
  const saleEndDate = product.SellEndDatetimeUTC ? new Date(product.SellEndDatetimeUTC) : null
  const isSaleActive = saleStartDate && saleEndDate && currentDate >= saleStartDate && currentDate <= saleEndDate

  return (
    <Card className="overflow-hidden flex flex-col h-full relative">
      {/* Badges */}
      <div className="absolute top-2 left-2 z-10 flex flex-col gap-1">
        {product.MarkAsNew && (
          <Badge variant="secondary" className="bg-blue-500 text-white text-xs">
            New
          </Badge>
        )}
        {product.DiscountPrice && product.DiscountPrice > 0 && (
          <Badge variant="destructive" className="bg-red-500 text-white text-xs">
            Sale
          </Badge>
        )}
        {isSaleActive && !product.DiscountPrice && (
          <Badge variant="destructive" className="bg-red-500 text-white text-xs">
            Sale
          </Badge>
        )}
      </div>

      <Link href={`/product/${product.ProductId}`}>
        <div className="aspect-square overflow-hidden relative">
          <div className="h-full w-full relative">
            <Image
              src={product.ProductImageUrl || "/placeholder.svg?height=300&width=300"}
              alt={product.ProductName || "Product"}
              fill
              className="object-cover transition-transform hover:scale-105"
              onError={(e) => {
                const target = e.target as HTMLImageElement
                target.src = "/placeholder.svg?height=300&width=300"
              }}
              priority={false}
              loading="lazy"
            />
          </div>

          {/* Countdown Timer Overlay */}
          {isSaleActive && product.SellEndDatetimeUTC && (
            <div className="absolute bottom-0 left-0 right-0 p-2 flex justify-center">
              <CountdownTimer endDate={product.SellEndDatetimeUTC} />
            </div>
          )}
        </div>
      </Link>

      <CardContent className="pt-4 flex-grow">
        {/* Rating */}
        <div className="flex items-center mb-2">
          <div className="flex">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`w-4 h-4 ${
                  i < Math.floor(product.Rating || 0) ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
                }`}
              />
            ))}
          </div>
          <span className="text-xs text-gray-500 ml-1">({product.Rating || 0})</span>
        </div>

        {/* Product Name */}
        <Link href={`/product/${product.ProductId}`} className="hover:underline">
          <h3 className="font-semibold text-lg line-clamp-2 mb-2">{product.ProductName || "Unnamed Product"}</h3>
        </Link>

        {/* Product Type */}
        {product.ProductTypeName && <p className="text-sm text-gray-500 mb-2">Type: {product.ProductTypeName}</p>}

        {/* Pricing */}
        <div className="flex flex-col">
          <div className="flex items-center gap-2">
            {/* Current Price */}
            {product.DiscountPrice ? (
              <>
                <span className="text-lg font-bold text-red-500">{formatPrice(product.DiscountPrice)}</span>
                <span className="text-xs text-gray-500 line-through">{formatPrice(product.Price || 0)}</span>
              </>
            ) : product.OldPrice && product.OldPrice > product.Price ? (
              <>
                <span className="text-lg font-bold text-red-500">{formatPrice(product.Price || 0)}</span>
                <span className="text-xs text-gray-500 line-through">{formatPrice(product.OldPrice)}</span>
              </>
            ) : (
              <span className="text-lg font-bold text-primary">{formatPrice(product.Price || 0)}</span>
            )}
          </div>

          {/* IQD Price */}
          {product.IQDPrice && (
            <span className="text-sm font-medium text-green-600 mt-0.5">{formatPrice(product.IQDPrice, "IQD")}</span>
          )}
        </div>
      </CardContent>

      <CardFooter className="p-3 pt-1 mt-auto">
        <div className="w-full">
          <div className="flex items-center justify-between gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2 text-xs font-medium flex-1 gap-1.5 text-white hover:opacity-90"
              style={{ backgroundColor: primaryColor }}
              asChild
            >
              <Link href={`/product/${product.ProductId}`}>
                <Eye className="h-3.5 w-3.5" />
                <span>View</span>
              </Link>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-full"
              onClick={handleAddToWishlist}
              disabled={addingToWishlist}
            >
              <Heart
                className={`h-4 w-4 ${wishlist.isInWishlist(product.ProductId) ? "fill-red-500 text-red-500" : ""}`}
              />
            </Button>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
