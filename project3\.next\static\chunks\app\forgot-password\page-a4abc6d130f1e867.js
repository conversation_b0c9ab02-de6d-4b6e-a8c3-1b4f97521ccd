(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2162,5409,7790],{8460:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E});var s=r(95155),a=r(12115),o=r(88482),n=r(97168),i=r(89852),l=r(82714),c=r(39365),d=r.n(c);r(30133);var u=r(1978),m=r(6874),p=r.n(m),f=r(35695),h=r(46767),g=r(43453);let y=(0,r(19946).A)("KeyRound",[["path",{d:"M2.586 17.414A2 2 0 0 0 2 18.828V21a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h1a1 1 0 0 0 1-1v-1a1 1 0 0 1 1-1h.172a2 2 0 0 0 1.414-.586l.814-.814a6.5 6.5 0 1 0-4-4z",key:"1s6t7t"}],["circle",{cx:"16.5",cy:"7.5",r:".5",fill:"currentColor",key:"w0ekpg"}]]);var x=r(53904),v=r(92138),b=r(32919),w=r(92657),N=r(78749),S=r(79891),P=r(65409),A=r(14503);function E(){let[e,t]=(0,a.useState)("phone"),[r,c]=(0,a.useState)("964"),[m,E]=(0,a.useState)("iq"),[_,T]=(0,a.useState)(""),[j,C]=(0,a.useState)(null),[k,I]=(0,a.useState)(0),[O,U]=(0,a.useState)(!1),[R,D]=(0,a.useState)(""),[M,L]=(0,a.useState)(!1),[q,F]=(0,a.useState)(!1),[G,W]=(0,a.useState)(""),[B,J]=(0,a.useState)({newPassword:"",confirmPassword:""}),{t:H}=(0,S.t)(),{toast:V}=(0,A.dj)(),Y=(0,f.useRouter)();(0,a.useEffect)(()=>{fetch("https://ipapi.co/json/").then(e=>e.json()).then(e=>{e.country_code&&(E(e.country_code.toLowerCase()),c(e.country_calling_code.replace("+","")))}).catch(()=>{E("iq"),c("964")})},[]),(0,a.useEffect)(()=>{if(k>0){let e=setTimeout(()=>I(k-1),1e3);return()=>clearTimeout(e)}},[k]);let z=()=>{I(60)},$=async e=>{if(e.preventDefault(),U(!0),D(""),!r){D("Phone number is required"),U(!1);return}let s=r.replace(/[^+\d]/g,"");if(s.length<8){D("Phone number must be at least 8 digits"),U(!1);return}if(!/^\+?[1-9]\d{7,14}$/.test(s)){D("Please enter a valid phone number"),U(!1);return}let a=s.startsWith("+")?s:"+".concat(s);try{var o,n,i,l;console.log("Step 1: Checking if user exists with phone number:",a);let e=await (0,P.MakeApiCallAsync)(P.Config.END_POINT_NAMES.GET_USER_BY_PHONE,null,{requestParameters:{PhoneNumber:a}},{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(console.log("User check response:",e),console.log("User check response data:",e.data),console.log("User check response data.data:",null==(o=e.data)?void 0:o.data),console.log("User check response data.data type:",typeof(null==(n=e.data)?void 0:n.data)),(null==(i=e.data)?void 0:i.errorMessage)||(null==(l=e.data)?void 0:l.error)||!e.data||!e.data.data||"[]"===e.data.data||Array.isArray(e.data.data)&&0===e.data.data.length){D("No account found with this phone number. Please verify your phone number or create a new account."),U(!1);return}console.log("Step 2: User found, proceeding with SMS verification");let r=await fetch("/api/sms/send-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:a,useWhatsApp:!0})}),s=await r.json();if(r.ok)C({phoneNumber:a}),t("verification"),z();else throw Error(s.error||"Failed to send verification code");V({title:"Verification Code Sent",description:"We've sent a verification code to ".concat(a)})}catch(e){console.error("Error in phone submission:",e),"auth/too-many-requests"===e.code?D("Too many verification attempts. Please wait a few minutes before trying again."):"auth/invalid-phone-number"===e.code?D("Invalid phone number format. Please check your phone number and try again."):"auth/quota-exceeded"===e.code?D("SMS quota exceeded. Please try again later."):D(e.message||"Failed to send verification code. Please try again."),V({title:"Error",description:e.message||"Failed to send verification code",type:"error"})}finally{U(!1)}},Q=async()=>{if(!(k>0)){U(!0),D("");try{let e=r.replace(/[^+\d]/g,""),t=e.startsWith("+")?e:"+".concat(e),s=await fetch("/api/sms/send-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:t,useWhatsApp:!0})}),a=await s.json();if(s.ok)C({phoneNumber:t}),z();else throw Error(a.error||"Failed to resend verification code");V({title:"Verification code resent",description:"A new verification code has been sent to your phone number."})}catch(e){console.error("Error resending verification code:",e),"auth/too-many-requests"===e.code?D("Too many verification attempts. Please wait a few minutes before trying again."):"auth/invalid-phone-number"===e.code?D("Invalid phone number format. Please check your phone number and try again."):"auth/quota-exceeded"===e.code?D("SMS quota exceeded. Please try again later."):D(e.message||"Failed to resend verification code. Please try again."),V({title:"Error",description:e.message||"Failed to resend verification code",type:"error"})}finally{U(!1)}}},Z=async e=>{e.preventDefault(),U(!0),D("");try{let e=await fetch("/api/sms/verify-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:j.phoneNumber,code:_})});if(await e.json(),e.ok){let e=j.phoneNumber,r=await (0,P.MakeApiCallAsync)(P.Config.END_POINT_NAMES.GET_USER_BY_PHONE,null,{requestParameters:{PhoneNumber:e}},{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(r.data&&!r.data.errorMessage&&"[]"!==r.data.data){let e;if("string"==typeof r.data.data)try{e=JSON.parse(r.data.data)}catch(t){console.error("Error parsing user data:",t),e=[]}else e=r.data.data;Array.isArray(e)&&e.length>0&&W(e[0].EmailAddress||e[0].Email||"")}t("reset"),V({title:"Phone Verified",description:"Your phone number has been verified. You can now reset your password."})}}catch(e){D(e.message||"Invalid verification code"),console.error("Error:",e),V({title:"Verification Failed",description:e.message||"Invalid verification code",type:"error"})}finally{U(!1)}},X=async e=>{if(e.preventDefault(),U(!0),D(""),!B.newPassword||B.newPassword.length<6){D("Password must be at least 6 characters long"),U(!1);return}if(B.newPassword!==B.confirmPassword){D("Passwords do not match"),U(!1);return}try{var t,s,a,o;let e=r.replace(/[^+\d]/g,""),n=e.startsWith("+")?e:"+".concat(e),i={requestParameters:{PhoneNumber:n,Password:B.newPassword}};console.log("Password reset request:",{phoneNumber:n,email:G,endpoint:P.Config.END_POINT_NAMES.RESET_PASSWORD_BY_PHONE});let l=await (0,P.MakeApiCallAsync)(P.Config.END_POINT_NAMES.RESET_PASSWORD_BY_PHONE,null,i,{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(console.log("Password reset response:",l),l.data&&200===l.data.statusCode&&"Ok"===l.data.statusMessage){V({title:"Password Reset Successful",description:l.data.message||"Your password has been reset successfully. You can now log in with your new password."}),setTimeout(()=>{Y.push("/login")},2e3);return}let c="Failed to reset password. Please try again.";if(null==(t=l.data)?void 0:t.errorMessage){let e=l.data.errorMessage;c=e.includes("Phone number is required")?"Phone number is required. Please try again.":e.includes("Password is required")?"Password is required. Please try again.":e.includes("User not found with the provided phone number")?"No account found with this phone number. Please verify your phone number.":e.includes("Database error occurred")?"A database error occurred. Please try again later or contact support.":e.includes("Operation error")?"A system error occurred. Please try again later.":e.includes("An unexpected error occurred")?"An unexpected error occurred. Please try again or contact support.":e}else(null==(s=l.data)?void 0:s.statusCode)===501?c="Password reset service is currently unavailable. Please try again later or contact support.":(null==(a=l.data)?void 0:a.statusCode)===404?c="User not found. Please verify your phone number.":(null==(o=l.data)?void 0:o.statusCode)===400&&(c="Invalid request. Please check your information and try again.");D(c),V({title:"Password Reset Failed",description:c,type:"error"})}catch(e){D(e.message||"Failed to reset password"),console.error("Error:",e),V({title:"Reset Failed",description:e.message||"Failed to reset password",type:"error"})}finally{U(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("h2",{className:"mt-6 text-3xl font-extrabold",children:["phone"===e&&"Reset Password","verification"===e&&"Verify Your Phone","reset"===e&&"Create New Password"]}),(0,s.jsxs)("p",{className:"mt-2 text-sm text-muted-foreground",children:["phone"===e&&"Enter your phone number to reset your password","verification"===e&&"Enter the code we sent to your phone","reset"===e&&"Enter your new password"]})]}),(0,s.jsxs)(o.Zp,{className:"mt-8 p-8 shadow-xl bg-card/100",children:[(0,s.jsx)("div",{className:"flex justify-center mb-8",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("phone"===e?"bg-primary text-primary-foreground":"bg-primary/20 text-primary"),children:(0,s.jsx)(h.A,{className:"w-4 h-4"})}),(0,s.jsx)("div",{className:"w-16 h-1 ".concat("phone"===e?"bg-primary/20":"bg-primary")}),(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("verification"===e?"bg-primary text-primary-foreground":"reset"===e?"bg-primary":"bg-primary/20 text-primary"),children:(0,s.jsx)(g.A,{className:"w-4 h-4"})}),(0,s.jsx)("div",{className:"w-16 h-1 ".concat("reset"===e?"bg-primary":"bg-primary/20")}),(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center ".concat("reset"===e?"bg-primary text-primary-foreground":"bg-primary/20 text-primary"),children:(0,s.jsx)(y,{className:"w-4 h-4"})})]})}),(0,s.jsxs)(u.P.div,{variants:{hidden:{opacity:0,x:-20},visible:{opacity:1,x:0},exit:{opacity:0,x:20}},initial:"hidden",animate:"visible",exit:"exit",transition:{duration:.3},children:["phone"===e&&(0,s.jsxs)("form",{onSubmit:$,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(l.J,{className:"block text-sm font-medium mb-2 text-center",children:"Phone Number"}),(0,s.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,s.jsx)("div",{className:"w-full max-w-[300px]",children:(0,s.jsx)(d(),{country:m,value:r,onChange:e=>{c(e),D("")},enableSearch:!0,searchPlaceholder:"Search country...",containerClass:"w-full",inputClass:"w-full p-4 border rounded-lg focus:ring-2 focus:ring-primary/50 ".concat(R?"border-destructive":""),buttonClass:"!border-input !bg-background hover:!bg-accent",dropdownClass:"!bg-background !border-input",disabled:O,countryCodeEditable:!1,isValid:(e,t)=>!!e&&!(e.length<8)&&!!/^\+?[1-9]\d{1,14}$/.test(e)})}),R&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:R})]})]}),(0,s.jsx)(n.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2",disabled:O,children:O?(0,s.jsx)(x.A,{className:"w-4 h-4 animate-spin"}):(0,s.jsxs)(s.Fragment,{children:["Send Code ",(0,s.jsx)(v.A,{className:"w-4 h-4"})]})}),(0,s.jsxs)("div",{className:"text-center text-sm mt-4",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Remember your password? "}),(0,s.jsx)(p(),{href:"/login",className:"text-primary hover:text-primary/80 hover:underline transition-colors",children:"Back to Login"})]})]}),"verification"===e&&(0,s.jsxs)("form",{onSubmit:Z,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(l.J,{className:"block text-sm font-medium mb-4 text-center",children:"Verification Code"}),(0,s.jsx)("div",{className:"flex justify-center items-center gap-3",children:[...Array(6)].map((e,t)=>(0,s.jsx)(i.p,{type:"number",maxLength:1,className:"w-10 h-10 sm:w-12 sm:h-12 text-center text-lg sm:text-2xl font-semibold rounded-lg focus:ring-2 focus:ring-primary/50 transition-all",value:_[t]||"",onChange:e=>{let r=_.split("");r[t]=e.target.value,T(r.join("")),e.target.value&&e.target.nextElementSibling&&e.target.nextElementSibling.focus(),D("")},disabled:O},t))}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)("button",{type:"button",onClick:Q,className:"text-sm ".concat(k>0?"text-muted-foreground":"text-primary hover:underline"),disabled:k>0||O,children:k>0?"Resend code in ".concat((e=>{let t=Math.floor(e/60);return"".concat(t,":").concat((e%60).toString().padStart(2,"0"))})(k)):"Resend code"})}),R&&(0,s.jsx)("p",{className:"text-sm text-destructive text-center mt-2",children:R})]}),(0,s.jsx)(n.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2",disabled:O||6!==_.length,children:O?(0,s.jsx)(x.A,{className:"w-4 h-4 animate-spin"}):(0,s.jsxs)(s.Fragment,{children:["Verify ",(0,s.jsx)(g.A,{className:"w-4 h-4"})]})})]}),"reset"===e&&(0,s.jsxs)("form",{onSubmit:X,className:"space-y-6",children:[G&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-4",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(g.A,{className:"w-5 h-5 text-green-600 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-green-800",children:"Account Found"}),(0,s.jsx)("p",{className:"text-sm text-green-600",children:G})]})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.J,{className:"block text-sm font-medium mb-2",children:"New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.p,{type:M?"text":"password",value:B.newPassword,onChange:e=>{J({...B,newPassword:e.target.value}),D("")},className:"pl-10 pr-10",placeholder:"Enter new password",required:!0,minLength:6,disabled:O}),(0,s.jsx)(b.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"}),(0,s.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 hover:bg-transparent",onClick:()=>L(!M),disabled:O,children:M?(0,s.jsx)(w.A,{className:"w-4 h-4 text-muted-foreground"}):(0,s.jsx)(N.A,{className:"w-4 h-4 text-muted-foreground"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(l.J,{className:"block text-sm font-medium mb-2",children:"Confirm New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.p,{type:q?"text":"password",value:B.confirmPassword,onChange:e=>{J({...B,confirmPassword:e.target.value}),D("")},className:"pl-10 pr-10",placeholder:"Confirm new password",required:!0,minLength:6,disabled:O}),(0,s.jsx)(b.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"}),(0,s.jsx)(n.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 hover:bg-transparent",onClick:()=>F(!q),disabled:O,children:q?(0,s.jsx)(w.A,{className:"w-4 h-4 text-muted-foreground"}):(0,s.jsx)(N.A,{className:"w-4 h-4 text-muted-foreground"})})]})]}),R&&(0,s.jsx)("p",{className:"text-sm text-destructive text-center",children:R}),(0,s.jsx)(n.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors",disabled:O||!B.newPassword||!B.confirmPassword,children:O?(0,s.jsx)(x.A,{className:"w-4 h-4 animate-spin"}):"Reset Password"})]})]},e)]})]})})}},12650:(e,t,r)=>{Promise.resolve().then(r.bind(r,8460))},14503:(e,t,r)=>{"use strict";r.d(t,{dj:()=>u,oR:()=>d});var s=r(12115);let a=0,o=new Map,n=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},i=[],l={toasts:[]};function c(e){l=((e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?n(r):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}})(l,e),i.forEach(e=>{e(l)})}function d(e){let{duration:t=2e3,...r}=e,s=(a=(a+1)%100).toString(),o=()=>c({type:"DISMISS_TOAST",toastId:s});return c({type:"ADD_TOAST",toast:{...r,id:s,duration:t,open:!0,onOpenChange:e=>{e||o()}}}),setTimeout(()=>{o()},t),{id:s,dismiss:o,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function u(){let[e,t]=s.useState(l);return s.useEffect(()=>(i.push(t),()=>{let e=i.indexOf(t);e>-1&&i.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}d.success=(e,t)=>d({description:e,type:"success",duration:2e3,...t}),d.error=(e,t)=>d({description:e,type:"error",duration:2e3,...t}),d.warning=(e,t)=>d({description:e,type:"warning",duration:2e3,...t}),d.info=(e,t)=>d({description:e,type:"info",duration:2e3,...t})},32919:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},35695:(e,t,r)=>{"use strict";var s=r(18999);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"usePathname")&&r.d(t,{usePathname:function(){return s.usePathname}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},43453:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},46767:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},53904:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(52596),a=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},61204:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});let s={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",COMMON_CONTROLLER_SUB_URL:"api/v1/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile"}}},65409:(e,t,r)=>{"use strict";r.d(t,{$g:()=>u,Config:()=>o,MakeApiCallAsync:()=>l,XX:()=>d,k6:()=>c});var s=r(23464),a=r(61204);s.A.defaults.timeout=3e4,"https:"===window.location.protocol&&a.T.ADMIN_BASE_URL.includes("localhost")&&(s.A.defaults.httpsAgent={rejectUnauthorized:!1});let o={ADMIN_BASE_URL:a.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...a.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",RESET_PASSWORD_FIREBASE:"reset-password-firebase",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile",INSERT_PRODUCT_REVIEW:"Insert-Product-Review",GET_PRODUCT_REVIEWS:"get-product-reviews"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},n=async()=>{try{{try{let e=await fetch("/api/auth/get-token",{method:"GET",credentials:"include"});if(e.ok){let t=await e.json();if(t.token)return console.log("\uD83D\uDD10 Retrieved JWT token from secure HttpOnly cookie"),t.token}}catch(e){console.log("API token retrieval failed, trying client-side cookies:",e)}for(let e of document.cookie.split(";")){let[t,r]=e.trim().split("=");if("auth_token"===t)return console.log("\uD83D\uDD10 Retrieved JWT token from client-side cookie"),decodeURIComponent(r)}let e=localStorage.getItem("token")||localStorage.getItem("authToken");if(e)return console.log("⚠️ Using token from localStorage (migrating to secure cookies)"),localStorage.removeItem("token"),localStorage.removeItem("authToken"),e}return null}catch(e){return console.error("Error getting token for header:",e),null}},i=async()=>{try{{for(let r of document.cookie.split(";")){let[s,a]=r.trim().split("=");if("auth_user"===s)try{var e,t;let r=JSON.parse(decodeURIComponent(a)),s=(null==(e=r.UserId)?void 0:e.toString())||(null==(t=r.UserID)?void 0:t.toString());if(s)return console.log("\uD83D\uDD10 Retrieved User ID from secure cookie"),s}catch(e){console.warn("Failed to parse user data from cookie:",e)}}let r=localStorage.getItem("userId")||localStorage.getItem("userID");if(r)return console.log("⚠️ Using User ID from localStorage (migrating to secure cookies)"),localStorage.removeItem("userId"),localStorage.removeItem("userID"),r}return null}catch(e){return console.error("Error getting user ID for header:",e),null}},l=async function(e,t,r,a,l){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let c=(e=>{if(!e)return e;let t=JSON.parse(JSON.stringify(e));return t.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from request body (will use JWT token instead)"),delete t.UserId),t.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from request body (will use JWT token instead)"),delete t.UserID),t.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from request body (will use JWT token instead)"),delete t.user_id),t.requestParameters&&(t.requestParameters.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from requestParameters (will use JWT token instead)"),delete t.requestParameters.UserId),t.requestParameters.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from requestParameters (will use JWT token instead)"),delete t.requestParameters.UserID),t.requestParameters.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from requestParameters (will use JWT token instead)"),delete t.requestParameters.user_id)),t})(r),d={...a};if(!d.hasOwnProperty("Authorization")){let e=await n();e&&(d.Authorization="Bearer "+e,console.log("\uD83D\uDD10 Added JWT token to Authorization header"))}if(!d.hasOwnProperty("Token")){let e=await n();d.Token=null!=e?e:"",e&&console.log("\uD83D\uDD10 Added JWT token to Token header (backward compatibility)")}if(!d.hasOwnProperty("UserID")){let e=await i();d.UserID=null!=e?e:""}d.hasOwnProperty("Accept")||(d.Accept="application/json"),d.hasOwnProperty("Content-Type")||(d["Content-Type"]="application/json");let u=o.ADMIN_BASE_URL+(null===t||void 0==t?o.DYNAMIC_METHOD_SUB_URL:t)+e;l=null!=l?l:"POST";let m={headers:d,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===l)return await s.A.post(u,c,m);if("GET"==l)return m.params=c,await s.A.get(u,m);return{data:{errorMessage:"Unsupported method type: ".concat(l),status:"method_not_supported"}}}catch(t){console.error("API call failed:",t);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(t&&"object"==typeof t&&"response"in t&&t.response){var c,d;let r=null==(c=t.response)?void 0:c.data;e.data={errorMessage:(null==r?void 0:r.errorMessage)||"An error occurred while processing your request.",status:null==(d=t.response)?void 0:d.status}}else if(t&&"object"==typeof t&&"request"in t){let r="Network error: No response received from server.";t.message&&t.message.includes("Network Error")&&(r="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+o.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:r,status:"network_error"}}else e.data={errorMessage:t&&"object"==typeof t&&"message"in t?t.message:"An unexpected error occurred",status:"request_error"};return e}},c=async()=>{try{return console.log("Using default currency rate (1430) - API endpoint not available"),1430}catch(e){return console.error("Error fetching currency rate:",e),1430}},d=(e,t)=>Math.round(e*t),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return null==e||isNaN(e)?"IQD"===t?"0 IQD":"$0.00":"IQD"===t?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},78749:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},79891:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l,t:()=>c});var s=r(95155),a=r(12115);let o={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us",searchProducts:"Search products..."},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",searchProducts:"ابحث عن المنتجات...",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}};var n=r(94213);let i=(0,a.createContext)(void 0);function l(e){let{children:t}=e,[r,l]=(0,a.useState)("light"),[c,d]=(0,a.useState)("en"),[u,m]=(0,a.useState)("#0074b2"),[p,f]=(0,a.useState)("#ffffff");return(0,a.useEffect)(()=>{let e=(0,n.N)(u);f(e),document.documentElement.style.setProperty("--primary",u),document.documentElement.style.setProperty("--primary-foreground",e)},[u]),(0,s.jsx)(i.Provider,{value:{theme:r,language:c,primaryColor:u,primaryTextColor:p,toggleTheme:()=>{l("light"===r?"dark":"light")},setLanguage:e=>{d(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{m(e);let t=(0,n.N)(e);f(t),document.documentElement.style.setProperty("--primary",e),document.documentElement.style.setProperty("--primary-foreground",t)},t:e=>(function(e,t){let r=o[t];return e in r?r[e]:"en"!==t&&e in o.en?o.en[e]:e})(e,c)},children:t})}function c(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},82714:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var s=r(95155),a=r(12115),o=r(40968),n=r(74466),i=r(53999);let l=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(o.b,{ref:t,className:(0,i.cn)(l(),r),...a})});c.displayName=o.b.displayName},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>u});var s=r(95155),a=r(12115),o=r(53999);let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});n.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});l.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(95155),a=r(12115),o=r(53999);let n=a.forwardRef((e,t)=>{let{className:r,type:a,...n}=e;return(0,s.jsx)("input",{type:a,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...n})});n.displayName="Input"},92138:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},94213:(e,t,r)=>{"use strict";function s(e,t){let r=e=>{let t=e.replace("#",""),r=parseInt(t.slice(0,2),16)/255,s=[r,parseInt(t.slice(2,4),16)/255,parseInt(t.slice(4,6),16)/255].map(e=>e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4));return .2126*s[0]+.7152*s[1]+.0722*s[2]},s=r(e),a=r(t);return(Math.max(s,a)+.05)/(Math.min(s,a)+.05)}function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"AA",r=s(e,"#ffffff"),a=s(e,"#000000"),o="AAA"===t?7:4.5;return r>=o&&a>=o?r>a?"#ffffff":"#000000":r>=o?"#ffffff":a>=o?"#000000":r>a?"#ffffff":"#000000"}r.d(t,{N:()=>a})},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>l});var s=r(95155),a=r(12115),o=r(99708),n=r(74466),i=r(53999);let l=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:n,asChild:c=!1,...d}=e,u=c?o.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(l({variant:a,size:n,className:r})),ref:t,...d})});c.displayName="Button"}},e=>{e.O(0,[7540,4277,3464,4706,4042,3165,8441,5964,7358],()=>e(e.s=12650)),_N_E=e.O()}]);