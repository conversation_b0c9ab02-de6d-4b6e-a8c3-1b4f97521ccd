(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5878],{40968:(e,r,t)=>{"use strict";t.d(r,{b:()=>l});var a=t(12115),s=t(63655),n=t(95155),i=a.forwardRef((e,r)=>(0,n.jsx)(s.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var l=i},48166:(e,r,t)=>{Promise.resolve().then(t.bind(t,74538))},49026:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>d,TN:()=>o});var a=t(95155),s=t(12115),n=t(74466),i=t(53999);let l=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-red-500 dark:border-destructive [&>svg]:text-red-500"}},defaultVariants:{variant:"default"}}),d=s.forwardRef((e,r)=>{let{className:t,variant:s,...n}=e;return(0,a.jsx)("div",{ref:r,role:"alert",className:(0,i.cn)(l({variant:s}),t),...n})});d.displayName="Alert",s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h5",{ref:r,className:(0,i.cn)("mb-1 font-medium leading-none tracking-tight",t),...s})}).displayName="AlertTitle";let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,i.cn)("text-sm [&_p]:leading-relaxed",t),...s})});o.displayName="AlertDescription"},51154:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},53999:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var a=t(52596),s=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,s.QP)((0,a.$)(r))}},63655:(e,r,t)=>{"use strict";t.d(r,{hO:()=>d,sG:()=>l});var a=t(12115),s=t(47650),n=t(99708),i=t(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,n.TL)(`Primitive.${r}`),s=a.forwardRef((e,a)=>{let{asChild:s,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(s?t:r,{...n,ref:a})});return s.displayName=`Primitive.${r}`,{...e,[r]:s}},{});function d(e,r){e&&s.flushSync(()=>e.dispatchEvent(r))}},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>i});var a=t(52596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=a.$,i=(e,r)=>t=>{var a;if((null==r?void 0:r.variants)==null)return n(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:l}=r,d=Object.keys(i).map(e=>{let r=null==t?void 0:t[e],a=null==l?void 0:l[e];if(null===r)return null;let n=s(r)||s(a);return i[e][n]}),o=t&&Object.entries(t).reduce((e,r)=>{let[t,a]=r;return void 0===a||(e[t]=a),e},{});return n(e,d,null==r||null==(a=r.compoundVariants)?void 0:a.reduce((e,r)=>{let{class:t,className:a,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...l,...o}[r]):({...l,...o})[r]===t})?[...e,t,a]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},74538:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var a=t(95155),s=t(12115),n=t(88482),i=t(97168),l=t(89852),d=t(82714),o=t(49026);let c=(0,t(19946).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);var u=t(75525),f=t(51154),m=t(6874),p=t.n(m);function v(){let[e,r]=(0,s.useState)(""),[t,m]=(0,s.useState)(""),[v,x]=(0,s.useState)(!1),[h,g]=(0,s.useState)(""),[y,b]=(0,s.useState)(""),[N,j]=(0,s.useState)("send"),[w,S]=(0,s.useState)(""),C=async()=>{if(!e.trim())return void b("Please enter a phone number");x(!0),b(""),g("");try{let r=await fetch("/api/sms/send-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:e})}),t=await r.json();r.ok?(g("SMS sent successfully! Message ID: ".concat(t.messageId)),S(t.messageId),j("verify"),t.verificationCode&&g(e=>"".concat(e,"\nDevelopment Code: ").concat(t.verificationCode))):b(t.error||"Failed to send SMS")}catch(e){b("Network error occurred"),console.error("Error:",e)}finally{x(!1)}},k=async()=>{if(!t.trim())return void b("Please enter the verification code");x(!0),b(""),g("");try{let r=await fetch("/api/sms/verify-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:e,code:t})}),a=await r.json();r.ok?g("Phone number verified successfully! ✅"):b(a.error||"Verification failed")}catch(e){b("Network error occurred"),console.error("Error:",e)}finally{x(!1)}};return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)(p(),{href:"/",className:"text-primary hover:text-primary/80 mb-4 inline-block",children:"← Back to Home"}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Test Twilio SMS"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Test the SMS verification functionality"})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"text-center",children:[(0,a.jsx)("div",{className:"mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4",children:"send"===N?(0,a.jsx)(c,{className:"w-6 h-6 text-primary"}):(0,a.jsx)(u.A,{className:"w-6 h-6 text-primary"})}),(0,a.jsx)(n.ZB,{children:"send"===N?"Send SMS":"Verify Code"}),(0,a.jsx)(n.BT,{children:"send"===N?"Enter a phone number to send a verification code":"Enter the verification code you received"})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[y&&(0,a.jsx)(o.Fc,{variant:"destructive",children:(0,a.jsx)(o.TN,{children:y})}),h&&(0,a.jsx)(o.Fc,{children:(0,a.jsx)(o.TN,{className:"whitespace-pre-line",children:h})}),"send"===N?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"phone",children:"Phone Number"}),(0,a.jsx)(l.p,{id:"phone",type:"tel",placeholder:"+1234567890",value:e,onChange:e=>r(e.target.value),disabled:v}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Include country code (e.g., +1 for US, +964 for Iraq)"})]}),(0,a.jsx)(i.$,{onClick:C,disabled:v,className:"w-full",children:v?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Sending SMS..."]}):"Send Verification Code"})]}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(d.J,{htmlFor:"code",children:"Verification Code"}),(0,a.jsx)(l.p,{id:"code",type:"text",placeholder:"123456",value:t,onChange:e=>m(e.target.value.replace(/\D/g,"").slice(0,6)),disabled:v,maxLength:6}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:["Phone: ",e]})]}),(0,a.jsx)(i.$,{onClick:k,disabled:v||6!==t.length,className:"w-full",children:v?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Verifying..."]}):"Verify Code"}),(0,a.jsx)(i.$,{variant:"outline",onClick:()=>{j("send"),r(""),m(""),g(""),b(""),S("")},disabled:v,className:"w-full",children:"Start Over"})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 bg-gray-50 p-3 rounded-md",children:[(0,a.jsx)("strong",{children:"Note:"})," This is a test page for Twilio SMS functionality. In development mode, the verification code will be displayed for testing purposes."]})]})]})]})})}},75525:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},82714:(e,r,t)=>{"use strict";t.d(r,{J:()=>o});var a=t(95155),s=t(12115),n=t(40968),i=t(74466),l=t(53999);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)(n.b,{ref:r,className:(0,l.cn)(d(),t),...s})});o.displayName=n.b.displayName},88482:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>l,wL:()=>u});var a=t(95155),s=t(12115),n=t(53999);let i=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});i.displayName="Card";let l=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...s})});l.displayName="CardHeader";let d=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});d.displayName="CardTitle";let o=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...s})});o.displayName="CardDescription";let c=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...s})});c.displayName="CardContent";let u=s.forwardRef((e,r)=>{let{className:t,...s}=e;return(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...s})});u.displayName="CardFooter"},89852:(e,r,t)=>{"use strict";t.d(r,{p:()=>i});var a=t(95155),s=t(12115),n=t(53999);let i=s.forwardRef((e,r)=>{let{className:t,type:s,...i}=e;return(0,a.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...i})});i.displayName="Input"},97168:(e,r,t)=>{"use strict";t.d(r,{$:()=>o,r:()=>d});var a=t(95155),s=t(12115),n=t(99708),i=t(74466),l=t(53999);let d=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=s.forwardRef((e,r)=>{let{className:t,variant:s,size:i,asChild:o=!1,...c}=e,u=o?n.DX:"button";return(0,a.jsx)(u,{className:(0,l.cn)(d({variant:s,size:i,className:t})),ref:r,...c})});o.displayName="Button"}},e=>{e.O(0,[4277,4706,8441,5964,7358],()=>e(e.s=48166)),_N_E=e.O()}]);