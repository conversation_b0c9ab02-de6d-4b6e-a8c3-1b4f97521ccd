(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5409,5673,7790],{2482:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(95155),o=t(12115),n=t(97168),a=t(89852),l=t(88482),i=t(82714),d=t(65409);function c(){var e,r,t,c,u,p,f;let[m,g]=(0,o.useState)("+65464646"),[h,v]=(0,o.useState)("newpassword123"),[y,N]=(0,o.useState)(null),[_,b]=(0,o.useState)(!1),x=async()=>{b(!0);try{let e={requestParameters:{PhoneNumber:m,Password:h}};console.log("Testing reset password API with:",e);let r=await (0,d.MakeApiCallAsync)(d.Config.END_POINT_NAMES.RESET_PASSWORD_BY_PHONE,null,e,{Accept:"application/json","Content-Type":"application/json"},"POST",!0);console.log("Reset Password API Response:",r),N(r)}catch(e){console.error("Error testing reset password API:",e),N({error:e instanceof Error?e.message:String(e)})}finally{b(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-background p-8",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Test Reset Password API"}),(0,s.jsx)(l.Zp,{className:"p-6 mb-6",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(i.J,{className:"block text-sm font-medium mb-2",children:"Phone Number"}),(0,s.jsx)(a.p,{type:"text",value:m,onChange:e=>g(e.target.value),placeholder:"+65464646"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(i.J,{className:"block text-sm font-medium mb-2",children:"New Password"}),(0,s.jsx)(a.p,{type:"password",value:h,onChange:e=>v(e.target.value),placeholder:"newpassword123"})]}),(0,s.jsx)(n.$,{onClick:x,disabled:_,className:"w-full",children:_?"Testing...":"Test Reset Password API"})]})}),y&&(0,s.jsxs)(l.Zp,{className:"p-6",children:[(0,s.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"API Response:"}),(0,s.jsx)("pre",{className:"bg-gray-100 p-4 rounded-lg overflow-auto text-sm",children:JSON.stringify(y,null,2)}),(0,s.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Status Code:"})," ",null==(e=y.data)?void 0:e.statusCode]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Status Message:"})," ",null==(r=y.data)?void 0:r.statusMessage]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Message:"})," ",(null==(t=y.data)?void 0:t.message)||"None"]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Error Message:"})," ",(null==(c=y.data)?void 0:c.errorMessage)||"None"]}),(null==(u=y.data)?void 0:u.statusCode)===200&&(null==(p=y.data)?void 0:p.statusMessage)==="Ok"&&(0,s.jsx)("p",{className:"text-green-600 font-semibold",children:"✓ Password reset successful!"}),(null==(f=y.data)?void 0:f.errorMessage)&&(0,s.jsx)("p",{className:"text-red-600 font-semibold",children:"✗ Password reset failed"})]}),(0,s.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"SQL Query Parameters Used:"}),(0,s.jsxs)("ul",{className:"text-sm space-y-1",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"@PhoneNumber:"})," ",m]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"@Password:"})," ",h]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"@ModifiedBy:"})," TestResetPassword"]})]}),(0,s.jsx)("p",{className:"text-xs text-gray-600 mt-2",children:"SQL: UPDATE TOP(1) Users SET Password = @Password, ModifiedOn = GETDATE(), ModifiedBy = @ModifiedBy WHERE (PhoneNo = @PhoneNumber OR MobileNo = @PhoneNumber) AND IsActive = 1"})]})]})]})})}},6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>a,t:()=>n});var s=t(12115);function o(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function n(...e){return r=>{let t=!1,s=e.map(e=>{let s=o(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():o(e[r],null)}}}}function a(...e){return s.useCallback(n(...e),e)}},40968:(e,r,t)=>{"use strict";t.d(r,{b:()=>l});var s=t(12115),o=t(63655),n=t(95155),a=s.forwardRef((e,r)=>(0,n.jsx)(o.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));a.displayName="Label";var l=a},53999:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n});var s=t(52596),o=t(39688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,o.QP)((0,s.$)(r))}},61204:(e,r,t)=>{"use strict";t.d(r,{T:()=>s});let s={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",COMMON_CONTROLLER_SUB_URL:"api/v1/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile"}}},63655:(e,r,t)=>{"use strict";t.d(r,{hO:()=>i,sG:()=>l});var s=t(12115),o=t(47650),n=t(99708),a=t(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,n.TL)(`Primitive.${r}`),o=s.forwardRef((e,s)=>{let{asChild:o,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?t:r,{...n,ref:s})});return o.displayName=`Primitive.${r}`,{...e,[r]:o}},{});function i(e,r){e&&o.flushSync(()=>e.dispatchEvent(r))}},65409:(e,r,t)=>{"use strict";t.d(r,{$g:()=>u,Config:()=>n,MakeApiCallAsync:()=>i,XX:()=>c,k6:()=>d});var s=t(23464),o=t(61204);s.A.defaults.timeout=3e4,"https:"===window.location.protocol&&o.T.ADMIN_BASE_URL.includes("localhost")&&(s.A.defaults.httpsAgent={rejectUnauthorized:!1});let n={ADMIN_BASE_URL:o.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...o.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",RESET_PASSWORD_FIREBASE:"reset-password-firebase",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile",INSERT_PRODUCT_REVIEW:"Insert-Product-Review",GET_PRODUCT_REVIEWS:"get-product-reviews"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},a=async()=>{try{{try{let e=await fetch("/api/auth/get-token",{method:"GET",credentials:"include"});if(e.ok){let r=await e.json();if(r.token)return console.log("\uD83D\uDD10 Retrieved JWT token from secure HttpOnly cookie"),r.token}}catch(e){console.log("API token retrieval failed, trying client-side cookies:",e)}for(let e of document.cookie.split(";")){let[r,t]=e.trim().split("=");if("auth_token"===r)return console.log("\uD83D\uDD10 Retrieved JWT token from client-side cookie"),decodeURIComponent(t)}let e=localStorage.getItem("token")||localStorage.getItem("authToken");if(e)return console.log("⚠️ Using token from localStorage (migrating to secure cookies)"),localStorage.removeItem("token"),localStorage.removeItem("authToken"),e}return null}catch(e){return console.error("Error getting token for header:",e),null}},l=async()=>{try{{for(let t of document.cookie.split(";")){let[s,o]=t.trim().split("=");if("auth_user"===s)try{var e,r;let t=JSON.parse(decodeURIComponent(o)),s=(null==(e=t.UserId)?void 0:e.toString())||(null==(r=t.UserID)?void 0:r.toString());if(s)return console.log("\uD83D\uDD10 Retrieved User ID from secure cookie"),s}catch(e){console.warn("Failed to parse user data from cookie:",e)}}let t=localStorage.getItem("userId")||localStorage.getItem("userID");if(t)return console.log("⚠️ Using User ID from localStorage (migrating to secure cookies)"),localStorage.removeItem("userId"),localStorage.removeItem("userID"),t}return null}catch(e){return console.error("Error getting user ID for header:",e),null}},i=async function(e,r,t,o,i){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let d=(e=>{if(!e)return e;let r=JSON.parse(JSON.stringify(e));return r.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from request body (will use JWT token instead)"),delete r.UserId),r.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from request body (will use JWT token instead)"),delete r.UserID),r.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from request body (will use JWT token instead)"),delete r.user_id),r.requestParameters&&(r.requestParameters.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from requestParameters (will use JWT token instead)"),delete r.requestParameters.UserId),r.requestParameters.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from requestParameters (will use JWT token instead)"),delete r.requestParameters.UserID),r.requestParameters.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from requestParameters (will use JWT token instead)"),delete r.requestParameters.user_id)),r})(t),c={...o};if(!c.hasOwnProperty("Authorization")){let e=await a();e&&(c.Authorization="Bearer "+e,console.log("\uD83D\uDD10 Added JWT token to Authorization header"))}if(!c.hasOwnProperty("Token")){let e=await a();c.Token=null!=e?e:"",e&&console.log("\uD83D\uDD10 Added JWT token to Token header (backward compatibility)")}if(!c.hasOwnProperty("UserID")){let e=await l();c.UserID=null!=e?e:""}c.hasOwnProperty("Accept")||(c.Accept="application/json"),c.hasOwnProperty("Content-Type")||(c["Content-Type"]="application/json");let u=n.ADMIN_BASE_URL+(null===r||void 0==r?n.DYNAMIC_METHOD_SUB_URL:r)+e;i=null!=i?i:"POST";let p={headers:c,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===i)return await s.A.post(u,d,p);if("GET"==i)return p.params=d,await s.A.get(u,p);return{data:{errorMessage:"Unsupported method type: ".concat(i),status:"method_not_supported"}}}catch(r){console.error("API call failed:",r);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(r&&"object"==typeof r&&"response"in r&&r.response){var d,c;let t=null==(d=r.response)?void 0:d.data;e.data={errorMessage:(null==t?void 0:t.errorMessage)||"An error occurred while processing your request.",status:null==(c=r.response)?void 0:c.status}}else if(r&&"object"==typeof r&&"request"in r){let t="Network error: No response received from server.";r.message&&r.message.includes("Network Error")&&(t="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+n.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:t,status:"network_error"}}else e.data={errorMessage:r&&"object"==typeof r&&"message"in r?r.message:"An unexpected error occurred",status:"request_error"};return e}},d=async()=>{try{return console.log("Using default currency rate (1430) - API endpoint not available"),1430}catch(e){return console.error("Error fetching currency rate:",e),1430}},c=(e,r)=>Math.round(e*r),u=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return null==e||isNaN(e)?"IQD"===r?"0 IQD":"$0.00":"IQD"===r?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},67465:(e,r,t)=>{Promise.resolve().then(t.bind(t,2482))},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>a});var s=t(52596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.$,a=(e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return n(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:l}=r,i=Object.keys(a).map(e=>{let r=null==t?void 0:t[e],s=null==l?void 0:l[e];if(null===r)return null;let n=o(r)||o(s);return a[e][n]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return n(e,i,null==r||null==(s=r.compoundVariants)?void 0:s.reduce((e,r)=>{let{class:t,className:s,...o}=r;return Object.entries(o).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...l,...d}[r]):({...l,...d})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},82714:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var s=t(95155),o=t(12115),n=t(40968),a=t(74466),l=t(53999);let i=(0,a.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,s.jsx)(n.b,{ref:r,className:(0,l.cn)(i(),t),...o})});d.displayName=n.b.displayName},88482:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>a,aR:()=>l,wL:()=>u});var s=t(95155),o=t(12115),n=t(53999);let a=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...o})});a.displayName="Card";let l=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...o})});l.displayName="CardHeader";let i=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,s.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...o})});i.displayName="CardTitle";let d=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,s.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...o})});d.displayName="CardDescription";let c=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...o})});c.displayName="CardContent";let u=o.forwardRef((e,r)=>{let{className:t,...o}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...o})});u.displayName="CardFooter"},89852:(e,r,t)=>{"use strict";t.d(r,{p:()=>a});var s=t(95155),o=t(12115),n=t(53999);let a=o.forwardRef((e,r)=>{let{className:t,type:o,...a}=e;return(0,s.jsx)("input",{type:o,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...a})});a.displayName="Input"},97168:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>i});var s=t(95155),o=t(12115),n=t(99708),a=t(74466),l=t(53999);let i=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=o.forwardRef((e,r)=>{let{className:t,variant:o,size:a,asChild:d=!1,...c}=e,u=d?n.DX:"button";return(0,s.jsx)(u,{className:(0,l.cn)(i({variant:o,size:a,className:t})),ref:r,...c})});d.displayName="Button"},99708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>l,Dc:()=>d,TL:()=>a});var s=t(12115),o=t(6101),n=t(95155);function a(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...n}=e;if(s.isValidElement(t)){var a;let e,l,i=(a=t,(l=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(l=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),d=function(e,r){let t={...r};for(let s in r){let o=e[s],n=r[s];/^on[A-Z]/.test(s)?o&&n?t[s]=(...e)=>{let r=n(...e);return o(...e),r}:o&&(t[s]=o):"style"===s?t[s]={...o,...n}:"className"===s&&(t[s]=[o,n].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props);return t.type!==s.Fragment&&(d.ref=r?(0,o.t)(r,i):i),s.cloneElement(t,d)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:o,...a}=e,l=s.Children.toArray(o),i=l.find(c);if(i){let e=i.props.children,o=l.map(r=>r!==i?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(r,{...a,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,o):null})}return(0,n.jsx)(r,{...a,ref:t,children:o})});return t.displayName=`${e}.Slot`,t}var l=a("Slot"),i=Symbol("radix.slottable");function d(e){let r=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=i,r}function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}}},e=>{e.O(0,[4277,3464,8441,5964,7358],()=>e(e.s=67465)),_N_E=e.O()}]);