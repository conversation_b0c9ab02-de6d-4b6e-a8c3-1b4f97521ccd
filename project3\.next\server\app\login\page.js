(()=>{var a={};a.id=4520,a.ids=[4520],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10287:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,46387)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\login\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\login\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/login/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},12597:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20859:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>A});var d=c(60687),e=c(43210),f=c(55192),g=c(24934),h=c(68988),i=c(39390),j=c(97905),k=c(41550),l=c(64021),m=c(78122),n=c(99891),o=c(85814),p=c.n(o),q=c(16189),r=c(77080),s=c(832),t=c(73541),u=c(96241),v=c(12597),w=c(13861);let x=e.forwardRef(({className:a,...b},c)=>{let[f,i]=e.useState(!1);return(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(h.p,{type:f?"text":"password",className:(0,u.cn)("pr-10",a),ref:c,...b}),(0,d.jsx)(g.$,{type:"button",variant:"ghost",size:"icon",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>i(!f),"aria-label":f?"Hide password":"Show password",children:f?(0,d.jsx)(v.A,{className:"h-4 w-4","aria-hidden":"true"}):(0,d.jsx)(w.A,{className:"h-4 w-4","aria-hidden":"true"})})]})});x.displayName="PasswordInput";var y=c(71702),z=c(15488);function A(){let{isAuthorized:a,isLoading:b}=(0,t.zK)({requireAuth:!1,redirectIfAuthenticated:!0}),[c,o]=(0,e.useState)(""),[v,w]=(0,e.useState)({email:"",password:""}),{t:A}=(0,r.t)(),{login:B,isLoading:C,isLoggedIn:D}=(0,s.J)(),{toast:E}=(0,y.dj)();(0,q.useRouter)();let{executeRecaptcha:F}=(0,z._Y)(),[G,H]=(0,e.useState)({email:"",password:""}),[I,J]=(0,e.useState)(!1),K=()=>{let a={email:"",password:""},b=!0;return v.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v.email)||(a.email="Please enter a valid email address",b=!1):(a.email="Email is required",b=!1),v.password.trim()||(a.password="Password is required",b=!1),H(a),b};if(b||null===a)return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Checking authentication..."})]})});if(!a)return null;let L=async a=>{if(a.preventDefault(),K()){o("");try{if(!F)return void o("reCAPTCHA not available. Please refresh the page.");J(!0);let a=await F("login");if(!a){o("reCAPTCHA verification failed. Please try again."),J(!1);return}console.log("reCAPTCHA token obtained:",a.substring(0,20)+"..."),J(!1),await fetch("/api/auth/clear-cookies",{method:"POST",credentials:"include"});let b=await B(v.email,v.password,a);if(b.success){E({title:"Login Successful",description:b.message,variant:"default"});try{(await fetch("/api/auth/get-token",{method:"GET",credentials:"include"})).ok||console.warn("Failed to refresh auth token after login")}catch(a){console.error("Error refreshing token:",a)}let a=new URLSearchParams(window.location.search).get("redirect");if(await new Promise(a=>setTimeout(a,300)),a){let b=decodeURIComponent(a);if(b.startsWith("/")&&!b.startsWith("//")){console.log("\uD83D\uDD04 Hard redirecting to stored URL after login:",b),window.location.href=b;return}}console.log("\uD83C\uDFE0 No valid redirect URL, going to account page"),window.location.href="/account"}else o(b.message),E({title:"Login Failed",description:b.message,variant:"destructive"})}catch(b){let a=b.message||"Failed to login";o(a),E({title:"Login Error",description:a,variant:"destructive"}),console.error("Error:",b),J(!1)}}};return(0,d.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:(0,d.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h2",{className:"mt-6 text-3xl font-extrabold",children:"Welcome Back"}),(0,d.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:"Sign in to your account"})]}),(0,d.jsx)(f.Zp,{className:"mt-8 p-8 shadow-xl bg-card",children:(0,d.jsx)(j.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:(0,d.jsxs)("form",{onSubmit:L,className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{className:"block text-sm font-medium mb-2",children:"Email"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(h.p,{type:"email",value:v.email,onChange:a=>{w({...v,email:a.target.value}),G.email&&K()},className:(0,u.cn)("pl-10",G.email&&"border-red-500"),required:!0,disabled:C}),(0,d.jsx)(k.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]}),G.email&&(0,d.jsx)("p",{className:"mt-1 text-sm text-destructive",children:G.email})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{className:"block text-sm font-medium mb-2",children:"Password"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(x,{value:v.password,onChange:a=>{w({...v,password:a.target.value}),G.password&&K()},className:(0,u.cn)("pl-10",G.password&&"border-red-500"),required:!0,disabled:C}),(0,d.jsx)(l.A,{className:"w-4 h-4 absolute left-3 top-3 text-muted-foreground"})]}),G.password&&(0,d.jsx)("p",{className:"mt-1 text-sm text-destructive",children:G.password})]}),c&&(0,d.jsx)("div",{className:"text-red-500 text-sm text-center",children:c}),(0,d.jsx)(g.$,{type:"submit",className:"w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors",disabled:C||I,children:C||I?(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(m.A,{className:"w-4 h-4 animate-spin"}),I?"Verifying...":"Signing In..."]}):(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(n.A,{className:"w-4 h-4"}),"Sign In"]})}),(0,d.jsxs)("div",{className:"text-xs text-center text-muted-foreground",children:["This site is protected by reCAPTCHA and the Google"," ",(0,d.jsx)("a",{href:"https://policies.google.com/privacy",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"Privacy Policy"})," ","and"," ",(0,d.jsx)("a",{href:"https://policies.google.com/terms",target:"_blank",rel:"noopener noreferrer",className:"text-primary hover:underline",children:"Terms of Service"})," ","apply."]}),(0,d.jsxs)("div",{className:"text-center text-sm space-y-2",children:[(0,d.jsx)("div",{children:(0,d.jsx)(p(),{href:"/forgot-password",className:"text-primary hover:text-primary/80 hover:underline transition-colors",children:"Forgot your password?"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-muted-foreground",children:"Don't have an account? "}),(0,d.jsx)(p(),{href:"/signup",className:"text-primary hover:text-primary/80 hover:underline transition-colors",children:"Sign up"})]})]})]})})})]})})}},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34544:(a,b,c)=>{Promise.resolve().then(c.bind(c,20859))},39390:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(96241);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46387:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\login\\page.tsx","default")},55192:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h,wL:()=>l});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b}));l.displayName="CardFooter"},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},68988:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"},71702:(a,b,c)=>{"use strict";c.d(b,{dj:()=>l});var d=c(43210);let e=0,f=new Map,g=a=>{if(f.has(a))return;let b=setTimeout(()=>{f.delete(a),j({type:"REMOVE_TOAST",toastId:a})},1e6);f.set(a,b)},h=[],i={toasts:[]};function j(a){i=((a,b)=>{switch(b.type){case"ADD_TOAST":return{...a,toasts:[b.toast,...a.toasts].slice(0,1)};case"UPDATE_TOAST":return{...a,toasts:a.toasts.map(a=>a.id===b.toast.id?{...a,...b.toast}:a)};case"DISMISS_TOAST":{let{toastId:c}=b;return c?g(c):a.toasts.forEach(a=>{g(a.id)}),{...a,toasts:a.toasts.map(a=>a.id===c||void 0===c?{...a,open:!1}:a)}}case"REMOVE_TOAST":if(void 0===b.toastId)return{...a,toasts:[]};return{...a,toasts:a.toasts.filter(a=>a.id!==b.toastId)}}})(i,a),h.forEach(a=>{a(i)})}function k({duration:a=2e3,...b}){let c=(e=(e+1)%Number.MAX_SAFE_INTEGER).toString(),d=()=>j({type:"DISMISS_TOAST",toastId:c});return j({type:"ADD_TOAST",toast:{...b,id:c,duration:a,open:!0,onOpenChange:a=>{a||d()}}}),setTimeout(()=>{d()},a),{id:c,dismiss:d,update:a=>j({type:"UPDATE_TOAST",toast:{...a,id:c}})}}function l(){let[a,b]=d.useState(i);return d.useEffect(()=>(h.push(b),()=>{let a=h.indexOf(b);a>-1&&h.splice(a,1)}),[a]),{...a,toast:k,dismiss:a=>j({type:"DISMISS_TOAST",toastId:a})}}k.success=(a,b)=>k({description:a,type:"success",duration:2e3,...b}),k.error=(a,b)=>k({description:a,type:"error",duration:2e3,...b}),k.warning=(a,b)=>k({description:a,type:"warning",duration:2e3,...b}),k.info=(a,b)=>k({description:a,type:"info",duration:2e3,...b})},73541:(a,b,c)=>{"use strict";c.d(b,{zK:()=>f}),c(60687);var d=c(16189),e=c(832);function f(a={}){let{redirectTo:b="/login",requireAuth:c=!0,redirectIfAuthenticated:g=!1}=a,{isLoggedIn:h,isLoading:i}=(0,e.J)();(0,d.useRouter)(),(0,d.usePathname)(),console.log("\uD83D\uDD27 useAuthGuard: Auth state check",{isLoggedIn:h,isLoading:i,requireAuth:c,redirectIfAuthenticated:g});let j=null;return i?(console.log("\uD83D\uDD27 useAuthGuard: Still loading, isAuthorized = null"),j=null):c&&!h?(console.log("\uD83D\uDD12 useAuthGuard: Authentication required but user not logged in, isAuthorized = false"),j=!1):g&&h?(console.log("\uD83D\uDD13 useAuthGuard: User authenticated but should be redirected, isAuthorized = false"),j=!1):(console.log("✅ useAuthGuard: User is authorized, isAuthorized = true"),j=!0),console.log("\uD83D\uDD27 useAuthGuard: Final result",{isAuthorized:j,isLoading:i,isLoggedIn:h}),{isAuthorized:j,isLoading:i,isLoggedIn:h}}},74075:a=>{"use strict";a.exports=require("zlib")},78122:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78148:(a,b,c)=>{"use strict";c.d(b,{b:()=>h});var d=c(43210),e=c(14163),f=c(60687),g=d.forwardRef((a,b)=>(0,f.jsx)(e.sG.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));g.displayName="Label";var h=g},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94735:a=>{"use strict";a.exports=require("events")},99696:(a,b,c)=>{Promise.resolve().then(c.bind(c,46387))},99891:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,1697,4773,9822],()=>b(b.s=10287));module.exports=c})();