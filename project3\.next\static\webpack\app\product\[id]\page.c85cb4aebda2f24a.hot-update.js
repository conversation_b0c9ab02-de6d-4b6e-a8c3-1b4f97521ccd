"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/product-details-client.tsx":
/*!*****************************************************!*\
  !*** ./app/product/[id]/product-details-client.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_modern_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/modern-toast */ \"(app-pages-browser)/./components/ui/modern-toast.tsx\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _contexts_currency_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/currency-context */ \"(app-pages-browser)/./contexts/currency-context.tsx\");\n/* harmony import */ var _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/settings-context */ \"(app-pages-browser)/./contexts/settings-context.tsx\");\n/* harmony import */ var _components_products_product_specifications__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/products/product-specifications */ \"(app-pages-browser)/./components/products/product-specifications.tsx\");\n/* harmony import */ var _components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/products/product-media-gallery */ \"(app-pages-browser)/./components/products/product-media-gallery.tsx\");\n/* harmony import */ var _components_ui_countdown__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/countdown */ \"(app-pages-browser)/./components/ui/countdown.tsx\");\n/* harmony import */ var _product_loading__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./product-loading */ \"(app-pages-browser)/./app/product/[id]/product-loading.tsx\");\n/* harmony import */ var _product_error__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./product-error */ \"(app-pages-browser)/./app/product/[id]/product-error.tsx\");\n/* harmony import */ var _components_ui_product_reviews_display__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/product-reviews-display */ \"(app-pages-browser)/./components/ui/product-reviews-display.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to construct image URL\nconst constructImageUrl = (attachmentUrl)=>{\n    if (!attachmentUrl) return \"/placeholder.svg?height=400&width=400\";\n    if (attachmentUrl.startsWith(\"http\")) {\n        return attachmentUrl;\n    }\n    const baseUrl = \"https://admin.codemedicalapps.com\";\n    // Normalize path (ensure it starts with exactly one slash)\n    let normalizedPath = attachmentUrl.startsWith(\"/\") ? attachmentUrl : \"/\".concat(attachmentUrl);\n    // Remove any double slashes in the path\n    normalizedPath = normalizedPath.replace(/\\/+/g, '/');\n    return \"\".concat(baseUrl).concat(normalizedPath);\n};\nfunction ProductDetailsClient(param) {\n    let { productId } = param;\n    var _product_Rating;\n    _s();\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__.useWishlist)();\n    const { rate } = (0,_contexts_currency_context__WEBPACK_IMPORTED_MODULE_11__.useCurrency)();\n    const { primaryColor, primaryTextColor } = (0,_contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings)();\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [activeImage, setActiveImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoLinks, setVideoLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideoIndex, setSelectedVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingToWishlist, setAddingToWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationType, setAnimationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ProductDetailsClient.useState\": ()=>{\n            // Initialize with first option selected for each attribute if none selected\n            const initial = {};\n            if (product === null || product === void 0 ? void 0 : product.AttributesJson) {\n                product.AttributesJson.forEach({\n                    \"ProductDetailsClient.useState\": (attr)=>{\n                        const key = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        initial[key] = true; // Select first option by default\n                    }\n                }[\"ProductDetailsClient.useState\"]);\n            }\n            return initial;\n        }\n    }[\"ProductDetailsClient.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetailsClient.useEffect\": ()=>{\n            fetchProduct();\n        }\n    }[\"ProductDetailsClient.useEffect\"], [\n        productId\n    ]);\n    const fetchProduct = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Try direct API call first, then fallback to proxy if CORS issues\n            const requestBody = {\n                requestParameters: {\n                    ProductId: Number.parseInt(productId, 10),\n                    recordValueJson: \"[]\"\n                }\n            };\n            console.log(\"Fetching product with ID:\", productId, \"Request body:\", requestBody);\n            let response;\n            try {\n                // Import MakeApiCallAsync for JWT token handling\n                const { MakeApiCallAsync } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api-helper */ \"(app-pages-browser)/./lib/api-helper.ts\"));\n                // Use API helper which automatically handles JWT tokens and removes UserID\n                response = await MakeApiCallAsync(\"get-product_detail\", null, requestBody, {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                }, \"POST\", true);\n                console.log(\"API helper response:\", response.data);\n            } catch (apiHelperError) {\n                console.log(\"API helper failed, trying proxy route:\", apiHelperError);\n                // Fallback to proxy route\n                response = await axios__WEBPACK_IMPORTED_MODULE_19__[\"default\"].post(\"/api/product-detail\", requestBody, {\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                console.log(\"Proxy API response:\", response.data);\n            }\n            if (response.data) {\n                // Check if response.data has a nested data property (from API proxy)\n                const responseData = response.data.data ? response.data : response.data;\n                if (responseData && responseData.data) {\n                    try {\n                        // Parse the response data\n                        const parsedData = JSON.parse(responseData.data);\n                        console.log(\"Parsed product data:\", parsedData);\n                        if (parsedData) {\n                            // The API might return an array with one item or a single object\n                            const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;\n                            if (productData) {\n                                // Ensure AttributesJson is properly parsed if it's a string\n                                if (productData.AttributesJson && typeof productData.AttributesJson === \"string\") {\n                                    try {\n                                        productData.AttributesJson = JSON.parse(productData.AttributesJson);\n                                    } catch (e) {\n                                        console.error(\"Error parsing AttributesJson:\", e);\n                                        productData.AttributesJson = [];\n                                    }\n                                } else if (!productData.AttributesJson) {\n                                    productData.AttributesJson = [];\n                                }\n                                console.log(\"Product data with attributes:\", productData);\n                                console.log(\"CategoryName in product data:\", productData.CategoryName);\n                                console.log(\"CategoryID in product data:\", productData.CategoryID);\n                                setProduct(productData);\n                                // Set active image\n                                if (productData.ProductImagesJson && productData.ProductImagesJson.length > 0) {\n                                    const primaryImage = productData.ProductImagesJson.find((img)=>img.IsPrimary) || productData.ProductImagesJson[0];\n                                    setActiveImage(constructImageUrl(primaryImage.AttachmentURL));\n                                }\n                                // Handle comma-separated video links\n                                if (productData.VideoLink) {\n                                    console.log(\"Video links found:\", productData.VideoLink);\n                                    const links = productData.VideoLink.split(\",\").map((link)=>link.trim());\n                                    const processedLinks = links.map((link)=>constructVideoUrl(link));\n                                    setVideoLinks(processedLinks);\n                                    setSelectedVideoIndex(0);\n                                }\n                                // Set initial quantity based on product minimum order quantity\n                                if (productData.OrderMinimumQuantity > 0) {\n                                    setQuantity(productData.OrderMinimumQuantity);\n                                }\n                            } else {\n                                console.error(\"No product data found in parsed response\");\n                                setError(\"Product with ID \".concat(productId, \" not found. Please check if this product exists.\"));\n                            }\n                        } else {\n                            console.error(\"Invalid product data format - parsedData is null/undefined\");\n                            setError(\"Invalid product data format\");\n                        }\n                    } catch (parseError) {\n                        console.error(\"Error parsing product data:\", parseError, \"Raw data:\", responseData.data);\n                        setError(\"Error parsing product data\");\n                    }\n                } else {\n                    console.error(\"No data property in API response:\", response.data);\n                    setError(\"No data in API response\");\n                }\n            } else {\n                console.error(\"Empty response from API\");\n                setError(\"Empty response from server\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching product:\", error);\n            // More detailed error handling\n            if (error.response) {\n                var _error_response_data;\n                // Server responded with error status\n                console.error(\"Server error:\", error.response.status, error.response.data);\n                setError(\"Server error: \".concat(error.response.status, \" - \").concat(((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Unknown error\"));\n            } else if (error.request) {\n                // Request was made but no response received\n                console.error(\"Network error:\", error.request);\n                setError(\"Network error - please check your connection\");\n            } else {\n                // Something else happened\n                console.error(\"Request setup error:\", error.message);\n                setError(\"Error: \".concat(error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const constructVideoUrl = (videoLink)=>{\n        if (!videoLink) return \"\";\n        if (videoLink.includes(\"youtube.com\") || videoLink.includes(\"youtu.be\")) {\n            return videoLink;\n        }\n        // For MP4 videos, use a proxy URL to handle CORS\n        if (videoLink.startsWith(\"http\")) {\n            return \"/api/video-proxy?url=\".concat(encodeURIComponent(videoLink));\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedVideoLink = videoLink.startsWith(\"/\") ? videoLink : \"/\".concat(videoLink);\n        return \"/api/video-proxy?url=\".concat(encodeURIComponent(\"\".concat(baseUrl).concat(normalizedVideoLink)));\n    };\n    // Group attributes by ProductAttributeID\n    const groupedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[groupedAttributes]\": ()=>{\n            if (!(product === null || product === void 0 ? void 0 : product.AttributesJson)) return {};\n            return product.AttributesJson.reduce({\n                \"ProductDetailsClient.useMemo[groupedAttributes]\": (groups, attr)=>{\n                    const groupId = attr.ProductAttributeID;\n                    if (!groups[groupId]) {\n                        groups[groupId] = [];\n                    }\n                    groups[groupId].push(attr);\n                    return groups;\n                }\n            }[\"ProductDetailsClient.useMemo[groupedAttributes]\"], {});\n        }\n    }[\"ProductDetailsClient.useMemo[groupedAttributes]\"], [\n        product === null || product === void 0 ? void 0 : product.AttributesJson\n    ]);\n    // Handle attribute selection with conditional behavior\n    const handleAttributeChange = (attr, isChecked, isRadioGroup)=>{\n        setSelectedAttributes((prev)=>{\n            const newState = {\n                ...prev\n            };\n            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n            // For radio groups, uncheck all other attributes in the same group\n            if (isRadioGroup && isChecked) {\n                Object.keys(prev).forEach((key)=>{\n                    if (key.startsWith(\"\".concat(attr.ProductAttributeID, \"_\")) && key !== attrKey) {\n                        newState[key] = false;\n                    }\n                });\n            }\n            // Set the selected attribute\n            // For checkboxes, toggle the state\n            // For radio buttons, always set to true (since we already unset others if needed)\n            newState[attrKey] = isRadioGroup ? true : !prev[attrKey];\n            return newState;\n        });\n    };\n    // Render price with all price-related information\n    const renderPrice = ()=>{\n        if (!product) return null;\n        const showDiscount = product.DiscountPrice && product.DiscountPrice < product.Price;\n        const adjustedPrice = calculateAdjustedPrice();\n        const showAdjustedPrice = adjustedPrice !== product.Price && adjustedPrice !== (product.DiscountPrice || product.Price);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-baseline gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-3xl font-bold text-primary\",\n                            children: [\n                                \"$\",\n                                showDiscount ? (product.DiscountPrice || 0).toFixed(2) : adjustedPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 13\n                        }, this),\n                        showAdjustedPrice && !showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 13\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded\",\n                            children: [\n                                Math.round((product.Price - (product.DiscountPrice || 0)) / product.Price * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 9\n                }, this),\n                product.PriceIQD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-lg font-medium text-gray-600\",\n                    children: [\n                        product.PriceIQD.toLocaleString(),\n                        \" IQD\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 472,\n                    columnNumber: 11\n                }, this),\n                product.PointNo && product.PointNo > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                        children: [\n                            \"Buy & Earn \",\n                            product.PointNo,\n                            \" $ credit\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 479,\n                    columnNumber: 11\n                }, this),\n                product.OldPrice && product.OldPrice > product.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"line-through\",\n                            children: [\n                                \"$\",\n                                product.OldPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-green-600\",\n                            children: [\n                                Math.round((product.OldPrice - (product.DiscountPrice || product.Price)) / product.OldPrice * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 433,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate adjusted price based on selected attributes\n    const calculateAdjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProductDetailsClient.useCallback[calculateAdjustedPrice]\": ()=>{\n            if (!product) return 0;\n            let adjustedPrice = product.Price;\n            if (product.AttributesJson && product.AttributesJson.length > 0) {\n                product.AttributesJson.forEach({\n                    \"ProductDetailsClient.useCallback[calculateAdjustedPrice]\": (attr)=>{\n                        const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        if (selectedAttributes[attrKey] && typeof attr.PriceAdjustment === \"number\" && typeof attr.PriceAdjustmentType === \"number\") {\n                            switch(attr.PriceAdjustmentType){\n                                case 1:\n                                    adjustedPrice += attr.PriceAdjustment;\n                                    break;\n                                case 2:\n                                    adjustedPrice += product.Price * attr.PriceAdjustment / 100;\n                                    break;\n                            }\n                        }\n                    }\n                }[\"ProductDetailsClient.useCallback[calculateAdjustedPrice]\"]);\n            }\n            return Math.max(0, adjustedPrice); // Ensure price doesn't go below 0\n        }\n    }[\"ProductDetailsClient.useCallback[calculateAdjustedPrice]\"], [\n        product,\n        selectedAttributes\n    ]);\n    const adjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[adjustedPrice]\": ()=>calculateAdjustedPrice()\n    }[\"ProductDetailsClient.useMemo[adjustedPrice]\"], [\n        calculateAdjustedPrice\n    ]);\n    // Render product badges\n    const renderBadges = ()=>{\n        if (!product) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute top-4 left-4 z-10 flex flex-col gap-2\",\n            children: [\n                product.IsDiscountAllowed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-red-500 hover:bg-red-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"SALE\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 545,\n                    columnNumber: 11\n                }, this),\n                product.MarkAsNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-green-500 hover:bg-green-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"NEW\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 550,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 543,\n            columnNumber: 7\n        }, this);\n    };\n    // Combine images and videos into a single media array for the gallery\n    const mediaItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[mediaItems]\": ()=>{\n            var _product_ProductImagesJson;\n            const items = [];\n            // Add product images\n            if (product === null || product === void 0 ? void 0 : (_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : _product_ProductImagesJson.length) {\n                product.ProductImagesJson.forEach({\n                    \"ProductDetailsClient.useMemo[mediaItems]\": (img)=>{\n                        items.push({\n                            type: \"image\",\n                            url: constructImageUrl(img.AttachmentURL),\n                            alt: (product === null || product === void 0 ? void 0 : product.ProductName) || \"Product image\",\n                            thumbnail: constructImageUrl(img.AttachmentURL)\n                        });\n                    }\n                }[\"ProductDetailsClient.useMemo[mediaItems]\"]);\n            }\n            // Add videos\n            videoLinks.forEach({\n                \"ProductDetailsClient.useMemo[mediaItems]\": (videoUrl, index)=>{\n                    items.push({\n                        type: \"video\",\n                        url: videoUrl,\n                        alt: \"\".concat((product === null || product === void 0 ? void 0 : product.ProductName) || \"Product\", \" - Video \").concat(index + 1)\n                    });\n                }\n            }[\"ProductDetailsClient.useMemo[mediaItems]\"]);\n            return items;\n        }\n    }[\"ProductDetailsClient.useMemo[mediaItems]\"], [\n        product,\n        videoLinks,\n        activeImage\n    ]);\n    const animateCounter = (type)=>{\n        setAnimationType(type);\n        setIsAnimating(true);\n        setTimeout(()=>setIsAnimating(false), 300);\n    };\n    const incrementQuantity = ()=>{\n        if (product) {\n            const maxQuantity = product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity;\n            if (quantity < maxQuantity) {\n                setQuantity((prev)=>prev + 1);\n                animateCounter(\"increment\");\n            } else {\n                // Visual feedback when max quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Maximum quantity of \".concat(maxQuantity, \" reached\"));\n            }\n        }\n    };\n    const decrementQuantity = ()=>{\n        if (product) {\n            const minQuantity = product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1;\n            if (quantity > minQuantity) {\n                setQuantity((prev)=>prev - 1);\n                animateCounter(\"decrement\");\n            } else {\n                // Visual feedback when min quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Minimum quantity is \".concat(minQuantity));\n            }\n        }\n    };\n    // Dynamic button styles based on state\n    const getButtonStyles = (type)=>{\n        const baseStyles = \"flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 rounded-full transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-offset-1\";\n        const disabledStyles = \"bg-gray-100 text-gray-400 cursor-not-allowed\";\n        if (type === \"increment\") {\n            const isMax = product && quantity >= (product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity);\n            return \"\".concat(baseStyles, \" \").concat(isMax ? disabledStyles : \"bg-primary text-white hover:bg-primary/90 focus:ring-primary/50\");\n        } else {\n            const isMin = product && quantity <= (product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1);\n            return \"\".concat(baseStyles, \" \").concat(isMin ? disabledStyles : \"bg-primary text-white hover:bg-primary/90 focus:ring-primary/50\");\n        }\n    };\n    // Counter display with animation\n    const CounterDisplay = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center justify-center w-10 sm:w-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm sm:text-base font-medium transition-all duration-200 \".concat(isAnimating ? \"scale-125 text-primary\" : \"scale-100\"),\n                    children: quantity\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 657,\n                    columnNumber: 7\n                }, this),\n                isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"absolute text-xs font-bold text-primary transition-all duration-200 \".concat(animationType === \"increment\" ? \"-top-4 sm:-top-5\" : \"top-4 sm:top-5\"),\n                    children: animationType === \"increment\" ? \"+1\" : \"-1\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 665,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 656,\n            columnNumber: 5\n        }, this);\n    // Early return if product is not loaded yet\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_loading__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 680,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            error: error,\n            retry: fetchProduct\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 684,\n            columnNumber: 12\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Product Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 690,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: [\n                        'The product with ID \"',\n                        productId,\n                        '\" could not be found. It may not exist in the database.'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 691,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/products\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"View All Products\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 696,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Check the products list to find available product IDs\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 702,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 695,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 689,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.Breadcrumb, {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbList, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 716,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 720,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products?category=\".concat(product.CategoryID || \"all\"),\n                                    children: product.CategoryName || \"Medical Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 722,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 728,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 734,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbPage, {\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 735,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 714,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 713,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden mb-6\",\n                children: (()=>{\n                    // Only show countdown if both sale start and end dates are provided\n                    if (!product.SellStartDatetimeUTC || !product.SellEndDatetimeUTC) {\n                        return null;\n                    }\n                    const now = new Date();\n                    const saleStart = new Date(product.SellStartDatetimeUTC);\n                    const saleEnd = new Date(product.SellEndDatetimeUTC);\n                    // Check if sale is currently active or upcoming\n                    const isSaleActive = now >= saleStart && now <= saleEnd;\n                    const isSaleUpcoming = now < saleStart;\n                    const isSaleExpired = now > saleEnd;\n                    // Don't show timer if sale has expired\n                    if (isSaleExpired) {\n                        return null;\n                    }\n                    // Determine the countdown target date\n                    const countdownEndDate = isSaleUpcoming ? product.SellStartDatetimeUTC : product.SellEndDatetimeUTC;\n                    // Determine the message based on sale state\n                    let timerMessage = \"🔥 Limited Time Sale!\";\n                    let urgencyMessage = \"Sale ends soon - grab yours now!\";\n                    if (isSaleUpcoming) {\n                        timerMessage = \"⏰ Sale Starts Soon!\";\n                        urgencyMessage = \"Get ready for an amazing deal!\";\n                    } else if (isSaleActive) {\n                        timerMessage = \"🔥 Limited Time Sale!\";\n                        urgencyMessage = \"Sale ends soon - don't miss out!\";\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-xl\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-2 mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 text-red-500 animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 781,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-bold text-red-600\",\n                                        children: timerMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 780,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_countdown__WEBPACK_IMPORTED_MODULE_15__.Countdown, {\n                                    endDate: countdownEndDate,\n                                    className: \"transform hover:scale-105 transition-transform duration-200\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 788,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 787,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-center text-xs font-medium text-gray-700\",\n                                children: urgencyMessage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 794,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 779,\n                        columnNumber: 13\n                    }, this);\n                })()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 742,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_14__.ProductMediaGallery, {\n                            media: mediaItems,\n                            className: \"w-full rounded-lg overflow-hidden\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 805,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 804,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:w-1/2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-2\",\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 813,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            ...Array(5)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(i < Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 817,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 ml-2\",\n                                        children: [\n                                            \"(\",\n                                            product.Rating || 0,\n                                            \") \",\n                                            product.TotalReviews || 0,\n                                            \" reviews\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 829,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 11\n                            }, this),\n                            renderPrice(),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:block\",\n                                children: (()=>{\n                                    // Only show countdown if both sale start and end dates are provided\n                                    if (!product.SellStartDatetimeUTC || !product.SellEndDatetimeUTC) {\n                                        return null;\n                                    }\n                                    const now = new Date();\n                                    const saleStart = new Date(product.SellStartDatetimeUTC);\n                                    const saleEnd = new Date(product.SellEndDatetimeUTC);\n                                    // Check if sale is currently active or upcoming\n                                    const isSaleActive = now >= saleStart && now <= saleEnd;\n                                    const isSaleUpcoming = now < saleStart;\n                                    const isSaleExpired = now > saleEnd;\n                                    // Don't show timer if sale has expired\n                                    if (isSaleExpired) {\n                                        return null;\n                                    }\n                                    // Determine the countdown target date\n                                    const countdownEndDate = isSaleUpcoming ? product.SellStartDatetimeUTC : product.SellEndDatetimeUTC;\n                                    // Determine the message based on sale state\n                                    let timerMessage = \"🔥 Limited Time Sale!\";\n                                    let urgencyMessage = \"Sale ends soon - grab yours now!\";\n                                    if (isSaleUpcoming) {\n                                        timerMessage = \"⏰ Sale Starts Soon!\";\n                                        urgencyMessage = \"Get ready for an amazing deal!\";\n                                    } else if (isSaleActive) {\n                                        timerMessage = \"🔥 Limited Time Sale!\";\n                                        urgencyMessage = \"Sale ends soon - don't miss out!\";\n                                    }\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 p-4 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center gap-3 mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-6 w-6 text-red-500 animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 877,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-bold text-red-600\",\n                                                        children: timerMessage\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 878,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-center mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_countdown__WEBPACK_IMPORTED_MODULE_15__.Countdown, {\n                                                    endDate: countdownEndDate,\n                                                    className: \"transform hover:scale-105 transition-transform duration-200\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 884,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 883,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center text-sm font-medium text-gray-700\",\n                                                children: urgencyMessage\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 890,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 875,\n                                        columnNumber: 17\n                                    }, this);\n                                })()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 838,\n                                columnNumber: 11\n                            }, this),\n                            product.ShortDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-sm max-w-none mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: product.ShortDescription\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 901,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 900,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 border-t border-gray-200 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 909,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Choose your preferences from the options below.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 912,\n                                        columnNumber: 13\n                                    }, this),\n                                    Object.entries(groupedAttributes).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: Object.entries(groupedAttributes).map((param)=>{\n                                            let [groupId, attributes] = param;\n                                            var _attributes_, _attributes_1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            ((_attributes_ = attributes[0]) === null || _attributes_ === void 0 ? void 0 : _attributes_.DisplayName) || ((_attributes_1 = attributes[0]) === null || _attributes_1 === void 0 ? void 0 : _attributes_1.AttributeName),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 920,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 pl-4\",\n                                                        children: attributes.map((attr)=>{\n                                                            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                                                            const isSelected = !!selectedAttributes[attrKey];\n                                                            const isRadioGroup = attributes.length > 1;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center h-5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: isRadioGroup ? \"radio\" : \"checkbox\",\n                                                                            id: \"attr-\".concat(attrKey),\n                                                                            name: \"attr-group-\".concat(groupId),\n                                                                            className: \"h-4 w-4 \".concat(isRadioGroup ? \"rounded-full\" : \"rounded\", \" border-gray-300 text-primary focus:ring-primary\"),\n                                                                            checked: isSelected,\n                                                                            onChange: (e)=>handleAttributeChange(attr, e.target.checked, isRadioGroup)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                            lineNumber: 934,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 933,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-3 text-sm\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"attr-\".concat(attrKey),\n                                                                            className: \"font-medium \".concat(isSelected ? \"text-primary\" : \"text-gray-700\"),\n                                                                            children: [\n                                                                                attr.AttributeValueText,\n                                                                                (attr.PriceAdjustment || attr.PriceAdjustment === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-2 text-sm font-normal text-green-600\",\n                                                                                    children: [\n                                                                                        \"(\",\n                                                                                        attr.PriceAdjustmentType === 1 ? \"+\" : \"\",\n                                                                                        \"$\",\n                                                                                        attr.PriceAdjustment,\n                                                                                        \" \",\n                                                                                        attr.PriceAdjustmentType === 2 ? \"%\" : \"\",\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                                    lineNumber: 963,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                            lineNumber: 952,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 951,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, attrKey, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 932,\n                                                                columnNumber: 29\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 925,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, \"attr-group-\".concat(groupId), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 916,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No additional product details available.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 986,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 908,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Quantity:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 995,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between sm:justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: decrementQuantity,\n                                                            className: getButtonStyles(\"decrement\"),\n                                                            disabled: quantity <= (product.OrderMinimumQuantity || 1),\n                                                            \"aria-label\": \"Decrease quantity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"h-4 w-4 sm:h-5 sm:w-5\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                fill: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                    lineNumber: 1010,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1004,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 998,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CounterDisplay, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1018,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: incrementQuantity,\n                                                            className: getButtonStyles(\"increment\"),\n                                                            disabled: product.OrderMaximumQuantity > 0 ? quantity >= Math.min(product.OrderMaximumQuantity, product.StockQuantity) : quantity >= product.StockQuantity,\n                                                            \"aria-label\": \"Increase quantity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"h-4 w-4 sm:h-5 sm:w-5\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                fill: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                    lineNumber: 1040,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1034,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1020,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 997,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row sm:items-center gap-2 sm:ml-4\",\n                                                    children: [\n                                                        product.OrderMinimumQuantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Min: \",\n                                                                product.OrderMinimumQuantity\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1051,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.OrderMaximumQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Max:\",\n                                                                \" \",\n                                                                Math.min(product.OrderMaximumQuantity, product.StockQuantity)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1057,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1049,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 996,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 994,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 993,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-3 sm:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none text-sm sm:text-base\",\n                                        disabled: product.StockQuantity <= 0 || addingToCart,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToCart(true);\n                                            try {\n                                                var _product_ProductImagesJson_, _product_ProductImagesJson;\n                                                // Get the first product image or use a placeholder\n                                                const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0 ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL) : \"/placeholder.jpg\";\n                                                // Get selected attributes\n                                                const selectedAttrs = (product.AttributesJson || []).filter((attr)=>selectedAttributes[\"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID)]);\n                                                // Add to cart using the cart context with attributes and adjusted price\n                                                cart.addToCart({\n                                                    id: product.ProductId,\n                                                    name: product.ProductName,\n                                                    price: product.DiscountPrice || product.Price,\n                                                    discountPrice: product.DiscountPrice,\n                                                    image: productImage,\n                                                    originalPrice: product.Price\n                                                }, quantity, selectedAttrs, product.PriceIQD, rate // Pass currency rate as the fifth parameter\n                                                );\n                                                // Show modern toast notification\n                                                (0,_components_ui_modern_toast__WEBPACK_IMPORTED_MODULE_8__.showModernAddToCartToast)({\n                                                    productName: product.ProductName,\n                                                    quantity,\n                                                    productImage: ((_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : (_product_ProductImagesJson_ = _product_ProductImagesJson[0]) === null || _product_ProductImagesJson_ === void 0 ? void 0 : _product_ProductImagesJson_.AttachmentURL) || \"/placeholder.svg\",\n                                                    onViewCart: ()=>{\n                                                        window.location.href = \"/cart\";\n                                                    }\n                                                });\n                                            } catch (error) {\n                                                console.error(\"Error adding to cart:\", error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Failed to add product to cart. Please try again.\");\n                                            } finally{\n                                                setAddingToCart(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1137,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1139,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: addingToCart ? \"Adding...\" : \"Add to Cart\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1141,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1073,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none text-sm sm:text-base sm:flex-initial sm:min-w-[120px]\",\n                                        disabled: addingToWishlist,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToWishlist(true);\n                                            try {\n                                                // Check if product is already in wishlist\n                                                const isAlreadyInWishlist = wishlist.isInWishlist(product.ProductId);\n                                                if (isAlreadyInWishlist) {\n                                                    // Remove from wishlist if already there\n                                                    wishlist.removeFromWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(product.ProductName, \" removed from wishlist\"));\n                                                } else {\n                                                    var _product_ProductImagesJson_, _product_ProductImagesJson;\n                                                    // Add to wishlist\n                                                    const productUrl = \"/product/\".concat(product.ProductId);\n                                                    const imageUrl = ((_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : (_product_ProductImagesJson_ = _product_ProductImagesJson[0]) === null || _product_ProductImagesJson_ === void 0 ? void 0 : _product_ProductImagesJson_.AttachmentURL) || \"/placeholder.svg\";\n                                                    const price = product.DiscountPrice || product.Price;\n                                                    wishlist.addToWishlist(product.ProductId, product.ProductName, productUrl, imageUrl, price);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(product.ProductName, \" added to wishlist\"));\n                                                }\n                                            } catch (error) {\n                                                console.error(\"Error updating wishlist:\", error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Failed to update wishlist. Please try again.\");\n                                            } finally{\n                                                setAddingToWishlist(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1189,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-5 w-5\",\n                                                fill: product && wishlist.isInWishlist(product.ProductId) ? \"currentColor\" : \"none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1191,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: addingToWishlist ? \"Updating...\" : product && wishlist.isInWishlist(product.ProductId) ? \"Remove\" : \"Wishlist\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1200,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground text-sm sm:text-base sm:flex-initial sm:min-w-[100px]\",\n                                        onClick: ()=>{\n                                            if (navigator.share) {\n                                                navigator.share({\n                                                    title: (product === null || product === void 0 ? void 0 : product.MetaTitle) || (product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    text: (product === null || product === void 0 ? void 0 : product.MetaDescription) || \"Check out this product: \".concat(product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    url: window.location.href\n                                                }).catch((err)=>console.error(\"Error sharing:\", err));\n                                            } else {\n                                                // Fallback - copy to clipboard\n                                                navigator.clipboard.writeText(window.location.href);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Product link copied to clipboard\");\n                                            }\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1231,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: \"Share\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1232,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1210,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1071,\n                                columnNumber: 11\n                            }, this),\n                            product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-3\",\n                                        children: \"Product Tags\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs bg-white/70 hover:bg-white transition-colors\",\n                                                children: keyword.trim()\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1242,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1240,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1238,\n                                columnNumber: 13\n                            }, this),\n                            product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1258,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"About This Product\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 leading-relaxed\",\n                                        children: product.MetaDescription\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1256,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 812,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 802,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                    defaultValue: \"description\",\n                    className: \"w-full\",\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                            className: \"grid w-full grid-cols-3 mb-6 gap-2 bg-transparent p-0 h-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"description\",\n                                    className: \"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === \"description\" ? primaryColor : \"rgb(209 213 219)\",\n                                        color: activeTab === \"description\" ? primaryTextColor : \"rgb(55 65 81)\",\n                                        transform: activeTab === \"description\" ? \"scale(1.05)\" : \"scale(1)\",\n                                        boxShadow: activeTab === \"description\" ? \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\" : \"none\",\n                                        borderColor: activeTab === \"description\" ? primaryColor : \"transparent\"\n                                    },\n                                    children: \"Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"reviews\",\n                                    className: \"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === \"reviews\" ? primaryColor : \"rgb(209 213 219)\",\n                                        color: activeTab === \"reviews\" ? primaryTextColor : \"rgb(55 65 81)\",\n                                        transform: activeTab === \"reviews\" ? \"scale(1.05)\" : \"scale(1)\",\n                                        boxShadow: activeTab === \"reviews\" ? \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\" : \"none\",\n                                        borderColor: activeTab === \"reviews\" ? primaryColor : \"transparent\"\n                                    },\n                                    children: \"Specifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1302,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"shipping\",\n                                    className: \"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === \"shipping\" ? primaryColor : \"rgb(209 213 219)\",\n                                        color: activeTab === \"shipping\" ? primaryTextColor : \"rgb(55 65 81)\",\n                                        transform: activeTab === \"shipping\" ? \"scale(1.05)\" : \"scale(1)\",\n                                        boxShadow: activeTab === \"shipping\" ? \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\" : \"none\",\n                                        borderColor: activeTab === \"shipping\" ? primaryColor : \"transparent\"\n                                    },\n                                    children: \"Reviews\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1321,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"description\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Product Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1348,\n                                        columnNumber: 15\n                                    }, this),\n                                    product.FullDescription ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none\",\n                                        dangerouslySetInnerHTML: {\n                                            __html: product.FullDescription\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1350,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: product.ShortDescription || \"This is a high-quality medical product designed to meet professional standards.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1356,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900 mb-2\",\n                                                                children: \"Key Features\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1362,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-sm text-gray-700 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Professional grade quality\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1366,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Durable construction\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1367,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Easy to use\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1368,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Reliable performance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1369,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1365,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1361,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900 mb-2\",\n                                                                children: \"Benefits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1373,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-sm text-gray-700 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Enhanced efficiency\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1377,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Cost-effective solution\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1378,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Long-lasting durability\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1379,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Professional results\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1380,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1376,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1372,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1360,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1355,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1347,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1343,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"reviews\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Product Specifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1394,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_specifications__WEBPACK_IMPORTED_MODULE_13__.ProductSpecifications, {\n                                        attributes: product.AttributesJson || []\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1397,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1393,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1389,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"shipping\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Customer Reviews\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1408,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            1,\n                                                            2,\n                                                            3,\n                                                            4,\n                                                            5\n                                                        ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"w-6 h-6 \".concat(star <= Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                            }, star, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1413,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1411,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: ((_product_Rating = product.Rating) === null || _product_Rating === void 0 ? void 0 : _product_Rating.toFixed(1)) || \"0.0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1424,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            \"out of 5\",\n                                                            product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \" \",\n                                                                    \"• \",\n                                                                    product.TotalReviews,\n                                                                    \" review\",\n                                                                    product.TotalReviews !== 1 ? \"s\" : \"\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1429,\n                                                                columnNumber: 23\n                                                            }, this) : null\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1423,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1410,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_product_reviews_display__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    productId: product.ProductId,\n                                                    showTitle: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1439,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1438,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1409,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1407,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1403,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 1271,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 1270,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n        lineNumber: 711,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDetailsClient, \"fhF87DY2zm3u0DvUJErcoKedGAA=\", false, function() {\n    return [\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__.useWishlist,\n        _contexts_currency_context__WEBPACK_IMPORTED_MODULE_11__.useCurrency,\n        _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings\n    ];\n});\n_c = ProductDetailsClient;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductDetailsClient);\nvar _c;\n$RefreshReg$(_c, \"ProductDetailsClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9wcm9kdWN0L1tpZF0vcHJvZHVjdC1kZXRhaWxzLWNsaWVudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFa0U7QUFFckM7QUFDSDtBQWNKO0FBQzBCO0FBUVo7QUFDNEM7QUFDbEM7QUFDZjtBQUN5QztBQUN0QjtBQUNRO0FBQ0E7QUFDQTtBQUMyQjtBQUNIO0FBQzVCO0FBQ1A7QUFDSjtBQUNpQztBQXlFNUUseUNBQXlDO0FBQ3pDLE1BQU1xQyxvQkFBb0IsQ0FBQ0M7SUFDekIsSUFBSSxDQUFDQSxlQUFlLE9BQU87SUFFM0IsSUFBSUEsY0FBY0MsVUFBVSxDQUFDLFNBQVM7UUFDcEMsT0FBT0Q7SUFDVDtJQUVBLE1BQU1FLFVBQVU7SUFDaEIsMkRBQTJEO0lBQzNELElBQUlDLGlCQUFpQkgsY0FBY0MsVUFBVSxDQUFDLE9BQzFDRCxnQkFDQSxJQUFrQixPQUFkQTtJQUNSLHdDQUF3QztJQUN4Q0csaUJBQWlCQSxlQUFlQyxPQUFPLENBQUMsUUFBUTtJQUVoRCxPQUFPLEdBQWFELE9BQVZELFNBQXlCLE9BQWZDO0FBQ3RCO0FBRUEsU0FBU0UscUJBQXFCLEtBQWtDO1FBQWxDLEVBQUVDLFNBQVMsRUFBdUIsR0FBbEM7UUEwd0NQQzs7SUF6d0NyQixNQUFNQyxPQUFPbkIsK0RBQU9BO0lBQ3BCLE1BQU1vQixXQUFXbkIsd0VBQVdBO0lBQzVCLE1BQU0sRUFBRW9CLElBQUksRUFBRSxHQUFHbkIsd0VBQVdBO0lBQzVCLE1BQU0sRUFBRW9CLFlBQVksRUFBRUMsZ0JBQWdCLEVBQUUsR0FBR3BCLHdFQUFXQTtJQUV0RCxNQUFNLENBQUNlLFNBQVNNLFdBQVcsR0FBR25ELCtDQUFRQSxDQUFpQjtJQUN2RCxNQUFNLENBQUNvRCxTQUFTQyxXQUFXLEdBQUdyRCwrQ0FBUUEsQ0FBVTtJQUNoRCxNQUFNLENBQUNzRCxVQUFVQyxZQUFZLEdBQUd2RCwrQ0FBUUEsQ0FBUztJQUNqRCxNQUFNLENBQUN3RCxhQUFhQyxlQUFlLEdBQUd6RCwrQ0FBUUEsQ0FBUztJQUN2RCxNQUFNLENBQUMwRCxZQUFZQyxjQUFjLEdBQUczRCwrQ0FBUUEsQ0FBVyxFQUFFO0lBQ3pELE1BQU0sQ0FBQzRELG9CQUFvQkMsc0JBQXNCLEdBQUc3RCwrQ0FBUUEsQ0FBUztJQUNyRSxNQUFNLENBQUM4RCxjQUFjQyxnQkFBZ0IsR0FBRy9ELCtDQUFRQSxDQUFVO0lBQzFELE1BQU0sQ0FBQ2dFLGtCQUFrQkMsb0JBQW9CLEdBQUdqRSwrQ0FBUUEsQ0FBVTtJQUNsRSxNQUFNLENBQUNrRSxPQUFPQyxTQUFTLEdBQUduRSwrQ0FBUUEsQ0FBZ0I7SUFDbEQsTUFBTSxDQUFDb0UsV0FBV0MsYUFBYSxHQUFHckUsK0NBQVFBLENBQVM7SUFDbkQsTUFBTSxDQUFDc0UsYUFBYUMsZUFBZSxHQUFHdkUsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDd0UsZUFBZUMsaUJBQWlCLEdBQUd6RSwrQ0FBUUEsQ0FFaEQ7SUFDRixNQUFNLENBQUMwRSxvQkFBb0JDLHNCQUFzQixHQUFHM0UsK0NBQVFBO3lDQUV6RDtZQUNELDRFQUE0RTtZQUM1RSxNQUFNNEUsVUFBc0MsQ0FBQztZQUM3QyxJQUFJL0Isb0JBQUFBLDhCQUFBQSxRQUFTZ0MsY0FBYyxFQUFFO2dCQUMzQmhDLFFBQVFnQyxjQUFjLENBQUNDLE9BQU87cURBQUMsQ0FBQ0M7d0JBQzlCLE1BQU1DLE1BQU0sR0FBOEJELE9BQTNCQSxLQUFLRSxrQkFBa0IsRUFBQyxLQUF5QixPQUF0QkYsS0FBS0csZ0JBQWdCO3dCQUMvRE4sT0FBTyxDQUFDSSxJQUFJLEdBQUcsTUFBTSxpQ0FBaUM7b0JBQ3hEOztZQUNGO1lBQ0EsT0FBT0o7UUFDVDs7SUFFQTNFLGdEQUFTQTswQ0FBQztZQUNSa0Y7UUFDRjt5Q0FBRztRQUFDdkM7S0FBVTtJQUVkLE1BQU11QyxlQUFlO1FBQ25COUIsV0FBVztRQUNYYyxTQUFTO1FBQ1QsSUFBSTtZQUNGLG1FQUFtRTtZQUNuRSxNQUFNaUIsY0FBYztnQkFDbEJDLG1CQUFtQjtvQkFDakJDLFdBQVdDLE9BQU9DLFFBQVEsQ0FBQzVDLFdBQVc7b0JBQ3RDNkMsaUJBQWlCO2dCQUNuQjtZQUNGO1lBRUFDLFFBQVFDLEdBQUcsQ0FDVCw2QkFDQS9DLFdBQ0EsaUJBQ0F3QztZQUdGLElBQUlRO1lBQ0osSUFBSTtnQkFDRixpREFBaUQ7Z0JBQ2pELE1BQU0sRUFBRUMsZ0JBQWdCLEVBQUUsR0FBRyxNQUFNLHVKQUEwQjtnQkFFN0QsMkVBQTJFO2dCQUMzRUQsV0FBVyxNQUFNQyxpQkFDZixzQkFDQSxNQUNBVCxhQUNBO29CQUNFVSxRQUFRO29CQUNSLGdCQUFnQjtnQkFDbEIsR0FDQSxRQUNBO2dCQUVGSixRQUFRQyxHQUFHLENBQUMsd0JBQXdCQyxTQUFTRyxJQUFJO1lBQ25ELEVBQUUsT0FBT0MsZ0JBQWdCO2dCQUN2Qk4sUUFBUUMsR0FBRyxDQUFDLDBDQUEwQ0s7Z0JBQ3RELDBCQUEwQjtnQkFDMUJKLFdBQVcsTUFBTXZGLDhDQUFLQSxDQUFDNEYsSUFBSSxDQUFDLHVCQUF1QmIsYUFBYTtvQkFDOURjLFNBQVM7d0JBQ1BKLFFBQVE7d0JBQ1IsZ0JBQWdCO29CQUNsQjtnQkFDRjtnQkFDQUosUUFBUUMsR0FBRyxDQUFDLHVCQUF1QkMsU0FBU0csSUFBSTtZQUNsRDtZQUVBLElBQUlILFNBQVNHLElBQUksRUFBRTtnQkFDakIscUVBQXFFO2dCQUNyRSxNQUFNSSxlQUFlUCxTQUFTRyxJQUFJLENBQUNBLElBQUksR0FBR0gsU0FBU0csSUFBSSxHQUFHSCxTQUFTRyxJQUFJO2dCQUV2RSxJQUFJSSxnQkFBZ0JBLGFBQWFKLElBQUksRUFBRTtvQkFDckMsSUFBSTt3QkFDRiwwQkFBMEI7d0JBQzFCLE1BQU1LLGFBQWFDLEtBQUtDLEtBQUssQ0FBQ0gsYUFBYUosSUFBSTt3QkFDL0NMLFFBQVFDLEdBQUcsQ0FBQyx3QkFBd0JTO3dCQUVwQyxJQUFJQSxZQUFZOzRCQUNkLGlFQUFpRTs0QkFDakUsTUFBTUcsY0FBY0MsTUFBTUMsT0FBTyxDQUFDTCxjQUM5QkEsVUFBVSxDQUFDLEVBQUUsR0FDYkE7NEJBRUosSUFBSUcsYUFBYTtnQ0FDZiw0REFBNEQ7Z0NBQzVELElBQ0VBLFlBQVkxQixjQUFjLElBQzFCLE9BQU8wQixZQUFZMUIsY0FBYyxLQUFLLFVBQ3RDO29DQUNBLElBQUk7d0NBQ0YwQixZQUFZMUIsY0FBYyxHQUFHd0IsS0FBS0MsS0FBSyxDQUNyQ0MsWUFBWTFCLGNBQWM7b0NBRTlCLEVBQUUsT0FBTzZCLEdBQUc7d0NBQ1ZoQixRQUFReEIsS0FBSyxDQUFDLGlDQUFpQ3dDO3dDQUMvQ0gsWUFBWTFCLGNBQWMsR0FBRyxFQUFFO29DQUNqQztnQ0FDRixPQUFPLElBQUksQ0FBQzBCLFlBQVkxQixjQUFjLEVBQUU7b0NBQ3RDMEIsWUFBWTFCLGNBQWMsR0FBRyxFQUFFO2dDQUNqQztnQ0FFQWEsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ1k7Z0NBQzdDYixRQUFRQyxHQUFHLENBQ1QsaUNBQ0FZLFlBQVlJLFlBQVk7Z0NBRTFCakIsUUFBUUMsR0FBRyxDQUNULCtCQUNBWSxZQUFZSyxVQUFVO2dDQUd4QnpELFdBQVdvRDtnQ0FFWCxtQkFBbUI7Z0NBQ25CLElBQ0VBLFlBQVlNLGlCQUFpQixJQUM3Qk4sWUFBWU0saUJBQWlCLENBQUNDLE1BQU0sR0FBRyxHQUN2QztvQ0FDQSxNQUFNQyxlQUNKUixZQUFZTSxpQkFBaUIsQ0FBQ0csSUFBSSxDQUNoQyxDQUFDQyxNQUNDQSxJQUFJQyxTQUFTLEtBQ1pYLFlBQVlNLGlCQUFpQixDQUFDLEVBQUU7b0NBQ3ZDcEQsZUFBZXBCLGtCQUFrQjBFLGFBQWFJLGFBQWE7Z0NBQzdEO2dDQUVBLHFDQUFxQztnQ0FDckMsSUFBSVosWUFBWWEsU0FBUyxFQUFFO29DQUN6QjFCLFFBQVFDLEdBQUcsQ0FBQyxzQkFBc0JZLFlBQVlhLFNBQVM7b0NBQ3ZELE1BQU1DLFFBQVFkLFlBQVlhLFNBQVMsQ0FBQ0UsS0FBSyxDQUFDLEtBQUtDLEdBQUcsQ0FDaEQsQ0FBQ0MsT0FBaUJBLEtBQUtDLElBQUk7b0NBRTdCLE1BQU1DLGlCQUFpQkwsTUFBTUUsR0FBRyxDQUFDLENBQUNDLE9BQ2hDRyxrQkFBa0JIO29DQUVwQjdELGNBQWMrRDtvQ0FDZDdELHNCQUFzQjtnQ0FDeEI7Z0NBRUEsK0RBQStEO2dDQUMvRCxJQUFJMEMsWUFBWXFCLG9CQUFvQixHQUFHLEdBQUc7b0NBQ3hDckUsWUFBWWdELFlBQVlxQixvQkFBb0I7Z0NBQzlDOzRCQUNGLE9BQU87Z0NBQ0xsQyxRQUFReEIsS0FBSyxDQUFDO2dDQUNkQyxTQUNFLG1CQUE2QixPQUFWdkIsV0FBVTs0QkFFakM7d0JBQ0YsT0FBTzs0QkFDTDhDLFFBQVF4QixLQUFLLENBQ1g7NEJBRUZDLFNBQVM7d0JBQ1g7b0JBQ0YsRUFBRSxPQUFPMEQsWUFBWTt3QkFDbkJuQyxRQUFReEIsS0FBSyxDQUNYLCtCQUNBMkQsWUFDQSxhQUNBMUIsYUFBYUosSUFBSTt3QkFFbkI1QixTQUFTO29CQUNYO2dCQUNGLE9BQU87b0JBQ0x1QixRQUFReEIsS0FBSyxDQUFDLHFDQUFxQzBCLFNBQVNHLElBQUk7b0JBQ2hFNUIsU0FBUztnQkFDWDtZQUNGLE9BQU87Z0JBQ0x1QixRQUFReEIsS0FBSyxDQUFDO2dCQUNkQyxTQUFTO1lBQ1g7UUFDRixFQUFFLE9BQU9ELE9BQVk7WUFDbkJ3QixRQUFReEIsS0FBSyxDQUFDLDJCQUEyQkE7WUFFekMsK0JBQStCO1lBQy9CLElBQUlBLE1BQU0wQixRQUFRLEVBQUU7b0JBU2QxQjtnQkFSSixxQ0FBcUM7Z0JBQ3JDd0IsUUFBUXhCLEtBQUssQ0FDWCxpQkFDQUEsTUFBTTBCLFFBQVEsQ0FBQ2tDLE1BQU0sRUFDckI1RCxNQUFNMEIsUUFBUSxDQUFDRyxJQUFJO2dCQUVyQjVCLFNBQ0UsaUJBQ0VELE9BRGVBLE1BQU0wQixRQUFRLENBQUNrQyxNQUFNLEVBQUMsT0FFdEMsT0FEQzVELEVBQUFBLHVCQUFBQSxNQUFNMEIsUUFBUSxDQUFDRyxJQUFJLGNBQW5CN0IsMkNBQUFBLHFCQUFxQjZELE9BQU8sS0FBSTtZQUd0QyxPQUFPLElBQUk3RCxNQUFNOEQsT0FBTyxFQUFFO2dCQUN4Qiw0Q0FBNEM7Z0JBQzVDdEMsUUFBUXhCLEtBQUssQ0FBQyxrQkFBa0JBLE1BQU04RCxPQUFPO2dCQUM3QzdELFNBQVM7WUFDWCxPQUFPO2dCQUNMLDBCQUEwQjtnQkFDMUJ1QixRQUFReEIsS0FBSyxDQUFDLHdCQUF3QkEsTUFBTTZELE9BQU87Z0JBQ25ENUQsU0FBUyxVQUF3QixPQUFkRCxNQUFNNkQsT0FBTztZQUNsQztRQUNGLFNBQVU7WUFDUjFFLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTXNFLG9CQUFvQixDQUFDTTtRQUN6QixJQUFJLENBQUNBLFdBQVcsT0FBTztRQUV2QixJQUFJQSxVQUFVQyxRQUFRLENBQUMsa0JBQWtCRCxVQUFVQyxRQUFRLENBQUMsYUFBYTtZQUN2RSxPQUFPRDtRQUNUO1FBQ0EsaURBQWlEO1FBQ2pELElBQUlBLFVBQVUxRixVQUFVLENBQUMsU0FBUztZQUNoQyxPQUFPLHdCQUFzRCxPQUE5QjRGLG1CQUFtQkY7UUFDcEQ7UUFDQSxNQUFNekYsVUFBVTtRQUNoQixNQUFNNEYsc0JBQXNCSCxVQUFVMUYsVUFBVSxDQUFDLE9BQzdDMEYsWUFDQSxJQUFjLE9BQVZBO1FBQ1IsT0FBTyx3QkFFTCxPQUY2QkUsbUJBQzdCLEdBQWFDLE9BQVY1RixTQUE4QixPQUFwQjRGO0lBRWpCO0lBRUEseUNBQXlDO0lBQ3pDLE1BQU1DLG9CQUFvQm5JLDhDQUFPQTsyREFBQztZQUNoQyxJQUFJLEVBQUMyQyxvQkFBQUEsOEJBQUFBLFFBQVNnQyxjQUFjLEdBQUUsT0FBTyxDQUFDO1lBRXRDLE9BQU9oQyxRQUFRZ0MsY0FBYyxDQUFDeUQsTUFBTTttRUFBQyxDQUFDQyxRQUFReEQ7b0JBQzVDLE1BQU15RCxVQUFVekQsS0FBS0Usa0JBQWtCO29CQUN2QyxJQUFJLENBQUNzRCxNQUFNLENBQUNDLFFBQVEsRUFBRTt3QkFDcEJELE1BQU0sQ0FBQ0MsUUFBUSxHQUFHLEVBQUU7b0JBQ3RCO29CQUNBRCxNQUFNLENBQUNDLFFBQVEsQ0FBQ0MsSUFBSSxDQUFDMUQ7b0JBQ3JCLE9BQU93RDtnQkFDVDtrRUFBRyxDQUFDO1FBQ047MERBQUc7UUFBQzFGLG9CQUFBQSw4QkFBQUEsUUFBU2dDLGNBQWM7S0FBQztJQUU1Qix1REFBdUQ7SUFDdkQsTUFBTTZELHdCQUF3QixDQUM1QjNELE1BQ0E0RCxXQUNBQztRQUVBakUsc0JBQXNCLENBQUNrRTtZQUNyQixNQUFNQyxXQUFXO2dCQUFFLEdBQUdELElBQUk7WUFBQztZQUMzQixNQUFNRSxVQUFVLEdBQThCaEUsT0FBM0JBLEtBQUtFLGtCQUFrQixFQUFDLEtBQXlCLE9BQXRCRixLQUFLRyxnQkFBZ0I7WUFFbkUsbUVBQW1FO1lBQ25FLElBQUkwRCxnQkFBZ0JELFdBQVc7Z0JBQzdCSyxPQUFPQyxJQUFJLENBQUNKLE1BQU0vRCxPQUFPLENBQUMsQ0FBQ0U7b0JBQ3pCLElBQ0VBLElBQUl6QyxVQUFVLENBQUMsR0FBMkIsT0FBeEJ3QyxLQUFLRSxrQkFBa0IsRUFBQyxTQUMxQ0QsUUFBUStELFNBQ1I7d0JBQ0FELFFBQVEsQ0FBQzlELElBQUksR0FBRztvQkFDbEI7Z0JBQ0Y7WUFDRjtZQUVBLDZCQUE2QjtZQUM3QixtQ0FBbUM7WUFDbkMsa0ZBQWtGO1lBQ2xGOEQsUUFBUSxDQUFDQyxRQUFRLEdBQUdILGVBQWUsT0FBTyxDQUFDQyxJQUFJLENBQUNFLFFBQVE7WUFFeEQsT0FBT0Q7UUFDVDtJQUNGO0lBRUEsa0RBQWtEO0lBQ2xELE1BQU1JLGNBQWM7UUFDbEIsSUFBSSxDQUFDckcsU0FBUyxPQUFPO1FBRXJCLE1BQU1zRyxlQUNKdEcsUUFBUXVHLGFBQWEsSUFBSXZHLFFBQVF1RyxhQUFhLEdBQUd2RyxRQUFRd0csS0FBSztRQUNoRSxNQUFNQyxnQkFBZ0JDO1FBQ3RCLE1BQU1DLG9CQUNKRixrQkFBa0J6RyxRQUFRd0csS0FBSyxJQUMvQkMsa0JBQW1CekcsQ0FBQUEsUUFBUXVHLGFBQWEsSUFBSXZHLFFBQVF3RyxLQUFLO1FBRTNELHFCQUNFLDhEQUFDSTtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBS0QsV0FBVTs7Z0NBQWtDO2dDQUUvQ1AsZUFDRyxDQUFDdEcsUUFBUXVHLGFBQWEsSUFBSSxHQUFHUSxPQUFPLENBQUMsS0FDckNOLGNBQWNNLE9BQU8sQ0FBQzs7Ozs7Ozt3QkFJM0JULDhCQUNDLDhEQUFDUTs0QkFBS0QsV0FBVTs7Z0NBQXFDO2dDQUNqRDdHLFFBQVF3RyxLQUFLLENBQUNPLE9BQU8sQ0FBQzs7Ozs7Ozt3QkFLM0JKLHFCQUFxQixDQUFDTCw4QkFDckIsOERBQUNROzRCQUFLRCxXQUFVOztnQ0FBcUM7Z0NBQ2pEN0csUUFBUXdHLEtBQUssQ0FBQ08sT0FBTyxDQUFDOzs7Ozs7O3dCQUszQlQsOEJBQ0MsOERBQUNROzRCQUFLRCxXQUFVOztnQ0FDYkcsS0FBS0MsS0FBSyxDQUNULENBQUVqSCxRQUFRd0csS0FBSyxHQUFJeEcsQ0FBQUEsUUFBUXVHLGFBQWEsSUFBSSxFQUFDLElBQzNDdkcsUUFBUXdHLEtBQUssR0FDYjtnQ0FDRjs7Ozs7Ozs7Ozs7OztnQkFPUHhHLFFBQVFrSCxRQUFRLGtCQUNmLDhEQUFDTjtvQkFBSUMsV0FBVTs7d0JBQ1o3RyxRQUFRa0gsUUFBUSxDQUFDQyxjQUFjO3dCQUFHOzs7Ozs7O2dCQUt0Q25ILFFBQVFvSCxPQUFPLElBQUlwSCxRQUFRb0gsT0FBTyxHQUFHLG1CQUNwQyw4REFBQ1I7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFLRCxXQUFVOzs0QkFBc0c7NEJBQ3hHN0csUUFBUW9ILE9BQU87NEJBQUM7Ozs7Ozs7Ozs7OztnQkFNakNwSCxRQUFRcUgsUUFBUSxJQUFJckgsUUFBUXFILFFBQVEsR0FBR3JILFFBQVF3RyxLQUFLLGtCQUNuRCw4REFBQ0k7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDQzs0QkFBS0QsV0FBVTs7Z0NBQWU7Z0NBQUU3RyxRQUFRcUgsUUFBUSxDQUFDTixPQUFPLENBQUM7Ozs7Ozs7c0NBQzFELDhEQUFDRDs0QkFBS0QsV0FBVTs7Z0NBQ2JHLEtBQUtDLEtBQUssQ0FDVCxDQUFFakgsUUFBUXFILFFBQVEsR0FBSXJILENBQUFBLFFBQVF1RyxhQUFhLElBQUl2RyxRQUFRd0csS0FBSyxLQUMxRHhHLFFBQVFxSCxRQUFRLEdBQ2hCO2dDQUNGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBT2Q7SUFFQSx3REFBd0Q7SUFDeEQsTUFBTVgseUJBQXlCcEosa0RBQVdBO29FQUFDO1lBQ3pDLElBQUksQ0FBQzBDLFNBQVMsT0FBTztZQUVyQixJQUFJeUcsZ0JBQWdCekcsUUFBUXdHLEtBQUs7WUFFakMsSUFBSXhHLFFBQVFnQyxjQUFjLElBQUloQyxRQUFRZ0MsY0FBYyxDQUFDaUMsTUFBTSxHQUFHLEdBQUc7Z0JBQy9EakUsUUFBUWdDLGNBQWMsQ0FBQ0MsT0FBTztnRkFBQyxDQUFDQzt3QkFDOUIsTUFBTWdFLFVBQVUsR0FBOEJoRSxPQUEzQkEsS0FBS0Usa0JBQWtCLEVBQUMsS0FBeUIsT0FBdEJGLEtBQUtHLGdCQUFnQjt3QkFDbkUsSUFDRVIsa0JBQWtCLENBQUNxRSxRQUFRLElBQzNCLE9BQU9oRSxLQUFLb0YsZUFBZSxLQUFLLFlBQ2hDLE9BQU9wRixLQUFLcUYsbUJBQW1CLEtBQUssVUFDcEM7NEJBQ0EsT0FBUXJGLEtBQUtxRixtQkFBbUI7Z0NBQzlCLEtBQUs7b0NBQ0hkLGlCQUFpQnZFLEtBQUtvRixlQUFlO29DQUNyQztnQ0FDRixLQUFLO29DQUNIYixpQkFBaUIsUUFBU0QsS0FBSyxHQUFHdEUsS0FBS29GLGVBQWUsR0FBSTtvQ0FDMUQ7NEJBQ0o7d0JBQ0Y7b0JBQ0Y7O1lBQ0Y7WUFFQSxPQUFPTixLQUFLUSxHQUFHLENBQUMsR0FBR2YsZ0JBQWdCLGtDQUFrQztRQUN2RTttRUFBRztRQUFDekc7UUFBUzZCO0tBQW1CO0lBRWhDLE1BQU00RSxnQkFBZ0JwSiw4Q0FBT0E7dURBQzNCLElBQU1xSjtzREFDTjtRQUFDQTtLQUF1QjtJQUcxQix3QkFBd0I7SUFDeEIsTUFBTWUsZUFBZTtRQUNuQixJQUFJLENBQUN6SCxTQUFTLE9BQU87UUFFckIscUJBQ0UsOERBQUM0RztZQUFJQyxXQUFVOztnQkFDWjdHLFFBQVEwSCxpQkFBaUIsa0JBQ3hCLDhEQUFDL0ksdURBQUtBO29CQUFDa0ksV0FBVTs4QkFBcUU7Ozs7OztnQkFJdkY3RyxRQUFRMkgsU0FBUyxrQkFDaEIsOERBQUNoSix1REFBS0E7b0JBQUNrSSxXQUFVOzhCQUF5RTs7Ozs7Ozs7Ozs7O0lBTWxHO0lBRUEsc0VBQXNFO0lBQ3RFLE1BQU1lLGFBQWF2Syw4Q0FBT0E7b0RBQWM7Z0JBSWxDMkM7WUFISixNQUFNNkgsUUFBcUIsRUFBRTtZQUU3QixxQkFBcUI7WUFDckIsSUFBSTdILG9CQUFBQSwrQkFBQUEsNkJBQUFBLFFBQVNnRSxpQkFBaUIsY0FBMUJoRSxpREFBQUEsMkJBQTRCaUUsTUFBTSxFQUFFO2dCQUN0Q2pFLFFBQVFnRSxpQkFBaUIsQ0FBQy9CLE9BQU87Z0VBQUMsQ0FBQ21DO3dCQUNqQ3lELE1BQU1qQyxJQUFJLENBQUM7NEJBQ1RrQyxNQUFNOzRCQUNOQyxLQUFLdkksa0JBQWtCNEUsSUFBSUUsYUFBYTs0QkFDeEMwRCxLQUFLaEksQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTaUksV0FBVyxLQUFJOzRCQUM3QkMsV0FBVzFJLGtCQUFrQjRFLElBQUlFLGFBQWE7d0JBQ2hEO29CQUNGOztZQUNGO1lBRUEsYUFBYTtZQUNiekQsV0FBV29CLE9BQU87NERBQUMsQ0FBQ2tHLFVBQVVDO29CQUM1QlAsTUFBTWpDLElBQUksQ0FBQzt3QkFDVGtDLE1BQU07d0JBQ05DLEtBQUtJO3dCQUNMSCxLQUFLLEdBQWdESSxPQUE3Q3BJLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU2lJLFdBQVcsS0FBSSxXQUFVLGFBQXFCLE9BQVZHLFFBQVE7b0JBRS9EO2dCQUNGOztZQUVBLE9BQU9QO1FBQ1Q7bURBQUc7UUFBQzdIO1FBQVNhO1FBQVlGO0tBQVk7SUFFckMsTUFBTTBILGlCQUFpQixDQUFDUDtRQUN0QmxHLGlCQUFpQmtHO1FBQ2pCcEcsZUFBZTtRQUNmNEcsV0FBVyxJQUFNNUcsZUFBZSxRQUFRO0lBQzFDO0lBRUEsTUFBTTZHLG9CQUFvQjtRQUN4QixJQUFJdkksU0FBUztZQUNYLE1BQU13SSxjQUNKeEksUUFBUXlJLG9CQUFvQixHQUFHLElBQzNCekIsS0FBSzBCLEdBQUcsQ0FBQzFJLFFBQVF5SSxvQkFBb0IsRUFBRXpJLFFBQVEySSxhQUFhLElBQzVEM0ksUUFBUTJJLGFBQWE7WUFDM0IsSUFBSWxJLFdBQVcrSCxhQUFhO2dCQUMxQjlILFlBQVksQ0FBQ3NGLE9BQVNBLE9BQU87Z0JBQzdCcUMsZUFBZTtZQUNqQixPQUFPO2dCQUNMLCtDQUErQztnQkFDL0N6Six5Q0FBS0EsQ0FBQ2dLLElBQUksQ0FBQyx1QkFBbUMsT0FBWkosYUFBWTtZQUNoRDtRQUNGO0lBQ0Y7SUFFQSxNQUFNSyxvQkFBb0I7UUFDeEIsSUFBSTdJLFNBQVM7WUFDWCxNQUFNOEksY0FDSjlJLFFBQVErRSxvQkFBb0IsR0FBRyxJQUFJL0UsUUFBUStFLG9CQUFvQixHQUFHO1lBQ3BFLElBQUl0RSxXQUFXcUksYUFBYTtnQkFDMUJwSSxZQUFZLENBQUNzRixPQUFTQSxPQUFPO2dCQUM3QnFDLGVBQWU7WUFDakIsT0FBTztnQkFDTCwrQ0FBK0M7Z0JBQy9DekoseUNBQUtBLENBQUNnSyxJQUFJLENBQUMsdUJBQW1DLE9BQVpFO1lBQ3BDO1FBQ0Y7SUFDRjtJQUVBLHVDQUF1QztJQUN2QyxNQUFNQyxrQkFBa0IsQ0FBQ2pCO1FBQ3ZCLE1BQU1rQixhQUNKO1FBQ0YsTUFBTUMsaUJBQWlCO1FBRXZCLElBQUluQixTQUFTLGFBQWE7WUFDeEIsTUFBTW9CLFFBQ0psSixXQUNBUyxZQUNHVCxDQUFBQSxRQUFReUksb0JBQW9CLEdBQUcsSUFDNUJ6QixLQUFLMEIsR0FBRyxDQUFDMUksUUFBUXlJLG9CQUFvQixFQUFFekksUUFBUTJJLGFBQWEsSUFDNUQzSSxRQUFRMkksYUFBYTtZQUM3QixPQUFPLEdBQ0xPLE9BRFFGLFlBQVcsS0FJcEIsT0FIQ0UsUUFDSUQsaUJBQ0E7UUFFUixPQUFPO1lBQ0wsTUFBTUUsUUFDSm5KLFdBQ0FTLFlBQ0dULENBQUFBLFFBQVErRSxvQkFBb0IsR0FBRyxJQUFJL0UsUUFBUStFLG9CQUFvQixHQUFHO1lBQ3ZFLE9BQU8sR0FDTG9FLE9BRFFILFlBQVcsS0FJcEIsT0FIQ0csUUFDSUYsaUJBQ0E7UUFFUjtJQUNGO0lBRUEsaUNBQWlDO0lBQ2pDLE1BQU1HLGlCQUFpQixrQkFDckIsOERBQUN4QztZQUFJQyxXQUFVOzs4QkFDYiw4REFBQ0M7b0JBQ0NELFdBQVcsZ0VBRVYsT0FEQ3BGLGNBQWMsMkJBQTJCOzhCQUcxQ2hCOzs7Ozs7Z0JBRUZnQiw2QkFDQyw4REFBQ3FGO29CQUNDRCxXQUFXLHVFQUlWLE9BSENsRixrQkFBa0IsY0FDZCxxQkFDQTs4QkFHTEEsa0JBQWtCLGNBQWMsT0FBTzs7Ozs7Ozs7Ozs7O0lBTWhELDRDQUE0QztJQUM1QyxJQUFJcEIsU0FBUztRQUNYLHFCQUFPLDhEQUFDbEIseURBQWNBOzs7OztJQUN4QjtJQUVBLElBQUlnQyxPQUFPO1FBQ1QscUJBQU8sOERBQUMvQix1REFBWUE7WUFBQytCLE9BQU9BO1lBQU9nSSxPQUFPL0c7Ozs7OztJQUM1QztJQUVBLElBQUksQ0FBQ3RDLFNBQVM7UUFDWixxQkFDRSw4REFBQzRHO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDeUM7b0JBQUd6QyxXQUFVOzhCQUEwQjs7Ozs7OzhCQUN4Qyw4REFBQzBDO29CQUFFMUMsV0FBVTs7d0JBQU87d0JBQ0k5Rzt3QkFBVTs7Ozs7Ozs4QkFHbEMsOERBQUM2RztvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUN0SixrREFBSUE7NEJBQUNpTSxNQUFLO3NDQUNULDRFQUFDeEwseURBQU1BOztrREFDTCw4REFBQ0gsaUlBQVNBO3dDQUFDZ0osV0FBVTs7Ozs7O29DQUFpQjs7Ozs7Ozs7Ozs7O3NDQUkxQyw4REFBQzBDOzRCQUFFMUMsV0FBVTtzQ0FBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU03QztJQUVBLHFCQUNFLDhEQUFDRDtRQUFJQyxXQUFVOzswQkFFYiw4REFBQzVJLGlFQUFVQTtnQkFBQzRJLFdBQVU7MEJBQ3BCLDRFQUFDekkscUVBQWNBOztzQ0FDYiw4REFBQ0YscUVBQWNBO3NDQUNiLDRFQUFDQyxxRUFBY0E7Z0NBQUNzTCxPQUFPOzBDQUNyQiw0RUFBQ2xNLGtEQUFJQTtvQ0FBQ2lNLE1BQUs7OENBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBR25CLDhEQUFDbEwsMEVBQW1CQTs7Ozs7c0NBQ3BCLDhEQUFDSixxRUFBY0E7c0NBQ2IsNEVBQUNDLHFFQUFjQTtnQ0FBQ3NMLE9BQU87MENBQ3JCLDRFQUFDbE0sa0RBQUlBO29DQUFDaU0sTUFBTSxzQkFBa0QsT0FBNUJ4SixRQUFRK0QsVUFBVSxJQUFJOzhDQUNyRC9ELFFBQVE4RCxZQUFZLElBQUk7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSS9CLDhEQUFDeEYsMEVBQW1CQTs7Ozs7c0NBQ3BCLDhEQUFDSixxRUFBY0E7c0NBQ2IsNEVBQUNDLHFFQUFjQTtnQ0FBQ3NMLE9BQU87MENBQ3JCLDRFQUFDbE0sa0RBQUlBO29DQUFDaU0sTUFBSzs4Q0FBWTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FHM0IsOERBQUNsTCwwRUFBbUJBOzs7OztzQ0FDcEIsOERBQUNKLHFFQUFjQTtzQ0FDYiw0RUFBQ0cscUVBQWNBOzBDQUFFMkIsUUFBUWlJLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTTFDLDhEQUFDckI7Z0JBQUlDLFdBQVU7MEJBQ1osQ0FBQztvQkFDQSxvRUFBb0U7b0JBQ3BFLElBQUksQ0FBQzdHLFFBQVEwSixvQkFBb0IsSUFBSSxDQUFDMUosUUFBUTJKLGtCQUFrQixFQUFFO3dCQUNoRSxPQUFPO29CQUNUO29CQUVBLE1BQU1DLE1BQU0sSUFBSUM7b0JBQ2hCLE1BQU1DLFlBQVksSUFBSUQsS0FBSzdKLFFBQVEwSixvQkFBb0I7b0JBQ3ZELE1BQU1LLFVBQVUsSUFBSUYsS0FBSzdKLFFBQVEySixrQkFBa0I7b0JBRW5ELGdEQUFnRDtvQkFDaEQsTUFBTUssZUFBZUosT0FBT0UsYUFBYUYsT0FBT0c7b0JBQ2hELE1BQU1FLGlCQUFpQkwsTUFBTUU7b0JBQzdCLE1BQU1JLGdCQUFnQk4sTUFBTUc7b0JBRTVCLHVDQUF1QztvQkFDdkMsSUFBSUcsZUFBZTt3QkFDakIsT0FBTztvQkFDVDtvQkFFQSxzQ0FBc0M7b0JBQ3RDLE1BQU1DLG1CQUFtQkYsaUJBQWlCakssUUFBUTBKLG9CQUFvQixHQUFHMUosUUFBUTJKLGtCQUFrQjtvQkFFbkcsNENBQTRDO29CQUM1QyxJQUFJUyxlQUFlO29CQUNuQixJQUFJQyxpQkFBaUI7b0JBRXJCLElBQUlKLGdCQUFnQjt3QkFDbEJHLGVBQWU7d0JBQ2ZDLGlCQUFpQjtvQkFDbkIsT0FBTyxJQUFJTCxjQUFjO3dCQUN2QkksZUFBZTt3QkFDZkMsaUJBQWlCO29CQUNuQjtvQkFFQSxxQkFDRSw4REFBQ3pEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDOUksaUlBQUtBO3dDQUFDOEksV0FBVTs7Ozs7O2tEQUNqQiw4REFBQ0M7d0NBQUtELFdBQVU7a0RBQ2J1RDs7Ozs7Ozs7Ozs7OzBDQUlMLDhEQUFDeEQ7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUN6SCxnRUFBU0E7b0NBQ1JrTCxTQUFTSDtvQ0FDVHRELFdBQVU7Ozs7Ozs7Ozs7OzBDQUlkLDhEQUFDMEM7Z0NBQUUxQyxXQUFVOzBDQUNWd0Q7Ozs7Ozs7Ozs7OztnQkFJVDs7Ozs7OzBCQUdGLDhEQUFDekQ7Z0JBQUlDLFdBQVU7O2tDQUViLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQzFILDRGQUFtQkE7NEJBQ2xCb0wsT0FBTzNDOzRCQUNQZixXQUFVOzs7Ozs7Ozs7OztrQ0FLZCw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDeUM7Z0NBQUd6QyxXQUFVOzBDQUEwQjdHLFFBQVFpSSxXQUFXOzs7Ozs7MENBRzNELDhEQUFDckI7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTtrREFDWjsrQ0FBSWxELE1BQU07eUNBQUcsQ0FBQ2UsR0FBRyxDQUFDLENBQUM4RixHQUFHQyxrQkFDckIsOERBQUM3TSxpSUFBSUE7Z0RBRUhpSixXQUFXLFdBSVYsT0FIQzRELElBQUl6RCxLQUFLMEQsS0FBSyxDQUFDMUssUUFBUTJLLE1BQU0sSUFBSSxLQUM3QixvQ0FDQTsrQ0FKREY7Ozs7Ozs7Ozs7a0RBU1gsOERBQUMzRDt3Q0FBS0QsV0FBVTs7NENBQTZCOzRDQUN6QzdHLFFBQVEySyxNQUFNLElBQUk7NENBQUU7NENBQUczSyxRQUFRNEssWUFBWSxJQUFJOzRDQUFFOzs7Ozs7Ozs7Ozs7OzRCQUt0RHZFOzBDQUdELDhEQUFDTztnQ0FBSUMsV0FBVTswQ0FDWixDQUFDO29DQUNBLG9FQUFvRTtvQ0FDcEUsSUFBSSxDQUFDN0csUUFBUTBKLG9CQUFvQixJQUFJLENBQUMxSixRQUFRMkosa0JBQWtCLEVBQUU7d0NBQ2hFLE9BQU87b0NBQ1Q7b0NBRUEsTUFBTUMsTUFBTSxJQUFJQztvQ0FDaEIsTUFBTUMsWUFBWSxJQUFJRCxLQUFLN0osUUFBUTBKLG9CQUFvQjtvQ0FDdkQsTUFBTUssVUFBVSxJQUFJRixLQUFLN0osUUFBUTJKLGtCQUFrQjtvQ0FFbkQsZ0RBQWdEO29DQUNoRCxNQUFNSyxlQUFlSixPQUFPRSxhQUFhRixPQUFPRztvQ0FDaEQsTUFBTUUsaUJBQWlCTCxNQUFNRTtvQ0FDN0IsTUFBTUksZ0JBQWdCTixNQUFNRztvQ0FFNUIsdUNBQXVDO29DQUN2QyxJQUFJRyxlQUFlO3dDQUNqQixPQUFPO29DQUNUO29DQUVBLHNDQUFzQztvQ0FDdEMsTUFBTUMsbUJBQW1CRixpQkFBaUJqSyxRQUFRMEosb0JBQW9CLEdBQUcxSixRQUFRMkosa0JBQWtCO29DQUVuRyw0Q0FBNEM7b0NBQzVDLElBQUlTLGVBQWU7b0NBQ25CLElBQUlDLGlCQUFpQjtvQ0FFckIsSUFBSUosZ0JBQWdCO3dDQUNsQkcsZUFBZTt3Q0FDZkMsaUJBQWlCO29DQUNuQixPQUFPLElBQUlMLGNBQWM7d0NBQ3ZCSSxlQUFlO3dDQUNmQyxpQkFBaUI7b0NBQ25CO29DQUVBLHFCQUNFLDhEQUFDekQ7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUM5SSxpSUFBS0E7d0RBQUM4SSxXQUFVOzs7Ozs7a0VBQ2pCLDhEQUFDQzt3REFBS0QsV0FBVTtrRUFDYnVEOzs7Ozs7Ozs7Ozs7MERBSUwsOERBQUN4RDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ3pILGdFQUFTQTtvREFDUmtMLFNBQVNIO29EQUNUdEQsV0FBVTs7Ozs7Ozs7Ozs7MERBSWQsOERBQUMwQztnREFBRTFDLFdBQVU7MERBQ1Z3RDs7Ozs7Ozs7Ozs7O2dDQUlUOzs7Ozs7NEJBSURySyxRQUFRNkssZ0JBQWdCLGtCQUN2Qiw4REFBQ2pFO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDMEM7OENBQUd2SixRQUFRNkssZ0JBQWdCOzs7Ozs7Ozs7OzswQ0FPaEMsOERBQUNqRTtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNpRTt3Q0FBR2pFLFdBQVU7a0RBQTJDOzs7Ozs7a0RBR3pELDhEQUFDMEM7d0NBQUUxQyxXQUFVO2tEQUE2Qjs7Ozs7O29DQUd6Q1YsT0FBTzRFLE9BQU8sQ0FBQ3ZGLG1CQUFtQnZCLE1BQU0sR0FBRyxrQkFDMUMsOERBQUMyQzt3Q0FBSUMsV0FBVTtrREFDWlYsT0FBTzRFLE9BQU8sQ0FBQ3ZGLG1CQUFtQmQsR0FBRyxDQUNwQztnREFBQyxDQUFDaUIsU0FBU3FGLFdBQVc7Z0RBR2ZBLGNBQ0NBO2lFQUhOLDhEQUFDcEU7Z0RBQWtDQyxXQUFVOztrRUFDM0MsOERBQUNvRTt3REFBR3BFLFdBQVU7OzREQUNYbUUsRUFBQUEsZUFBQUEsVUFBVSxDQUFDLEVBQUUsY0FBYkEsbUNBQUFBLGFBQWVFLFdBQVcsT0FDekJGLGdCQUFBQSxVQUFVLENBQUMsRUFBRSxjQUFiQSxvQ0FBQUEsY0FBZUcsYUFBYTs0REFBQzs7Ozs7OztrRUFHakMsOERBQUN2RTt3REFBSUMsV0FBVTtrRUFDWm1FLFdBQVd0RyxHQUFHLENBQUMsQ0FBQ3hDOzREQUNmLE1BQU1nRSxVQUFVLEdBQThCaEUsT0FBM0JBLEtBQUtFLGtCQUFrQixFQUFDLEtBQXlCLE9BQXRCRixLQUFLRyxnQkFBZ0I7NERBQ25FLE1BQU0rSSxhQUFhLENBQUMsQ0FBQ3ZKLGtCQUFrQixDQUFDcUUsUUFBUTs0REFDaEQsTUFBTUgsZUFBZWlGLFdBQVcvRyxNQUFNLEdBQUc7NERBRXpDLHFCQUNFLDhEQUFDMkM7Z0VBQWtCQyxXQUFVOztrRkFDM0IsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUNiLDRFQUFDd0U7NEVBQ0N2RCxNQUFNL0IsZUFBZSxVQUFVOzRFQUMvQnVGLElBQUksUUFBZ0IsT0FBUnBGOzRFQUNacUYsTUFBTSxjQUFzQixPQUFSNUY7NEVBQ3BCa0IsV0FBVyxXQUVWLE9BRENkLGVBQWUsaUJBQWlCLFdBQ2pDOzRFQUNEeUYsU0FBU0o7NEVBQ1RLLFVBQVUsQ0FBQzVILElBQ1RnQyxzQkFDRTNELE1BQ0EyQixFQUFFNkgsTUFBTSxDQUFDRixPQUFPLEVBQ2hCekY7Ozs7Ozs7Ozs7O2tGQUtSLDhEQUFDYTt3RUFBSUMsV0FBVTtrRkFDYiw0RUFBQzhFOzRFQUNDQyxTQUFTLFFBQWdCLE9BQVIxRjs0RUFDakJXLFdBQVcsZUFJVixPQUhDdUUsYUFDSSxpQkFDQTs7Z0ZBR0xsSixLQUFLMkosa0JBQWtCO2dGQUN0QjNKLENBQUFBLEtBQUtvRixlQUFlLElBQ3BCcEYsS0FBS29GLGVBQWUsS0FBSyxvQkFDekIsOERBQUNSO29GQUFLRCxXQUFVOzt3RkFBMEM7d0ZBRXZEM0UsS0FBS3FGLG1CQUFtQixLQUFLLElBQzFCLE1BQ0E7d0ZBQUc7d0ZBQ0xyRixLQUFLb0YsZUFBZTt3RkFBRTt3RkFDdkJwRixLQUFLcUYsbUJBQW1CLEtBQUssSUFDMUIsTUFDQTt3RkFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrREF2Q1ByQjs7Ozs7d0RBK0NkOzs7Ozs7OytDQTVETSxjQUFzQixPQUFSUDs7Ozs7Ozs7Ozs2REFtRTlCLDhEQUFDNEQ7d0NBQUUxQyxXQUFVO2tEQUF3Qjs7Ozs7Ozs7Ozs7OzBDQU96Qyw4REFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0M7NENBQUtELFdBQVU7c0RBQXNCOzs7Ozs7c0RBQ3RDLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ2lGOzREQUNDQyxTQUFTbEQ7NERBQ1RoQyxXQUFXa0MsZ0JBQWdCOzREQUMzQmlELFVBQVV2TCxZQUFhVCxDQUFBQSxRQUFRK0Usb0JBQW9CLElBQUk7NERBQ3ZEa0gsY0FBVztzRUFFWCw0RUFBQ0M7Z0VBQ0NDLE9BQU07Z0VBQ050RixXQUFVO2dFQUNWdUYsU0FBUTtnRUFDUkMsTUFBSzswRUFFTCw0RUFBQ0M7b0VBQ0NDLFVBQVM7b0VBQ1RDLEdBQUU7b0VBQ0ZDLFVBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7c0VBS2YsOERBQUNyRDs7Ozs7c0VBRUQsOERBQUMwQzs0REFDQ0MsU0FBU3hEOzREQUNUMUIsV0FBV2tDLGdCQUFnQjs0REFDM0JpRCxVQUNFaE0sUUFBUXlJLG9CQUFvQixHQUFHLElBQzNCaEksWUFDQXVHLEtBQUswQixHQUFHLENBQ04xSSxRQUFReUksb0JBQW9CLEVBQzVCekksUUFBUTJJLGFBQWEsSUFFdkJsSSxZQUFZVCxRQUFRMkksYUFBYTs0REFFdkNzRCxjQUFXO3NFQUVYLDRFQUFDQztnRUFDQ0MsT0FBTTtnRUFDTnRGLFdBQVU7Z0VBQ1Z1RixTQUFRO2dFQUNSQyxNQUFLOzBFQUVMLDRFQUFDQztvRUFDQ0MsVUFBUztvRUFDVEMsR0FBRTtvRUFDRkMsVUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4REFNakIsOERBQUM3RjtvREFBSUMsV0FBVTs7d0RBQ1o3RyxRQUFRK0Usb0JBQW9CLEdBQUcsbUJBQzlCLDhEQUFDK0I7NERBQUtELFdBQVU7O2dFQUF3QjtnRUFDaEM3RyxRQUFRK0Usb0JBQW9COzs7Ozs7O3dEQUlyQy9FLFFBQVF5SSxvQkFBb0IsR0FBRyxtQkFDOUIsOERBQUMzQjs0REFBS0QsV0FBVTs7Z0VBQXdCO2dFQUNqQztnRUFDSkcsS0FBSzBCLEdBQUcsQ0FDUDFJLFFBQVF5SSxvQkFBb0IsRUFDNUJ6SSxRQUFRMkksYUFBYTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQVVuQyw4REFBQy9CO2dDQUFJQyxXQUFVOztrREFFYiw4REFBQ2lGO3dDQUNDaEUsTUFBSzt3Q0FDTGpCLFdBQVU7d0NBQ1ZtRixVQUFVaE0sUUFBUTJJLGFBQWEsSUFBSSxLQUFLMUg7d0NBQ3hDOEssU0FBUzs0Q0FDUCxJQUFJLENBQUMvTCxTQUFTOzRDQUVka0IsZ0JBQWdCOzRDQUNoQixJQUFJO29EQXVDRWxCLDZCQUFBQTtnREF0Q0osbURBQW1EO2dEQUNuRCxNQUFNME0sZUFDSjFNLFFBQVFnRSxpQkFBaUIsSUFDekJoRSxRQUFRZ0UsaUJBQWlCLENBQUNDLE1BQU0sR0FBRyxJQUMvQnpFLGtCQUNFUSxRQUFRZ0UsaUJBQWlCLENBQUMsRUFBRSxDQUFDTSxhQUFhLElBRTVDO2dEQUVOLDBCQUEwQjtnREFDMUIsTUFBTXFJLGdCQUFnQixDQUFDM00sUUFBUWdDLGNBQWMsSUFBSSxFQUFFLEVBQUU0SyxNQUFNLENBQ3pELENBQUMxSyxPQUNDTCxrQkFBa0IsQ0FDaEIsR0FBOEJLLE9BQTNCQSxLQUFLRSxrQkFBa0IsRUFBQyxLQUF5QixPQUF0QkYsS0FBS0csZ0JBQWdCLEVBQ3BEO2dEQUdMLHdFQUF3RTtnREFDeEVwQyxLQUFLNE0sU0FBUyxDQUNaO29EQUNFdkIsSUFBSXRMLFFBQVF5QyxTQUFTO29EQUNyQjhJLE1BQU12TCxRQUFRaUksV0FBVztvREFDekI2RSxPQUFPOU0sUUFBUXVHLGFBQWEsSUFBSXZHLFFBQVF3RyxLQUFLO29EQUM3Q3VHLGVBQWUvTSxRQUFRdUcsYUFBYTtvREFDcEN5RyxPQUFPTjtvREFDUE8sZUFBZWpOLFFBQVF3RyxLQUFLO2dEQUM5QixHQUNBL0YsVUFDQWtNLGVBQ0EzTSxRQUFRa0gsUUFBUSxFQUNoQi9HLEtBQUssNENBQTRDOztnREFHbkQsaUNBQWlDO2dEQUNqQ3RCLHFGQUF3QkEsQ0FBQztvREFDdkJxTyxhQUFhbE4sUUFBUWlJLFdBQVc7b0RBQ2hDeEg7b0RBQ0FpTSxjQUNFMU0sRUFBQUEsNkJBQUFBLFFBQVFnRSxpQkFBaUIsY0FBekJoRSxrREFBQUEsOEJBQUFBLDBCQUEyQixDQUFDLEVBQUUsY0FBOUJBLGtEQUFBQSw0QkFBZ0NzRSxhQUFhLEtBQzdDO29EQUNGNkksWUFBWTt3REFDVkMsT0FBT0MsUUFBUSxDQUFDN0QsSUFBSSxHQUFHO29EQUN6QjtnREFDRjs0Q0FDRixFQUFFLE9BQU9uSSxPQUFPO2dEQUNkd0IsUUFBUXhCLEtBQUssQ0FBQyx5QkFBeUJBO2dEQUN2Q3pDLHlDQUFLQSxDQUFDeUMsS0FBSyxDQUNUOzRDQUVKLFNBQVU7Z0RBQ1JILGdCQUFnQjs0Q0FDbEI7d0NBQ0Y7OzRDQUVDRCw2QkFDQyw4REFBQzJGO2dEQUFJQyxXQUFVOzs7OztxRUFFZiw4REFBQ3BKLGlJQUFZQTtnREFBQ29KLFdBQVU7Ozs7OzswREFFMUIsOERBQUNDOzBEQUFNN0YsZUFBZSxjQUFjOzs7Ozs7Ozs7Ozs7a0RBSXRDLDhEQUFDNks7d0NBQ0NoRSxNQUFLO3dDQUNMakIsV0FBVTt3Q0FDVm1GLFVBQVU3Szt3Q0FDVjRLLFNBQVM7NENBQ1AsSUFBSSxDQUFDL0wsU0FBUzs0Q0FFZG9CLG9CQUFvQjs0Q0FDcEIsSUFBSTtnREFDRiwwQ0FBMEM7Z0RBQzFDLE1BQU1rTSxzQkFBc0JwTixTQUFTcU4sWUFBWSxDQUMvQ3ZOLFFBQVF5QyxTQUFTO2dEQUduQixJQUFJNksscUJBQXFCO29EQUN2Qix3Q0FBd0M7b0RBQ3hDcE4sU0FBU3NOLGtCQUFrQixDQUFDeE4sUUFBUXlDLFNBQVM7b0RBQzdDN0QseUNBQUtBLENBQUM2TyxPQUFPLENBQ1gsR0FBdUIsT0FBcEJ6TixRQUFRaUksV0FBVyxFQUFDO2dEQUUzQixPQUFPO3dEQUdZakksNkJBQUFBO29EQUZqQixrQkFBa0I7b0RBQ2xCLE1BQU0wTixhQUFhLFlBQThCLE9BQWxCMU4sUUFBUXlDLFNBQVM7b0RBQ2hELE1BQU1rTCxXQUFXM04sRUFBQUEsNkJBQUFBLFFBQVFnRSxpQkFBaUIsY0FBekJoRSxrREFBQUEsOEJBQUFBLDBCQUEyQixDQUFDLEVBQUUsY0FBOUJBLGtEQUFBQSw0QkFBZ0NzRSxhQUFhLEtBQUk7b0RBQ2xFLE1BQU13SSxRQUFROU0sUUFBUXVHLGFBQWEsSUFBSXZHLFFBQVF3RyxLQUFLO29EQUVwRHRHLFNBQVMwTixhQUFhLENBQ3BCNU4sUUFBUXlDLFNBQVMsRUFDakJ6QyxRQUFRaUksV0FBVyxFQUNuQnlGLFlBQ0FDLFVBQ0FiO29EQUVGbE8seUNBQUtBLENBQUM2TyxPQUFPLENBQUMsR0FBdUIsT0FBcEJ6TixRQUFRaUksV0FBVyxFQUFDO2dEQUN2Qzs0Q0FDRixFQUFFLE9BQU81RyxPQUFPO2dEQUNkd0IsUUFBUXhCLEtBQUssQ0FBQyw0QkFBNEJBO2dEQUMxQ3pDLHlDQUFLQSxDQUFDeUMsS0FBSyxDQUFDOzRDQUNkLFNBQVU7Z0RBQ1JELG9CQUFvQjs0Q0FDdEI7d0NBQ0Y7OzRDQUVDRCxpQ0FDQyw4REFBQ3lGO2dEQUFJQyxXQUFVOzs7OztxRUFFZiw4REFBQ25KLGlJQUFLQTtnREFDSm1KLFdBQVU7Z0RBQ1Z3RixNQUNFck0sV0FBV0UsU0FBU3FOLFlBQVksQ0FBQ3ZOLFFBQVF5QyxTQUFTLElBQzlDLGlCQUNBOzs7Ozs7MERBSVYsOERBQUNxRTtnREFBS0QsV0FBVTswREFDYjFGLG1CQUNHLGdCQUNBbkIsV0FBV0UsU0FBU3FOLFlBQVksQ0FBQ3ZOLFFBQVF5QyxTQUFTLElBQ2xELFdBQ0E7Ozs7Ozs7Ozs7OztrREFLUiw4REFBQ3FKO3dDQUNDaEUsTUFBSzt3Q0FDTGpCLFdBQVU7d0NBQ1ZrRixTQUFTOzRDQUNQLElBQUk4QixVQUFVQyxLQUFLLEVBQUU7Z0RBQ25CRCxVQUNHQyxLQUFLLENBQUM7b0RBQ0xDLE9BQU8vTixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNnTyxTQUFTLE1BQUloTyxvQkFBQUEsOEJBQUFBLFFBQVNpSSxXQUFXO29EQUNqRGdHLE1BQ0VqTyxDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNrTyxlQUFlLEtBQ3hCLDJCQUFnRCxPQUFyQmxPLG9CQUFBQSw4QkFBQUEsUUFBU2lJLFdBQVc7b0RBQ2pERixLQUFLcUYsT0FBT0MsUUFBUSxDQUFDN0QsSUFBSTtnREFDM0IsR0FDQzJFLEtBQUssQ0FBQyxDQUFDQyxNQUFRdkwsUUFBUXhCLEtBQUssQ0FBQyxrQkFBa0IrTTs0Q0FDcEQsT0FBTztnREFDTCwrQkFBK0I7Z0RBQy9CUCxVQUFVUSxTQUFTLENBQUNDLFNBQVMsQ0FBQ2xCLE9BQU9DLFFBQVEsQ0FBQzdELElBQUk7Z0RBQ2xENUsseUNBQUtBLENBQUM2TyxPQUFPLENBQUM7NENBQ2hCO3dDQUNGOzswREFFQSw4REFBQzlQLGlJQUFNQTtnREFBQ2tKLFdBQVU7Ozs7OzswREFDbEIsOERBQUNDO2dEQUFLRCxXQUFVOzBEQUFtQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQUt0RDdHLFFBQVF1TyxZQUFZLGtCQUNuQiw4REFBQzNIO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2lFO3dDQUFHakUsV0FBVTtrREFBaUM7Ozs7OztrREFDL0MsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNaN0csUUFBUXVPLFlBQVksQ0FBQzlKLEtBQUssQ0FBQyxLQUFLQyxHQUFHLENBQUMsQ0FBQzhKLFNBQVNwRyxzQkFDN0MsOERBQUN6Six1REFBS0E7Z0RBRUo4UCxTQUFRO2dEQUNSNUgsV0FBVTswREFFVDJILFFBQVE1SixJQUFJOytDQUpSd0Q7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBWWRwSSxRQUFRa08sZUFBZSxrQkFDdEIsOERBQUN0SDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNpRTt3Q0FBR2pFLFdBQVU7OzBEQUNaLDhEQUFDL0ksaUlBQUtBO2dEQUFDK0ksV0FBVTs7Ozs7OzRDQUFnQzs7Ozs7OztrREFHbkQsOERBQUMwQzt3Q0FBRTFDLFdBQVU7a0RBQ1Y3RyxRQUFRa08sZUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFsQyw4REFBQ3RIO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDdEkscURBQUlBO29CQUNIbVEsY0FBYTtvQkFDYjdILFdBQVU7b0JBQ1Y4SCxPQUFPcE47b0JBQ1BxTixlQUFlcE47O3NDQUVmLDhEQUFDL0MseURBQVFBOzRCQUFDb0ksV0FBVTs7OENBQ2xCLDhEQUFDbkksNERBQVdBO29DQUNWaVEsT0FBTTtvQ0FDTjlILFdBQVU7b0NBQ1ZnSSxPQUFPO3dDQUNMQyxpQkFDRXZOLGNBQWMsZ0JBQ1ZuQixlQUNBO3dDQUNOMk8sT0FDRXhOLGNBQWMsZ0JBQ1ZsQixtQkFDQTt3Q0FDTjJPLFdBQ0V6TixjQUFjLGdCQUFnQixnQkFBZ0I7d0NBQ2hEME4sV0FDRTFOLGNBQWMsZ0JBQ1YsNEVBQ0E7d0NBQ04yTixhQUNFM04sY0FBYyxnQkFBZ0JuQixlQUFlO29DQUNqRDs4Q0FDRDs7Ozs7OzhDQUdELDhEQUFDMUIsNERBQVdBO29DQUNWaVEsT0FBTTtvQ0FDTjlILFdBQVU7b0NBQ1ZnSSxPQUFPO3dDQUNMQyxpQkFDRXZOLGNBQWMsWUFBWW5CLGVBQWU7d0NBQzNDMk8sT0FDRXhOLGNBQWMsWUFBWWxCLG1CQUFtQjt3Q0FDL0MyTyxXQUFXek4sY0FBYyxZQUFZLGdCQUFnQjt3Q0FDckQwTixXQUNFMU4sY0FBYyxZQUNWLDRFQUNBO3dDQUNOMk4sYUFDRTNOLGNBQWMsWUFBWW5CLGVBQWU7b0NBQzdDOzhDQUNEOzs7Ozs7OENBR0QsOERBQUMxQiw0REFBV0E7b0NBQ1ZpUSxPQUFNO29DQUNOOUgsV0FBVTtvQ0FDVmdJLE9BQU87d0NBQ0xDLGlCQUNFdk4sY0FBYyxhQUFhbkIsZUFBZTt3Q0FDNUMyTyxPQUNFeE4sY0FBYyxhQUFhbEIsbUJBQW1CO3dDQUNoRDJPLFdBQ0V6TixjQUFjLGFBQWEsZ0JBQWdCO3dDQUM3QzBOLFdBQ0UxTixjQUFjLGFBQ1YsNEVBQ0E7d0NBQ04yTixhQUNFM04sY0FBYyxhQUFhbkIsZUFBZTtvQ0FDOUM7OENBQ0Q7Ozs7Ozs7Ozs7OztzQ0FLSCw4REFBQzVCLDREQUFXQTs0QkFDVm1RLE9BQU07NEJBQ045SCxXQUFVO3NDQUVWLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNpRTt3Q0FBR2pFLFdBQVU7a0RBQTZCOzs7Ozs7b0NBQzFDN0csUUFBUW1QLGVBQWUsaUJBQ3RCLDhEQUFDdkk7d0NBQ0NDLFdBQVU7d0NBQ1Z1SSx5QkFBeUI7NENBQUVDLFFBQVFyUCxRQUFRbVAsZUFBZTt3Q0FBQzs7Ozs7NkRBRzdELDhEQUFDdkk7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDMEM7Z0RBQUUxQyxXQUFVOzBEQUNWN0csUUFBUTZLLGdCQUFnQixJQUN2Qjs7Ozs7OzBEQUVKLDhEQUFDakU7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNvRTtnRUFBR3BFLFdBQVU7MEVBQW1DOzs7Ozs7MEVBR2pELDhEQUFDeUk7Z0VBQUd6SSxXQUFVOztrRkFDWiw4REFBQzBJO2tGQUFHOzs7Ozs7a0ZBQ0osOERBQUNBO2tGQUFHOzs7Ozs7a0ZBQ0osOERBQUNBO2tGQUFHOzs7Ozs7a0ZBQ0osOERBQUNBO2tGQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0VBR1IsOERBQUMzSTt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNvRTtnRUFBR3BFLFdBQVU7MEVBQW1DOzs7Ozs7MEVBR2pELDhEQUFDeUk7Z0VBQUd6SSxXQUFVOztrRkFDWiw4REFBQzBJO2tGQUFHOzs7Ozs7a0ZBQ0osOERBQUNBO2tGQUFHOzs7Ozs7a0ZBQ0osOERBQUNBO2tGQUFHOzs7Ozs7a0ZBQ0osOERBQUNBO2tGQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FTbEIsOERBQUMvUSw0REFBV0E7NEJBQ1ZtUSxPQUFNOzRCQUNOOUgsV0FBVTtzQ0FFViw0RUFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDaUU7d0NBQUdqRSxXQUFVO2tEQUE2Qjs7Ozs7O2tEQUczQyw4REFBQzNILCtGQUFxQkE7d0NBQ3BCOEwsWUFBWWhMLFFBQVFnQyxjQUFjLElBQUksRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSzlDLDhEQUFDeEQsNERBQVdBOzRCQUNWbVEsT0FBTTs0QkFDTjlILFdBQVU7c0NBRVYsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ2lFO3dDQUFHakUsV0FBVTtrREFBNkI7Ozs7OztrREFDM0MsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTtrRUFDWjs0REFBQzs0REFBRzs0REFBRzs0REFBRzs0REFBRzt5REFBRSxDQUFDbkMsR0FBRyxDQUFDLENBQUM4SyxxQkFDcEIsOERBQUM1UixpSUFBSUE7Z0VBRUhpSixXQUFXLFdBSVYsT0FIQzJJLFFBQVF4SSxLQUFLMEQsS0FBSyxDQUFDMUssUUFBUTJLLE1BQU0sSUFBSSxLQUNqQyxvQ0FDQTsrREFKRDZFOzs7Ozs7Ozs7O2tFQVNYLDhEQUFDNUk7d0RBQUlDLFdBQVU7OzBFQUNiLDhEQUFDQztnRUFBS0QsV0FBVTswRUFDYjdHLEVBQUFBLGtCQUFBQSxRQUFRMkssTUFBTSxjQUFkM0ssc0NBQUFBLGdCQUFnQitHLE9BQU8sQ0FBQyxPQUFNOzs7Ozs7NERBQ3pCOzREQUFJOzREQUVYL0csUUFBUTRLLFlBQVksaUJBQ25CLDhEQUFDOUQ7O29FQUNFO29FQUFJO29FQUNGOUcsUUFBUTRLLFlBQVk7b0VBQUM7b0VBQ3ZCNUssUUFBUTRLLFlBQVksS0FBSyxJQUFJLE1BQU07Ozs7Ozt1RUFFcEM7Ozs7Ozs7Ozs7Ozs7MERBSVIsOERBQUNoRTtnREFBSUMsV0FBVTswREFDYiw0RUFBQ3RILCtFQUFxQkE7b0RBQ3BCUSxXQUFXQyxRQUFReUMsU0FBUztvREFDNUJnTixXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVL0I7R0FweUNTM1A7O1FBQ01oQiwyREFBT0E7UUFDSEMsb0VBQVdBO1FBQ1hDLG9FQUFXQTtRQUNlQyxvRUFBV0E7OztLQUovQ2E7QUFzeUNULGlFQUFlQSxvQkFBb0JBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xceWh5YXNvZnRcXERvd25sb2Fkc1xcZWNcXC5ORVQgOCBWZXJzaW9uIC0gTGF0ZXN0XFxwcm9qZWN0XFxjb2RlbWVkaWNhbFxccHJvamVjdDNcXGFwcFxccHJvZHVjdFxcW2lkXVxccHJvZHVjdC1kZXRhaWxzLWNsaWVudC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZU1lbW8sIHVzZUNhbGxiYWNrIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VQYXJhbXMgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XG5pbXBvcnQgYXhpb3MgZnJvbSBcImF4aW9zXCI7XG5pbXBvcnQge1xuICBTaG9wcGluZ0NhcnQsXG4gIEhlYXJ0LFxuICBTaGFyZTIsXG4gIFN0YXIsXG4gIENoZXZyb25SaWdodCxcbiAgQ2hldnJvbkRvd24sXG4gIEFycm93TGVmdCxcbiAgQ2hlY2ssXG4gIFRydWNrLFxuICBSb3RhdGVDY3csXG4gIEF3YXJkLFxuICBDbG9jayxcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIjtcbmltcG9ydCB7XG4gIEJyZWFkY3J1bWIsXG4gIEJyZWFkY3J1bWJJdGVtLFxuICBCcmVhZGNydW1iTGluayxcbiAgQnJlYWRjcnVtYkxpc3QsXG4gIEJyZWFkY3J1bWJQYWdlLFxuICBCcmVhZGNydW1iU2VwYXJhdG9yLFxufSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2JyZWFkY3J1bWJcIjtcbmltcG9ydCB7IFRhYnMsIFRhYnNDb250ZW50LCBUYWJzTGlzdCwgVGFic1RyaWdnZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3RhYnNcIjtcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9iYWRnZVwiO1xuaW1wb3J0IHsgdG9hc3QgfSBmcm9tIFwic29ubmVyXCI7XG5pbXBvcnQgeyBzaG93TW9kZXJuQWRkVG9DYXJ0VG9hc3QgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL21vZGVybi10b2FzdFwiO1xuaW1wb3J0IHsgdXNlQ2FydCB9IGZyb20gXCJAL2NvbnRleHRzL2NhcnQtY29udGV4dFwiO1xuaW1wb3J0IHsgdXNlV2lzaGxpc3QgfSBmcm9tIFwiQC9jb250ZXh0cy93aXNobGlzdC1jb250ZXh0XCI7XG5pbXBvcnQgeyB1c2VDdXJyZW5jeSB9IGZyb20gXCJAL2NvbnRleHRzL2N1cnJlbmN5LWNvbnRleHRcIjtcbmltcG9ydCB7IHVzZVNldHRpbmdzIH0gZnJvbSBcIkAvY29udGV4dHMvc2V0dGluZ3MtY29udGV4dFwiO1xuaW1wb3J0IHsgUHJvZHVjdFNwZWNpZmljYXRpb25zIH0gZnJvbSBcIkAvY29tcG9uZW50cy9wcm9kdWN0cy9wcm9kdWN0LXNwZWNpZmljYXRpb25zXCI7XG5pbXBvcnQgeyBQcm9kdWN0TWVkaWFHYWxsZXJ5IH0gZnJvbSBcIkAvY29tcG9uZW50cy9wcm9kdWN0cy9wcm9kdWN0LW1lZGlhLWdhbGxlcnlcIjtcbmltcG9ydCB7IENvdW50ZG93biB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY291bnRkb3duXCI7XG5pbXBvcnQgUHJvZHVjdExvYWRpbmcgZnJvbSBcIi4vcHJvZHVjdC1sb2FkaW5nXCI7XG5pbXBvcnQgUHJvZHVjdEVycm9yIGZyb20gXCIuL3Byb2R1Y3QtZXJyb3JcIjtcbmltcG9ydCBQcm9kdWN0UmV2aWV3c0Rpc3BsYXkgZnJvbSBcIkAvY29tcG9uZW50cy91aS9wcm9kdWN0LXJldmlld3MtZGlzcGxheVwiO1xuXG50eXBlIE1lZGlhSXRlbSA9IHtcbiAgdHlwZTogXCJpbWFnZVwiIHwgXCJ2aWRlb1wiO1xuICB1cmw6IHN0cmluZztcbiAgdGh1bWJuYWlsPzogc3RyaW5nO1xuICBhbHQ/OiBzdHJpbmc7XG59O1xuXG5pbnRlcmZhY2UgUHJvZHVjdEltYWdlIHtcbiAgQXR0YWNobWVudElEOiBudW1iZXI7XG4gIEF0dGFjaG1lbnROYW1lOiBzdHJpbmc7XG4gIEF0dGFjaG1lbnRVUkw6IHN0cmluZztcbiAgUHJvZHVjdElEOiBudW1iZXI7XG4gIElzUHJpbWFyeT86IGJvb2xlYW47XG59XG5cbmludGVyZmFjZSBQcm9kdWN0U2hpcHBpbmdNZXRob2Qge1xuICBTaGlwcGluZ01ldGhvZElEOiBudW1iZXI7XG4gIFNoaXBwaW5nTWV0aG9kTmFtZTogc3RyaW5nO1xufVxuXG5pbnRlcmZhY2UgUHJvZHVjdEF0dHJpYnV0ZSB7XG4gIFByb2R1Y3RJRDogbnVtYmVyO1xuICBQcm9kdWN0QXR0cmlidXRlSUQ6IG51bWJlcjtcbiAgQXR0cmlidXRlTmFtZTogc3RyaW5nO1xuICBEaXNwbGF5TmFtZTogc3RyaW5nO1xuICBBdHRyaWJ1dGVWYWx1ZUlEOiBudW1iZXI7XG4gIEF0dHJpYnV0ZVZhbHVlVGV4dDogc3RyaW5nO1xuICBQcmljZUFkanVzdG1lbnQ/OiBudW1iZXI7XG4gIFByaWNlQWRqdXN0bWVudFR5cGU/OiBudW1iZXI7XG59XG5cbmludGVyZmFjZSBQcm9kdWN0IHtcbiAgUHJvZHVjdElkOiBudW1iZXI7XG4gIFByb2R1Y3ROYW1lOiBzdHJpbmc7XG4gIFNob3J0RGVzY3JpcHRpb24/OiBzdHJpbmc7XG4gIEZ1bGxEZXNjcmlwdGlvbj86IHN0cmluZztcbiAgUHJpY2U6IG51bWJlcjtcbiAgRGlzY291bnRQcmljZT86IG51bWJlcjtcbiAgT2xkUHJpY2U/OiBudW1iZXI7XG4gIFByaWNlSVFEPzogbnVtYmVyO1xuICBQb2ludE5vPzogbnVtYmVyO1xuICBTdG9ja1F1YW50aXR5OiBudW1iZXI7XG4gIElzQm91bmRUb1N0b2NrUXVhbnRpdHk6IGJvb2xlYW47XG4gIERpc3BsYXlTdG9ja1F1YW50aXR5OiBib29sZWFuO1xuICBNZXRhVGl0bGU/OiBzdHJpbmc7XG4gIE1ldGFLZXl3b3Jkcz86IHN0cmluZztcbiAgTWV0YURlc2NyaXB0aW9uPzogc3RyaW5nO1xuICBWZW5kb3JOYW1lPzogc3RyaW5nO1xuICBSYXRpbmc6IG51bWJlcjtcbiAgVG90YWxSZXZpZXdzPzogbnVtYmVyO1xuICBJc1NoaXBwaW5nRnJlZTogYm9vbGVhbjtcbiAgSXNSZXR1cm5BYmxlOiBib29sZWFuO1xuICBNYXJrQXNOZXc6IGJvb2xlYW47XG4gIE9yZGVyTWF4aW11bVF1YW50aXR5OiBudW1iZXI7XG4gIE9yZGVyTWluaW11bVF1YW50aXR5OiBudW1iZXI7XG4gIEVzdGltYXRlZFNoaXBwaW5nRGF5czogbnVtYmVyO1xuICBJc0Rpc2NvdW50QWxsb3dlZDogYm9vbGVhbjtcbiAgUHJvZHVjdEltYWdlc0pzb246IFByb2R1Y3RJbWFnZVtdO1xuICBQcm9kdWN0U2hpcE1ldGhvZHNKc29uPzogUHJvZHVjdFNoaXBwaW5nTWV0aG9kW107XG4gIENhdGVnb3J5SUQ/OiBudW1iZXI7XG4gIENhdGVnb3J5TmFtZT86IHN0cmluZztcbiAgVmlkZW9MaW5rPzogc3RyaW5nO1xuICBBdHRyaWJ1dGVzSnNvbj86IFByb2R1Y3RBdHRyaWJ1dGVbXTtcbiAgU2VsbFN0YXJ0RGF0ZXRpbWVVVEM/OiBzdHJpbmc7XG4gIFNlbGxFbmREYXRldGltZVVUQz86IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIFByb2R1Y3REZXRhaWxzUHJvcHMge1xuICBwcm9kdWN0SWQ6IHN0cmluZztcbn1cblxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGNvbnN0cnVjdCBpbWFnZSBVUkxcbmNvbnN0IGNvbnN0cnVjdEltYWdlVXJsID0gKGF0dGFjaG1lbnRVcmw6IHN0cmluZyB8IG51bGwpID0+IHtcbiAgaWYgKCFhdHRhY2htZW50VXJsKSByZXR1cm4gXCIvcGxhY2Vob2xkZXIuc3ZnP2hlaWdodD00MDAmd2lkdGg9NDAwXCI7XG5cbiAgaWYgKGF0dGFjaG1lbnRVcmwuc3RhcnRzV2l0aChcImh0dHBcIikpIHtcbiAgICByZXR1cm4gYXR0YWNobWVudFVybDtcbiAgfVxuXG4gIGNvbnN0IGJhc2VVcmwgPSBcImh0dHBzOi8vYWRtaW4uY29kZW1lZGljYWxhcHBzLmNvbVwiO1xuICAvLyBOb3JtYWxpemUgcGF0aCAoZW5zdXJlIGl0IHN0YXJ0cyB3aXRoIGV4YWN0bHkgb25lIHNsYXNoKVxuICBsZXQgbm9ybWFsaXplZFBhdGggPSBhdHRhY2htZW50VXJsLnN0YXJ0c1dpdGgoXCIvXCIpXG4gICAgPyBhdHRhY2htZW50VXJsXG4gICAgOiBgLyR7YXR0YWNobWVudFVybH1gO1xuICAvLyBSZW1vdmUgYW55IGRvdWJsZSBzbGFzaGVzIGluIHRoZSBwYXRoXG4gIG5vcm1hbGl6ZWRQYXRoID0gbm9ybWFsaXplZFBhdGgucmVwbGFjZSgvXFwvKy9nLCAnLycpO1xuXG4gIHJldHVybiBgJHtiYXNlVXJsfSR7bm9ybWFsaXplZFBhdGh9YDtcbn07XG5cbmZ1bmN0aW9uIFByb2R1Y3REZXRhaWxzQ2xpZW50KHsgcHJvZHVjdElkIH06IFByb2R1Y3REZXRhaWxzUHJvcHMpIHtcbiAgY29uc3QgY2FydCA9IHVzZUNhcnQoKTtcbiAgY29uc3Qgd2lzaGxpc3QgPSB1c2VXaXNobGlzdCgpO1xuICBjb25zdCB7IHJhdGUgfSA9IHVzZUN1cnJlbmN5KCk7XG4gIGNvbnN0IHsgcHJpbWFyeUNvbG9yLCBwcmltYXJ5VGV4dENvbG9yIH0gPSB1c2VTZXR0aW5ncygpO1xuXG4gIGNvbnN0IFtwcm9kdWN0LCBzZXRQcm9kdWN0XSA9IHVzZVN0YXRlPFByb2R1Y3QgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGU8Ym9vbGVhbj4odHJ1ZSk7XG4gIGNvbnN0IFtxdWFudGl0eSwgc2V0UXVhbnRpdHldID0gdXNlU3RhdGU8bnVtYmVyPigxKTtcbiAgY29uc3QgW2FjdGl2ZUltYWdlLCBzZXRBY3RpdmVJbWFnZV0gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiXCIpO1xuICBjb25zdCBbdmlkZW9MaW5rcywgc2V0VmlkZW9MaW5rc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oW10pO1xuICBjb25zdCBbc2VsZWN0ZWRWaWRlb0luZGV4LCBzZXRTZWxlY3RlZFZpZGVvSW5kZXhdID0gdXNlU3RhdGU8bnVtYmVyPigwKTtcbiAgY29uc3QgW2FkZGluZ1RvQ2FydCwgc2V0QWRkaW5nVG9DYXJ0XSA9IHVzZVN0YXRlPGJvb2xlYW4+KGZhbHNlKTtcbiAgY29uc3QgW2FkZGluZ1RvV2lzaGxpc3QsIHNldEFkZGluZ1RvV2lzaGxpc3RdID0gdXNlU3RhdGU8Ym9vbGVhbj4oZmFsc2UpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbYWN0aXZlVGFiLCBzZXRBY3RpdmVUYWJdID0gdXNlU3RhdGU8c3RyaW5nPihcImRlc2NyaXB0aW9uXCIpO1xuICBjb25zdCBbaXNBbmltYXRpbmcsIHNldElzQW5pbWF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2FuaW1hdGlvblR5cGUsIHNldEFuaW1hdGlvblR5cGVdID0gdXNlU3RhdGU8XG4gICAgXCJpbmNyZW1lbnRcIiB8IFwiZGVjcmVtZW50XCIgfCBudWxsXG4gID4obnVsbCk7XG4gIGNvbnN0IFtzZWxlY3RlZEF0dHJpYnV0ZXMsIHNldFNlbGVjdGVkQXR0cmlidXRlc10gPSB1c2VTdGF0ZTx7XG4gICAgW2tleTogc3RyaW5nXTogYm9vbGVhbjtcbiAgfT4oKCkgPT4ge1xuICAgIC8vIEluaXRpYWxpemUgd2l0aCBmaXJzdCBvcHRpb24gc2VsZWN0ZWQgZm9yIGVhY2ggYXR0cmlidXRlIGlmIG5vbmUgc2VsZWN0ZWRcbiAgICBjb25zdCBpbml0aWFsOiB7IFtrZXk6IHN0cmluZ106IGJvb2xlYW4gfSA9IHt9O1xuICAgIGlmIChwcm9kdWN0Py5BdHRyaWJ1dGVzSnNvbikge1xuICAgICAgcHJvZHVjdC5BdHRyaWJ1dGVzSnNvbi5mb3JFYWNoKChhdHRyKSA9PiB7XG4gICAgICAgIGNvbnN0IGtleSA9IGAke2F0dHIuUHJvZHVjdEF0dHJpYnV0ZUlEfV8ke2F0dHIuQXR0cmlidXRlVmFsdWVJRH1gO1xuICAgICAgICBpbml0aWFsW2tleV0gPSB0cnVlOyAvLyBTZWxlY3QgZmlyc3Qgb3B0aW9uIGJ5IGRlZmF1bHRcbiAgICAgIH0pO1xuICAgIH1cbiAgICByZXR1cm4gaW5pdGlhbDtcbiAgfSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBmZXRjaFByb2R1Y3QoKTtcbiAgfSwgW3Byb2R1Y3RJZF0pO1xuXG4gIGNvbnN0IGZldGNoUHJvZHVjdCA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgIHNldEVycm9yKG51bGwpO1xuICAgIHRyeSB7XG4gICAgICAvLyBUcnkgZGlyZWN0IEFQSSBjYWxsIGZpcnN0LCB0aGVuIGZhbGxiYWNrIHRvIHByb3h5IGlmIENPUlMgaXNzdWVzXG4gICAgICBjb25zdCByZXF1ZXN0Qm9keSA9IHtcbiAgICAgICAgcmVxdWVzdFBhcmFtZXRlcnM6IHtcbiAgICAgICAgICBQcm9kdWN0SWQ6IE51bWJlci5wYXJzZUludChwcm9kdWN0SWQsIDEwKSxcbiAgICAgICAgICByZWNvcmRWYWx1ZUpzb246IFwiW11cIixcbiAgICAgICAgfSxcbiAgICAgIH07XG5cbiAgICAgIGNvbnNvbGUubG9nKFxuICAgICAgICBcIkZldGNoaW5nIHByb2R1Y3Qgd2l0aCBJRDpcIixcbiAgICAgICAgcHJvZHVjdElkLFxuICAgICAgICBcIlJlcXVlc3QgYm9keTpcIixcbiAgICAgICAgcmVxdWVzdEJvZHlcbiAgICAgICk7XG5cbiAgICAgIGxldCByZXNwb25zZTtcbiAgICAgIHRyeSB7XG4gICAgICAgIC8vIEltcG9ydCBNYWtlQXBpQ2FsbEFzeW5jIGZvciBKV1QgdG9rZW4gaGFuZGxpbmdcbiAgICAgICAgY29uc3QgeyBNYWtlQXBpQ2FsbEFzeW5jIH0gPSBhd2FpdCBpbXBvcnQoXCJAL2xpYi9hcGktaGVscGVyXCIpO1xuXG4gICAgICAgIC8vIFVzZSBBUEkgaGVscGVyIHdoaWNoIGF1dG9tYXRpY2FsbHkgaGFuZGxlcyBKV1QgdG9rZW5zIGFuZCByZW1vdmVzIFVzZXJJRFxuICAgICAgICByZXNwb25zZSA9IGF3YWl0IE1ha2VBcGlDYWxsQXN5bmMoXG4gICAgICAgICAgXCJnZXQtcHJvZHVjdF9kZXRhaWxcIixcbiAgICAgICAgICBudWxsLFxuICAgICAgICAgIHJlcXVlc3RCb2R5LFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIEFjY2VwdDogXCJhcHBsaWNhdGlvbi9qc29uXCIsXG4gICAgICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcbiAgICAgICAgICB9LFxuICAgICAgICAgIFwiUE9TVFwiLFxuICAgICAgICAgIHRydWVcbiAgICAgICAgKTtcbiAgICAgICAgY29uc29sZS5sb2coXCJBUEkgaGVscGVyIHJlc3BvbnNlOlwiLCByZXNwb25zZS5kYXRhKTtcbiAgICAgIH0gY2F0Y2ggKGFwaUhlbHBlckVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKFwiQVBJIGhlbHBlciBmYWlsZWQsIHRyeWluZyBwcm94eSByb3V0ZTpcIiwgYXBpSGVscGVyRXJyb3IpO1xuICAgICAgICAvLyBGYWxsYmFjayB0byBwcm94eSByb3V0ZVxuICAgICAgICByZXNwb25zZSA9IGF3YWl0IGF4aW9zLnBvc3QoXCIvYXBpL3Byb2R1Y3QtZGV0YWlsXCIsIHJlcXVlc3RCb2R5LCB7XG4gICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgQWNjZXB0OiBcImFwcGxpY2F0aW9uL2pzb25cIixcbiAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICAgIH0sXG4gICAgICAgIH0pO1xuICAgICAgICBjb25zb2xlLmxvZyhcIlByb3h5IEFQSSByZXNwb25zZTpcIiwgcmVzcG9uc2UuZGF0YSk7XG4gICAgICB9XG5cbiAgICAgIGlmIChyZXNwb25zZS5kYXRhKSB7XG4gICAgICAgIC8vIENoZWNrIGlmIHJlc3BvbnNlLmRhdGEgaGFzIGEgbmVzdGVkIGRhdGEgcHJvcGVydHkgKGZyb20gQVBJIHByb3h5KVxuICAgICAgICBjb25zdCByZXNwb25zZURhdGEgPSByZXNwb25zZS5kYXRhLmRhdGEgPyByZXNwb25zZS5kYXRhIDogcmVzcG9uc2UuZGF0YTtcblxuICAgICAgICBpZiAocmVzcG9uc2VEYXRhICYmIHJlc3BvbnNlRGF0YS5kYXRhKSB7XG4gICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIFBhcnNlIHRoZSByZXNwb25zZSBkYXRhXG4gICAgICAgICAgICBjb25zdCBwYXJzZWREYXRhID0gSlNPTi5wYXJzZShyZXNwb25zZURhdGEuZGF0YSk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcIlBhcnNlZCBwcm9kdWN0IGRhdGE6XCIsIHBhcnNlZERhdGEpO1xuXG4gICAgICAgICAgICBpZiAocGFyc2VkRGF0YSkge1xuICAgICAgICAgICAgICAvLyBUaGUgQVBJIG1pZ2h0IHJldHVybiBhbiBhcnJheSB3aXRoIG9uZSBpdGVtIG9yIGEgc2luZ2xlIG9iamVjdFxuICAgICAgICAgICAgICBjb25zdCBwcm9kdWN0RGF0YSA9IEFycmF5LmlzQXJyYXkocGFyc2VkRGF0YSlcbiAgICAgICAgICAgICAgICA/IHBhcnNlZERhdGFbMF1cbiAgICAgICAgICAgICAgICA6IHBhcnNlZERhdGE7XG5cbiAgICAgICAgICAgICAgaWYgKHByb2R1Y3REYXRhKSB7XG4gICAgICAgICAgICAgICAgLy8gRW5zdXJlIEF0dHJpYnV0ZXNKc29uIGlzIHByb3Blcmx5IHBhcnNlZCBpZiBpdCdzIGEgc3RyaW5nXG4gICAgICAgICAgICAgICAgaWYgKFxuICAgICAgICAgICAgICAgICAgcHJvZHVjdERhdGEuQXR0cmlidXRlc0pzb24gJiZcbiAgICAgICAgICAgICAgICAgIHR5cGVvZiBwcm9kdWN0RGF0YS5BdHRyaWJ1dGVzSnNvbiA9PT0gXCJzdHJpbmdcIlxuICAgICAgICAgICAgICAgICkge1xuICAgICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgcHJvZHVjdERhdGEuQXR0cmlidXRlc0pzb24gPSBKU09OLnBhcnNlKFxuICAgICAgICAgICAgICAgICAgICAgIHByb2R1Y3REYXRhLkF0dHJpYnV0ZXNKc29uXG4gICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBwYXJzaW5nIEF0dHJpYnV0ZXNKc29uOlwiLCBlKTtcbiAgICAgICAgICAgICAgICAgICAgcHJvZHVjdERhdGEuQXR0cmlidXRlc0pzb24gPSBbXTtcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9IGVsc2UgaWYgKCFwcm9kdWN0RGF0YS5BdHRyaWJ1dGVzSnNvbikge1xuICAgICAgICAgICAgICAgICAgcHJvZHVjdERhdGEuQXR0cmlidXRlc0pzb24gPSBbXTtcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIlByb2R1Y3QgZGF0YSB3aXRoIGF0dHJpYnV0ZXM6XCIsIHByb2R1Y3REYXRhKTtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcbiAgICAgICAgICAgICAgICAgIFwiQ2F0ZWdvcnlOYW1lIGluIHByb2R1Y3QgZGF0YTpcIixcbiAgICAgICAgICAgICAgICAgIHByb2R1Y3REYXRhLkNhdGVnb3J5TmFtZVxuICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coXG4gICAgICAgICAgICAgICAgICBcIkNhdGVnb3J5SUQgaW4gcHJvZHVjdCBkYXRhOlwiLFxuICAgICAgICAgICAgICAgICAgcHJvZHVjdERhdGEuQ2F0ZWdvcnlJRFxuICAgICAgICAgICAgICAgICk7XG5cbiAgICAgICAgICAgICAgICBzZXRQcm9kdWN0KHByb2R1Y3REYXRhKTtcblxuICAgICAgICAgICAgICAgIC8vIFNldCBhY3RpdmUgaW1hZ2VcbiAgICAgICAgICAgICAgICBpZiAoXG4gICAgICAgICAgICAgICAgICBwcm9kdWN0RGF0YS5Qcm9kdWN0SW1hZ2VzSnNvbiAmJlxuICAgICAgICAgICAgICAgICAgcHJvZHVjdERhdGEuUHJvZHVjdEltYWdlc0pzb24ubGVuZ3RoID4gMFxuICAgICAgICAgICAgICAgICkge1xuICAgICAgICAgICAgICAgICAgY29uc3QgcHJpbWFyeUltYWdlID1cbiAgICAgICAgICAgICAgICAgICAgcHJvZHVjdERhdGEuUHJvZHVjdEltYWdlc0pzb24uZmluZChcbiAgICAgICAgICAgICAgICAgICAgICAoaW1nOiB7IElzUHJpbWFyeTogYm9vbGVhbjsgQXR0YWNobWVudFVSTDogc3RyaW5nIH0pID0+XG4gICAgICAgICAgICAgICAgICAgICAgICBpbWcuSXNQcmltYXJ5XG4gICAgICAgICAgICAgICAgICAgICkgfHwgcHJvZHVjdERhdGEuUHJvZHVjdEltYWdlc0pzb25bMF07XG4gICAgICAgICAgICAgICAgICBzZXRBY3RpdmVJbWFnZShjb25zdHJ1Y3RJbWFnZVVybChwcmltYXJ5SW1hZ2UuQXR0YWNobWVudFVSTCkpO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIC8vIEhhbmRsZSBjb21tYS1zZXBhcmF0ZWQgdmlkZW8gbGlua3NcbiAgICAgICAgICAgICAgICBpZiAocHJvZHVjdERhdGEuVmlkZW9MaW5rKSB7XG4gICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhcIlZpZGVvIGxpbmtzIGZvdW5kOlwiLCBwcm9kdWN0RGF0YS5WaWRlb0xpbmspO1xuICAgICAgICAgICAgICAgICAgY29uc3QgbGlua3MgPSBwcm9kdWN0RGF0YS5WaWRlb0xpbmsuc3BsaXQoXCIsXCIpLm1hcChcbiAgICAgICAgICAgICAgICAgICAgKGxpbms6IHN0cmluZykgPT4gbGluay50cmltKClcbiAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICBjb25zdCBwcm9jZXNzZWRMaW5rcyA9IGxpbmtzLm1hcCgobGluazogc3RyaW5nKSA9PlxuICAgICAgICAgICAgICAgICAgICBjb25zdHJ1Y3RWaWRlb1VybChsaW5rKVxuICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICAgIHNldFZpZGVvTGlua3MocHJvY2Vzc2VkTGlua3MpO1xuICAgICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRWaWRlb0luZGV4KDApO1xuICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgIC8vIFNldCBpbml0aWFsIHF1YW50aXR5IGJhc2VkIG9uIHByb2R1Y3QgbWluaW11bSBvcmRlciBxdWFudGl0eVxuICAgICAgICAgICAgICAgIGlmIChwcm9kdWN0RGF0YS5PcmRlck1pbmltdW1RdWFudGl0eSA+IDApIHtcbiAgICAgICAgICAgICAgICAgIHNldFF1YW50aXR5KHByb2R1Y3REYXRhLk9yZGVyTWluaW11bVF1YW50aXR5KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIk5vIHByb2R1Y3QgZGF0YSBmb3VuZCBpbiBwYXJzZWQgcmVzcG9uc2VcIik7XG4gICAgICAgICAgICAgICAgc2V0RXJyb3IoXG4gICAgICAgICAgICAgICAgICBgUHJvZHVjdCB3aXRoIElEICR7cHJvZHVjdElkfSBub3QgZm91bmQuIFBsZWFzZSBjaGVjayBpZiB0aGlzIHByb2R1Y3QgZXhpc3RzLmBcbiAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKFxuICAgICAgICAgICAgICAgIFwiSW52YWxpZCBwcm9kdWN0IGRhdGEgZm9ybWF0IC0gcGFyc2VkRGF0YSBpcyBudWxsL3VuZGVmaW5lZFwiXG4gICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgIHNldEVycm9yKFwiSW52YWxpZCBwcm9kdWN0IGRhdGEgZm9ybWF0XCIpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0gY2F0Y2ggKHBhcnNlRXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXG4gICAgICAgICAgICAgIFwiRXJyb3IgcGFyc2luZyBwcm9kdWN0IGRhdGE6XCIsXG4gICAgICAgICAgICAgIHBhcnNlRXJyb3IsXG4gICAgICAgICAgICAgIFwiUmF3IGRhdGE6XCIsXG4gICAgICAgICAgICAgIHJlc3BvbnNlRGF0YS5kYXRhXG4gICAgICAgICAgICApO1xuICAgICAgICAgICAgc2V0RXJyb3IoXCJFcnJvciBwYXJzaW5nIHByb2R1Y3QgZGF0YVwiKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS5lcnJvcihcIk5vIGRhdGEgcHJvcGVydHkgaW4gQVBJIHJlc3BvbnNlOlwiLCByZXNwb25zZS5kYXRhKTtcbiAgICAgICAgICBzZXRFcnJvcihcIk5vIGRhdGEgaW4gQVBJIHJlc3BvbnNlXCIpO1xuICAgICAgICB9XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKFwiRW1wdHkgcmVzcG9uc2UgZnJvbSBBUElcIik7XG4gICAgICAgIHNldEVycm9yKFwiRW1wdHkgcmVzcG9uc2UgZnJvbSBzZXJ2ZXJcIik7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGZldGNoaW5nIHByb2R1Y3Q6XCIsIGVycm9yKTtcblxuICAgICAgLy8gTW9yZSBkZXRhaWxlZCBlcnJvciBoYW5kbGluZ1xuICAgICAgaWYgKGVycm9yLnJlc3BvbnNlKSB7XG4gICAgICAgIC8vIFNlcnZlciByZXNwb25kZWQgd2l0aCBlcnJvciBzdGF0dXNcbiAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICBcIlNlcnZlciBlcnJvcjpcIixcbiAgICAgICAgICBlcnJvci5yZXNwb25zZS5zdGF0dXMsXG4gICAgICAgICAgZXJyb3IucmVzcG9uc2UuZGF0YVxuICAgICAgICApO1xuICAgICAgICBzZXRFcnJvcihcbiAgICAgICAgICBgU2VydmVyIGVycm9yOiAke2Vycm9yLnJlc3BvbnNlLnN0YXR1c30gLSAke1xuICAgICAgICAgICAgZXJyb3IucmVzcG9uc2UuZGF0YT8ubWVzc2FnZSB8fCBcIlVua25vd24gZXJyb3JcIlxuICAgICAgICAgIH1gXG4gICAgICAgICk7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLnJlcXVlc3QpIHtcbiAgICAgICAgLy8gUmVxdWVzdCB3YXMgbWFkZSBidXQgbm8gcmVzcG9uc2UgcmVjZWl2ZWRcbiAgICAgICAgY29uc29sZS5lcnJvcihcIk5ldHdvcmsgZXJyb3I6XCIsIGVycm9yLnJlcXVlc3QpO1xuICAgICAgICBzZXRFcnJvcihcIk5ldHdvcmsgZXJyb3IgLSBwbGVhc2UgY2hlY2sgeW91ciBjb25uZWN0aW9uXCIpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gU29tZXRoaW5nIGVsc2UgaGFwcGVuZWRcbiAgICAgICAgY29uc29sZS5lcnJvcihcIlJlcXVlc3Qgc2V0dXAgZXJyb3I6XCIsIGVycm9yLm1lc3NhZ2UpO1xuICAgICAgICBzZXRFcnJvcihgRXJyb3I6ICR7ZXJyb3IubWVzc2FnZX1gKTtcbiAgICAgIH1cbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGNvbnN0cnVjdFZpZGVvVXJsID0gKHZpZGVvTGluazogc3RyaW5nIHwgbnVsbCkgPT4ge1xuICAgIGlmICghdmlkZW9MaW5rKSByZXR1cm4gXCJcIjtcblxuICAgIGlmICh2aWRlb0xpbmsuaW5jbHVkZXMoXCJ5b3V0dWJlLmNvbVwiKSB8fCB2aWRlb0xpbmsuaW5jbHVkZXMoXCJ5b3V0dS5iZVwiKSkge1xuICAgICAgcmV0dXJuIHZpZGVvTGluaztcbiAgICB9XG4gICAgLy8gRm9yIE1QNCB2aWRlb3MsIHVzZSBhIHByb3h5IFVSTCB0byBoYW5kbGUgQ09SU1xuICAgIGlmICh2aWRlb0xpbmsuc3RhcnRzV2l0aChcImh0dHBcIikpIHtcbiAgICAgIHJldHVybiBgL2FwaS92aWRlby1wcm94eT91cmw9JHtlbmNvZGVVUklDb21wb25lbnQodmlkZW9MaW5rKX1gO1xuICAgIH1cbiAgICBjb25zdCBiYXNlVXJsID0gXCJodHRwczovL2FkbWluLmNvZGVtZWRpY2FsYXBwcy5jb21cIjtcbiAgICBjb25zdCBub3JtYWxpemVkVmlkZW9MaW5rID0gdmlkZW9MaW5rLnN0YXJ0c1dpdGgoXCIvXCIpXG4gICAgICA/IHZpZGVvTGlua1xuICAgICAgOiBgLyR7dmlkZW9MaW5rfWA7XG4gICAgcmV0dXJuIGAvYXBpL3ZpZGVvLXByb3h5P3VybD0ke2VuY29kZVVSSUNvbXBvbmVudChcbiAgICAgIGAke2Jhc2VVcmx9JHtub3JtYWxpemVkVmlkZW9MaW5rfWBcbiAgICApfWA7XG4gIH07XG5cbiAgLy8gR3JvdXAgYXR0cmlidXRlcyBieSBQcm9kdWN0QXR0cmlidXRlSURcbiAgY29uc3QgZ3JvdXBlZEF0dHJpYnV0ZXMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICBpZiAoIXByb2R1Y3Q/LkF0dHJpYnV0ZXNKc29uKSByZXR1cm4ge307XG5cbiAgICByZXR1cm4gcHJvZHVjdC5BdHRyaWJ1dGVzSnNvbi5yZWR1Y2UoKGdyb3VwcywgYXR0cikgPT4ge1xuICAgICAgY29uc3QgZ3JvdXBJZCA9IGF0dHIuUHJvZHVjdEF0dHJpYnV0ZUlEO1xuICAgICAgaWYgKCFncm91cHNbZ3JvdXBJZF0pIHtcbiAgICAgICAgZ3JvdXBzW2dyb3VwSWRdID0gW107XG4gICAgICB9XG4gICAgICBncm91cHNbZ3JvdXBJZF0ucHVzaChhdHRyKTtcbiAgICAgIHJldHVybiBncm91cHM7XG4gICAgfSwge30gYXMgUmVjb3JkPG51bWJlciwgdHlwZW9mIHByb2R1Y3QuQXR0cmlidXRlc0pzb24+KTtcbiAgfSwgW3Byb2R1Y3Q/LkF0dHJpYnV0ZXNKc29uXSk7XG5cbiAgLy8gSGFuZGxlIGF0dHJpYnV0ZSBzZWxlY3Rpb24gd2l0aCBjb25kaXRpb25hbCBiZWhhdmlvclxuICBjb25zdCBoYW5kbGVBdHRyaWJ1dGVDaGFuZ2UgPSAoXG4gICAgYXR0cjogUHJvZHVjdEF0dHJpYnV0ZSxcbiAgICBpc0NoZWNrZWQ6IGJvb2xlYW4sXG4gICAgaXNSYWRpb0dyb3VwOiBib29sZWFuXG4gICkgPT4ge1xuICAgIHNldFNlbGVjdGVkQXR0cmlidXRlcygocHJldikgPT4ge1xuICAgICAgY29uc3QgbmV3U3RhdGUgPSB7IC4uLnByZXYgfTtcbiAgICAgIGNvbnN0IGF0dHJLZXkgPSBgJHthdHRyLlByb2R1Y3RBdHRyaWJ1dGVJRH1fJHthdHRyLkF0dHJpYnV0ZVZhbHVlSUR9YDtcblxuICAgICAgLy8gRm9yIHJhZGlvIGdyb3VwcywgdW5jaGVjayBhbGwgb3RoZXIgYXR0cmlidXRlcyBpbiB0aGUgc2FtZSBncm91cFxuICAgICAgaWYgKGlzUmFkaW9Hcm91cCAmJiBpc0NoZWNrZWQpIHtcbiAgICAgICAgT2JqZWN0LmtleXMocHJldikuZm9yRWFjaCgoa2V5KSA9PiB7XG4gICAgICAgICAgaWYgKFxuICAgICAgICAgICAga2V5LnN0YXJ0c1dpdGgoYCR7YXR0ci5Qcm9kdWN0QXR0cmlidXRlSUR9X2ApICYmXG4gICAgICAgICAgICBrZXkgIT09IGF0dHJLZXlcbiAgICAgICAgICApIHtcbiAgICAgICAgICAgIG5ld1N0YXRlW2tleV0gPSBmYWxzZTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgfVxuXG4gICAgICAvLyBTZXQgdGhlIHNlbGVjdGVkIGF0dHJpYnV0ZVxuICAgICAgLy8gRm9yIGNoZWNrYm94ZXMsIHRvZ2dsZSB0aGUgc3RhdGVcbiAgICAgIC8vIEZvciByYWRpbyBidXR0b25zLCBhbHdheXMgc2V0IHRvIHRydWUgKHNpbmNlIHdlIGFscmVhZHkgdW5zZXQgb3RoZXJzIGlmIG5lZWRlZClcbiAgICAgIG5ld1N0YXRlW2F0dHJLZXldID0gaXNSYWRpb0dyb3VwID8gdHJ1ZSA6ICFwcmV2W2F0dHJLZXldO1xuXG4gICAgICByZXR1cm4gbmV3U3RhdGU7XG4gICAgfSk7XG4gIH07XG5cbiAgLy8gUmVuZGVyIHByaWNlIHdpdGggYWxsIHByaWNlLXJlbGF0ZWQgaW5mb3JtYXRpb25cbiAgY29uc3QgcmVuZGVyUHJpY2UgPSAoKSA9PiB7XG4gICAgaWYgKCFwcm9kdWN0KSByZXR1cm4gbnVsbDtcblxuICAgIGNvbnN0IHNob3dEaXNjb3VudCA9XG4gICAgICBwcm9kdWN0LkRpc2NvdW50UHJpY2UgJiYgcHJvZHVjdC5EaXNjb3VudFByaWNlIDwgcHJvZHVjdC5QcmljZTtcbiAgICBjb25zdCBhZGp1c3RlZFByaWNlID0gY2FsY3VsYXRlQWRqdXN0ZWRQcmljZSgpO1xuICAgIGNvbnN0IHNob3dBZGp1c3RlZFByaWNlID1cbiAgICAgIGFkanVzdGVkUHJpY2UgIT09IHByb2R1Y3QuUHJpY2UgJiZcbiAgICAgIGFkanVzdGVkUHJpY2UgIT09IChwcm9kdWN0LkRpc2NvdW50UHJpY2UgfHwgcHJvZHVjdC5QcmljZSk7XG5cbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgIHsvKiBNYWluIFByaWNlICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtYmFzZWxpbmUgZ2FwLTJcIj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1wcmltYXJ5XCI+XG4gICAgICAgICAgICAkXG4gICAgICAgICAgICB7c2hvd0Rpc2NvdW50XG4gICAgICAgICAgICAgID8gKHByb2R1Y3QuRGlzY291bnRQcmljZSB8fCAwKS50b0ZpeGVkKDIpXG4gICAgICAgICAgICAgIDogYWRqdXN0ZWRQcmljZS50b0ZpeGVkKDIpfVxuICAgICAgICAgIDwvc3Bhbj5cblxuICAgICAgICAgIHsvKiBPbGQgUHJpY2UgKi99XG4gICAgICAgICAge3Nob3dEaXNjb3VudCAmJiAoXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZ3JheS00MDAgbGluZS10aHJvdWdoXCI+XG4gICAgICAgICAgICAgICR7cHJvZHVjdC5QcmljZS50b0ZpeGVkKDIpfVxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogQWRqdXN0ZWQgUHJpY2UgKi99XG4gICAgICAgICAge3Nob3dBZGp1c3RlZFByaWNlICYmICFzaG93RGlzY291bnQgJiYgKFxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LWdyYXktNDAwIGxpbmUtdGhyb3VnaFwiPlxuICAgICAgICAgICAgICAke3Byb2R1Y3QuUHJpY2UudG9GaXhlZCgyKX1cbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICApfVxuXG4gICAgICAgICAgey8qIERpc2NvdW50IEJhZGdlICovfVxuICAgICAgICAgIHtzaG93RGlzY291bnQgJiYgKFxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibWwtMiBweC0yIHB5LTEgYmctcmVkLTEwMCB0ZXh0LXJlZC04MDAgdGV4dC14cyBmb250LW1lZGl1bSByb3VuZGVkXCI+XG4gICAgICAgICAgICAgIHtNYXRoLnJvdW5kKFxuICAgICAgICAgICAgICAgICgocHJvZHVjdC5QcmljZSAtIChwcm9kdWN0LkRpc2NvdW50UHJpY2UgfHwgMCkpIC9cbiAgICAgICAgICAgICAgICAgIHByb2R1Y3QuUHJpY2UpICpcbiAgICAgICAgICAgICAgICAgIDEwMFxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAlIE9GRlxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBJUUQgUHJpY2UgKi99XG4gICAgICAgIHtwcm9kdWN0LlByaWNlSVFEICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTEgdGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICB7cHJvZHVjdC5QcmljZUlRRC50b0xvY2FsZVN0cmluZygpfSBJUURcbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogQ3JlZGl0ICovfVxuICAgICAgICB7cHJvZHVjdC5Qb2ludE5vICYmIHByb2R1Y3QuUG9pbnRObyA+IDAgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMlwiPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTIuNSBweS0wLjUgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gYmctYW1iZXItMTAwIHRleHQtYW1iZXItODAwXCI+XG4gICAgICAgICAgICAgIEJ1eSAmIEVhcm4ge3Byb2R1Y3QuUG9pbnROb30gJCBjcmVkaXRcbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogT2xkIFByaWNlIChpZiBkaWZmZXJlbnQgZnJvbSBjdXJyZW50IHByaWNlKSAqL31cbiAgICAgICAge3Byb2R1Y3QuT2xkUHJpY2UgJiYgcHJvZHVjdC5PbGRQcmljZSA+IHByb2R1Y3QuUHJpY2UgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMSB0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImxpbmUtdGhyb3VnaFwiPiR7cHJvZHVjdC5PbGRQcmljZS50b0ZpeGVkKDIpfTwvc3Bhbj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1ncmVlbi02MDBcIj5cbiAgICAgICAgICAgICAge01hdGgucm91bmQoXG4gICAgICAgICAgICAgICAgKChwcm9kdWN0Lk9sZFByaWNlIC0gKHByb2R1Y3QuRGlzY291bnRQcmljZSB8fCBwcm9kdWN0LlByaWNlKSkgL1xuICAgICAgICAgICAgICAgICAgcHJvZHVjdC5PbGRQcmljZSkgKlxuICAgICAgICAgICAgICAgICAgMTAwXG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICUgT0ZGXG4gICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIC8vIENhbGN1bGF0ZSBhZGp1c3RlZCBwcmljZSBiYXNlZCBvbiBzZWxlY3RlZCBhdHRyaWJ1dGVzXG4gIGNvbnN0IGNhbGN1bGF0ZUFkanVzdGVkUHJpY2UgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKCFwcm9kdWN0KSByZXR1cm4gMDtcblxuICAgIGxldCBhZGp1c3RlZFByaWNlID0gcHJvZHVjdC5QcmljZTtcblxuICAgIGlmIChwcm9kdWN0LkF0dHJpYnV0ZXNKc29uICYmIHByb2R1Y3QuQXR0cmlidXRlc0pzb24ubGVuZ3RoID4gMCkge1xuICAgICAgcHJvZHVjdC5BdHRyaWJ1dGVzSnNvbi5mb3JFYWNoKChhdHRyKSA9PiB7XG4gICAgICAgIGNvbnN0IGF0dHJLZXkgPSBgJHthdHRyLlByb2R1Y3RBdHRyaWJ1dGVJRH1fJHthdHRyLkF0dHJpYnV0ZVZhbHVlSUR9YDtcbiAgICAgICAgaWYgKFxuICAgICAgICAgIHNlbGVjdGVkQXR0cmlidXRlc1thdHRyS2V5XSAmJlxuICAgICAgICAgIHR5cGVvZiBhdHRyLlByaWNlQWRqdXN0bWVudCA9PT0gXCJudW1iZXJcIiAmJlxuICAgICAgICAgIHR5cGVvZiBhdHRyLlByaWNlQWRqdXN0bWVudFR5cGUgPT09IFwibnVtYmVyXCJcbiAgICAgICAgKSB7XG4gICAgICAgICAgc3dpdGNoIChhdHRyLlByaWNlQWRqdXN0bWVudFR5cGUpIHtcbiAgICAgICAgICAgIGNhc2UgMTogLy8gRml4ZWQgYW1vdW50XG4gICAgICAgICAgICAgIGFkanVzdGVkUHJpY2UgKz0gYXR0ci5QcmljZUFkanVzdG1lbnQ7XG4gICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgY2FzZSAyOiAvLyBQZXJjZW50YWdlXG4gICAgICAgICAgICAgIGFkanVzdGVkUHJpY2UgKz0gKHByb2R1Y3QuUHJpY2UgKiBhdHRyLlByaWNlQWRqdXN0bWVudCkgLyAxMDA7XG4gICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIE1hdGgubWF4KDAsIGFkanVzdGVkUHJpY2UpOyAvLyBFbnN1cmUgcHJpY2UgZG9lc24ndCBnbyBiZWxvdyAwXG4gIH0sIFtwcm9kdWN0LCBzZWxlY3RlZEF0dHJpYnV0ZXNdKTtcblxuICBjb25zdCBhZGp1c3RlZFByaWNlID0gdXNlTWVtbyhcbiAgICAoKSA9PiBjYWxjdWxhdGVBZGp1c3RlZFByaWNlKCksXG4gICAgW2NhbGN1bGF0ZUFkanVzdGVkUHJpY2VdXG4gICk7XG5cbiAgLy8gUmVuZGVyIHByb2R1Y3QgYmFkZ2VzXG4gIGNvbnN0IHJlbmRlckJhZGdlcyA9ICgpID0+IHtcbiAgICBpZiAoIXByb2R1Y3QpIHJldHVybiBudWxsO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTQgbGVmdC00IHotMTAgZmxleCBmbGV4LWNvbCBnYXAtMlwiPlxuICAgICAgICB7cHJvZHVjdC5Jc0Rpc2NvdW50QWxsb3dlZCAmJiAoXG4gICAgICAgICAgPEJhZGdlIGNsYXNzTmFtZT1cImJnLXJlZC01MDAgaG92ZXI6YmctcmVkLTYwMCB0ZXh0LXdoaXRlIHRleHQtc20gZm9udC1ib2xkIHB4LTMgcHktMVwiPlxuICAgICAgICAgICAgU0FMRVxuICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICl9XG4gICAgICAgIHtwcm9kdWN0Lk1hcmtBc05ldyAmJiAoXG4gICAgICAgICAgPEJhZGdlIGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwMCBob3ZlcjpiZy1ncmVlbi02MDAgdGV4dC13aGl0ZSB0ZXh0LXNtIGZvbnQtYm9sZCBweC0zIHB5LTFcIj5cbiAgICAgICAgICAgIE5FV1xuICAgICAgICAgIDwvQmFkZ2U+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9O1xuXG4gIC8vIENvbWJpbmUgaW1hZ2VzIGFuZCB2aWRlb3MgaW50byBhIHNpbmdsZSBtZWRpYSBhcnJheSBmb3IgdGhlIGdhbGxlcnlcbiAgY29uc3QgbWVkaWFJdGVtcyA9IHVzZU1lbW88TWVkaWFJdGVtW10+KCgpID0+IHtcbiAgICBjb25zdCBpdGVtczogTWVkaWFJdGVtW10gPSBbXTtcblxuICAgIC8vIEFkZCBwcm9kdWN0IGltYWdlc1xuICAgIGlmIChwcm9kdWN0Py5Qcm9kdWN0SW1hZ2VzSnNvbj8ubGVuZ3RoKSB7XG4gICAgICBwcm9kdWN0LlByb2R1Y3RJbWFnZXNKc29uLmZvckVhY2goKGltZykgPT4ge1xuICAgICAgICBpdGVtcy5wdXNoKHtcbiAgICAgICAgICB0eXBlOiBcImltYWdlXCIgYXMgY29uc3QsXG4gICAgICAgICAgdXJsOiBjb25zdHJ1Y3RJbWFnZVVybChpbWcuQXR0YWNobWVudFVSTCksXG4gICAgICAgICAgYWx0OiBwcm9kdWN0Py5Qcm9kdWN0TmFtZSB8fCBcIlByb2R1Y3QgaW1hZ2VcIixcbiAgICAgICAgICB0aHVtYm5haWw6IGNvbnN0cnVjdEltYWdlVXJsKGltZy5BdHRhY2htZW50VVJMKSxcbiAgICAgICAgfSk7XG4gICAgICB9KTtcbiAgICB9XG5cbiAgICAvLyBBZGQgdmlkZW9zXG4gICAgdmlkZW9MaW5rcy5mb3JFYWNoKCh2aWRlb1VybCwgaW5kZXgpID0+IHtcbiAgICAgIGl0ZW1zLnB1c2goe1xuICAgICAgICB0eXBlOiBcInZpZGVvXCIgYXMgY29uc3QsXG4gICAgICAgIHVybDogdmlkZW9VcmwsXG4gICAgICAgIGFsdDogYCR7cHJvZHVjdD8uUHJvZHVjdE5hbWUgfHwgXCJQcm9kdWN0XCJ9IC0gVmlkZW8gJHtpbmRleCArIDF9YCxcbiAgICAgICAgLy8gRG9uJ3Qgc2V0IHRodW1ibmFpbCBzbyBWaWRlb1RodW1ibmFpbCBjb21wb25lbnQgd2lsbCBnZW5lcmF0ZSBpdCBmcm9tIHRoZSB2aWRlb1xuICAgICAgfSk7XG4gICAgfSk7XG5cbiAgICByZXR1cm4gaXRlbXM7XG4gIH0sIFtwcm9kdWN0LCB2aWRlb0xpbmtzLCBhY3RpdmVJbWFnZV0pO1xuXG4gIGNvbnN0IGFuaW1hdGVDb3VudGVyID0gKHR5cGU6IFwiaW5jcmVtZW50XCIgfCBcImRlY3JlbWVudFwiKSA9PiB7XG4gICAgc2V0QW5pbWF0aW9uVHlwZSh0eXBlKTtcbiAgICBzZXRJc0FuaW1hdGluZyh0cnVlKTtcbiAgICBzZXRUaW1lb3V0KCgpID0+IHNldElzQW5pbWF0aW5nKGZhbHNlKSwgMzAwKTtcbiAgfTtcblxuICBjb25zdCBpbmNyZW1lbnRRdWFudGl0eSA9ICgpID0+IHtcbiAgICBpZiAocHJvZHVjdCkge1xuICAgICAgY29uc3QgbWF4UXVhbnRpdHkgPVxuICAgICAgICBwcm9kdWN0Lk9yZGVyTWF4aW11bVF1YW50aXR5ID4gMFxuICAgICAgICAgID8gTWF0aC5taW4ocHJvZHVjdC5PcmRlck1heGltdW1RdWFudGl0eSwgcHJvZHVjdC5TdG9ja1F1YW50aXR5KVxuICAgICAgICAgIDogcHJvZHVjdC5TdG9ja1F1YW50aXR5O1xuICAgICAgaWYgKHF1YW50aXR5IDwgbWF4UXVhbnRpdHkpIHtcbiAgICAgICAgc2V0UXVhbnRpdHkoKHByZXYpID0+IHByZXYgKyAxKTtcbiAgICAgICAgYW5pbWF0ZUNvdW50ZXIoXCJpbmNyZW1lbnRcIik7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBWaXN1YWwgZmVlZGJhY2sgd2hlbiBtYXggcXVhbnRpdHkgaXMgcmVhY2hlZFxuICAgICAgICB0b2FzdC5pbmZvKGBNYXhpbXVtIHF1YW50aXR5IG9mICR7bWF4UXVhbnRpdHl9IHJlYWNoZWRgKTtcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZGVjcmVtZW50UXVhbnRpdHkgPSAoKSA9PiB7XG4gICAgaWYgKHByb2R1Y3QpIHtcbiAgICAgIGNvbnN0IG1pblF1YW50aXR5ID1cbiAgICAgICAgcHJvZHVjdC5PcmRlck1pbmltdW1RdWFudGl0eSA+IDAgPyBwcm9kdWN0Lk9yZGVyTWluaW11bVF1YW50aXR5IDogMTtcbiAgICAgIGlmIChxdWFudGl0eSA+IG1pblF1YW50aXR5KSB7XG4gICAgICAgIHNldFF1YW50aXR5KChwcmV2KSA9PiBwcmV2IC0gMSk7XG4gICAgICAgIGFuaW1hdGVDb3VudGVyKFwiZGVjcmVtZW50XCIpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gVmlzdWFsIGZlZWRiYWNrIHdoZW4gbWluIHF1YW50aXR5IGlzIHJlYWNoZWRcbiAgICAgICAgdG9hc3QuaW5mbyhgTWluaW11bSBxdWFudGl0eSBpcyAke21pblF1YW50aXR5fWApO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICAvLyBEeW5hbWljIGJ1dHRvbiBzdHlsZXMgYmFzZWQgb24gc3RhdGVcbiAgY29uc3QgZ2V0QnV0dG9uU3R5bGVzID0gKHR5cGU6IFwiaW5jcmVtZW50XCIgfCBcImRlY3JlbWVudFwiKSA9PiB7XG4gICAgY29uc3QgYmFzZVN0eWxlcyA9XG4gICAgICBcImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctNiBoLTYgc206dy03IHNtOmgtNyByb3VuZGVkLWZ1bGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTEgZm9jdXM6cmluZy1vZmZzZXQtMVwiO1xuICAgIGNvbnN0IGRpc2FibGVkU3R5bGVzID0gXCJiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNDAwIGN1cnNvci1ub3QtYWxsb3dlZFwiO1xuXG4gICAgaWYgKHR5cGUgPT09IFwiaW5jcmVtZW50XCIpIHtcbiAgICAgIGNvbnN0IGlzTWF4ID1cbiAgICAgICAgcHJvZHVjdCAmJlxuICAgICAgICBxdWFudGl0eSA+PVxuICAgICAgICAgIChwcm9kdWN0Lk9yZGVyTWF4aW11bVF1YW50aXR5ID4gMFxuICAgICAgICAgICAgPyBNYXRoLm1pbihwcm9kdWN0Lk9yZGVyTWF4aW11bVF1YW50aXR5LCBwcm9kdWN0LlN0b2NrUXVhbnRpdHkpXG4gICAgICAgICAgICA6IHByb2R1Y3QuU3RvY2tRdWFudGl0eSk7XG4gICAgICByZXR1cm4gYCR7YmFzZVN0eWxlc30gJHtcbiAgICAgICAgaXNNYXhcbiAgICAgICAgICA/IGRpc2FibGVkU3R5bGVzXG4gICAgICAgICAgOiBcImJnLXByaW1hcnkgdGV4dC13aGl0ZSBob3ZlcjpiZy1wcmltYXJ5LzkwIGZvY3VzOnJpbmctcHJpbWFyeS81MFwiXG4gICAgICB9YDtcbiAgICB9IGVsc2Uge1xuICAgICAgY29uc3QgaXNNaW4gPVxuICAgICAgICBwcm9kdWN0ICYmXG4gICAgICAgIHF1YW50aXR5IDw9XG4gICAgICAgICAgKHByb2R1Y3QuT3JkZXJNaW5pbXVtUXVhbnRpdHkgPiAwID8gcHJvZHVjdC5PcmRlck1pbmltdW1RdWFudGl0eSA6IDEpO1xuICAgICAgcmV0dXJuIGAke2Jhc2VTdHlsZXN9ICR7XG4gICAgICAgIGlzTWluXG4gICAgICAgICAgPyBkaXNhYmxlZFN0eWxlc1xuICAgICAgICAgIDogXCJiZy1wcmltYXJ5IHRleHQtd2hpdGUgaG92ZXI6YmctcHJpbWFyeS85MCBmb2N1czpyaW5nLXByaW1hcnkvNTBcIlxuICAgICAgfWA7XG4gICAgfVxuICB9O1xuXG4gIC8vIENvdW50ZXIgZGlzcGxheSB3aXRoIGFuaW1hdGlvblxuICBjb25zdCBDb3VudGVyRGlzcGxheSA9ICgpID0+IChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHctMTAgc206dy0xMlwiPlxuICAgICAgPHNwYW5cbiAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC1zbSBzbTp0ZXh0LWJhc2UgZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgaXNBbmltYXRpbmcgPyBcInNjYWxlLTEyNSB0ZXh0LXByaW1hcnlcIiA6IFwic2NhbGUtMTAwXCJcbiAgICAgICAgfWB9XG4gICAgICA+XG4gICAgICAgIHtxdWFudGl0eX1cbiAgICAgIDwvc3Bhbj5cbiAgICAgIHtpc0FuaW1hdGluZyAmJiAoXG4gICAgICAgIDxzcGFuXG4gICAgICAgICAgY2xhc3NOYW1lPXtgYWJzb2x1dGUgdGV4dC14cyBmb250LWJvbGQgdGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCAke1xuICAgICAgICAgICAgYW5pbWF0aW9uVHlwZSA9PT0gXCJpbmNyZW1lbnRcIlxuICAgICAgICAgICAgICA/IFwiLXRvcC00IHNtOi10b3AtNVwiXG4gICAgICAgICAgICAgIDogXCJ0b3AtNCBzbTp0b3AtNVwiXG4gICAgICAgICAgfWB9XG4gICAgICAgID5cbiAgICAgICAgICB7YW5pbWF0aW9uVHlwZSA9PT0gXCJpbmNyZW1lbnRcIiA/IFwiKzFcIiA6IFwiLTFcIn1cbiAgICAgICAgPC9zcGFuPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcblxuICAvLyBFYXJseSByZXR1cm4gaWYgcHJvZHVjdCBpcyBub3QgbG9hZGVkIHlldFxuICBpZiAobG9hZGluZykge1xuICAgIHJldHVybiA8UHJvZHVjdExvYWRpbmcgLz47XG4gIH1cblxuICBpZiAoZXJyb3IpIHtcbiAgICByZXR1cm4gPFByb2R1Y3RFcnJvciBlcnJvcj17ZXJyb3J9IHJldHJ5PXtmZXRjaFByb2R1Y3R9IC8+O1xuICB9XG5cbiAgaWYgKCFwcm9kdWN0KSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS04IHRleHQtY2VudGVyXCI+XG4gICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgbWItNFwiPlByb2R1Y3QgTm90IEZvdW5kPC9oMT5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwibWItNlwiPlxuICAgICAgICAgIFRoZSBwcm9kdWN0IHdpdGggSUQgXCJ7cHJvZHVjdElkfVwiIGNvdWxkIG5vdCBiZSBmb3VuZC4gSXQgbWF5IG5vdCBleGlzdFxuICAgICAgICAgIGluIHRoZSBkYXRhYmFzZS5cbiAgICAgICAgPC9wPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvcHJvZHVjdHNcIj5cbiAgICAgICAgICAgIDxCdXR0b24+XG4gICAgICAgICAgICAgIDxBcnJvd0xlZnQgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgVmlldyBBbGwgUHJvZHVjdHNcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgIENoZWNrIHRoZSBwcm9kdWN0cyBsaXN0IHRvIGZpbmQgYXZhaWxhYmxlIHByb2R1Y3QgSURzXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgICk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHktOCBweC00IHctZnVsbCBtYXgtdy1bMTIwMHB4XSBvdmVyZmxvdy14LWhpZGRlblwiPlxuICAgICAgey8qIEJyZWFkY3J1bWIgKi99XG4gICAgICA8QnJlYWRjcnVtYiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgIDxCcmVhZGNydW1iTGlzdD5cbiAgICAgICAgICA8QnJlYWRjcnVtYkl0ZW0+XG4gICAgICAgICAgICA8QnJlYWRjcnVtYkxpbmsgYXNDaGlsZD5cbiAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9cIj5Ib21lPC9MaW5rPlxuICAgICAgICAgICAgPC9CcmVhZGNydW1iTGluaz5cbiAgICAgICAgICA8L0JyZWFkY3J1bWJJdGVtPlxuICAgICAgICAgIDxCcmVhZGNydW1iU2VwYXJhdG9yIC8+XG4gICAgICAgICAgPEJyZWFkY3J1bWJJdGVtPlxuICAgICAgICAgICAgPEJyZWFkY3J1bWJMaW5rIGFzQ2hpbGQ+XG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9e2AvcHJvZHVjdHM/Y2F0ZWdvcnk9JHtwcm9kdWN0LkNhdGVnb3J5SUQgfHwgXCJhbGxcIn1gfT5cbiAgICAgICAgICAgICAgICB7cHJvZHVjdC5DYXRlZ29yeU5hbWUgfHwgXCJNZWRpY2FsIFByb2R1Y3RzXCJ9XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvQnJlYWRjcnVtYkxpbms+XG4gICAgICAgICAgPC9CcmVhZGNydW1iSXRlbT5cbiAgICAgICAgICA8QnJlYWRjcnVtYlNlcGFyYXRvciAvPlxuICAgICAgICAgIDxCcmVhZGNydW1iSXRlbT5cbiAgICAgICAgICAgIDxCcmVhZGNydW1iTGluayBhc0NoaWxkPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3Byb2R1Y3RzXCI+UHJvZHVjdHM8L0xpbms+XG4gICAgICAgICAgICA8L0JyZWFkY3J1bWJMaW5rPlxuICAgICAgICAgIDwvQnJlYWRjcnVtYkl0ZW0+XG4gICAgICAgICAgPEJyZWFkY3J1bWJTZXBhcmF0b3IgLz5cbiAgICAgICAgICA8QnJlYWRjcnVtYkl0ZW0+XG4gICAgICAgICAgICA8QnJlYWRjcnVtYlBhZ2U+e3Byb2R1Y3QuUHJvZHVjdE5hbWV9PC9CcmVhZGNydW1iUGFnZT5cbiAgICAgICAgICA8L0JyZWFkY3J1bWJJdGVtPlxuICAgICAgICA8L0JyZWFkY3J1bWJMaXN0PlxuICAgICAgPC9CcmVhZGNydW1iPlxuXG4gICAgICB7LyogTW9iaWxlIENvdW50ZG93biBUaW1lciAtIFRvcCBvZiBwYWdlIG9uIG1vYmlsZSBvbmx5ICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJsZzpoaWRkZW4gbWItNlwiPlxuICAgICAgICB7KCgpID0+IHtcbiAgICAgICAgICAvLyBPbmx5IHNob3cgY291bnRkb3duIGlmIGJvdGggc2FsZSBzdGFydCBhbmQgZW5kIGRhdGVzIGFyZSBwcm92aWRlZFxuICAgICAgICAgIGlmICghcHJvZHVjdC5TZWxsU3RhcnREYXRldGltZVVUQyB8fCAhcHJvZHVjdC5TZWxsRW5kRGF0ZXRpbWVVVEMpIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgIH1cblxuICAgICAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgICAgICAgY29uc3Qgc2FsZVN0YXJ0ID0gbmV3IERhdGUocHJvZHVjdC5TZWxsU3RhcnREYXRldGltZVVUQyk7XG4gICAgICAgICAgY29uc3Qgc2FsZUVuZCA9IG5ldyBEYXRlKHByb2R1Y3QuU2VsbEVuZERhdGV0aW1lVVRDKTtcblxuICAgICAgICAgIC8vIENoZWNrIGlmIHNhbGUgaXMgY3VycmVudGx5IGFjdGl2ZSBvciB1cGNvbWluZ1xuICAgICAgICAgIGNvbnN0IGlzU2FsZUFjdGl2ZSA9IG5vdyA+PSBzYWxlU3RhcnQgJiYgbm93IDw9IHNhbGVFbmQ7XG4gICAgICAgICAgY29uc3QgaXNTYWxlVXBjb21pbmcgPSBub3cgPCBzYWxlU3RhcnQ7XG4gICAgICAgICAgY29uc3QgaXNTYWxlRXhwaXJlZCA9IG5vdyA+IHNhbGVFbmQ7XG5cbiAgICAgICAgICAvLyBEb24ndCBzaG93IHRpbWVyIGlmIHNhbGUgaGFzIGV4cGlyZWRcbiAgICAgICAgICBpZiAoaXNTYWxlRXhwaXJlZCkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gRGV0ZXJtaW5lIHRoZSBjb3VudGRvd24gdGFyZ2V0IGRhdGVcbiAgICAgICAgICBjb25zdCBjb3VudGRvd25FbmREYXRlID0gaXNTYWxlVXBjb21pbmcgPyBwcm9kdWN0LlNlbGxTdGFydERhdGV0aW1lVVRDIDogcHJvZHVjdC5TZWxsRW5kRGF0ZXRpbWVVVEM7XG5cbiAgICAgICAgICAvLyBEZXRlcm1pbmUgdGhlIG1lc3NhZ2UgYmFzZWQgb24gc2FsZSBzdGF0ZVxuICAgICAgICAgIGxldCB0aW1lck1lc3NhZ2UgPSBcIvCflKUgTGltaXRlZCBUaW1lIFNhbGUhXCI7XG4gICAgICAgICAgbGV0IHVyZ2VuY3lNZXNzYWdlID0gXCJTYWxlIGVuZHMgc29vbiAtIGdyYWIgeW91cnMgbm93IVwiO1xuXG4gICAgICAgICAgaWYgKGlzU2FsZVVwY29taW5nKSB7XG4gICAgICAgICAgICB0aW1lck1lc3NhZ2UgPSBcIuKPsCBTYWxlIFN0YXJ0cyBTb29uIVwiO1xuICAgICAgICAgICAgdXJnZW5jeU1lc3NhZ2UgPSBcIkdldCByZWFkeSBmb3IgYW4gYW1hemluZyBkZWFsIVwiO1xuICAgICAgICAgIH0gZWxzZSBpZiAoaXNTYWxlQWN0aXZlKSB7XG4gICAgICAgICAgICB0aW1lck1lc3NhZ2UgPSBcIvCflKUgTGltaXRlZCBUaW1lIFNhbGUhXCI7XG4gICAgICAgICAgICB1cmdlbmN5TWVzc2FnZSA9IFwiU2FsZSBlbmRzIHNvb24gLSBkb24ndCBtaXNzIG91dCFcIjtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTMgYmctZ3JhZGllbnQtdG8tciBmcm9tLXJlZC01MCB0by1vcmFuZ2UtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQteGxcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBnYXAtMiBtYi0zXCI+XG4gICAgICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1yZWQtNTAwIGFuaW1hdGUtcHVsc2VcIiAvPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1ib2xkIHRleHQtcmVkLTYwMFwiPlxuICAgICAgICAgICAgICAgICAge3RpbWVyTWVzc2FnZX1cbiAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWNlbnRlciBtYi0zXCI+XG4gICAgICAgICAgICAgICAgPENvdW50ZG93blxuICAgICAgICAgICAgICAgICAgZW5kRGF0ZT17Y291bnRkb3duRW5kRGF0ZX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRyYW5zZm9ybSBob3ZlcjpzY2FsZS0xMDUgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciB0ZXh0LXhzIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj5cbiAgICAgICAgICAgICAgICB7dXJnZW5jeU1lc3NhZ2V9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICk7XG4gICAgICAgIH0pKCl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGxnOmZsZXgtcm93IGdhcC04XCI+XG4gICAgICAgIHsvKiBQcm9kdWN0IE1lZGlhIEdhbGxlcnkgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibGc6dy0xLzJcIj5cbiAgICAgICAgICA8UHJvZHVjdE1lZGlhR2FsbGVyeVxuICAgICAgICAgICAgbWVkaWE9e21lZGlhSXRlbXN9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcm91bmRlZC1sZyBvdmVyZmxvdy1oaWRkZW5cIlxuICAgICAgICAgIC8+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBQcm9kdWN0IERldGFpbHMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWQ6dy0xLzJcIj5cbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgbWItMlwiPntwcm9kdWN0LlByb2R1Y3ROYW1lfTwvaDE+XG5cbiAgICAgICAgICB7LyogUmF0aW5nICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4XCI+XG4gICAgICAgICAgICAgIHtbLi4uQXJyYXkoNSldLm1hcCgoXywgaSkgPT4gKFxuICAgICAgICAgICAgICAgIDxTdGFyXG4gICAgICAgICAgICAgICAgICBrZXk9e2l9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTQgaC00ICR7XG4gICAgICAgICAgICAgICAgICAgIGkgPCBNYXRoLmZsb29yKHByb2R1Y3QuUmF0aW5nIHx8IDApXG4gICAgICAgICAgICAgICAgICAgICAgPyBcInRleHQteWVsbG93LTQwMCBmaWxsLXllbGxvdy00MDBcIlxuICAgICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LWdyYXktMzAwXCJcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDAgbWwtMlwiPlxuICAgICAgICAgICAgICAoe3Byb2R1Y3QuUmF0aW5nIHx8IDB9KSB7cHJvZHVjdC5Ub3RhbFJldmlld3MgfHwgMH0gcmV2aWV3c1xuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFByaWNlICovfVxuICAgICAgICAgIHtyZW5kZXJQcmljZSgpfVxuXG4gICAgICAgICAgey8qIERlc2t0b3AgQ291bnRkb3duIFRpbWVyIC0gT25seSBzaG93IG9uIGRlc2t0b3AgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbGc6YmxvY2tcIj5cbiAgICAgICAgICAgIHsoKCkgPT4ge1xuICAgICAgICAgICAgICAvLyBPbmx5IHNob3cgY291bnRkb3duIGlmIGJvdGggc2FsZSBzdGFydCBhbmQgZW5kIGRhdGVzIGFyZSBwcm92aWRlZFxuICAgICAgICAgICAgICBpZiAoIXByb2R1Y3QuU2VsbFN0YXJ0RGF0ZXRpbWVVVEMgfHwgIXByb2R1Y3QuU2VsbEVuZERhdGV0aW1lVVRDKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpO1xuICAgICAgICAgICAgICBjb25zdCBzYWxlU3RhcnQgPSBuZXcgRGF0ZShwcm9kdWN0LlNlbGxTdGFydERhdGV0aW1lVVRDKTtcbiAgICAgICAgICAgICAgY29uc3Qgc2FsZUVuZCA9IG5ldyBEYXRlKHByb2R1Y3QuU2VsbEVuZERhdGV0aW1lVVRDKTtcblxuICAgICAgICAgICAgICAvLyBDaGVjayBpZiBzYWxlIGlzIGN1cnJlbnRseSBhY3RpdmUgb3IgdXBjb21pbmdcbiAgICAgICAgICAgICAgY29uc3QgaXNTYWxlQWN0aXZlID0gbm93ID49IHNhbGVTdGFydCAmJiBub3cgPD0gc2FsZUVuZDtcbiAgICAgICAgICAgICAgY29uc3QgaXNTYWxlVXBjb21pbmcgPSBub3cgPCBzYWxlU3RhcnQ7XG4gICAgICAgICAgICAgIGNvbnN0IGlzU2FsZUV4cGlyZWQgPSBub3cgPiBzYWxlRW5kO1xuXG4gICAgICAgICAgICAgIC8vIERvbid0IHNob3cgdGltZXIgaWYgc2FsZSBoYXMgZXhwaXJlZFxuICAgICAgICAgICAgICBpZiAoaXNTYWxlRXhwaXJlZCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgLy8gRGV0ZXJtaW5lIHRoZSBjb3VudGRvd24gdGFyZ2V0IGRhdGVcbiAgICAgICAgICAgICAgY29uc3QgY291bnRkb3duRW5kRGF0ZSA9IGlzU2FsZVVwY29taW5nID8gcHJvZHVjdC5TZWxsU3RhcnREYXRldGltZVVUQyA6IHByb2R1Y3QuU2VsbEVuZERhdGV0aW1lVVRDO1xuXG4gICAgICAgICAgICAgIC8vIERldGVybWluZSB0aGUgbWVzc2FnZSBiYXNlZCBvbiBzYWxlIHN0YXRlXG4gICAgICAgICAgICAgIGxldCB0aW1lck1lc3NhZ2UgPSBcIvCflKUgTGltaXRlZCBUaW1lIFNhbGUhXCI7XG4gICAgICAgICAgICAgIGxldCB1cmdlbmN5TWVzc2FnZSA9IFwiU2FsZSBlbmRzIHNvb24gLSBncmFiIHlvdXJzIG5vdyFcIjtcblxuICAgICAgICAgICAgICBpZiAoaXNTYWxlVXBjb21pbmcpIHtcbiAgICAgICAgICAgICAgICB0aW1lck1lc3NhZ2UgPSBcIuKPsCBTYWxlIFN0YXJ0cyBTb29uIVwiO1xuICAgICAgICAgICAgICAgIHVyZ2VuY3lNZXNzYWdlID0gXCJHZXQgcmVhZHkgZm9yIGFuIGFtYXppbmcgZGVhbCFcIjtcbiAgICAgICAgICAgICAgfSBlbHNlIGlmIChpc1NhbGVBY3RpdmUpIHtcbiAgICAgICAgICAgICAgICB0aW1lck1lc3NhZ2UgPSBcIvCflKUgTGltaXRlZCBUaW1lIFNhbGUhXCI7XG4gICAgICAgICAgICAgICAgdXJnZW5jeU1lc3NhZ2UgPSBcIlNhbGUgZW5kcyBzb29uIC0gZG9uJ3QgbWlzcyBvdXQhXCI7XG4gICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItNiBwLTQgYmctZ3JhZGllbnQtdG8tciBmcm9tLXJlZC01MCB0by1vcmFuZ2UtNTAgYm9yZGVyIGJvcmRlci1yZWQtMjAwIHJvdW5kZWQteGxcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTMgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwiaC02IHctNiB0ZXh0LXJlZC01MDAgYW5pbWF0ZS1wdWxzZVwiIC8+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIHRleHQtcmVkLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIHt0aW1lck1lc3NhZ2V9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1jZW50ZXIgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgICA8Q291bnRkb3duXG4gICAgICAgICAgICAgICAgICAgICAgZW5kRGF0ZT17Y291bnRkb3duRW5kRGF0ZX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0cmFuc2Zvcm0gaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+XG4gICAgICAgICAgICAgICAgICAgIHt1cmdlbmN5TWVzc2FnZX1cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH0pKCl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU2hvcnQgRGVzY3JpcHRpb24gKi99XG4gICAgICAgICAge3Byb2R1Y3QuU2hvcnREZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInByb3NlIHByb3NlLXNtIG1heC13LW5vbmUgbWItNlwiPlxuICAgICAgICAgICAgICA8cD57cHJvZHVjdC5TaG9ydERlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogU3RvY2sgc3RhdHVzIGluZGljYXRvciByZW1vdmVkIGFzIHBlciB1c2VyIHJlcXVlc3QgKi99XG5cbiAgICAgICAgICB7LyogUHJvZHVjdCBhdHRyaWJ1dGVzIHdpdGggcmFkaW8gYnV0dG9uIGdyb3VwcyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTYgYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwIHB0LTRcIj5cbiAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+XG4gICAgICAgICAgICAgIFByb2R1Y3QgRGV0YWlsc1xuICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMCBtYi00XCI+XG4gICAgICAgICAgICAgIENob29zZSB5b3VyIHByZWZlcmVuY2VzIGZyb20gdGhlIG9wdGlvbnMgYmVsb3cuXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICB7T2JqZWN0LmVudHJpZXMoZ3JvdXBlZEF0dHJpYnV0ZXMpLmxlbmd0aCA+IDAgPyAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgICAgICAge09iamVjdC5lbnRyaWVzKGdyb3VwZWRBdHRyaWJ1dGVzKS5tYXAoXG4gICAgICAgICAgICAgICAgICAoW2dyb3VwSWQsIGF0dHJpYnV0ZXNdKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtgYXR0ci1ncm91cC0ke2dyb3VwSWR9YH0gY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2F0dHJpYnV0ZXNbMF0/LkRpc3BsYXlOYW1lIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGF0dHJpYnV0ZXNbMF0/LkF0dHJpYnV0ZU5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICA6XG4gICAgICAgICAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMiBwbC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7YXR0cmlidXRlcy5tYXAoKGF0dHIpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgYXR0cktleSA9IGAke2F0dHIuUHJvZHVjdEF0dHJpYnV0ZUlEfV8ke2F0dHIuQXR0cmlidXRlVmFsdWVJRH1gO1xuICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBpc1NlbGVjdGVkID0gISFzZWxlY3RlZEF0dHJpYnV0ZXNbYXR0cktleV07XG4gICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzUmFkaW9Hcm91cCA9IGF0dHJpYnV0ZXMubGVuZ3RoID4gMTtcblxuICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXthdHRyS2V5fSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGgtNVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0eXBlPXtpc1JhZGlvR3JvdXAgPyBcInJhZGlvXCIgOiBcImNoZWNrYm94XCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ9e2BhdHRyLSR7YXR0cktleX1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWU9e2BhdHRyLWdyb3VwLSR7Z3JvdXBJZH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGgtNCB3LTQgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzUmFkaW9Hcm91cCA/IFwicm91bmRlZC1mdWxsXCIgOiBcInJvdW5kZWRcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gYm9yZGVyLWdyYXktMzAwIHRleHQtcHJpbWFyeSBmb2N1czpyaW5nLXByaW1hcnlgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2lzU2VsZWN0ZWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlQXR0cmlidXRlQ2hhbmdlKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdHRyLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlLnRhcmdldC5jaGVja2VkLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1JhZGlvR3JvdXBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtMyB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxsYWJlbFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGh0bWxGb3I9e2BhdHRyLSR7YXR0cktleX1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZvbnQtbWVkaXVtICR7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpc1NlbGVjdGVkXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJ0ZXh0LXByaW1hcnlcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwidGV4dC1ncmF5LTcwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7YXR0ci5BdHRyaWJ1dGVWYWx1ZVRleHR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgeyhhdHRyLlByaWNlQWRqdXN0bWVudCB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXR0ci5QcmljZUFkanVzdG1lbnQgPT09IDApICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm1sLTIgdGV4dC1zbSBmb250LW5vcm1hbCB0ZXh0LWdyZWVuLTYwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHthdHRyLlByaWNlQWRqdXN0bWVudFR5cGUgPT09IDFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwiK1wiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcIlwifVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAke2F0dHIuUHJpY2VBZGp1c3RtZW50fXtcIiBcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2F0dHIuUHJpY2VBZGp1c3RtZW50VHlwZSA9PT0gMlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCIlXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IFwiXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj5cbiAgICAgICAgICAgICAgICBObyBhZGRpdGlvbmFsIHByb2R1Y3QgZGV0YWlscyBhdmFpbGFibGUuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogUXVhbnRpdHkgU2VsZWN0b3IgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgc206aXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5RdWFudGl0eTo8L3NwYW4+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHNtOmp1c3RpZnktc3RhcnRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtkZWNyZW1lbnRRdWFudGl0eX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtnZXRCdXR0b25TdHlsZXMoXCJkZWNyZW1lbnRcIil9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtxdWFudGl0eSA8PSAocHJvZHVjdC5PcmRlck1pbmltdW1RdWFudGl0eSB8fCAxKX1cbiAgICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkRlY3JlYXNlIHF1YW50aXR5XCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPHN2Z1xuICAgICAgICAgICAgICAgICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNCB3LTQgc206aC01IHNtOnctNVwiXG4gICAgICAgICAgICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyMCAyMFwiXG4gICAgICAgICAgICAgICAgICAgICAgZmlsbD1cImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICA8cGF0aFxuICAgICAgICAgICAgICAgICAgICAgICAgZmlsbFJ1bGU9XCJldmVub2RkXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIGQ9XCJNMyAxMGExIDEgMCAwMTEtMWgxMmExIDEgMCAxMTAgMkg0YTEgMSAwIDAxLTEtMXpcIlxuICAgICAgICAgICAgICAgICAgICAgICAgY2xpcFJ1bGU9XCJldmVub2RkXCJcbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuXG4gICAgICAgICAgICAgICAgICA8Q291bnRlckRpc3BsYXkgLz5cblxuICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtpbmNyZW1lbnRRdWFudGl0eX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtnZXRCdXR0b25TdHlsZXMoXCJpbmNyZW1lbnRcIil9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtcbiAgICAgICAgICAgICAgICAgICAgICBwcm9kdWN0Lk9yZGVyTWF4aW11bVF1YW50aXR5ID4gMFxuICAgICAgICAgICAgICAgICAgICAgICAgPyBxdWFudGl0eSA+PVxuICAgICAgICAgICAgICAgICAgICAgICAgICBNYXRoLm1pbihcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9kdWN0Lk9yZGVyTWF4aW11bVF1YW50aXR5LFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHByb2R1Y3QuU3RvY2tRdWFudGl0eVxuICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICA6IHF1YW50aXR5ID49IHByb2R1Y3QuU3RvY2tRdWFudGl0eVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJJbmNyZWFzZSBxdWFudGl0eVwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxzdmdcbiAgICAgICAgICAgICAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJoLTQgdy00IHNtOmgtNSBzbTp3LTVcIlxuICAgICAgICAgICAgICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjAgMjBcIlxuICAgICAgICAgICAgICAgICAgICAgIGZpbGw9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPHBhdGhcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpbGxSdWxlPVwiZXZlbm9kZFwiXG4gICAgICAgICAgICAgICAgICAgICAgICBkPVwiTTEwIDNhMSAxIDAgMDExIDF2NWg1YTEgMSAwIDExMCAyaC01djVhMSAxIDAgMTEtMiAwdi01SDRhMSAxIDAgMTEwLTJoNVY0YTEgMSAwIDAxMS0xelwiXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGlwUnVsZT1cImV2ZW5vZGRcIlxuICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgc206aXRlbXMtY2VudGVyIGdhcC0yIHNtOm1sLTRcIj5cbiAgICAgICAgICAgICAgICAgIHtwcm9kdWN0Lk9yZGVyTWluaW11bVF1YW50aXR5ID4gMSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIE1pbjoge3Byb2R1Y3QuT3JkZXJNaW5pbXVtUXVhbnRpdHl9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgIHtwcm9kdWN0Lk9yZGVyTWF4aW11bVF1YW50aXR5ID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTUwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIE1heDp7XCIgXCJ9XG4gICAgICAgICAgICAgICAgICAgICAge01hdGgubWluKFxuICAgICAgICAgICAgICAgICAgICAgICAgcHJvZHVjdC5PcmRlck1heGltdW1RdWFudGl0eSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHByb2R1Y3QuU3RvY2tRdWFudGl0eVxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogQWN0aW9uIEJ1dHRvbnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNtOmZsZXgtcm93IGdhcC0zIHNtOmdhcC00XCI+XG4gICAgICAgICAgICB7LyogQWRkIHRvIENhcnQgQnV0dG9uICovfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yIHB5LTMgcHgtNCByb3VuZGVkLW1kIGJnLXByaW1hcnkgdGV4dC13aGl0ZSBob3ZlcjpiZy1wcmltYXJ5LzkwIGRpc2FibGVkOm9wYWNpdHktNTAgZGlzYWJsZWQ6cG9pbnRlci1ldmVudHMtbm9uZSB0ZXh0LXNtIHNtOnRleHQtYmFzZVwiXG4gICAgICAgICAgICAgIGRpc2FibGVkPXtwcm9kdWN0LlN0b2NrUXVhbnRpdHkgPD0gMCB8fCBhZGRpbmdUb0NhcnR9XG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoIXByb2R1Y3QpIHJldHVybjtcblxuICAgICAgICAgICAgICAgIHNldEFkZGluZ1RvQ2FydCh0cnVlKTtcbiAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgLy8gR2V0IHRoZSBmaXJzdCBwcm9kdWN0IGltYWdlIG9yIHVzZSBhIHBsYWNlaG9sZGVyXG4gICAgICAgICAgICAgICAgICBjb25zdCBwcm9kdWN0SW1hZ2UgPVxuICAgICAgICAgICAgICAgICAgICBwcm9kdWN0LlByb2R1Y3RJbWFnZXNKc29uICYmXG4gICAgICAgICAgICAgICAgICAgIHByb2R1Y3QuUHJvZHVjdEltYWdlc0pzb24ubGVuZ3RoID4gMFxuICAgICAgICAgICAgICAgICAgICAgID8gY29uc3RydWN0SW1hZ2VVcmwoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHByb2R1Y3QuUHJvZHVjdEltYWdlc0pzb25bMF0uQXR0YWNobWVudFVSTFxuICAgICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICAgIDogXCIvcGxhY2Vob2xkZXIuanBnXCI7XG5cbiAgICAgICAgICAgICAgICAgIC8vIEdldCBzZWxlY3RlZCBhdHRyaWJ1dGVzXG4gICAgICAgICAgICAgICAgICBjb25zdCBzZWxlY3RlZEF0dHJzID0gKHByb2R1Y3QuQXR0cmlidXRlc0pzb24gfHwgW10pLmZpbHRlcihcbiAgICAgICAgICAgICAgICAgICAgKGF0dHIpID0+XG4gICAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRBdHRyaWJ1dGVzW1xuICAgICAgICAgICAgICAgICAgICAgICAgYCR7YXR0ci5Qcm9kdWN0QXR0cmlidXRlSUR9XyR7YXR0ci5BdHRyaWJ1dGVWYWx1ZUlEfWBcbiAgICAgICAgICAgICAgICAgICAgICBdXG4gICAgICAgICAgICAgICAgICApO1xuXG4gICAgICAgICAgICAgICAgICAvLyBBZGQgdG8gY2FydCB1c2luZyB0aGUgY2FydCBjb250ZXh0IHdpdGggYXR0cmlidXRlcyBhbmQgYWRqdXN0ZWQgcHJpY2VcbiAgICAgICAgICAgICAgICAgIGNhcnQuYWRkVG9DYXJ0KFxuICAgICAgICAgICAgICAgICAgICB7XG4gICAgICAgICAgICAgICAgICAgICAgaWQ6IHByb2R1Y3QuUHJvZHVjdElkLFxuICAgICAgICAgICAgICAgICAgICAgIG5hbWU6IHByb2R1Y3QuUHJvZHVjdE5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgcHJpY2U6IHByb2R1Y3QuRGlzY291bnRQcmljZSB8fCBwcm9kdWN0LlByaWNlLCAvLyBVc2UgZGlzY291bnQgcHJpY2UgaWYgYXZhaWxhYmxlXG4gICAgICAgICAgICAgICAgICAgICAgZGlzY291bnRQcmljZTogcHJvZHVjdC5EaXNjb3VudFByaWNlLFxuICAgICAgICAgICAgICAgICAgICAgIGltYWdlOiBwcm9kdWN0SW1hZ2UsXG4gICAgICAgICAgICAgICAgICAgICAgb3JpZ2luYWxQcmljZTogcHJvZHVjdC5QcmljZSwgLy8gQWx3YXlzIHN0b3JlIHRoZSBvcmlnaW5hbCBwcmljZVxuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgICBxdWFudGl0eSxcbiAgICAgICAgICAgICAgICAgICAgc2VsZWN0ZWRBdHRycyxcbiAgICAgICAgICAgICAgICAgICAgcHJvZHVjdC5QcmljZUlRRCwgLy8gUGFzcyBJUUQgcHJpY2UgYXMgdGhlIGZvdXJ0aCBwYXJhbWV0ZXJcbiAgICAgICAgICAgICAgICAgICAgcmF0ZSAvLyBQYXNzIGN1cnJlbmN5IHJhdGUgYXMgdGhlIGZpZnRoIHBhcmFtZXRlclxuICAgICAgICAgICAgICAgICAgKTtcblxuICAgICAgICAgICAgICAgICAgLy8gU2hvdyBtb2Rlcm4gdG9hc3Qgbm90aWZpY2F0aW9uXG4gICAgICAgICAgICAgICAgICBzaG93TW9kZXJuQWRkVG9DYXJ0VG9hc3Qoe1xuICAgICAgICAgICAgICAgICAgICBwcm9kdWN0TmFtZTogcHJvZHVjdC5Qcm9kdWN0TmFtZSxcbiAgICAgICAgICAgICAgICAgICAgcXVhbnRpdHksXG4gICAgICAgICAgICAgICAgICAgIHByb2R1Y3RJbWFnZTpcbiAgICAgICAgICAgICAgICAgICAgICBwcm9kdWN0LlByb2R1Y3RJbWFnZXNKc29uPy5bMF0/LkF0dGFjaG1lbnRVUkwgfHxcbiAgICAgICAgICAgICAgICAgICAgICBcIi9wbGFjZWhvbGRlci5zdmdcIixcbiAgICAgICAgICAgICAgICAgICAgb25WaWV3Q2FydDogKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gXCIvY2FydFwiO1xuICAgICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBhZGRpbmcgdG8gY2FydDpcIiwgZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgdG9hc3QuZXJyb3IoXG4gICAgICAgICAgICAgICAgICAgIFwiRmFpbGVkIHRvIGFkZCBwcm9kdWN0IHRvIGNhcnQuIFBsZWFzZSB0cnkgYWdhaW4uXCJcbiAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgICAgICAgICAgIHNldEFkZGluZ1RvQ2FydChmYWxzZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7YWRkaW5nVG9DYXJ0ID8gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC01IHctNSBib3JkZXItMiBib3JkZXItY3VycmVudCBib3JkZXItdC10cmFuc3BhcmVudCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1zcGluXCIgLz5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICA8U2hvcHBpbmdDYXJ0IGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8c3Bhbj57YWRkaW5nVG9DYXJ0ID8gXCJBZGRpbmcuLi5cIiA6IFwiQWRkIHRvIENhcnRcIn08L3NwYW4+XG4gICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgey8qIFdpc2hsaXN0IEJ1dHRvbiAqL31cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC0yIHB5LTMgcHgtNCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctdHJhbnNwYXJlbnQgaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmQgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIHRleHQtc20gc206dGV4dC1iYXNlIHNtOmZsZXgtaW5pdGlhbCBzbTptaW4tdy1bMTIwcHhdXCJcbiAgICAgICAgICAgICAgZGlzYWJsZWQ9e2FkZGluZ1RvV2lzaGxpc3R9XG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAoIXByb2R1Y3QpIHJldHVybjtcblxuICAgICAgICAgICAgICAgIHNldEFkZGluZ1RvV2lzaGxpc3QodHJ1ZSk7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgIC8vIENoZWNrIGlmIHByb2R1Y3QgaXMgYWxyZWFkeSBpbiB3aXNobGlzdFxuICAgICAgICAgICAgICAgICAgY29uc3QgaXNBbHJlYWR5SW5XaXNobGlzdCA9IHdpc2hsaXN0LmlzSW5XaXNobGlzdChcbiAgICAgICAgICAgICAgICAgICAgcHJvZHVjdC5Qcm9kdWN0SWRcbiAgICAgICAgICAgICAgICAgICk7XG5cbiAgICAgICAgICAgICAgICAgIGlmIChpc0FscmVhZHlJbldpc2hsaXN0KSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIFJlbW92ZSBmcm9tIHdpc2hsaXN0IGlmIGFscmVhZHkgdGhlcmVcbiAgICAgICAgICAgICAgICAgICAgd2lzaGxpc3QucmVtb3ZlRnJvbVdpc2hsaXN0KHByb2R1Y3QuUHJvZHVjdElkKTtcbiAgICAgICAgICAgICAgICAgICAgdG9hc3Quc3VjY2VzcyhcbiAgICAgICAgICAgICAgICAgICAgICBgJHtwcm9kdWN0LlByb2R1Y3ROYW1lfSByZW1vdmVkIGZyb20gd2lzaGxpc3RgXG4gICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAvLyBBZGQgdG8gd2lzaGxpc3RcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJvZHVjdFVybCA9IGAvcHJvZHVjdC8ke3Byb2R1Y3QuUHJvZHVjdElkfWA7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGltYWdlVXJsID0gcHJvZHVjdC5Qcm9kdWN0SW1hZ2VzSnNvbj8uWzBdPy5BdHRhY2htZW50VVJMIHx8IFwiL3BsYWNlaG9sZGVyLnN2Z1wiO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBwcmljZSA9IHByb2R1Y3QuRGlzY291bnRQcmljZSB8fCBwcm9kdWN0LlByaWNlO1xuICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgd2lzaGxpc3QuYWRkVG9XaXNobGlzdChcbiAgICAgICAgICAgICAgICAgICAgICBwcm9kdWN0LlByb2R1Y3RJZCxcbiAgICAgICAgICAgICAgICAgICAgICBwcm9kdWN0LlByb2R1Y3ROYW1lLFxuICAgICAgICAgICAgICAgICAgICAgIHByb2R1Y3RVcmwsXG4gICAgICAgICAgICAgICAgICAgICAgaW1hZ2VVcmwsXG4gICAgICAgICAgICAgICAgICAgICAgcHJpY2VcbiAgICAgICAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgICAgICAgICAgdG9hc3Quc3VjY2VzcyhgJHtwcm9kdWN0LlByb2R1Y3ROYW1lfSBhZGRlZCB0byB3aXNobGlzdGApO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgdXBkYXRpbmcgd2lzaGxpc3Q6XCIsIGVycm9yKTtcbiAgICAgICAgICAgICAgICAgIHRvYXN0LmVycm9yKFwiRmFpbGVkIHRvIHVwZGF0ZSB3aXNobGlzdC4gUGxlYXNlIHRyeSBhZ2Fpbi5cIik7XG4gICAgICAgICAgICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgICAgICAgICAgIHNldEFkZGluZ1RvV2lzaGxpc3QoZmFsc2UpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2FkZGluZ1RvV2lzaGxpc3QgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTUgdy01IGJvcmRlci0yIGJvcmRlci1jdXJyZW50IGJvcmRlci10LXRyYW5zcGFyZW50IHJvdW5kZWQtZnVsbCBhbmltYXRlLXNwaW5cIiAvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxIZWFydFxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC01IHctNVwiXG4gICAgICAgICAgICAgICAgICBmaWxsPXtcbiAgICAgICAgICAgICAgICAgICAgcHJvZHVjdCAmJiB3aXNobGlzdC5pc0luV2lzaGxpc3QocHJvZHVjdC5Qcm9kdWN0SWQpXG4gICAgICAgICAgICAgICAgICAgICAgPyBcImN1cnJlbnRDb2xvclwiXG4gICAgICAgICAgICAgICAgICAgICAgOiBcIm5vbmVcIlxuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNyLW9ubHkgbWQ6bm90LXNyLW9ubHkgbWQ6aW5saW5lXCI+XG4gICAgICAgICAgICAgICAge2FkZGluZ1RvV2lzaGxpc3RcbiAgICAgICAgICAgICAgICAgID8gXCJVcGRhdGluZy4uLlwiXG4gICAgICAgICAgICAgICAgICA6IHByb2R1Y3QgJiYgd2lzaGxpc3QuaXNJbldpc2hsaXN0KHByb2R1Y3QuUHJvZHVjdElkKVxuICAgICAgICAgICAgICAgICAgPyBcIlJlbW92ZVwiXG4gICAgICAgICAgICAgICAgICA6IFwiV2lzaGxpc3RcIn1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgIHsvKiBTaGFyZSBCdXR0b24gKi99XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBnYXAtMiBweS0zIHB4LTQgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLXRyYW5zcGFyZW50IGhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kIHRleHQtc20gc206dGV4dC1iYXNlIHNtOmZsZXgtaW5pdGlhbCBzbTptaW4tdy1bMTAwcHhdXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChuYXZpZ2F0b3Iuc2hhcmUpIHtcbiAgICAgICAgICAgICAgICAgIG5hdmlnYXRvclxuICAgICAgICAgICAgICAgICAgICAuc2hhcmUoe1xuICAgICAgICAgICAgICAgICAgICAgIHRpdGxlOiBwcm9kdWN0Py5NZXRhVGl0bGUgfHwgcHJvZHVjdD8uUHJvZHVjdE5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgdGV4dDpcbiAgICAgICAgICAgICAgICAgICAgICAgIHByb2R1Y3Q/Lk1ldGFEZXNjcmlwdGlvbiB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgYENoZWNrIG91dCB0aGlzIHByb2R1Y3Q6ICR7cHJvZHVjdD8uUHJvZHVjdE5hbWV9YCxcbiAgICAgICAgICAgICAgICAgICAgICB1cmw6IHdpbmRvdy5sb2NhdGlvbi5ocmVmLFxuICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAuY2F0Y2goKGVycikgPT4gY29uc29sZS5lcnJvcihcIkVycm9yIHNoYXJpbmc6XCIsIGVycikpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAvLyBGYWxsYmFjayAtIGNvcHkgdG8gY2xpcGJvYXJkXG4gICAgICAgICAgICAgICAgICBuYXZpZ2F0b3IuY2xpcGJvYXJkLndyaXRlVGV4dCh3aW5kb3cubG9jYXRpb24uaHJlZik7XG4gICAgICAgICAgICAgICAgICB0b2FzdC5zdWNjZXNzKFwiUHJvZHVjdCBsaW5rIGNvcGllZCB0byBjbGlwYm9hcmRcIik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8U2hhcmUyIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzci1vbmx5IG1kOm5vdC1zci1vbmx5IG1kOmlubGluZVwiPlNoYXJlPC9zcGFuPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogTWV0YSBLZXl3b3JkcyAqL31cbiAgICAgICAgICB7cHJvZHVjdC5NZXRhS2V5d29yZHMgJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02IHAtNCBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MCB0by1wdXJwbGUtNTAgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWJsdWUtMjAwXCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTNcIj5Qcm9kdWN0IFRhZ3M8L2gzPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAge3Byb2R1Y3QuTWV0YUtleXdvcmRzLnNwbGl0KFwiLFwiKS5tYXAoKGtleXdvcmQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8QmFkZ2VcbiAgICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cInNlY29uZGFyeVwiXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgYmctd2hpdGUvNzAgaG92ZXI6Ymctd2hpdGUgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7a2V5d29yZC50cmltKCl9XG4gICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7LyogTWV0YSBEZXNjcmlwdGlvbiAqL31cbiAgICAgICAgICB7cHJvZHVjdC5NZXRhRGVzY3JpcHRpb24gJiYgKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC02IHAtNiBiZy1ncmFkaWVudC10by1yIGZyb20tZ3JlZW4tNTAgdG8tYmx1ZS01MCByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTMgZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8QXdhcmQgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWdyZWVuLTYwMCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICBBYm91dCBUaGlzIFByb2R1Y3RcbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTcwMCBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgICB7cHJvZHVjdC5NZXRhRGVzY3JpcHRpb259XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBQcm9kdWN0IFRhYnMgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTEyXCI+XG4gICAgICAgIDxUYWJzXG4gICAgICAgICAgZGVmYXVsdFZhbHVlPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbFwiXG4gICAgICAgICAgdmFsdWU9e2FjdGl2ZVRhYn1cbiAgICAgICAgICBvblZhbHVlQ2hhbmdlPXtzZXRBY3RpdmVUYWJ9XG4gICAgICAgID5cbiAgICAgICAgICA8VGFic0xpc3QgY2xhc3NOYW1lPVwiZ3JpZCB3LWZ1bGwgZ3JpZC1jb2xzLTMgbWItNiBnYXAtMiBiZy10cmFuc3BhcmVudCBwLTAgaC1hdXRvXCI+XG4gICAgICAgICAgICA8VGFic1RyaWdnZXJcbiAgICAgICAgICAgICAgdmFsdWU9XCJkZXNjcmlwdGlvblwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQtbGcgcHgtOCBweS00IHRleHQtbGcgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgYm9yZGVyLTIgYm9yZGVyLXRyYW5zcGFyZW50IGRhdGEtW3N0YXRlPWluYWN0aXZlXTpiZy1ncmF5LTMwMCBkYXRhLVtzdGF0ZT1pbmFjdGl2ZV06dGV4dC1ncmF5LTcwMCBkYXRhLVtzdGF0ZT1pbmFjdGl2ZV06c2NhbGUtMTAwIGhvdmVyOmJnLWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6c2NhbGUtMTAyXCJcbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6XG4gICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09IFwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgICAgICAgICA/IHByaW1hcnlDb2xvclxuICAgICAgICAgICAgICAgICAgICA6IFwicmdiKDIwOSAyMTMgMjE5KVwiLFxuICAgICAgICAgICAgICAgIGNvbG9yOlxuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSBcImRlc2NyaXB0aW9uXCJcbiAgICAgICAgICAgICAgICAgICAgPyBwcmltYXJ5VGV4dENvbG9yXG4gICAgICAgICAgICAgICAgICAgIDogXCJyZ2IoNTUgNjUgODEpXCIsXG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtOlxuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSBcImRlc2NyaXB0aW9uXCIgPyBcInNjYWxlKDEuMDUpXCIgOiBcInNjYWxlKDEpXCIsXG4gICAgICAgICAgICAgICAgYm94U2hhZG93OlxuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSBcImRlc2NyaXB0aW9uXCJcbiAgICAgICAgICAgICAgICAgICAgPyBcIjAgMTBweCAxNXB4IC0zcHggcmdiYSgwLCAwLCAwLCAwLjEpLCAwIDRweCA2cHggLTJweCByZ2JhKDAsIDAsIDAsIDAuMDUpXCJcbiAgICAgICAgICAgICAgICAgICAgOiBcIm5vbmVcIixcbiAgICAgICAgICAgICAgICBib3JkZXJDb2xvcjpcbiAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gXCJkZXNjcmlwdGlvblwiID8gcHJpbWFyeUNvbG9yIDogXCJ0cmFuc3BhcmVudFwiLFxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBPdmVydmlld1xuICAgICAgICAgICAgPC9UYWJzVHJpZ2dlcj5cbiAgICAgICAgICAgIDxUYWJzVHJpZ2dlclxuICAgICAgICAgICAgICB2YWx1ZT1cInJldmlld3NcIlxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkLWxnIHB4LTggcHktNCB0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGJvcmRlci0yIGJvcmRlci10cmFuc3BhcmVudCBkYXRhLVtzdGF0ZT1pbmFjdGl2ZV06YmctZ3JheS0zMDAgZGF0YS1bc3RhdGU9aW5hY3RpdmVdOnRleHQtZ3JheS03MDAgZGF0YS1bc3RhdGU9aW5hY3RpdmVdOnNjYWxlLTEwMCBob3ZlcjpiZy1ncmF5LTQwMCBob3Zlcjp0ZXh0LXdoaXRlIGhvdmVyOnNjYWxlLTEwMlwiXG4gICAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOlxuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSBcInJldmlld3NcIiA/IHByaW1hcnlDb2xvciA6IFwicmdiKDIwOSAyMTMgMjE5KVwiLFxuICAgICAgICAgICAgICAgIGNvbG9yOlxuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSBcInJldmlld3NcIiA/IHByaW1hcnlUZXh0Q29sb3IgOiBcInJnYig1NSA2NSA4MSlcIixcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06IGFjdGl2ZVRhYiA9PT0gXCJyZXZpZXdzXCIgPyBcInNjYWxlKDEuMDUpXCIgOiBcInNjYWxlKDEpXCIsXG4gICAgICAgICAgICAgICAgYm94U2hhZG93OlxuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSBcInJldmlld3NcIlxuICAgICAgICAgICAgICAgICAgICA/IFwiMCAxMHB4IDE1cHggLTNweCByZ2JhKDAsIDAsIDAsIDAuMSksIDAgNHB4IDZweCAtMnB4IHJnYmEoMCwgMCwgMCwgMC4wNSlcIlxuICAgICAgICAgICAgICAgICAgICA6IFwibm9uZVwiLFxuICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOlxuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSBcInJldmlld3NcIiA/IHByaW1hcnlDb2xvciA6IFwidHJhbnNwYXJlbnRcIixcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgU3BlY2lmaWNhdGlvbnNcbiAgICAgICAgICAgIDwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgICA8VGFic1RyaWdnZXJcbiAgICAgICAgICAgICAgdmFsdWU9XCJzaGlwcGluZ1wiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQtbGcgcHgtOCBweS00IHRleHQtbGcgZm9udC1zZW1pYm9sZCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgYm9yZGVyLTIgYm9yZGVyLXRyYW5zcGFyZW50IGRhdGEtW3N0YXRlPWluYWN0aXZlXTpiZy1ncmF5LTMwMCBkYXRhLVtzdGF0ZT1pbmFjdGl2ZV06dGV4dC1ncmF5LTcwMCBkYXRhLVtzdGF0ZT1pbmFjdGl2ZV06c2NhbGUtMTAwIGhvdmVyOmJnLWdyYXktNDAwIGhvdmVyOnRleHQtd2hpdGUgaG92ZXI6c2NhbGUtMTAyXCJcbiAgICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6XG4gICAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09IFwic2hpcHBpbmdcIiA/IHByaW1hcnlDb2xvciA6IFwicmdiKDIwOSAyMTMgMjE5KVwiLFxuICAgICAgICAgICAgICAgIGNvbG9yOlxuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSBcInNoaXBwaW5nXCIgPyBwcmltYXJ5VGV4dENvbG9yIDogXCJyZ2IoNTUgNjUgODEpXCIsXG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtOlxuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSBcInNoaXBwaW5nXCIgPyBcInNjYWxlKDEuMDUpXCIgOiBcInNjYWxlKDEpXCIsXG4gICAgICAgICAgICAgICAgYm94U2hhZG93OlxuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSBcInNoaXBwaW5nXCJcbiAgICAgICAgICAgICAgICAgICAgPyBcIjAgMTBweCAxNXB4IC0zcHggcmdiYSgwLCAwLCAwLCAwLjEpLCAwIDRweCA2cHggLTJweCByZ2JhKDAsIDAsIDAsIDAuMDUpXCJcbiAgICAgICAgICAgICAgICAgICAgOiBcIm5vbmVcIixcbiAgICAgICAgICAgICAgICBib3JkZXJDb2xvcjpcbiAgICAgICAgICAgICAgICAgIGFjdGl2ZVRhYiA9PT0gXCJzaGlwcGluZ1wiID8gcHJpbWFyeUNvbG9yIDogXCJ0cmFuc3BhcmVudFwiLFxuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBSZXZpZXdzXG4gICAgICAgICAgICA8L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgIDwvVGFic0xpc3Q+XG5cbiAgICAgICAgICA8VGFic0NvbnRlbnRcbiAgICAgICAgICAgIHZhbHVlPVwiZGVzY3JpcHRpb25cIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtNCBiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1zbVwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLThcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi00XCI+UHJvZHVjdCBPdmVydmlldzwvaDM+XG4gICAgICAgICAgICAgIHtwcm9kdWN0LkZ1bGxEZXNjcmlwdGlvbiA/IChcbiAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwcm9zZSBtYXgtdy1ub25lXCJcbiAgICAgICAgICAgICAgICAgIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MPXt7IF9faHRtbDogcHJvZHVjdC5GdWxsRGVzY3JpcHRpb24gfX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwIGxlYWRpbmctcmVsYXhlZFwiPlxuICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC5TaG9ydERlc2NyaXB0aW9uIHx8XG4gICAgICAgICAgICAgICAgICAgICAgXCJUaGlzIGlzIGEgaGlnaC1xdWFsaXR5IG1lZGljYWwgcHJvZHVjdCBkZXNpZ25lZCB0byBtZWV0IHByb2Zlc3Npb25hbCBzdGFuZGFyZHMuXCJ9XG4gICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTQgbXQtNlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcC00IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEtleSBGZWF0dXJlc1xuICAgICAgICAgICAgICAgICAgICAgIDwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTcwMCBzcGFjZS15LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaT7igKIgUHJvZmVzc2lvbmFsIGdyYWRlIHF1YWxpdHk8L2xpPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpPuKAoiBEdXJhYmxlIGNvbnN0cnVjdGlvbjwvbGk+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGk+4oCiIEVhc3kgdG8gdXNlPC9saT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaT7igKIgUmVsaWFibGUgcGVyZm9ybWFuY2U8L2xpPlxuICAgICAgICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktNTAgcC00IHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEJlbmVmaXRzXG4gICAgICAgICAgICAgICAgICAgICAgPC9oND5cbiAgICAgICAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNzAwIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpPuKAoiBFbmhhbmNlZCBlZmZpY2llbmN5PC9saT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxsaT7igKIgQ29zdC1lZmZlY3RpdmUgc29sdXRpb248L2xpPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGxpPuKAoiBMb25nLWxhc3RpbmcgZHVyYWJpbGl0eTwvbGk+XG4gICAgICAgICAgICAgICAgICAgICAgICA8bGk+4oCiIFByb2Zlc3Npb25hbCByZXN1bHRzPC9saT5cbiAgICAgICAgICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICAgIDxUYWJzQ29udGVudFxuICAgICAgICAgICAgdmFsdWU9XCJyZXZpZXdzXCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTQgYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctc21cIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC04XCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgbWItNFwiPlxuICAgICAgICAgICAgICAgIFByb2R1Y3QgU3BlY2lmaWNhdGlvbnNcbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPFByb2R1Y3RTcGVjaWZpY2F0aW9uc1xuICAgICAgICAgICAgICAgIGF0dHJpYnV0ZXM9e3Byb2R1Y3QuQXR0cmlidXRlc0pzb24gfHwgW119XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L1RhYnNDb250ZW50PlxuXG4gICAgICAgICAgPFRhYnNDb250ZW50XG4gICAgICAgICAgICB2YWx1ZT1cInNoaXBwaW5nXCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTQgYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctc21cIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC04XCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgbWItNFwiPkN1c3RvbWVyIFJldmlld3M8L2gzPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBzbTppdGVtcy1jZW50ZXIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAge1sxLCAyLCAzLCA0LCA1XS5tYXAoKHN0YXIpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8U3RhclxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtzdGFyfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy02IGgtNiAke1xuICAgICAgICAgICAgICAgICAgICAgICAgICBzdGFyIDw9IE1hdGguZmxvb3IocHJvZHVjdC5SYXRpbmcgfHwgMClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IFwidGV4dC15ZWxsb3ctNDAwIGZpbGwteWVsbG93LTQwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBcInRleHQtZ3JheS0zMDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3Byb2R1Y3QuUmF0aW5nPy50b0ZpeGVkKDEpIHx8IFwiMC4wXCJ9XG4gICAgICAgICAgICAgICAgICAgIDwvc3Bhbj57XCIgXCJ9XG4gICAgICAgICAgICAgICAgICAgIG91dCBvZiA1XG4gICAgICAgICAgICAgICAgICAgIHtwcm9kdWN0LlRvdGFsUmV2aWV3cyA/IChcbiAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtcIiBcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgIOKAoiB7cHJvZHVjdC5Ub3RhbFJldmlld3N9IHJldmlld1xuICAgICAgICAgICAgICAgICAgICAgICAge3Byb2R1Y3QuVG90YWxSZXZpZXdzICE9PSAxID8gXCJzXCIgOiBcIlwifVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgKSA6IG51bGx9XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZ3JheS01MCBwLTQgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgPFByb2R1Y3RSZXZpZXdzRGlzcGxheVxuICAgICAgICAgICAgICAgICAgICBwcm9kdWN0SWQ9e3Byb2R1Y3QuUHJvZHVjdElkfVxuICAgICAgICAgICAgICAgICAgICBzaG93VGl0bGU9e2ZhbHNlfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L1RhYnNDb250ZW50PlxuICAgICAgICA8L1RhYnM+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgUHJvZHVjdERldGFpbHNDbGllbnQ7XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VNZW1vIiwidXNlQ2FsbGJhY2siLCJMaW5rIiwiYXhpb3MiLCJTaG9wcGluZ0NhcnQiLCJIZWFydCIsIlNoYXJlMiIsIlN0YXIiLCJBcnJvd0xlZnQiLCJBd2FyZCIsIkNsb2NrIiwiQnV0dG9uIiwiQnJlYWRjcnVtYiIsIkJyZWFkY3J1bWJJdGVtIiwiQnJlYWRjcnVtYkxpbmsiLCJCcmVhZGNydW1iTGlzdCIsIkJyZWFkY3J1bWJQYWdlIiwiQnJlYWRjcnVtYlNlcGFyYXRvciIsIlRhYnMiLCJUYWJzQ29udGVudCIsIlRhYnNMaXN0IiwiVGFic1RyaWdnZXIiLCJCYWRnZSIsInRvYXN0Iiwic2hvd01vZGVybkFkZFRvQ2FydFRvYXN0IiwidXNlQ2FydCIsInVzZVdpc2hsaXN0IiwidXNlQ3VycmVuY3kiLCJ1c2VTZXR0aW5ncyIsIlByb2R1Y3RTcGVjaWZpY2F0aW9ucyIsIlByb2R1Y3RNZWRpYUdhbGxlcnkiLCJDb3VudGRvd24iLCJQcm9kdWN0TG9hZGluZyIsIlByb2R1Y3RFcnJvciIsIlByb2R1Y3RSZXZpZXdzRGlzcGxheSIsImNvbnN0cnVjdEltYWdlVXJsIiwiYXR0YWNobWVudFVybCIsInN0YXJ0c1dpdGgiLCJiYXNlVXJsIiwibm9ybWFsaXplZFBhdGgiLCJyZXBsYWNlIiwiUHJvZHVjdERldGFpbHNDbGllbnQiLCJwcm9kdWN0SWQiLCJwcm9kdWN0IiwiY2FydCIsIndpc2hsaXN0IiwicmF0ZSIsInByaW1hcnlDb2xvciIsInByaW1hcnlUZXh0Q29sb3IiLCJzZXRQcm9kdWN0IiwibG9hZGluZyIsInNldExvYWRpbmciLCJxdWFudGl0eSIsInNldFF1YW50aXR5IiwiYWN0aXZlSW1hZ2UiLCJzZXRBY3RpdmVJbWFnZSIsInZpZGVvTGlua3MiLCJzZXRWaWRlb0xpbmtzIiwic2VsZWN0ZWRWaWRlb0luZGV4Iiwic2V0U2VsZWN0ZWRWaWRlb0luZGV4IiwiYWRkaW5nVG9DYXJ0Iiwic2V0QWRkaW5nVG9DYXJ0IiwiYWRkaW5nVG9XaXNobGlzdCIsInNldEFkZGluZ1RvV2lzaGxpc3QiLCJlcnJvciIsInNldEVycm9yIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwiaXNBbmltYXRpbmciLCJzZXRJc0FuaW1hdGluZyIsImFuaW1hdGlvblR5cGUiLCJzZXRBbmltYXRpb25UeXBlIiwic2VsZWN0ZWRBdHRyaWJ1dGVzIiwic2V0U2VsZWN0ZWRBdHRyaWJ1dGVzIiwiaW5pdGlhbCIsIkF0dHJpYnV0ZXNKc29uIiwiZm9yRWFjaCIsImF0dHIiLCJrZXkiLCJQcm9kdWN0QXR0cmlidXRlSUQiLCJBdHRyaWJ1dGVWYWx1ZUlEIiwiZmV0Y2hQcm9kdWN0IiwicmVxdWVzdEJvZHkiLCJyZXF1ZXN0UGFyYW1ldGVycyIsIlByb2R1Y3RJZCIsIk51bWJlciIsInBhcnNlSW50IiwicmVjb3JkVmFsdWVKc29uIiwiY29uc29sZSIsImxvZyIsInJlc3BvbnNlIiwiTWFrZUFwaUNhbGxBc3luYyIsIkFjY2VwdCIsImRhdGEiLCJhcGlIZWxwZXJFcnJvciIsInBvc3QiLCJoZWFkZXJzIiwicmVzcG9uc2VEYXRhIiwicGFyc2VkRGF0YSIsIkpTT04iLCJwYXJzZSIsInByb2R1Y3REYXRhIiwiQXJyYXkiLCJpc0FycmF5IiwiZSIsIkNhdGVnb3J5TmFtZSIsIkNhdGVnb3J5SUQiLCJQcm9kdWN0SW1hZ2VzSnNvbiIsImxlbmd0aCIsInByaW1hcnlJbWFnZSIsImZpbmQiLCJpbWciLCJJc1ByaW1hcnkiLCJBdHRhY2htZW50VVJMIiwiVmlkZW9MaW5rIiwibGlua3MiLCJzcGxpdCIsIm1hcCIsImxpbmsiLCJ0cmltIiwicHJvY2Vzc2VkTGlua3MiLCJjb25zdHJ1Y3RWaWRlb1VybCIsIk9yZGVyTWluaW11bVF1YW50aXR5IiwicGFyc2VFcnJvciIsInN0YXR1cyIsIm1lc3NhZ2UiLCJyZXF1ZXN0IiwidmlkZW9MaW5rIiwiaW5jbHVkZXMiLCJlbmNvZGVVUklDb21wb25lbnQiLCJub3JtYWxpemVkVmlkZW9MaW5rIiwiZ3JvdXBlZEF0dHJpYnV0ZXMiLCJyZWR1Y2UiLCJncm91cHMiLCJncm91cElkIiwicHVzaCIsImhhbmRsZUF0dHJpYnV0ZUNoYW5nZSIsImlzQ2hlY2tlZCIsImlzUmFkaW9Hcm91cCIsInByZXYiLCJuZXdTdGF0ZSIsImF0dHJLZXkiLCJPYmplY3QiLCJrZXlzIiwicmVuZGVyUHJpY2UiLCJzaG93RGlzY291bnQiLCJEaXNjb3VudFByaWNlIiwiUHJpY2UiLCJhZGp1c3RlZFByaWNlIiwiY2FsY3VsYXRlQWRqdXN0ZWRQcmljZSIsInNob3dBZGp1c3RlZFByaWNlIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiIsInRvRml4ZWQiLCJNYXRoIiwicm91bmQiLCJQcmljZUlRRCIsInRvTG9jYWxlU3RyaW5nIiwiUG9pbnRObyIsIk9sZFByaWNlIiwiUHJpY2VBZGp1c3RtZW50IiwiUHJpY2VBZGp1c3RtZW50VHlwZSIsIm1heCIsInJlbmRlckJhZGdlcyIsIklzRGlzY291bnRBbGxvd2VkIiwiTWFya0FzTmV3IiwibWVkaWFJdGVtcyIsIml0ZW1zIiwidHlwZSIsInVybCIsImFsdCIsIlByb2R1Y3ROYW1lIiwidGh1bWJuYWlsIiwidmlkZW9VcmwiLCJpbmRleCIsImFuaW1hdGVDb3VudGVyIiwic2V0VGltZW91dCIsImluY3JlbWVudFF1YW50aXR5IiwibWF4UXVhbnRpdHkiLCJPcmRlck1heGltdW1RdWFudGl0eSIsIm1pbiIsIlN0b2NrUXVhbnRpdHkiLCJpbmZvIiwiZGVjcmVtZW50UXVhbnRpdHkiLCJtaW5RdWFudGl0eSIsImdldEJ1dHRvblN0eWxlcyIsImJhc2VTdHlsZXMiLCJkaXNhYmxlZFN0eWxlcyIsImlzTWF4IiwiaXNNaW4iLCJDb3VudGVyRGlzcGxheSIsInJldHJ5IiwiaDEiLCJwIiwiaHJlZiIsImFzQ2hpbGQiLCJTZWxsU3RhcnREYXRldGltZVVUQyIsIlNlbGxFbmREYXRldGltZVVUQyIsIm5vdyIsIkRhdGUiLCJzYWxlU3RhcnQiLCJzYWxlRW5kIiwiaXNTYWxlQWN0aXZlIiwiaXNTYWxlVXBjb21pbmciLCJpc1NhbGVFeHBpcmVkIiwiY291bnRkb3duRW5kRGF0ZSIsInRpbWVyTWVzc2FnZSIsInVyZ2VuY3lNZXNzYWdlIiwiZW5kRGF0ZSIsIm1lZGlhIiwiXyIsImkiLCJmbG9vciIsIlJhdGluZyIsIlRvdGFsUmV2aWV3cyIsIlNob3J0RGVzY3JpcHRpb24iLCJoMyIsImVudHJpZXMiLCJhdHRyaWJ1dGVzIiwiaDQiLCJEaXNwbGF5TmFtZSIsIkF0dHJpYnV0ZU5hbWUiLCJpc1NlbGVjdGVkIiwiaW5wdXQiLCJpZCIsIm5hbWUiLCJjaGVja2VkIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJsYWJlbCIsImh0bWxGb3IiLCJBdHRyaWJ1dGVWYWx1ZVRleHQiLCJidXR0b24iLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJhcmlhLWxhYmVsIiwic3ZnIiwieG1sbnMiLCJ2aWV3Qm94IiwiZmlsbCIsInBhdGgiLCJmaWxsUnVsZSIsImQiLCJjbGlwUnVsZSIsInByb2R1Y3RJbWFnZSIsInNlbGVjdGVkQXR0cnMiLCJmaWx0ZXIiLCJhZGRUb0NhcnQiLCJwcmljZSIsImRpc2NvdW50UHJpY2UiLCJpbWFnZSIsIm9yaWdpbmFsUHJpY2UiLCJwcm9kdWN0TmFtZSIsIm9uVmlld0NhcnQiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImlzQWxyZWFkeUluV2lzaGxpc3QiLCJpc0luV2lzaGxpc3QiLCJyZW1vdmVGcm9tV2lzaGxpc3QiLCJzdWNjZXNzIiwicHJvZHVjdFVybCIsImltYWdlVXJsIiwiYWRkVG9XaXNobGlzdCIsIm5hdmlnYXRvciIsInNoYXJlIiwidGl0bGUiLCJNZXRhVGl0bGUiLCJ0ZXh0IiwiTWV0YURlc2NyaXB0aW9uIiwiY2F0Y2giLCJlcnIiLCJjbGlwYm9hcmQiLCJ3cml0ZVRleHQiLCJNZXRhS2V5d29yZHMiLCJrZXl3b3JkIiwidmFyaWFudCIsImRlZmF1bHRWYWx1ZSIsInZhbHVlIiwib25WYWx1ZUNoYW5nZSIsInN0eWxlIiwiYmFja2dyb3VuZENvbG9yIiwiY29sb3IiLCJ0cmFuc2Zvcm0iLCJib3hTaGFkb3ciLCJib3JkZXJDb2xvciIsIkZ1bGxEZXNjcmlwdGlvbiIsImRhbmdlcm91c2x5U2V0SW5uZXJIVE1MIiwiX19odG1sIiwidWwiLCJsaSIsInN0YXIiLCJzaG93VGl0bGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/product-details-client.tsx\n"));

/***/ })

});