"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9321],{9776:(e,t,r)=>{r.d(t,{B:()=>i,H:()=>c});var o=r(95155),n=r(12115),a=r(65409);let s=(0,n.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,n.useState)(1500),[c,u]=(0,n.useState)(!0),l=async()=>{u(!0);try{let e=await (0,a.k6)();i(e)}catch(e){console.error("Failed to load currency rate:",e)}finally{u(!1)}},d=async()=>{await l()};return(0,n.useEffect)(()=>{l()},[]),(0,o.jsx)(s.Provider,{value:{rate:r,isLoading:c,convertToIQD:e=>(0,a.XX)(e,r),formatUSD:e=>(0,a.$g)(e,"USD"),formatIQD:e=>(0,a.$g)(e,"IQD"),refreshRate:d},children:t})}function c(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useCurrency must be used within a CurrencyProvider");return e}},53999:(e,t,r)=>{r.d(t,{cn:()=>a});var o=r(52596),n=r(39688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,o.$)(t))}},78067:(e,t,r)=>{r.d(t,{_:()=>i,e:()=>s});var o=r(95155),n=r(12115);let a=(0,n.createContext)(void 0);function s(e){let{children:t}=e,[r,s]=(0,n.useState)([]),[i,c]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{s(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e)}c(!0)},[]),(0,n.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(r))},[r]);let u=e=>{s(t=>t.filter(t=>t.id!==e))},l=r.reduce((e,t)=>e+t.quantity,0);(0,n.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(r))},[r]);let d=r.reduce((e,t)=>e+(t.discountPrice?Math.min(t.discountPrice,t.adjustedPrice):t.adjustedPrice)*t.quantity,0),f=r.reduce((e,t)=>e+(t.adjustedIqdPrice||t.iqdPrice||0)*t.quantity,0);return(0,o.jsx)(a.Provider,{value:{items:r,addToCart:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],o=arguments.length>3?arguments[3]:void 0,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1500;s(a=>{let s=e.price,i=o||Math.round(e.price*n),c=i;r.forEach(t=>{if(t.PriceAdjustment&&t.PriceAdjustmentType){let r=e.originalPrice||e.price;switch(t.PriceAdjustmentType){case 1:s+=t.PriceAdjustment,c+=Math.round(t.PriceAdjustment*n);break;case 2:let o=r*t.PriceAdjustment/100;s+=o,c+=Math.round(o*n)}}});let u=a.findIndex(t=>{var o;return t.id===e.id&&JSON.stringify(null==(o=t.attributes)?void 0:o.sort((e,t)=>e.ProductAttributeID-t.ProductAttributeID))===JSON.stringify(null==r?void 0:r.sort((e,t)=>e.ProductAttributeID-t.ProductAttributeID))});if(!(u>=0))return[...a,{...e,iqdPrice:i,adjustedIqdPrice:Math.max(0,c),quantity:t,attributes:r,adjustedPrice:Math.max(0,s),originalPrice:e.originalPrice}];{let e=[...a];return e[u].quantity+=t,e}})},removeFromCart:u,updateQuantity:(e,t)=>{if(t<=0)return void u(e);s(r=>r.map(r=>r.id===e?{...r,quantity:t}:r))},clearCart:()=>{s([])},totalItems:l,subtotal:d,subtotalIQD:f,total:d,totalIQD:f,isHydrated:i},children:t})}function i(){let e=(0,n.useContext)(a);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},79891:(e,t,r)=>{r.d(t,{Z:()=>c,t:()=>u});var o=r(95155),n=r(12115);let a={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us",searchProducts:"Search products..."},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",searchProducts:"ابحث عن المنتجات...",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}};var s=r(94213);let i=(0,n.createContext)(void 0);function c(e){let{children:t}=e,[r,c]=(0,n.useState)("light"),[u,l]=(0,n.useState)("en"),[d,f]=(0,n.useState)("#0074b2"),[g,m]=(0,n.useState)("#ffffff");return(0,n.useEffect)(()=>{let e=(0,s.N)(d);m(e),document.documentElement.style.setProperty("--primary",d),document.documentElement.style.setProperty("--primary-foreground",e)},[d]),(0,o.jsx)(i.Provider,{value:{theme:r,language:u,primaryColor:d,primaryTextColor:g,toggleTheme:()=>{c("light"===r?"dark":"light")},setLanguage:e=>{l(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{f(e);let t=(0,s.N)(e);m(t),document.documentElement.style.setProperty("--primary",e),document.documentElement.style.setProperty("--primary-foreground",t)},t:e=>(function(e,t){let r=a[t];return e in r?r[e]:"en"!==t&&e in a.en?a.en[e]:e})(e,u)},children:t})}function u(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},94213:(e,t,r)=>{function o(e,t){let r=e=>{let t=e.replace("#",""),r=parseInt(t.slice(0,2),16)/255,o=[r,parseInt(t.slice(2,4),16)/255,parseInt(t.slice(4,6),16)/255].map(e=>e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4));return .2126*o[0]+.7152*o[1]+.0722*o[2]},o=r(e),n=r(t);return(Math.max(o,n)+.05)/(Math.min(o,n)+.05)}function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"AA",r=o(e,"#ffffff"),n=o(e,"#000000"),a="AAA"===t?7:4.5;return r>=a&&n>=a?r>n?"#ffffff":"#000000":r>=a?"#ffffff":n>=a?"#000000":r>n?"#ffffff":"#000000"}r.d(t,{N:()=>n})},97168:(e,t,r)=>{r.d(t,{$:()=>u,r:()=>c});var o=r(95155),n=r(12115),a=r(99708),s=r(74466),i=r(53999);let c=(0,s.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),u=n.forwardRef((e,t)=>{let{className:r,variant:n,size:s,asChild:u=!1,...l}=e,d=u?a.DX:"button";return(0,o.jsx)(d,{className:(0,i.cn)(c({variant:n,size:s,className:r})),ref:t,...l})});u.displayName="Button"}}]);