(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6744],{7661:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i});var l=r(95155),d=r(98816),n=r(88482);function i(){let{user:e,isLoggedIn:s}=(0,d.J)();return s?(0,l.jsx)("div",{className:"container mx-auto p-8",children:(0,l.jsxs)(n.Zp,{className:"p-6",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"User Data Test"}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-semibold",children:"Raw User Object:"}),(0,l.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-xs overflow-auto",children:JSON.stringify(e,null,2)})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-semibold",children:"Specific Fields:"}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"FirstName:"}),' "',null==e?void 0:e.FirstName,'"']}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"LastName:"}),' "',null==e?void 0:e.LastName,'"']}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"Gender:"}),' "',null==e?void 0:e.Gender,'" (type: ',typeof(null==e?void 0:e.Gender),")"]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"CategoryId:"}),' "',null==e?void 0:e.CategoryId,'" (type: ',typeof(null==e?void 0:e.CategoryId),")"]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"CategoryID:"}),' "',null==e?void 0:e.CategoryID,'" (type: ',typeof(null==e?void 0:e.CategoryID),")"]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"Email:"}),' "',null==e?void 0:e.Email,'"']}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"EmailAddress:"}),' "',null==e?void 0:e.EmailAddress,'"']}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"PhoneNo:"}),' "',null==e?void 0:e.PhoneNo,'"']}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"MobileNo:"}),' "',null==e?void 0:e.MobileNo,'"']}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"CountryName:"}),' "',null==e?void 0:e.CountryName,'"']}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"AddressLineOne:"}),' "',null==e?void 0:e.AddressLineOne,'"']}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"CategoryName:"}),' "',null==e?void 0:e.CategoryName,'"']})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-semibold",children:"All Available Keys:"}),(0,l.jsx)("p",{className:"text-sm bg-gray-100 p-2 rounded",children:e?Object.keys(e).join(", "):"No user data"})]})]})]})}):(0,l.jsx)("div",{className:"container mx-auto p-8",children:(0,l.jsxs)(n.Zp,{className:"p-6",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"User Data Test"}),(0,l.jsx)("p",{children:"Please log in to see user data."})]})})}},31698:(e,s,r)=>{Promise.resolve().then(r.bind(r,7661))},53999:(e,s,r)=>{"use strict";r.d(s,{cn:()=>n});var l=r(52596),d=r(39688);function n(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,d.QP)((0,l.$)(s))}},88482:(e,s,r)=>{"use strict";r.d(s,{BT:()=>o,Wu:()=>c,ZB:()=>t,Zp:()=>i,aR:()=>a,wL:()=>x});var l=r(95155),d=r(12115),n=r(53999);let i=d.forwardRef((e,s)=>{let{className:r,...d}=e;return(0,l.jsx)("div",{ref:s,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...d})});i.displayName="Card";let a=d.forwardRef((e,s)=>{let{className:r,...d}=e;return(0,l.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...d})});a.displayName="CardHeader";let t=d.forwardRef((e,s)=>{let{className:r,...d}=e;return(0,l.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...d})});t.displayName="CardTitle";let o=d.forwardRef((e,s)=>{let{className:r,...d}=e;return(0,l.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",r),...d})});o.displayName="CardDescription";let c=d.forwardRef((e,s)=>{let{className:r,...d}=e;return(0,l.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",r),...d})});c.displayName="CardContent";let x=d.forwardRef((e,s)=>{let{className:r,...d}=e;return(0,l.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",r),...d})});x.displayName="CardFooter"}},e=>{e.O(0,[4277,3464,8816,8441,5964,7358],()=>e(e.s=31698)),_N_E=e.O()}]);