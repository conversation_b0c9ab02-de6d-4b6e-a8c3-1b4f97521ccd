"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./components/products/product-media-gallery.tsx":
/*!*******************************************************!*\
  !*** ./components/products/product-media-gallery.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductMediaGallery: () => (/* binding */ ProductMediaGallery)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ProductMediaGallery auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Component for generating video thumbnails\nfunction VideoThumbnail(param) {\n    let { src, alt, className } = param;\n    _s();\n    const [thumbnailSrc, setThumbnailSrc] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"VideoThumbnail.useEffect\": ()=>{\n            const generateThumbnail = {\n                \"VideoThumbnail.useEffect.generateThumbnail\": ()=>{\n                    const video = videoRef.current;\n                    const canvas = canvasRef.current;\n                    if (!video || !canvas) return;\n                    const context = canvas.getContext('2d');\n                    if (!context) return;\n                    // Set canvas dimensions to match video\n                    canvas.width = video.videoWidth || 320;\n                    canvas.height = video.videoHeight || 240;\n                    try {\n                        // Draw the current frame to canvas\n                        context.drawImage(video, 0, 0, canvas.width, canvas.height);\n                        // Convert canvas to data URL\n                        const dataURL = canvas.toDataURL('image/jpeg', 0.8);\n                        setThumbnailSrc(dataURL);\n                    } catch (err) {\n                        console.error('Error generating video thumbnail:', err);\n                        setError(true);\n                    }\n                }\n            }[\"VideoThumbnail.useEffect.generateThumbnail\"];\n            const handleLoadedData = {\n                \"VideoThumbnail.useEffect.handleLoadedData\": ()=>{\n                    const video = videoRef.current;\n                    if (video) {\n                        // Seek to 1 second or 10% of video duration, whichever is smaller\n                        const seekTime = Math.min(1, video.duration * 0.1);\n                        video.currentTime = seekTime;\n                    }\n                }\n            }[\"VideoThumbnail.useEffect.handleLoadedData\"];\n            const handleSeeked = {\n                \"VideoThumbnail.useEffect.handleSeeked\": ()=>{\n                    // Small delay to ensure frame is rendered\n                    setTimeout(generateThumbnail, 100);\n                }\n            }[\"VideoThumbnail.useEffect.handleSeeked\"];\n            const video = videoRef.current;\n            if (video) {\n                video.addEventListener('loadeddata', handleLoadedData);\n                video.addEventListener('seeked', handleSeeked);\n                video.addEventListener('error', {\n                    \"VideoThumbnail.useEffect\": ()=>setError(true)\n                }[\"VideoThumbnail.useEffect\"]);\n                return ({\n                    \"VideoThumbnail.useEffect\": ()=>{\n                        video.removeEventListener('loadeddata', handleLoadedData);\n                        video.removeEventListener('seeked', handleSeeked);\n                        video.removeEventListener('error', {\n                            \"VideoThumbnail.useEffect\": ()=>setError(true)\n                        }[\"VideoThumbnail.useEffect\"]);\n                    }\n                })[\"VideoThumbnail.useEffect\"];\n            }\n        }\n    }[\"VideoThumbnail.useEffect\"], [\n        src\n    ]);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"bg-gray-200 flex items-center justify-center\", className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 16,\n                className: \"text-gray-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                ref: videoRef,\n                src: src,\n                className: \"hidden\",\n                muted: true,\n                playsInline: true,\n                preload: \"metadata\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            thumbnailSrc ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: thumbnailSrc,\n                alt: alt || 'Video thumbnail',\n                className: \"w-full h-full object-cover\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-gray-200 flex items-center justify-center animate-pulse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    size: 16,\n                    className: \"text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(VideoThumbnail, \"84fzdndoZ7wJDakFY2R8LHgI1fs=\");\n_c = VideoThumbnail;\nfunction ProductMediaGallery(param) {\n    let { media, className } = param;\n    _s1();\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isVideoLoaded, setIsVideoLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isZoomed, setIsZoomed] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [zoomPosition, setZoomPosition] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [zoomLevel, setZoomLevel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1.5);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [activeMediaType, setActiveMediaType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Filter media based on active type\n    const filteredMedia = media.filter((item)=>activeMediaType === 'all' || item.type === activeMediaType);\n    const currentItem = filteredMedia[currentIndex] || media[0];\n    const hasMultipleItems = filteredMedia.length > 1;\n    // Reset index when media changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ProductMediaGallery.useEffect\": ()=>{\n            setCurrentIndex(0);\n            setIsPlaying(false);\n        }\n    }[\"ProductMediaGallery.useEffect\"], [\n        activeMediaType,\n        media\n    ]);\n    const goToPrev = ()=>{\n        setCurrentIndex((prev)=>prev === 0 ? filteredMedia.length - 1 : prev - 1);\n        setIsPlaying(false);\n    };\n    const goToNext = ()=>{\n        setCurrentIndex((prev)=>prev === filteredMedia.length - 1 ? 0 : prev + 1);\n        setIsPlaying(false);\n    };\n    const togglePlayPause = ()=>{\n        if (currentItem.type === 'video') {\n            if (videoRef.current) {\n                if (isPlaying) {\n                    videoRef.current.pause();\n                } else {\n                    videoRef.current.play();\n                }\n                setIsPlaying(!isPlaying);\n            }\n        }\n    };\n    const handleVideoEnded = ()=>{\n        setIsPlaying(false);\n        if (hasMultipleItems) {\n            goToNext();\n        }\n    };\n    const handleThumbnailClick = (index)=>{\n        setCurrentIndex(index);\n        setIsPlaying(false);\n        setIsZoomed(false);\n    };\n    const handleImageMouseMove = (e)=>{\n        if (!isZoomed || !imageRef.current) return;\n        const rect = imageRef.current.getBoundingClientRect();\n        const x = (e.clientX - rect.left) / rect.width * 100;\n        const y = (e.clientY - rect.top) / rect.height * 100;\n        setZoomPosition({\n            x,\n            y\n        });\n    };\n    const handleImageClick = ()=>{\n        if (currentItem.type === 'image') {\n            setIsZoomed(!isZoomed);\n        }\n    };\n    const handleImageDoubleClick = ()=>{\n        if (currentItem.type === 'image') {\n            setIsFullscreen(true);\n        }\n    };\n    const increaseZoom = ()=>{\n        setZoomLevel((prev)=>Math.min(prev + 0.5, 4));\n        setIsZoomed(true);\n    };\n    const decreaseZoom = ()=>{\n        setZoomLevel((prev)=>{\n            const newLevel = Math.max(prev - 0.5, 1);\n            if (newLevel === 1) {\n                setIsZoomed(false);\n            }\n            return newLevel;\n        });\n    };\n    const resetZoom = ()=>{\n        setZoomLevel(1.5);\n        setIsZoomed(false);\n    };\n    // Group media by type for filter buttons\n    const mediaCounts = media.reduce((acc, item)=>{\n        acc[item.type] = (acc[item.type] || 0) + 1;\n        return acc;\n    }, {});\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex flex-col gap-4', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative aspect-square w-full bg-gray-100 rounded-lg overflow-hidden\",\n                children: [\n                    currentItem.type === 'image' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-9ac6182445f1a16e\" + \" \" + \"relative w-full h-full overflow-hidden group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                id: \"9ac6182445f1a16e\",\n                                children: '.cursor-zoom-in.jsx-9ac6182445f1a16e{cursor:url(\\'data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><circle cx=\"11\" cy=\"11\" r=\"8\"/><path d=\"m21 21-4.35-4.35\"/><line x1=\"11\" y1=\"8\" x2=\"11\" y2=\"14\"/><line x1=\"8\" y1=\"11\" x2=\"14\" y2=\"11\"/></svg>\\')12 12,auto}.cursor-zoom-out.jsx-9ac6182445f1a16e{cursor:url(\\'data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><circle cx=\"11\" cy=\"11\" r=\"8\"/><path d=\"m21 21-4.35-4.35\"/><line x1=\"8\" y1=\"11\" x2=\"14\" y2=\"11\"/></svg>\\')12 12,auto}'\n                            }, void 0, false, void 0, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                ref: imageRef,\n                                src: currentItem.url,\n                                alt: currentItem.alt || 'Product image',\n                                style: isZoomed ? {\n                                    transform: \"scale(\".concat(zoomLevel, \")\"),\n                                    transformOrigin: \"\".concat(zoomPosition.x, \"% \").concat(zoomPosition.y, \"%\")\n                                } : {\n                                    transform: 'scale(1)'\n                                },\n                                onClick: handleImageClick,\n                                onDoubleClick: handleImageDoubleClick,\n                                onMouseMove: handleImageMouseMove,\n                                onMouseLeave: ()=>setIsZoomed(false),\n                                className: \"jsx-9ac6182445f1a16e\" + \" \" + ((0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-full h-full object-contain transition-transform duration-300\", isZoomed ? \"cursor-zoom-out\" : \"cursor-zoom-in\") || \"\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"absolute inset-0 flex items-center justify-center pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-white/95 rounded-full p-4 shadow-xl border-2 border-gray-200\",\n                                    children: isZoomed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"absolute top-2 left-2 flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1\",\n                                        children: isZoomed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-9ac6182445f1a16e\",\n                                                    children: [\n                                                        \"Zoom: \",\n                                                        Math.round(zoomLevel * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-9ac6182445f1a16e\",\n                                                    children: \"Click to zoom\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-9ac6182445f1a16e\" + \" \" + \"flex flex-col gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: increaseZoom,\n                                                title: \"Zoom in\",\n                                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: decreaseZoom,\n                                                title: \"Zoom out\",\n                                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: resetZoom,\n                                                title: \"Reset zoom\",\n                                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all text-xs\",\n                                                children: \"1:1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsFullscreen(true),\n                                title: \"View fullscreen\",\n                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"absolute top-2 right-12 bg-white/90 hover:bg-white text-gray-700 rounded p-2 shadow-sm transition-all\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                ref: videoRef,\n                                src: currentItem.url,\n                                className: \"w-full h-full object-contain\",\n                                controls: false,\n                                onEnded: handleVideoEnded,\n                                onPlay: ()=>setIsPlaying(true),\n                                onPause: ()=>setIsPlaying(false),\n                                onLoadedData: ()=>setIsVideoLoaded(true),\n                                playsInline: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            !isVideoLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center bg-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-pulse\",\n                                    children: \"Loading video...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: togglePlayPause,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('absolute inset-0 flex items-center justify-center transition-opacity', isPlaying ? 'opacity-0 hover:opacity-100' : 'opacity-80', !isVideoLoaded && 'hidden'),\n                                \"aria-label\": isPlaying ? 'Pause' : 'Play',\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-black/50 text-white rounded-full p-3\",\n                                    children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 30\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 52\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1\",\n                        children: [\n                            currentItem.type === 'image' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 12\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: 12\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: currentItem.type === 'image' ? 'Image' : 'Video'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, this),\n                    hasMultipleItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: goToPrev,\n                                className: \"absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all\",\n                                \"aria-label\": \"Previous media\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: goToNext,\n                                className: \"absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all\",\n                                \"aria-label\": \"Next media\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveMediaType('all'),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('px-3 py-1 text-sm rounded-full border', activeMediaType === 'all' ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                        children: [\n                            \"All (\",\n                            media.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, this),\n                    mediaCounts.image > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveMediaType('image'),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('px-3 py-1 text-sm rounded-full border flex items-center gap-1', activeMediaType === 'image' ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 14\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: mediaCounts.image\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 11\n                    }, this),\n                    mediaCounts.video > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveMediaType('video'),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('px-3 py-1 text-sm rounded-full border flex items-center gap-1', activeMediaType === 'video' ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: 14\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: mediaCounts.video\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, this),\n            hasMultipleItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 overflow-x-auto pb-2 -mx-2 px-2\",\n                children: filteredMedia.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleThumbnailClick(index),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('relative flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 transition-all', index === currentIndex ? 'border-blue-600 ring-2 ring-blue-400' : 'border-gray-200 hover:border-gray-400'),\n                        \"aria-label\": \"View \".concat(item.type, \" \").concat(index + 1),\n                        children: item.type === 'image' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: item.thumbnail || item.url,\n                            alt: item.alt || '',\n                            className: \"w-full h-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full h-full bg-gray-200 overflow-hidden\",\n                            children: [\n                                item.thumbnail ? // Use provided thumbnail if available\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: item.thumbnail,\n                                    alt: item.alt || 'Video thumbnail',\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 21\n                                }, this) : // Generate thumbnail from video\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                    src: item.url,\n                                    className: \"w-full h-full object-cover\",\n                                    muted: true,\n                                    playsInline: true,\n                                    preload: \"metadata\",\n                                    onLoadedMetadata: (e)=>{\n                                        const video = e.target;\n                                        video.currentTime = 1; // Seek to 1 second to get a frame\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center bg-black/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/90 rounded-full p-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            size: 12,\n                                            className: \"text-gray-700 ml-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 481,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 17\n                        }, this)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 438,\n                columnNumber: 9\n            }, this),\n            isFullscreen && currentItem.type === 'image' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/95 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full h-full flex items-center justify-center p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: currentItem.url,\n                            alt: currentItem.alt || 'Product image',\n                            className: \"max-w-full max-h-full object-contain\",\n                            style: {\n                                maxWidth: '95vw',\n                                maxHeight: '95vh'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsFullscreen(false),\n                            className: \"absolute top-4 right-4 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all\",\n                            title: \"Close fullscreen\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-6 w-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 13\n                        }, this),\n                        hasMultipleItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: goToPrev,\n                                    className: \"absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all\",\n                                    title: \"Previous image\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 523,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: goToNext,\n                                    className: \"absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all\",\n                                    title: \"Next image\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 525,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/70 text-white px-3 py-1 rounded-full text-sm\",\n                            children: [\n                                currentIndex + 1,\n                                \" of \",\n                                filteredMedia.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                    lineNumber: 496,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 495,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, this);\n}\n_s1(ProductMediaGallery, \"0G3bEXxMzDpxYPHFGofm4PcYc2I=\");\n_c1 = ProductMediaGallery;\nvar _c, _c1;\n$RefreshReg$(_c, \"VideoThumbnail\");\n$RefreshReg$(_c1, \"ProductMediaGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/products/product-media-gallery.tsx\n"));

/***/ })

});