"use strict";exports.id=6085,exports.ids=[6085],exports.modules={34993:(a,b,c)=>{c.d(b,{AB:()=>j,J5:()=>k,Qp:()=>i,tH:()=>n,tJ:()=>m,w1:()=>l});var d=c(60687),e=c(43210),f=c(8730),g=c(14952),h=(c(93661),c(96241));let i=e.forwardRef(({...a},b)=>(0,d.jsx)("nav",{ref:b,"aria-label":"breadcrumb",...a}));i.displayName="Breadcrumb";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("ol",{ref:c,className:(0,h.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",a),...b}));j.displayName="BreadcrumbList";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("li",{ref:c,className:(0,h.cn)("inline-flex items-center gap-1.5",a),...b}));k.displayName="BreadcrumbItem";let l=e.forwardRef(({asChild:a,className:b,...c},e)=>{let g=a?f.DX:"a";return(0,d.jsx)(g,{ref:e,className:(0,h.cn)("transition-colors hover:text-foreground",b),...c})});l.displayName="BreadcrumbLink";let m=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("span",{ref:c,role:"link","aria-disabled":"true","aria-current":"page",className:(0,h.cn)("font-normal text-foreground",a),...b}));m.displayName="BreadcrumbPage";let n=({children:a,className:b,...c})=>(0,d.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,h.cn)("[&>svg]:size-3.5",b),...c,children:a??(0,d.jsx)(g.A,{})});n.displayName="BreadcrumbSeparator"},55192:(a,b,c)=>{c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h,wL:()=>l});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b}));l.displayName="CardFooter"},99294:(a,b,c)=>{c.d(b,{tU:()=>V,av:()=>Y,j7:()=>W,Xi:()=>X});var d=c(60687),e=c(43210),f=c(70569),g=c(11273),h=c(9510),i=c(98599),j=c(96963),k=c(14163),l=c(13495),m=c(65551),n=c(43),o="rovingFocusGroup.onEntryFocus",p={bubbles:!1,cancelable:!0},q="RovingFocusGroup",[r,s,t]=(0,h.N)(q),[u,v]=(0,g.A)(q,[t]),[w,x]=u(q),y=e.forwardRef((a,b)=>(0,d.jsx)(r.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,d.jsx)(r.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,d.jsx)(z,{...a,ref:b})})}));y.displayName=q;var z=e.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:g,loop:h=!1,dir:j,currentTabStopId:r,defaultCurrentTabStopId:t,onCurrentTabStopIdChange:u,onEntryFocus:v,preventScrollOnEntryFocus:x=!1,...y}=a,z=e.useRef(null),A=(0,i.s)(b,z),B=(0,n.jH)(j),[C,E]=(0,m.i)({prop:r,defaultProp:t??null,onChange:u,caller:q}),[F,G]=e.useState(!1),H=(0,l.c)(v),I=s(c),J=e.useRef(!1),[K,L]=e.useState(0);return e.useEffect(()=>{let a=z.current;if(a)return a.addEventListener(o,H),()=>a.removeEventListener(o,H)},[H]),(0,d.jsx)(w,{scope:c,orientation:g,dir:B,loop:h,currentTabStopId:C,onItemFocus:e.useCallback(a=>E(a),[E]),onItemShiftTab:e.useCallback(()=>G(!0),[]),onFocusableItemAdd:e.useCallback(()=>L(a=>a+1),[]),onFocusableItemRemove:e.useCallback(()=>L(a=>a-1),[]),children:(0,d.jsx)(k.sG.div,{tabIndex:F||0===K?-1:0,"data-orientation":g,...y,ref:A,style:{outline:"none",...a.style},onMouseDown:(0,f.m)(a.onMouseDown,()=>{J.current=!0}),onFocus:(0,f.m)(a.onFocus,a=>{let b=!J.current;if(a.target===a.currentTarget&&b&&!F){let b=new CustomEvent(o,p);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=I().filter(a=>a.focusable);D([a.find(a=>a.active),a.find(a=>a.id===C),...a].filter(Boolean).map(a=>a.ref.current),x)}}J.current=!1}),onBlur:(0,f.m)(a.onBlur,()=>G(!1))})})}),A="RovingFocusGroupItem",B=e.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:g=!0,active:h=!1,tabStopId:i,children:l,...m}=a,n=(0,j.B)(),o=i||n,p=x(A,c),q=p.currentTabStopId===o,t=s(c),{onFocusableItemAdd:u,onFocusableItemRemove:v,currentTabStopId:w}=p;return e.useEffect(()=>{if(g)return u(),()=>v()},[g,u,v]),(0,d.jsx)(r.ItemSlot,{scope:c,id:o,focusable:g,active:h,children:(0,d.jsx)(k.sG.span,{tabIndex:q?0:-1,"data-orientation":p.orientation,...m,ref:b,onMouseDown:(0,f.m)(a.onMouseDown,a=>{g?p.onItemFocus(o):a.preventDefault()}),onFocus:(0,f.m)(a.onFocus,()=>p.onItemFocus(o)),onKeyDown:(0,f.m)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void p.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return C[e]}(a,p.orientation,p.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=t().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=p.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>D(c))}}),children:"function"==typeof l?l({isCurrentTabStop:q,hasTabStop:null!=w}):l})})});B.displayName=A;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var E=c(46059),F="Tabs",[G,H]=(0,g.A)(F,[v]),I=v(),[J,K]=G(F),L=e.forwardRef((a,b)=>{let{__scopeTabs:c,value:e,onValueChange:f,defaultValue:g,orientation:h="horizontal",dir:i,activationMode:l="automatic",...o}=a,p=(0,n.jH)(i),[q,r]=(0,m.i)({prop:e,onChange:f,defaultProp:g??"",caller:F});return(0,d.jsx)(J,{scope:c,baseId:(0,j.B)(),value:q,onValueChange:r,orientation:h,dir:p,activationMode:l,children:(0,d.jsx)(k.sG.div,{dir:p,"data-orientation":h,...o,ref:b})})});L.displayName=F;var M="TabsList",N=e.forwardRef((a,b)=>{let{__scopeTabs:c,loop:e=!0,...f}=a,g=K(M,c),h=I(c);return(0,d.jsx)(y,{asChild:!0,...h,orientation:g.orientation,dir:g.dir,loop:e,children:(0,d.jsx)(k.sG.div,{role:"tablist","aria-orientation":g.orientation,...f,ref:b})})});N.displayName=M;var O="TabsTrigger",P=e.forwardRef((a,b)=>{let{__scopeTabs:c,value:e,disabled:g=!1,...h}=a,i=K(O,c),j=I(c),l=S(i.baseId,e),m=T(i.baseId,e),n=e===i.value;return(0,d.jsx)(B,{asChild:!0,...j,focusable:!g,active:n,children:(0,d.jsx)(k.sG.button,{type:"button",role:"tab","aria-selected":n,"aria-controls":m,"data-state":n?"active":"inactive","data-disabled":g?"":void 0,disabled:g,id:l,...h,ref:b,onMouseDown:(0,f.m)(a.onMouseDown,a=>{g||0!==a.button||!1!==a.ctrlKey?a.preventDefault():i.onValueChange(e)}),onKeyDown:(0,f.m)(a.onKeyDown,a=>{[" ","Enter"].includes(a.key)&&i.onValueChange(e)}),onFocus:(0,f.m)(a.onFocus,()=>{let a="manual"!==i.activationMode;n||g||!a||i.onValueChange(e)})})})});P.displayName=O;var Q="TabsContent",R=e.forwardRef((a,b)=>{let{__scopeTabs:c,value:f,forceMount:g,children:h,...i}=a,j=K(Q,c),l=S(j.baseId,f),m=T(j.baseId,f),n=f===j.value,o=e.useRef(n);return e.useEffect(()=>{let a=requestAnimationFrame(()=>o.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,d.jsx)(E.C,{present:g||n,children:({present:c})=>(0,d.jsx)(k.sG.div,{"data-state":n?"active":"inactive","data-orientation":j.orientation,role:"tabpanel","aria-labelledby":l,hidden:!c,id:m,tabIndex:0,...i,ref:b,style:{...a.style,animationDuration:o.current?"0s":void 0},children:c&&h})})});function S(a,b){return`${a}-trigger-${b}`}function T(a,b){return`${a}-content-${b}`}R.displayName=Q;var U=c(96241);let V=L,W=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(N,{ref:c,className:(0,U.cn)("inline-flex h-8 items-center justify-center rounded-md bg-muted p-0.5 text-muted-foreground",a),...b}));W.displayName=N.displayName;let X=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(P,{ref:c,className:(0,U.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-2 py-1 text-xs font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...b}));X.displayName=P.displayName;let Y=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(R,{ref:c,className:(0,U.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...b}));Y.displayName=R.displayName}};