(()=>{var a={};a.id=1e3,a.ids=[1e3],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},2445:(a,b,c)=>{"use strict";c.d(b,{S:()=>k,k:()=>j});var d=c(60687),e=c(52581),f=c(5336),g=c(28561),h=c(11860),i=c(24934);let j=({productName:a,quantity:b,productImage:c,onViewCart:j})=>e.oR.custom(k=>(0,d.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-md w-full",children:(0,d.jsxs)("div",{className:"flex items-start gap-3",children:[(0,d.jsx)("div",{className:"flex-shrink-0",children:(0,d.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,d.jsx)(f.A,{className:"w-5 h-5 text-green-600"})})}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900 mb-1",children:"Added to cart"}),(0,d.jsxs)("p",{className:"text-sm text-gray-600 line-clamp-2",children:[b," \xd7 ",a]})]}),c&&(0,d.jsx)("div",{className:"flex-shrink-0 ml-3",children:(0,d.jsx)("img",{src:(a=>{if(!a)return"/placeholder.svg";if(a.startsWith("http://")||a.startsWith("https://")||a.startsWith("/placeholder")||a.startsWith("/images/")||a.startsWith("/assets/"))return a;let b="https://admin.codemedicalapps.com/".replace(/\/$/,""),c=a.startsWith("/")?a:`/${a}`;return c=c.replace(/\/+/g,"/"),`${b}${c}`})(c),alt:a,className:"w-12 h-12 rounded-md object-cover border border-gray-200",onError:a=>{let b=a.target;b.onerror=null,b.src="/placeholder.svg"}})})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mt-3",children:[(0,d.jsxs)(i.$,{size:"sm",variant:"outline",onClick:()=>{e.oR.dismiss(k),j?.()},className:"h-8 text-xs",children:[(0,d.jsx)(g.A,{className:"w-3 h-3 mr-1"}),"View Cart"]}),(0,d.jsx)(i.$,{size:"sm",variant:"ghost",onClick:()=>e.oR.dismiss(k),className:"h-8 text-xs text-gray-500 hover:text-gray-700",children:"Continue Shopping"})]})]}),(0,d.jsx)("button",{onClick:()=>e.oR.dismiss(k),className:"flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 transition-colors",children:(0,d.jsx)(h.A,{className:"w-4 h-4"})})]})}),{duration:5e3,position:"top-right"}),k=({productName:a,quantity:b})=>e.oR.success((0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(f.A,{className:"w-4 h-4 text-green-600"}),(0,d.jsxs)("span",{children:[b," \xd7 ",a," added to cart"]})]}),{duration:3e3})},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},8678:(a,b,c)=>{"use strict";c.d(b,{default:()=>$});var d=c(60687),e=c(43210),f=c.n(e),g=c(85814),h=c.n(g),i=c(51060),j=c(28559),k=c(48730),l=c(64398),m=c(28561),n=c(67760),o=c(62688);let p=(0,o.A)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]);var q=c(86561),r=c(24934),s=c(34993),t=c(99294),u=c(59821),v=c(52581),w=c(2445),x=c(93283),y=c(18868),z=c(79936),A=c(77080),B=c(55192),C=c(13964);let D=["General","Technical Specifications","Dimensions","Materials","Colors","Features","Warranty","Package Includes"];function E({attributes:a=[],className:b=""}){if(!a||0===a.length)return null;let c=Object.entries(a.reduce((a,b)=>{let c=b.DisplayName||b.AttributeName,d="Specifications",e=D.find(a=>c.toLowerCase().includes(a.toLowerCase()));return e?d=e:c.toLowerCase().includes("dimension")||c.toLowerCase().includes("size")||c.toLowerCase().includes("weight")?d="Dimensions":c.toLowerCase().includes("color")||c.toLowerCase().includes("colour")?d="Colors":c.toLowerCase().includes("material")||c.toLowerCase().includes("fabric")?d="Materials":(c.toLowerCase().includes("feature")||c.toLowerCase().includes("spec"))&&(d="Features"),a[d]||(a[d]=[]),a[d].push(b),a},{})).sort(([a],[b])=>{let c=D.indexOf(a),d=D.indexOf(b);return -1===c&&-1===d?a.localeCompare(b):-1===c?1:-1===d?-1:c-d});return(0,d.jsxs)("div",{className:b,children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Product Specifications"}),(0,d.jsx)("div",{className:"space-y-8",children:c.map(([a,b])=>(0,d.jsxs)(B.Zp,{className:"overflow-hidden",children:[(0,d.jsx)(B.aR,{className:"bg-gray-50 p-4 border-b",children:(0,d.jsx)(B.ZB,{className:"text-lg font-semibold",children:a})}),(0,d.jsx)(B.Wu,{className:"p-0",children:(0,d.jsx)("div",{className:"divide-y",children:b.map((b,c)=>{let e=b.DisplayName||b.AttributeName,g=b.AttributeValueText;return(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 p-4 hover:bg-gray-50 transition-colors",children:[(0,d.jsxs)("div",{className:"font-medium text-gray-900 flex items-center",children:[(0,d.jsx)(C.A,{className:"h-4 w-4 text-green-500 mr-2"}),e,":"]}),(0,d.jsx)("div",{className:"md:col-span-2 text-gray-700 mt-1 md:mt-0",children:"Colors"===a?(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:"w-6 h-6 rounded-full border border-gray-300",style:{backgroundColor:g.toLowerCase(),backgroundImage:["white","#fff","#ffffff","rgb(255,255,255)"].includes(g.toLowerCase())?"linear-gradient(45deg, #e5e7eb 25%, transparent 25%), linear-gradient(-45deg, #e5e7eb 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #e5e7eb 75%), linear-gradient(-45deg, transparent 75%, #e5e7eb 75%)":"none",backgroundSize:"8px 8px"}}),(0,d.jsx)("span",{className:"capitalize",children:g})]}):"Dimensions"===a&&(e.toLowerCase().includes("dimension")||e.toLowerCase().includes("size"))?(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[g.split("x").map((a,b)=>(0,d.jsxs)(f().Fragment,{children:[b>0&&(0,d.jsx)("span",{className:"mx-1 text-gray-400",children:"\xd7"}),(0,d.jsx)("span",{children:a.trim()})]},b)),!isNaN(parseFloat(g))&&(0,d.jsx)("span",{className:"ml-1 text-sm text-gray-500",children:"cm"})]}):(0,d.jsx)("span",{children:(a=>{if(!a)return"N/A";try{let b=new URL(a);return(0,d.jsxs)("a",{href:b.toString(),target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:["View ",b.hostname]})}catch{return a}})(g)})})]},`${b.ProductAttributeID}-${c}`)})})})]},a))})]})}var F=c(76180),G=c.n(F);let H=(0,o.A)("Video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]]),I=(0,o.A)("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),J=(0,o.A)("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);var K=c(99270);let L=(0,o.A)("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]),M=(0,o.A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),N=(0,o.A)("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);var O=c(47033),P=c(14952),Q=c(96241);function R({src:a,alt:b,className:c}){let[f,g]=(0,e.useState)(null),[h,i]=(0,e.useState)(!1),j=(0,e.useRef)(null),k=(0,e.useRef)(null);return h?(0,d.jsx)("div",{className:(0,Q.cn)("bg-gray-200 flex items-center justify-center",c),children:(0,d.jsx)(H,{size:16,className:"text-gray-500"})}):(0,d.jsxs)("div",{className:(0,Q.cn)("relative",c),children:[(0,d.jsx)("video",{ref:j,src:a,className:"hidden",muted:!0,playsInline:!0,preload:"metadata"}),(0,d.jsx)("canvas",{ref:k,className:"hidden"}),f?(0,d.jsx)("img",{src:f,alt:b||"Video thumbnail",className:"w-full h-full object-cover"}):(0,d.jsx)("div",{className:"w-full h-full bg-gray-200 flex items-center justify-center animate-pulse",children:(0,d.jsx)(H,{size:16,className:"text-gray-400"})})]})}function S({media:a,className:b}){let[c,f]=(0,e.useState)(0),[g,h]=(0,e.useState)(!1),[i,j]=(0,e.useState)(!1),[k,l]=(0,e.useState)(!1),[m,n]=(0,e.useState)({x:0,y:0}),[o,p]=(0,e.useState)(1.5),[q,r]=(0,e.useState)(!1),s=(0,e.useRef)(null),t=(0,e.useRef)(null),[u,v]=(0,e.useState)("all"),w=a.filter(a=>"all"===u||a.type===u),x=w[c]||a[0],y=w.length>1,z=()=>{f(a=>0===a?w.length-1:a-1),h(!1)},A=()=>{f(a=>a===w.length-1?0:a+1),h(!1)},B=a.reduce((a,b)=>(a[b.type]=(a[b.type]||0)+1,a),{});return(0,d.jsxs)("div",{className:(0,Q.cn)("flex flex-col gap-4",b),children:[(0,d.jsxs)("div",{className:"relative aspect-square w-full bg-gray-100 rounded-lg overflow-hidden",children:["image"===x.type?(0,d.jsxs)("div",{className:"jsx-9ac6182445f1a16e relative w-full h-full overflow-hidden group",children:[(0,d.jsx)(G(),{id:"9ac6182445f1a16e",children:'.cursor-zoom-in.jsx-9ac6182445f1a16e{cursor:url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/><line x1="11" y1="8" x2="11" y2="14"/><line x1="8" y1="11" x2="14" y2="11"/></svg>\')12 12,auto}.cursor-zoom-out.jsx-9ac6182445f1a16e{cursor:url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/><line x1="8" y1="11" x2="14" y2="11"/></svg>\')12 12,auto}'}),(0,d.jsx)("img",{ref:t,src:x.url,alt:x.alt||"Product image",style:k?{transform:`scale(${o})`,transformOrigin:`${m.x}% ${m.y}%`}:{transform:"scale(1)"},onClick:()=>{"image"===x.type&&l(!k)},onDoubleClick:()=>{"image"===x.type&&r(!0)},onMouseMove:a=>{if(!k||!t.current)return;let b=t.current.getBoundingClientRect();n({x:(a.clientX-b.left)/b.width*100,y:(a.clientY-b.top)/b.height*100})},onMouseLeave:()=>l(!1),className:"jsx-9ac6182445f1a16e "+((0,Q.cn)("w-full h-full object-contain transition-transform duration-300",k?"cursor-zoom-out":"cursor-zoom-in")||"")}),(0,d.jsx)("div",{className:"jsx-9ac6182445f1a16e absolute inset-0 flex items-center justify-center pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,d.jsx)("div",{className:"jsx-9ac6182445f1a16e bg-white/95 rounded-full p-4 shadow-xl border-2 border-gray-200",children:k?(0,d.jsx)(I,{className:"h-8 w-8 text-gray-700"}):(0,d.jsx)(J,{className:"h-8 w-8 text-gray-700"})})}),(0,d.jsxs)("div",{className:"jsx-9ac6182445f1a16e absolute top-2 left-2 flex flex-col gap-2",children:[(0,d.jsx)("div",{className:"jsx-9ac6182445f1a16e bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1",children:k?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(I,{className:"h-3 w-3"}),(0,d.jsxs)("span",{className:"jsx-9ac6182445f1a16e",children:["Zoom: ",Math.round(100*o),"%"]})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(J,{className:"h-3 w-3"}),(0,d.jsx)("span",{className:"jsx-9ac6182445f1a16e",children:"Click to zoom"})]})}),(0,d.jsxs)("div",{className:"jsx-9ac6182445f1a16e flex flex-col gap-1",children:[(0,d.jsx)("button",{onClick:()=>{p(a=>Math.min(a+.5,4)),l(!0)},title:"Zoom in",className:"jsx-9ac6182445f1a16e bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all",children:(0,d.jsx)(J,{className:"h-4 w-4"})}),(0,d.jsx)("button",{onClick:()=>{p(a=>{let b=Math.max(a-.5,1);return 1===b&&l(!1),b})},title:"Zoom out",className:"jsx-9ac6182445f1a16e bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all",children:(0,d.jsx)(I,{className:"h-4 w-4"})}),(0,d.jsx)("button",{onClick:()=>{p(1.5),l(!1)},title:"Reset zoom",className:"jsx-9ac6182445f1a16e bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all text-xs",children:"1:1"})]})]}),(0,d.jsx)("button",{onClick:()=>r(!0),title:"View fullscreen",className:"jsx-9ac6182445f1a16e absolute top-2 right-12 bg-white/90 hover:bg-white text-gray-700 rounded p-2 shadow-sm transition-all",children:(0,d.jsx)(K.A,{className:"h-4 w-4"})})]}):(0,d.jsxs)("div",{className:"relative w-full h-full",children:[(0,d.jsx)("video",{ref:s,src:x.url,className:"w-full h-full object-contain",controls:!1,onEnded:()=>{h(!1),y&&A()},onPlay:()=>h(!0),onPause:()=>h(!1),onLoadedData:()=>j(!0),playsInline:!0}),!i&&(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-200",children:(0,d.jsx)("div",{className:"animate-pulse",children:"Loading video..."})}),(0,d.jsx)("button",{onClick:()=>{"video"===x.type&&s.current&&(g?s.current.pause():s.current.play(),h(!g))},className:(0,Q.cn)("absolute inset-0 flex items-center justify-center transition-opacity",g?"opacity-0 hover:opacity-100":"opacity-80",!i&&"hidden"),"aria-label":g?"Pause":"Play",children:(0,d.jsx)("div",{className:"bg-black/50 text-white rounded-full p-3",children:g?(0,d.jsx)(L,{size:24}):(0,d.jsx)(M,{size:24})})})]}),(0,d.jsxs)("div",{className:"absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1",children:["image"===x.type?(0,d.jsx)(N,{size:12}):(0,d.jsx)(H,{size:12}),(0,d.jsx)("span",{children:"image"===x.type?"Image":"Video"})]}),y&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("button",{onClick:z,className:"absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all","aria-label":"Previous media",children:(0,d.jsx)(O.A,{size:20})}),(0,d.jsx)("button",{onClick:A,className:"absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all","aria-label":"Next media",children:(0,d.jsx)(P.A,{size:20})})]})]}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsxs)("button",{onClick:()=>v("all"),className:(0,Q.cn)("px-3 py-1 text-sm rounded-full border","all"===u?"bg-blue-600 text-white border-blue-600":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"),children:["All (",a.length,")"]}),B.image>0&&(0,d.jsxs)("button",{onClick:()=>v("image"),className:(0,Q.cn)("px-3 py-1 text-sm rounded-full border flex items-center gap-1","image"===u?"bg-blue-600 text-white border-blue-600":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"),children:[(0,d.jsx)(N,{size:14}),(0,d.jsx)("span",{children:B.image})]}),B.video>0&&(0,d.jsxs)("button",{onClick:()=>v("video"),className:(0,Q.cn)("px-3 py-1 text-sm rounded-full border flex items-center gap-1","video"===u?"bg-blue-600 text-white border-blue-600":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"),children:[(0,d.jsx)(H,{size:14}),(0,d.jsx)("span",{children:B.video})]})]}),y&&(0,d.jsx)("div",{className:"flex gap-2 sm:gap-3 overflow-x-auto pb-2 -mx-2 px-2",children:w.map((a,b)=>(0,d.jsx)("button",{onClick:()=>(a=>{let b=w[a];f(a),l(!1),b?.type==="video"?(h(!0),setTimeout(()=>{s.current&&s.current.play().catch(console.error)},100)):h(!1)})(b),className:(0,Q.cn)("relative flex-shrink-0 w-20 h-20 sm:w-16 sm:h-16 md:w-20 md:h-20 rounded-md overflow-hidden border-2 transition-all",b===c?"border-blue-600 ring-2 ring-blue-400":"border-gray-200 hover:border-gray-400"),"aria-label":`View ${a.type} ${b+1}`,children:"image"===a.type?(0,d.jsx)("img",{src:a.thumbnail||a.url,alt:a.alt||"",className:"w-full h-full object-cover"}):(0,d.jsxs)("div",{className:"relative w-full h-full overflow-hidden",children:[a.thumbnail?(0,d.jsx)("img",{src:a.thumbnail,alt:a.alt||"Video thumbnail",className:"w-full h-full object-cover"}):(0,d.jsx)(R,{src:a.url,alt:a.alt,className:"w-full h-full"}),(0,d.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black/30",children:(0,d.jsx)("div",{className:"bg-white/90 rounded-full p-1 sm:p-1",children:(0,d.jsx)(M,{size:14,className:"text-gray-700 ml-0.5 sm:w-3 sm:h-3"})})})]})},b))}),q&&"image"===x.type&&(0,d.jsx)("div",{className:"fixed inset-0 z-50 bg-black/95 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"relative w-full h-full flex items-center justify-center p-4",children:[(0,d.jsx)("img",{src:x.url,alt:x.alt||"Product image",className:"max-w-full max-h-full object-contain",style:{maxWidth:"95vw",maxHeight:"95vh"}}),(0,d.jsx)("button",{onClick:()=>r(!1),className:"absolute top-4 right-4 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all",title:"Close fullscreen",children:(0,d.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),y&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("button",{onClick:z,className:"absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all",title:"Previous image",children:(0,d.jsx)(O.A,{className:"h-6 w-6"})}),(0,d.jsx)("button",{onClick:A,className:"absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all",title:"Next image",children:(0,d.jsx)(P.A,{className:"h-6 w-6"})})]}),(0,d.jsxs)("div",{className:"absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/70 text-white px-3 py-1 rounded-full text-sm",children:[c+1," of ",w.length]})]})})]})}function T({endDate:a,className:b}){let[c,f]=(0,e.useState)({days:0,hours:0,minutes:0,seconds:0,completed:!1});return c.completed?(0,d.jsx)("span",{className:b,children:"Offer expired"}):(0,d.jsxs)("div",{className:`inline-flex items-center gap-0.5 sm:gap-2 bg-white rounded-lg p-1.5 sm:p-3 shadow-sm border ${b}`,children:[(0,d.jsxs)("div",{className:"flex flex-col items-center",children:[(0,d.jsx)("div",{className:"w-6 h-6 sm:w-12 sm:h-12 bg-white border border-red-400 sm:border-2 rounded sm:rounded-lg flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-[10px] sm:text-xl font-bold text-red-500",children:c.days.toString().padStart(2,"0")})}),(0,d.jsx)("span",{className:"text-[8px] sm:text-xs font-medium text-red-500 mt-0.5 sm:mt-1",children:"Days"})]}),(0,d.jsxs)("div",{className:"flex flex-col items-center",children:[(0,d.jsx)("div",{className:"w-6 h-6 sm:w-12 sm:h-12 bg-white border border-red-400 sm:border-2 rounded sm:rounded-lg flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-[10px] sm:text-xl font-bold text-red-500",children:c.hours.toString().padStart(2,"0")})}),(0,d.jsx)("span",{className:"text-[8px] sm:text-xs font-medium text-red-500 mt-0.5 sm:mt-1",children:"Hrs"})]}),(0,d.jsxs)("div",{className:"flex flex-col items-center",children:[(0,d.jsx)("div",{className:"w-6 h-6 sm:w-12 sm:h-12 bg-white border border-red-400 sm:border-2 rounded sm:rounded-lg flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-[10px] sm:text-xl font-bold text-red-500",children:c.minutes.toString().padStart(2,"0")})}),(0,d.jsx)("span",{className:"text-[8px] sm:text-xs font-medium text-red-500 mt-0.5 sm:mt-1",children:"Min"})]}),(0,d.jsxs)("div",{className:"flex flex-col items-center",children:[(0,d.jsx)("div",{className:"w-6 h-6 sm:w-12 sm:h-12 bg-white border border-red-400 sm:border-2 rounded sm:rounded-lg flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-[10px] sm:text-xl font-bold text-red-500",children:c.seconds.toString().padStart(2,"0")})}),(0,d.jsx)("span",{className:"text-[8px] sm:text-xs font-medium text-red-500 mt-0.5 sm:mt-1",children:"Sec"})]})]})}var U=c(71463);function V(){return(0,d.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row gap-8",children:[(0,d.jsxs)("div",{className:"md:w-1/2",children:[(0,d.jsx)(U.E,{className:"h-[400px] w-full rounded-lg"}),(0,d.jsx)("div",{className:"flex gap-2 mt-4",children:[void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsx)(U.E,{className:"h-20 w-20 rounded-lg"},b))})]}),(0,d.jsxs)("div",{className:"md:w-1/2 space-y-4",children:[(0,d.jsx)(U.E,{className:"h-8 w-3/4"}),(0,d.jsx)(U.E,{className:"h-6 w-1/2"}),(0,d.jsx)(U.E,{className:"h-4 w-full"}),(0,d.jsx)(U.E,{className:"h-4 w-full"}),(0,d.jsx)(U.E,{className:"h-4 w-3/4"}),(0,d.jsx)(U.E,{className:"h-10 w-1/3"}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(U.E,{className:"h-12 flex-1"}),(0,d.jsx)(U.E,{className:"h-12 w-12"}),(0,d.jsx)(U.E,{className:"h-12 w-12"})]})]})]})})}var W=c(93613);function X({error:a,retry:b}){return(0,d.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,d.jsxs)("div",{className:"flex flex-col items-center justify-center text-center p-8 border rounded-lg bg-red-50",children:[(0,d.jsx)(W.A,{className:"h-12 w-12 text-red-500 mb-4"}),(0,d.jsx)("h1",{className:"text-2xl font-bold mb-2",children:"Error Loading Product"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:a}),(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsx)(r.$,{onClick:b,children:"Try Again"}),(0,d.jsx)(h(),{href:"/products",children:(0,d.jsxs)(r.$,{variant:"outline",children:[(0,d.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"Back to Products"]})})]})]})})}var Y=c(42135);let Z=a=>{if(!a)return"/placeholder.svg?height=400&width=400";if(a.startsWith("http"))return a;let b=a.startsWith("/")?a:`/${a}`;return b=b.replace(/\/+/g,"/"),`https://admin.codemedicalapps.com${b}`},$=function({productId:a}){let b=(0,x._)(),f=(0,y.n)(),{rate:g}=(0,z.H)(),{primaryColor:o,primaryTextColor:B}=(0,A.t)(),[C,D]=(0,e.useState)(null),[F,G]=(0,e.useState)(!0),[H,I]=(0,e.useState)(1),[J,K]=(0,e.useState)(""),[L,M]=(0,e.useState)([]),[N,O]=(0,e.useState)(0),[P,Q]=(0,e.useState)(!1),[R,U]=(0,e.useState)(!1),[W,$]=(0,e.useState)(null),[_,aa]=(0,e.useState)("description"),[ab,ac]=(0,e.useState)(!1),[ad,ae]=(0,e.useState)(null),[af,ag]=(0,e.useState)(()=>{let a={};return C?.AttributesJson&&C.AttributesJson.forEach(b=>{a[`${b.ProductAttributeID}_${b.AttributeValueID}`]=!0}),a}),ah=async()=>{G(!0),$(null);try{let b,d={requestParameters:{ProductId:Number.parseInt(a,10),recordValueJson:"[]"}};console.log("Fetching product with ID:",a,"Request body:",d);try{let{MakeApiCallAsync:a}=await Promise.resolve().then(c.bind(c,40529));b=await a("get-product_detail",null,d,{Accept:"application/json","Content-Type":"application/json"},"POST",!0),console.log("API helper response:",b.data)}catch(a){console.log("API helper failed, trying proxy route:",a),b=await i.A.post("/api/product-detail",d,{headers:{Accept:"application/json","Content-Type":"application/json"}}),console.log("Proxy API response:",b.data)}if(b.data){let c=(b.data.data,b.data);if(c&&c.data)try{let b=JSON.parse(c.data);if(console.log("Parsed product data:",b),b){let c=Array.isArray(b)?b[0]:b;if(c){if(c.AttributesJson&&"string"==typeof c.AttributesJson)try{c.AttributesJson=JSON.parse(c.AttributesJson)}catch(a){console.error("Error parsing AttributesJson:",a),c.AttributesJson=[]}else c.AttributesJson||(c.AttributesJson=[]);if(console.log("Product data with attributes:",c),console.log("CategoryName in product data:",c.CategoryName),console.log("CategoryID in product data:",c.CategoryID),D(c),c.ProductImagesJson&&c.ProductImagesJson.length>0){let a=c.ProductImagesJson.find(a=>a.IsPrimary)||c.ProductImagesJson[0];K(Z(a.AttachmentURL))}if(c.VideoLink){console.log("Video links found:",c.VideoLink);let a=c.VideoLink.split(",").map(a=>a.trim()).map(a=>ai(a));M(a),O(0)}c.OrderMinimumQuantity>0&&I(c.OrderMinimumQuantity)}else console.error("No product data found in parsed response"),$(`Product with ID ${a} not found. Please check if this product exists.`)}else console.error("Invalid product data format - parsedData is null/undefined"),$("Invalid product data format")}catch(a){console.error("Error parsing product data:",a,"Raw data:",c.data),$("Error parsing product data")}else console.error("No data property in API response:",b.data),$("No data in API response")}else console.error("Empty response from API"),$("Empty response from server")}catch(a){console.error("Error fetching product:",a),a.response?(console.error("Server error:",a.response.status,a.response.data),$(`Server error: ${a.response.status} - ${a.response.data?.message||"Unknown error"}`)):a.request?(console.error("Network error:",a.request),$("Network error - please check your connection")):(console.error("Request setup error:",a.message),$(`Error: ${a.message}`))}finally{G(!1)}},ai=a=>{if(!a)return"";if(a.includes("youtube.com")||a.includes("youtu.be"))return a;if(a.startsWith("http"))return`/api/video-proxy?url=${encodeURIComponent(a)}`;let b=a.startsWith("/")?a:`/${a}`;return`/api/video-proxy?url=${encodeURIComponent(`https://admin.codemedicalapps.com${b}`)}`},aj=(0,e.useMemo)(()=>C?.AttributesJson?C.AttributesJson.reduce((a,b)=>{let c=b.ProductAttributeID;return a[c]||(a[c]=[]),a[c].push(b),a},{}):{},[C?.AttributesJson]),ak=(0,e.useCallback)(()=>{if(!C)return 0;let a=C.Price;return C.AttributesJson&&C.AttributesJson.length>0&&C.AttributesJson.forEach(b=>{if(af[`${b.ProductAttributeID}_${b.AttributeValueID}`]&&"number"==typeof b.PriceAdjustment&&"number"==typeof b.PriceAdjustmentType)switch(b.PriceAdjustmentType){case 1:a+=b.PriceAdjustment;break;case 2:a+=C.Price*b.PriceAdjustment/100}}),Math.max(0,a)},[C,af]);(0,e.useMemo)(()=>ak(),[ak]);let al=(0,e.useMemo)(()=>{let a=[];return C?.ProductImagesJson?.length&&C.ProductImagesJson.forEach(b=>{a.push({type:"image",url:Z(b.AttachmentURL),alt:C?.ProductName||"Product image",thumbnail:Z(b.AttachmentURL)})}),L.forEach((b,c)=>{a.push({type:"video",url:b,alt:`${C?.ProductName||"Product"} - Video ${c+1}`})}),a},[C,L,J]),am=a=>{ae(a),ac(!0),setTimeout(()=>ac(!1),300)},an=a=>{let b="flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 rounded-full transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-offset-1",c="bg-gray-100 text-gray-400 cursor-not-allowed";if("increment"===a){let a=C&&H>=(C.OrderMaximumQuantity>0?Math.min(C.OrderMaximumQuantity,C.StockQuantity):C.StockQuantity);return`${b} ${a?c:"bg-primary text-white hover:bg-primary/90 focus:ring-primary/50"}`}{let a=C&&H<=(C.OrderMinimumQuantity>0?C.OrderMinimumQuantity:1);return`${b} ${a?c:"bg-primary text-white hover:bg-primary/90 focus:ring-primary/50"}`}};return F?(0,d.jsx)(V,{}):W?(0,d.jsx)(X,{error:W,retry:ah}):C?(0,d.jsxs)("div",{className:"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden",children:[(0,d.jsx)(s.Qp,{className:"mb-6",children:(0,d.jsxs)(s.AB,{children:[(0,d.jsx)(s.J5,{children:(0,d.jsx)(s.w1,{asChild:!0,children:(0,d.jsx)(h(),{href:"/",children:"Home"})})}),(0,d.jsx)(s.tH,{}),(0,d.jsx)(s.J5,{children:(0,d.jsx)(s.w1,{asChild:!0,children:(0,d.jsx)(h(),{href:`/products?category=${C.CategoryID||"all"}`,children:C.CategoryName||"Medical Products"})})}),(0,d.jsx)(s.tH,{}),(0,d.jsx)(s.J5,{children:(0,d.jsx)(s.w1,{asChild:!0,children:(0,d.jsx)(h(),{href:"/products",children:"Products"})})}),(0,d.jsx)(s.tH,{}),(0,d.jsx)(s.J5,{children:(0,d.jsx)(s.tJ,{children:C.ProductName})})]})}),(0,d.jsx)("div",{className:"lg:hidden mb-6",children:(()=>{if(!C.SellStartDatetimeUTC||!C.SellEndDatetimeUTC)return null;let a=new Date,b=new Date(C.SellStartDatetimeUTC),c=new Date(C.SellEndDatetimeUTC),e=a<b;if(a>c)return null;let f=e?C.SellStartDatetimeUTC:C.SellEndDatetimeUTC,g="\uD83D\uDD25 Limited Time Sale!",h="Sale ends soon - grab yours now!";return e?(g="⏰ Sale Starts Soon!",h="Get ready for an amazing deal!"):a>=b&&a<=c&&(g="\uD83D\uDD25 Limited Time Sale!",h="Sale ends soon - don't miss out!"),(0,d.jsxs)("div",{className:"p-3 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-xl",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,d.jsx)(k.A,{className:"h-4 w-4 text-red-500 animate-pulse"}),(0,d.jsx)("span",{className:"text-sm font-bold text-red-600",children:g})]}),(0,d.jsx)("div",{className:"flex justify-center mb-3",children:(0,d.jsx)(T,{endDate:f,className:"transform hover:scale-105 transition-transform duration-200"})}),(0,d.jsx)("p",{className:"text-center text-xs font-medium text-gray-700",children:h})]})})()}),(0,d.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,d.jsx)("div",{className:"lg:w-1/2",children:(0,d.jsx)(S,{media:al,className:"w-full rounded-lg overflow-hidden"})}),(0,d.jsxs)("div",{className:"md:w-1/2",children:[(0,d.jsx)("h1",{className:"text-xl font-bold mb-2",children:C.ProductName}),(0,d.jsxs)("div",{className:"flex items-center mb-4",children:[(0,d.jsx)("div",{className:"flex",children:[void 0,void 0,void 0,void 0,void 0].map((a,b)=>(0,d.jsx)(l.A,{className:`w-4 h-4 ${b<Math.floor(C.Rating||0)?"text-yellow-400 fill-yellow-400":"text-gray-300"}`},b))}),(0,d.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["(",C.Rating||0,") ",C.TotalReviews||0," reviews"]})]}),(()=>{if(!C)return null;let a=C.DiscountPrice&&C.DiscountPrice<C.Price,b=ak(),c=b!==C.Price&&b!==(C.DiscountPrice||C.Price);return(0,d.jsxs)("div",{className:"mb-4",children:[(0,d.jsxs)("div",{className:"flex items-baseline gap-2",children:[(0,d.jsxs)("span",{className:"text-3xl font-bold text-primary",children:["$",a?(C.DiscountPrice||0).toFixed(2):b.toFixed(2)]}),a&&(0,d.jsxs)("span",{className:"text-lg text-gray-400 line-through",children:["$",C.Price.toFixed(2)]}),c&&!a&&(0,d.jsxs)("span",{className:"text-lg text-gray-400 line-through",children:["$",C.Price.toFixed(2)]}),a&&(0,d.jsxs)("span",{className:"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded",children:[Math.round((C.Price-(C.DiscountPrice||0))/C.Price*100),"% OFF"]})]}),C.PriceIQD&&(0,d.jsxs)("div",{className:"mt-1 text-lg font-medium text-gray-600",children:[C.PriceIQD.toLocaleString()," IQD"]}),C.PointNo&&C.PointNo>0&&(0,d.jsx)("div",{className:"mt-2",children:(0,d.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800",children:["Buy & Earn ",C.PointNo," $ credit"]})}),C.OldPrice&&C.OldPrice>C.Price&&(0,d.jsxs)("div",{className:"mt-1 text-sm text-gray-500",children:[(0,d.jsxs)("span",{className:"line-through",children:["$",C.OldPrice.toFixed(2)]}),(0,d.jsxs)("span",{className:"ml-2 text-green-600",children:[Math.round((C.OldPrice-(C.DiscountPrice||C.Price))/C.OldPrice*100),"% OFF"]})]})]})})(),(0,d.jsx)("div",{className:"hidden lg:block",children:(()=>{if(!C.SellStartDatetimeUTC||!C.SellEndDatetimeUTC)return null;let a=new Date,b=new Date(C.SellStartDatetimeUTC),c=new Date(C.SellEndDatetimeUTC),e=a<b;if(a>c)return null;let f=e?C.SellStartDatetimeUTC:C.SellEndDatetimeUTC,g="\uD83D\uDD25 Limited Time Sale!",h="Sale ends soon - grab yours now!";return e?(g="⏰ Sale Starts Soon!",h="Get ready for an amazing deal!"):a>=b&&a<=c&&(g="\uD83D\uDD25 Limited Time Sale!",h="Sale ends soon - don't miss out!"),(0,d.jsxs)("div",{className:"mb-6 p-4 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-xl",children:[(0,d.jsxs)("div",{className:"flex items-center justify-center gap-3 mb-4",children:[(0,d.jsx)(k.A,{className:"h-6 w-6 text-red-500 animate-pulse"}),(0,d.jsx)("span",{className:"text-lg font-bold text-red-600",children:g})]}),(0,d.jsx)("div",{className:"flex justify-center mb-4",children:(0,d.jsx)(T,{endDate:f,className:"transform hover:scale-105 transition-transform duration-200"})}),(0,d.jsx)("p",{className:"text-center text-sm font-medium text-gray-700",children:h})]})})()}),C.ShortDescription&&(0,d.jsx)("div",{className:"prose prose-sm max-w-none mb-6",children:(0,d.jsx)("p",{children:C.ShortDescription})}),(0,d.jsxs)("div",{className:"mb-6 border-t border-gray-200 pt-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Product Details"}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Choose your preferences from the options below."}),Object.entries(aj).length>0?(0,d.jsx)("div",{className:"space-y-6",children:Object.entries(aj).map(([a,b])=>(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("h4",{className:"text-sm font-medium text-gray-700",children:[b[0]?.DisplayName||b[0]?.AttributeName,":"]}),(0,d.jsx)("div",{className:"space-y-2 pl-4",children:b.map(c=>{let e=`${c.ProductAttributeID}_${c.AttributeValueID}`,f=!!af[e],g=b.length>1;return(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("div",{className:"flex items-center h-5",children:(0,d.jsx)("input",{type:g?"radio":"checkbox",id:`attr-${e}`,name:`attr-group-${a}`,className:`h-4 w-4 ${g?"rounded-full":"rounded"} border-gray-300 text-primary focus:ring-primary`,checked:f,onChange:a=>{var b;return b=a.target.checked,void ag(a=>{let d={...a},e=`${c.ProductAttributeID}_${c.AttributeValueID}`;return g&&b&&Object.keys(a).forEach(a=>{a.startsWith(`${c.ProductAttributeID}_`)&&a!==e&&(d[a]=!1)}),d[e]=!!g||!a[e],d})}})}),(0,d.jsx)("div",{className:"ml-3 text-sm",children:(0,d.jsxs)("label",{htmlFor:`attr-${e}`,className:`font-medium ${f?"text-primary":"text-gray-700"}`,children:[c.AttributeValueText,(c.PriceAdjustment||0===c.PriceAdjustment)&&(0,d.jsxs)("span",{className:"ml-2 text-sm font-normal text-green-600",children:["(",1===c.PriceAdjustmentType?"+":"","$",c.PriceAdjustment," ",2===c.PriceAdjustmentType?"%":"",")"]})]})})]},e)})})]},`attr-group-${a}`))}):(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"No additional product details available."})]}),(0,d.jsx)("div",{className:"mb-6",children:(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-3",children:[(0,d.jsx)("span",{className:"text-sm font-medium",children:"Quantity:"}),(0,d.jsxs)("div",{className:"flex items-center justify-between sm:justify-start",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("button",{onClick:()=>{if(C){let a=C.OrderMinimumQuantity>0?C.OrderMinimumQuantity:1;H>a?(I(a=>a-1),am("decrement")):v.oR.info(`Minimum quantity is ${a}`)}},className:an("decrement"),disabled:H<=(C.OrderMinimumQuantity||1),"aria-label":"Decrease quantity",children:(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 sm:h-5 sm:w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"})})}),(0,d.jsx)(()=>(0,d.jsxs)("div",{className:"relative flex items-center justify-center w-10 sm:w-12",children:[(0,d.jsx)("span",{className:`text-sm sm:text-base font-medium transition-all duration-200 ${ab?"scale-125 text-primary":"scale-100"}`,children:H}),ab&&(0,d.jsx)("span",{className:`absolute text-xs font-bold text-primary transition-all duration-200 ${"increment"===ad?"-top-4 sm:-top-5":"top-4 sm:top-5"}`,children:"increment"===ad?"+1":"-1"})]}),{}),(0,d.jsx)("button",{onClick:()=>{if(C){let a=C.OrderMaximumQuantity>0?Math.min(C.OrderMaximumQuantity,C.StockQuantity):C.StockQuantity;H<a?(I(a=>a+1),am("increment")):v.oR.info(`Maximum quantity of ${a} reached`)}},className:an("increment"),disabled:C.OrderMaximumQuantity>0?H>=Math.min(C.OrderMaximumQuantity,C.StockQuantity):H>=C.StockQuantity,"aria-label":"Increase quantity",children:(0,d.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 sm:h-5 sm:w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})})})]}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:ml-4",children:[C.OrderMinimumQuantity>1&&(0,d.jsxs)("span",{className:"text-xs text-gray-500",children:["Min: ",C.OrderMinimumQuantity]}),C.OrderMaximumQuantity>0&&(0,d.jsxs)("span",{className:"text-xs text-gray-500",children:["Max:"," ",Math.min(C.OrderMaximumQuantity,C.StockQuantity)]})]})]})]})}),(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-4",children:[(0,d.jsxs)("button",{type:"button",className:"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none text-sm sm:text-base",disabled:C.StockQuantity<=0||P,onClick:()=>{if(C){Q(!0);try{let a=C.ProductImagesJson&&C.ProductImagesJson.length>0?Z(C.ProductImagesJson[0].AttachmentURL):"/placeholder.jpg",c=(C.AttributesJson||[]).filter(a=>af[`${a.ProductAttributeID}_${a.AttributeValueID}`]);b.addToCart({id:C.ProductId,name:C.ProductName,price:C.DiscountPrice||C.Price,discountPrice:C.DiscountPrice,image:a,originalPrice:C.Price},H,c,C.PriceIQD,g),(0,w.k)({productName:C.ProductName,quantity:H,productImage:C.ProductImagesJson?.[0]?.AttachmentURL||"/placeholder.svg",onViewCart:()=>{window.location.href="/cart"}})}catch(a){console.error("Error adding to cart:",a),v.oR.error("Failed to add product to cart. Please try again.")}finally{Q(!1)}}},children:[P?(0,d.jsx)("div",{className:"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin"}):(0,d.jsx)(m.A,{className:"h-5 w-5"}),(0,d.jsx)("span",{children:P?"Adding...":"Add to Cart"})]}),(0,d.jsxs)("button",{type:"button",className:"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none text-sm sm:text-base sm:flex-initial sm:min-w-[120px]",disabled:R,onClick:()=>{if(C){U(!0);try{if(f.isInWishlist(C.ProductId))f.removeFromWishlist(C.ProductId),v.oR.success(`${C.ProductName} removed from wishlist`);else{let a=`/product/${C.ProductId}`,b=C.ProductImagesJson?.[0]?.AttachmentURL||"/placeholder.svg",c=C.DiscountPrice||C.Price;f.addToWishlist(C.ProductId,C.ProductName,a,b,c),v.oR.success(`${C.ProductName} added to wishlist`)}}catch(a){console.error("Error updating wishlist:",a),v.oR.error("Failed to update wishlist. Please try again.")}finally{U(!1)}}},children:[R?(0,d.jsx)("div",{className:"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin"}):(0,d.jsx)(n.A,{className:"h-5 w-5",fill:C&&f.isInWishlist(C.ProductId)?"currentColor":"none"}),(0,d.jsx)("span",{className:"sr-only md:not-sr-only md:inline",children:R?"Updating...":C&&f.isInWishlist(C.ProductId)?"Remove":"Wishlist"})]}),(0,d.jsxs)("button",{type:"button",className:"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground text-sm sm:text-base sm:flex-initial sm:min-w-[100px]",onClick:()=>{navigator.share?navigator.share({title:C?.MetaTitle||C?.ProductName,text:C?.MetaDescription||`Check out this product: ${C?.ProductName}`,url:window.location.href}).catch(a=>console.error("Error sharing:",a)):(navigator.clipboard.writeText(window.location.href),v.oR.success("Product link copied to clipboard"))},children:[(0,d.jsx)(p,{className:"h-5 w-5"}),(0,d.jsx)("span",{className:"sr-only md:not-sr-only md:inline",children:"Share"})]})]}),C.MetaKeywords&&(0,d.jsxs)("div",{className:"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200",children:[(0,d.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"Product Tags"}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:C.MetaKeywords.split(",").map((a,b)=>(0,d.jsx)(u.E,{variant:"secondary",className:"text-xs bg-white/70 hover:bg-white transition-colors",children:a.trim()},b))})]}),C.MetaDescription&&(0,d.jsxs)("div",{className:"mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200",children:[(0,d.jsxs)("h3",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,d.jsx)(q.A,{className:"h-5 w-5 text-green-600 mr-2"}),"About This Product"]}),(0,d.jsx)("p",{className:"text-gray-700 leading-relaxed",children:C.MetaDescription})]})]})]}),(0,d.jsx)("div",{className:"mt-12",children:(0,d.jsxs)(t.tU,{defaultValue:"description",className:"w-full",value:_,onValueChange:aa,children:[(0,d.jsxs)(t.j7,{className:"grid w-full grid-cols-3 mb-6 gap-2 bg-transparent p-0 h-auto",children:[(0,d.jsx)(t.Xi,{value:"description",className:"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"description"===_?o:"rgb(209 213 219)",color:"description"===_?B:"rgb(55 65 81)",transform:"description"===_?"scale(1.05)":"scale(1)",boxShadow:"description"===_?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"description"===_?o:"transparent"},children:"Overview"}),(0,d.jsx)(t.Xi,{value:"reviews",className:"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"reviews"===_?o:"rgb(209 213 219)",color:"reviews"===_?B:"rgb(55 65 81)",transform:"reviews"===_?"scale(1.05)":"scale(1)",boxShadow:"reviews"===_?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"reviews"===_?o:"transparent"},children:"Specifications"}),(0,d.jsx)(t.Xi,{value:"shipping",className:"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"shipping"===_?o:"rgb(209 213 219)",color:"shipping"===_?B:"rgb(55 65 81)",transform:"shipping"===_?"scale(1.05)":"scale(1)",boxShadow:"shipping"===_?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"shipping"===_?o:"transparent"},children:"Reviews"})]}),(0,d.jsx)(t.av,{value:"description",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,d.jsxs)("div",{className:"p-8",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Product Overview"}),C.FullDescription?(0,d.jsx)("div",{className:"prose max-w-none",dangerouslySetInnerHTML:{__html:C.FullDescription}}):(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("p",{className:"text-gray-700 leading-relaxed",children:C.ShortDescription||"This is a high-quality medical product designed to meet professional standards."}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-6",children:[(0,d.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Key Features"}),(0,d.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1",children:[(0,d.jsx)("li",{children:"• Professional grade quality"}),(0,d.jsx)("li",{children:"• Durable construction"}),(0,d.jsx)("li",{children:"• Easy to use"}),(0,d.jsx)("li",{children:"• Reliable performance"})]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Benefits"}),(0,d.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1",children:[(0,d.jsx)("li",{children:"• Enhanced efficiency"}),(0,d.jsx)("li",{children:"• Cost-effective solution"}),(0,d.jsx)("li",{children:"• Long-lasting durability"}),(0,d.jsx)("li",{children:"• Professional results"})]})]})]})]})]})}),(0,d.jsx)(t.av,{value:"reviews",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,d.jsxs)("div",{className:"p-8",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Product Specifications"}),(0,d.jsx)(E,{attributes:C.AttributesJson||[]})]})}),(0,d.jsx)(t.av,{value:"shipping",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,d.jsxs)("div",{className:"p-8",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Customer Reviews"}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4",children:[(0,d.jsx)("div",{className:"flex items-center",children:[1,2,3,4,5].map(a=>(0,d.jsx)(l.A,{className:`w-6 h-6 ${a<=Math.floor(C.Rating||0)?"text-yellow-400 fill-yellow-400":"text-gray-300"}`},a))}),(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,d.jsx)("span",{className:"font-medium",children:C.Rating?.toFixed(1)||"0.0"})," ","out of 5",C.TotalReviews?(0,d.jsxs)("span",{children:[" ","• ",C.TotalReviews," review",1!==C.TotalReviews?"s":""]}):null]})]}),(0,d.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg",children:(0,d.jsx)(Y.A,{productId:C.ProductId,showTitle:!1})})]})]})})]})})]}):(0,d.jsxs)("div",{className:"container mx-auto px-4 py-8 text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Product Not Found"}),(0,d.jsxs)("p",{className:"mb-6",children:['The product with ID "',a,'" could not be found. It may not exist in the database.']}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(h(),{href:"/products",children:(0,d.jsxs)(r.$,{children:[(0,d.jsx)(j.A,{className:"mr-2 h-4 w-4"}),"View All Products"]})}),(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Check the products list to find available product IDs"})]})]})}},10119:(a,b,c)=>{Promise.resolve().then(c.bind(c,98138))},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},13964:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14952:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28559:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42135:(a,b,c)=>{"use strict";c.d(b,{A:()=>m});var d=c(60687),e=c(43210),f=c(55192),g=c(24934),h=c(71463),i=c(64398),j=c(33872),k=c(58869);let l=(0,c(62688).A)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]);function m({productId:a,showTitle:b=!0}){let[c,m]=(0,e.useState)([]),[n,o]=(0,e.useState)(!0),[p,q]=(0,e.useState)(null),[r,s]=(0,e.useState)(!1),t=a=>(0,d.jsx)("div",{className:"flex items-center gap-1",children:[1,2,3,4,5].map(b=>(0,d.jsx)(i.A,{className:`h-4 w-4 ${b<=a?"fill-yellow-400 text-yellow-400":"text-gray-300"}`},b))}),u=r?c:c.slice(0,3),v=c.length>0?c.reduce((a,b)=>a+b.Rating,0)/c.length:0;return n?(0,d.jsxs)("div",{className:"space-y-4",children:[b&&(0,d.jsx)(h.E,{className:"h-6 w-48"}),[1,2,3].map(a=>(0,d.jsx)(f.Zp,{className:"p-4",children:(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(h.E,{className:"h-4 w-20"}),(0,d.jsx)(h.E,{className:"h-4 w-24"})]}),(0,d.jsx)(h.E,{className:"h-4 w-full"}),(0,d.jsx)(h.E,{className:"h-16 w-full"})]})},a))]}):p?(0,d.jsxs)(f.Zp,{className:"p-6 text-center",children:[(0,d.jsx)(j.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,d.jsx)("p",{className:"text-gray-500",children:p})]}):0===c.length?(0,d.jsxs)(f.Zp,{className:"p-6 text-center",children:[(0,d.jsx)(j.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,d.jsx)("h3",{className:"font-medium mb-1",children:"No Reviews Yet"}),(0,d.jsx)("p",{className:"text-gray-500 text-sm",children:"Be the first to review this product!"})]}):(0,d.jsxs)("div",{className:"space-y-4",children:[b&&(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold",children:"Customer Reviews"}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[t(Math.round(v)),(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:[v.toFixed(1)," (",c.length," review",1!==c.length?"s":"",")"]})]})]}),(0,d.jsx)("div",{className:"space-y-4",children:u.map(a=>(0,d.jsx)(f.Zp,{className:"p-4",children:(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)("div",{className:"flex items-start justify-between",children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:(0,d.jsx)(k.A,{className:"h-4 w-4 text-primary"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"font-medium text-sm",children:a.ReviewerName}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[t(a.Rating),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:(a=>{if(!a)return"N/A";try{return new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}catch{return a}})(a.ReviewDate||a.CreatedOn||"")})]})]})]})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium mb-1",children:a.Title}),(0,d.jsx)("p",{className:"text-gray-700 text-sm leading-relaxed",children:a.Body})]}),a.HelpfulCount&&a.HelpfulCount>0&&(0,d.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[(0,d.jsx)(l,{className:"h-3 w-3"}),(0,d.jsxs)("span",{children:[a.HelpfulCount," people found this helpful"]})]})]})},a.ReviewID))}),c.length>3&&(0,d.jsx)("div",{className:"text-center",children:(0,d.jsx)(g.$,{variant:"outline",onClick:()=>s(!r),children:r?"Show Less":`Show All ${c.length} Reviews`})})]})}},47033:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},48730:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},59821:(a,b,c)=>{"use strict";c.d(b,{E:()=>h});var d=c(60687),e=c(24224),f=c(96241);let g=(0,e.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}},62764:(a,b,c)=>{Promise.resolve().then(c.bind(c,8678))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},64531:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>i,generateMetadata:()=>h});var d=c(37413),e=c(94612),f=c(98138);async function g(a){try{let b=JSON.stringify({requestParameters:{ProductId:Number.parseInt(a,10),recordValueJson:"[]"}}),c=await e.A.request({method:"post",maxBodyLength:1/0,url:"https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail",headers:{Accept:"application/json","Content-Type":"application/json"},data:b});if(c.data&&c.data.data){let a=JSON.parse(c.data.data);return(Array.isArray(a)?a[0]:a)||null}return null}catch(a){return console.error("Error fetching product for metadata:",a),null}}async function h({params:a}){let{id:b}=await a,c=await g(b);if(!c)return{title:"Product Not Found - Medical Equipment",description:"The requested product could not be found."};let d=c.MetaTitle||`${c.ProductName} - Medical Equipment`,e=c.MetaDescription||c.ShortDescription||`Buy ${c.ProductName} at the best price. High-quality medical equipment with fast delivery.`,f=c.MetaKeywords||`${c.ProductName}, medical equipment, healthcare, ${c.CategoryName||"medical supplies"}`,h=c.ProductImagesJson&&c.ProductImagesJson.length>0?(a=>{if(!a)return"/placeholder.svg?height=400&width=400";if(a.startsWith("http"))return a;let b=a.startsWith("/")?a:`/${a}`;return b=b.replace(/\/+/g,"/"),`https://admin.codemedicalapps.com${b}`})(c.ProductImagesJson[0].AttachmentURL):"/placeholder.svg?height=400&width=400";return{title:d,description:e,keywords:f,openGraph:{title:d,description:e,type:"website",images:[{url:h,width:400,height:400,alt:c.ProductName}]},twitter:{card:"summary_large_image",title:d,description:e,images:[h]},other:{"product:price:amount":(c.DiscountPrice||c.Price).toString(),"product:price:currency":"USD","product:availability":c.StockQuantity>0?"in stock":"out of stock","product:condition":"new"}}}async function i({params:a}){let{id:b}=await a;return(0,d.jsx)(f.default,{productId:b})}},66829:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["product",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,64531)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\product\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\product\\[id]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/product/[id]/page",pathname:"/product/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/product/[id]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},71463:(a,b,c)=>{"use strict";c.d(b,{E:()=>f});var d=c(60687),e=c(96241);function f({className:a,...b}){return(0,d.jsx)("div",{className:(0,e.cn)("animate-pulse rounded-md bg-muted",a),...b})}},74075:a=>{"use strict";a.exports=require("zlib")},75913:(a,b,c)=>{"use strict";c(78778);var d=c(43210),e=function(a){return a&&"object"==typeof a&&"default"in a?a:{default:a}}(d),f="undefined"!=typeof process&&process.env&&!0,g=function(a){return"[object String]"===Object.prototype.toString.call(a)},h=function(){function a(a){var b=void 0===a?{}:a,c=b.name,d=void 0===c?"stylesheet":c,e=b.optimizeForSpeed,h=void 0===e?f:e;i(g(d),"`name` must be a string"),this._name=d,this._deletedRulePlaceholder="#"+d+"-deleted-rule____{}",i("boolean"==typeof h,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=h,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var b,c=a.prototype;return c.setOptimizeForSpeed=function(a){i("boolean"==typeof a,"`setOptimizeForSpeed` accepts a boolean"),i(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=a,this.inject()},c.isOptimizeForSpeed=function(){return this._optimizeForSpeed},c.inject=function(){var a=this;i(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(b,c){return"number"==typeof c?a._serverSheet.cssRules[c]={cssText:b}:a._serverSheet.cssRules.push({cssText:b}),c},deleteRule:function(b){a._serverSheet.cssRules[b]=null}}},c.getSheetForTag=function(a){if(a.sheet)return a.sheet;for(var b=0;b<document.styleSheets.length;b++)if(document.styleSheets[b].ownerNode===a)return document.styleSheets[b]},c.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},c.insertRule=function(a,b){return i(g(a),"`insertRule` accepts only strings"),"number"!=typeof b&&(b=this._serverSheet.cssRules.length),this._serverSheet.insertRule(a,b),this._rulesCount++},c.replaceRule=function(a,b){this._optimizeForSpeed;var c=this._serverSheet;if(b.trim()||(b=this._deletedRulePlaceholder),!c.cssRules[a])return a;c.deleteRule(a);try{c.insertRule(b,a)}catch(d){f||console.warn("StyleSheet: illegal rule: \n\n"+b+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),c.insertRule(this._deletedRulePlaceholder,a)}return a},c.deleteRule=function(a){this._serverSheet.deleteRule(a)},c.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},c.cssRules=function(){return this._serverSheet.cssRules},c.makeStyleTag=function(a,b,c){b&&i(g(b),"makeStyleTag accepts only strings as second parameter");var d=document.createElement("style");this._nonce&&d.setAttribute("nonce",this._nonce),d.type="text/css",d.setAttribute("data-"+a,""),b&&d.appendChild(document.createTextNode(b));var e=document.head||document.getElementsByTagName("head")[0];return c?e.insertBefore(d,c):e.appendChild(d),d},b=[{key:"length",get:function(){return this._rulesCount}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}}(a.prototype,b),a}();function i(a,b){if(!a)throw Error("StyleSheet: "+b+".")}var j=function(a){for(var b=5381,c=a.length;c;)b=33*b^a.charCodeAt(--c);return b>>>0},k={};function l(a,b){if(!b)return"jsx-"+a;var c=String(b),d=a+c;return k[d]||(k[d]="jsx-"+j(a+"-"+c)),k[d]}function m(a,b){var c=a+(b=b.replace(/\/style/gi,"\\/style"));return k[c]||(k[c]=b.replace(/__jsx-style-dynamic-selector/g,a)),k[c]}var n=function(){function a(a){var b=void 0===a?{}:a,c=b.styleSheet,d=void 0===c?null:c,e=b.optimizeForSpeed,f=void 0!==e&&e;this._sheet=d||new h({name:"styled-jsx",optimizeForSpeed:f}),this._sheet.inject(),d&&"boolean"==typeof f&&(this._sheet.setOptimizeForSpeed(f),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var b=a.prototype;return b.add=function(a){var b=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(a.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var c=this.getIdAndRules(a),d=c.styleId,e=c.rules;if(d in this._instancesCounts){this._instancesCounts[d]+=1;return}var f=e.map(function(a){return b._sheet.insertRule(a)}).filter(function(a){return -1!==a});this._indices[d]=f,this._instancesCounts[d]=1},b.remove=function(a){var b=this,c=this.getIdAndRules(a).styleId;if(function(a,b){if(!a)throw Error("StyleSheetRegistry: "+b+".")}(c in this._instancesCounts,"styleId: `"+c+"` not found"),this._instancesCounts[c]-=1,this._instancesCounts[c]<1){var d=this._fromServer&&this._fromServer[c];d?(d.parentNode.removeChild(d),delete this._fromServer[c]):(this._indices[c].forEach(function(a){return b._sheet.deleteRule(a)}),delete this._indices[c]),delete this._instancesCounts[c]}},b.update=function(a,b){this.add(b),this.remove(a)},b.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},b.cssRules=function(){var a=this,b=this._fromServer?Object.keys(this._fromServer).map(function(b){return[b,a._fromServer[b]]}):[],c=this._sheet.cssRules();return b.concat(Object.keys(this._indices).map(function(b){return[b,a._indices[b].map(function(a){return c[a].cssText}).join(a._optimizeForSpeed?"":"\n")]}).filter(function(a){return!!a[1]}))},b.styles=function(a){var b,c;return b=this.cssRules(),void 0===(c=a)&&(c={}),b.map(function(a){var b=a[0],d=a[1];return e.default.createElement("style",{id:"__"+b,key:"__"+b,nonce:c.nonce?c.nonce:void 0,dangerouslySetInnerHTML:{__html:d}})})},b.getIdAndRules=function(a){var b=a.children,c=a.dynamic,d=a.id;if(c){var e=l(d,c);return{styleId:e,rules:Array.isArray(b)?b.map(function(a){return m(e,a)}):[m(e,b)]}}return{styleId:l(d),rules:Array.isArray(b)?b:[b]}},b.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(a,b){return a[b.id.slice(2)]=b,a},{})},a}(),o=d.createContext(null);o.displayName="StyleSheetContext";e.default.useInsertionEffect||e.default.useLayoutEffect;var p=void 0;function q(a){var b=p||d.useContext(o);return b&&b.add(a),null}q.dynamic=function(a){return a.map(function(a){return l(a[0],a[1])}).join(" ")},b.style=q},76180:(a,b,c)=>{"use strict";a.exports=c(75913).style},78778:()=>{},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86561:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},93613:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},93661:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:a=>{"use strict";a.exports=require("events")},98138:(a,b,c)=>{"use strict";c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\product\\[id]\\product-details-client.tsx","default")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,1697,8658,4612,9822,6085],()=>b(b.s=66829));module.exports=c})();