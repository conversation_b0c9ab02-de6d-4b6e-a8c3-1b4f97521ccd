(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5467],{8094:(e,o,s)=>{"use strict";s.r(o),s.d(o,{default:()=>l});var t=s(95155),n=s(98816);class r{static setCookie(e,o){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("undefined"==typeof document)return;let t={...this.COOKIE_OPTIONS,...s},n="".concat(e,"=").concat(encodeURIComponent(o));t.maxAge&&(n+="; Max-Age=".concat(t.maxAge)),t.path&&(n+="; Path=".concat(t.path)),t.secure&&(n+="; Secure"),t.sameSite&&(n+="; SameSite=".concat(t.sameSite)),n+="; Domain=".concat("codemedicalapps.com"),document.cookie=n}static getCookie(e){if("undefined"==typeof document)return null;let o=e+"=";for(let e of document.cookie.split(";")){let s=e.trim();if(0===s.indexOf(o))return decodeURIComponent(s.substring(o.length))}return null}static deleteCookie(e){if("undefined"==typeof document)return;let o="".concat(e,"=; Max-Age=0; Path=/");o+="; Domain=".concat("codemedicalapps.com"),document.cookie=o}static saveLoginInfo(e,o){try{let s={UserId:e.UserId||e.UserID,UserName:e.UserName,Email:e.Email,FirstName:e.FirstName,LastName:e.LastName,PhoneNumber:e.PhoneNumber,Pointno:e.Pointno};this.setCookie(this.USER_COOKIE,JSON.stringify(s)),this.setCookie(this.TOKEN_COOKIE,o),this.setCookie(this.LOGIN_STATUS_COOKIE,"true"),console.log("Login info saved to secure cookies")}catch(e){console.error("Error saving login info to cookies:",e)}}static getLoginInfo(){try{let e=this.getCookie(this.USER_COOKIE),o=this.getCookie(this.TOKEN_COOKIE),s=this.getCookie(this.LOGIN_STATUS_COOKIE),t=e?JSON.parse(e):null;return{user:t,token:o,isLoggedIn:"true"===s&&t&&o}}catch(e){return console.error("Error reading login info from cookies:",e),{user:null,token:null,isLoggedIn:!1}}}static clearLoginInfo(){try{this.deleteCookie(this.USER_COOKIE),this.deleteCookie(this.TOKEN_COOKIE),this.deleteCookie(this.LOGIN_STATUS_COOKIE),localStorage.removeItem("user"),localStorage.removeItem("userInfo"),localStorage.removeItem("token"),localStorage.removeItem("authToken"),localStorage.removeItem("isLoggedIn"),localStorage.removeItem("userId"),localStorage.removeItem("userID"),console.log("\uD83D\uDD10 Login info cleared from cookies and localStorage")}catch(e){console.error("Error clearing login info:",e)}}static isUserLoggedIn(){let{isLoggedIn:e}=this.getLoginInfo();return e}static updateUserInfo(e){try{let{token:o}=this.getLoginInfo();o&&this.saveLoginInfo(e,o)}catch(e){console.error("Error updating user info in cookies:",e)}}static migrateFromLocalStorage(){try{let{isLoggedIn:e}=this.getLoginInfo();if(e)return;let o=localStorage.getItem("user")||localStorage.getItem("userInfo"),s=localStorage.getItem("token")||localStorage.getItem("authToken"),t=localStorage.getItem("isLoggedIn");if(o&&s&&"true"===t){let e=JSON.parse(o);this.saveLoginInfo(e,s),localStorage.removeItem("user"),localStorage.removeItem("userInfo"),localStorage.removeItem("token"),localStorage.removeItem("authToken"),localStorage.removeItem("isLoggedIn"),console.log("Successfully migrated login info from localStorage to cookies")}}catch(e){console.error("Error migrating from localStorage to cookies:",e)}}}r.COOKIE_OPTIONS={secure:!0,httpOnly:!1,sameSite:"strict",path:"/",maxAge:604800},r.HTTPONLY_COOKIE_OPTIONS={secure:!0,httpOnly:!0,sameSite:"strict",path:"/",maxAge:3600},r.USER_COOKIE="auth_user",r.TOKEN_COOKIE="auth_token",r.LOGIN_STATUS_COOKIE="is_logged_in";var i=s(12115);function l(){let{user:e,isLoggedIn:o,isLoading:s,token:l}=(0,n.J)(),[a,c]=(0,i.useState)(null),[d,g]=(0,i.useState)(""),m=e=>{if("undefined"==typeof document)return null;let o=e+"=";for(let e of document.cookie.split(";")){let s=e.trim();if(0===s.indexOf(o))return decodeURIComponent(s.substring(o.length))}return null};return(0,i.useEffect)(()=>{c(r.getLoginInfo()),"undefined"!=typeof document&&g(document.cookie)},[]),(0,t.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"User Debug Information"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-gray-100 p-4 rounded-lg",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"User Context State"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"isLoading:"})," ",s?"true":"false"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"isLoggedIn:"})," ",o?"true":"false"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"token:"})," ",l?"exists":"null"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"user:"})," ",e?"exists":"null"]}),e&&(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"UserId:"})," ",e.UserId||e.UserID]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Email:"})," ",e.Email]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Name:"})," ",e.FirstName," ",e.LastName]})]})]})]}),(0,t.jsxs)("div",{className:"bg-blue-100 p-4 rounded-lg",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Cookie Helper Info"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"isLoggedIn:"})," ",(null==a?void 0:a.isLoggedIn)?"true":"false"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"token:"})," ",(null==a?void 0:a.token)?"exists":"null"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"user:"})," ",(null==a?void 0:a.user)?"exists":"null"]}),(null==a?void 0:a.user)&&(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"UserId:"})," ",a.user.UserId||a.user.UserID]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Email:"})," ",a.user.Email]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Name:"})," ",a.user.FirstName," ",a.user.LastName]})]})]})]}),(0,t.jsxs)("div",{className:"bg-yellow-100 p-4 rounded-lg",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Raw Cookies"}),(0,t.jsx)("div",{className:"text-sm",children:(0,t.jsx)("p",{className:"break-all",children:d||"No cookies found"})})]}),(0,t.jsxs)("div",{className:"bg-green-100 p-4 rounded-lg",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Individual Cookie Values"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"auth_user:"})," ",m("auth_user")||"not found"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"auth_token:"})," ",m("auth_token")||"not found"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"is_logged_in:"})," ",m("is_logged_in")||"not found"]})]})]}),(0,t.jsxs)("div",{className:"bg-red-100 p-4 rounded-lg",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Actions"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("button",{onClick:()=>window.location.reload(),className:"bg-blue-500 text-white px-4 py-2 rounded mr-2",children:"Refresh Page"}),(0,t.jsx)("button",{onClick:()=>{r.clearLoginInfo(),window.location.reload()},className:"bg-red-500 text-white px-4 py-2 rounded",children:"Clear Cookies"})]})]})]})]})}},10727:(e,o,s)=>{Promise.resolve().then(s.bind(s,8094))}},e=>{e.O(0,[3464,8816,8441,5964,7358],()=>e(e.s=10727)),_N_E=e.O()}]);