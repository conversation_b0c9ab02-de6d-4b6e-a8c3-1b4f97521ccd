{"version": 1, "files": ["../../../webpack-runtime.js", "../../../chunks/4985.js", "../../../chunks/1697.js", "../../../chunks/8658.js", "../../../chunks/4612.js", "../../../chunks/9822.js", "../../../chunks/6085.js", "page_client-reference-manifest.js", "../../../../../package.json", "../../../../../contexts/wishlist-context.tsx", "../../../../../components/ui/button.tsx", "../../../../../components/ui/skeleton.tsx", "../../../../../components/ui/breadcrumb.tsx", "../../../../../components/ui/card.tsx", "../../../../../contexts/settings-context.tsx", "../../../../../components/ui/modern-toast.tsx", "../../../../../components/products/product-media-gallery.tsx", "../../../../../components/ui/countdown.tsx", "../../../../../lib/utils.ts", "../../../../../components/ui/tabs.tsx", "../../../../../components/ui/badge.tsx", "../../../../../contexts/cart-context.tsx", "../../../../../contexts/currency-context.tsx", "../../../../../components/products/product-specifications.tsx", "../../../../../app/product/[id]/product-error.tsx", "../../../../../app/product/[id]/product-loading.tsx", "../../../../../components/ui/product-reviews-display.tsx", "../../../../../lib/api-helper.ts", "../../../../../lib/translations.ts", "../../../../../lib/color-utils.ts"]}