(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4307],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>a,t:()=>o});var n=r(12115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=s(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():s(e[t],null)}}}}function a(...e){return n.useCallback(o(...e),e)}},37099:(e,t,r)=>{Promise.resolve().then(r.bind(r,53700))},53700:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(95155),s=r(12115),o=r(97168),a=r(88482);function l(){let[e,t]=(0,s.useState)("954B50"),[r,l]=(0,s.useState)(null),[i,c]=(0,s.useState)(null),[d,u]=(0,s.useState)(!1),p=async()=>{u(!0),l(null);try{let t,r={requestParameters:{CouponCode:e.toUpperCase(),cartJsonData:JSON.stringify([{ProductId:1,ProductName:"Test Product",Price:100,Quantity:1,IsDiscountAllowed:!0}])}};console.log("Testing coupon with params:",r);let n=await fetch("".concat("https://localhost:7149/","api/v1/dynamic/dataoperation/get-coupon-code-data"),{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(r)}),s=await n.text();console.log("Raw response:",s);try{t=JSON.parse(s)}catch(e){l({error:"Failed to parse response: "+s});return}console.log("API Response:",t),l(t)}catch(e){console.error("Error testing coupon:",e),l({error:e instanceof Error?e.message:"Unknown error occurred"})}finally{u(!1)}},f=async()=>{u(!0),c(null);try{let t,r={requestParameters:{FirstName:"Test",LastName:"User",Email:"<EMAIL>",Phone:"+1234567890",Address:"123 Test Street",City:"Test City",Country:"Test Country",ZipCode:"12345",OrderNote:"Test order with coupon",PaymentMethodId:1,CouponCode:e,cartJsonData:JSON.stringify([{ProductId:1,ProductName:"Test Product",Price:100,Quantity:1,IsDiscountAllowed:!0}])}};console.log("Placing order with params:",r);let n=await fetch("".concat("https://localhost:7149/","api/v1/dynamic/dataoperation/place-order"),{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(r)}),s=await n.text();console.log("Raw response:",s);try{t=JSON.parse(s)}catch(e){c({error:"Failed to parse response: "+s});return}console.log("API Response:",t),c(t)}catch(e){console.error("Error placing order:",e),c({error:e instanceof Error?e.message:"Unknown error occurred"})}finally{u(!1)}};return(0,n.jsx)("div",{className:"container mx-auto p-6",children:(0,n.jsxs)(a.Zp,{className:"max-w-2xl mx-auto p-6",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Test Order with Coupon (Local Backend)"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Coupon Code"}),(0,n.jsx)("input",{type:"text",value:e,onChange:e=>t(e.target.value),className:"w-full p-2 border rounded-md",placeholder:"Enter coupon code"})]}),(0,n.jsxs)("div",{className:"flex space-x-4",children:[(0,n.jsx)(o.$,{onClick:p,disabled:d,className:"flex-1",children:d?"Testing...":"Test Coupon"}),(0,n.jsx)(o.$,{onClick:f,disabled:d,className:"flex-1",variant:"outline",children:d?"Processing...":"Place Test Order"})]}),r&&(0,n.jsxs)("div",{className:"mt-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Coupon API Response:"}),(0,n.jsx)("pre",{className:"bg-gray-100 p-4 rounded-md overflow-auto text-sm",children:JSON.stringify(r,null,2)})]}),i&&(0,n.jsxs)("div",{className:"mt-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Order API Response:"}),(0,n.jsx)("pre",{className:"bg-gray-100 p-4 rounded-md overflow-auto text-sm",children:JSON.stringify(i,null,2)})]})]}),(0,n.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 rounded-md",children:[(0,n.jsx)("h4",{className:"font-semibold text-blue-800 mb-2",children:"Test Instructions:"}),(0,n.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,n.jsx)("li",{children:"• Make sure your local backend is running at https://localhost:7149"}),(0,n.jsx)("li",{children:'• Default test coupon code is "954B50" (update with a valid coupon in your database)'}),(0,n.jsx)("li",{children:'• First test if the coupon is valid using the "Test Coupon" button'}),(0,n.jsx)("li",{children:'• Then try placing a test order with the coupon using the "Place Test Order" button'}),(0,n.jsx)("li",{children:"• Check the browser console for detailed logs"})]})]})]})})}},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var n=r(52596),s=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,n.$)(t))}},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var n=r(52596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.$,a=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:l}=t,i=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let o=s(t)||s(n);return a[e][o]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,i,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>i,Zp:()=>a,aR:()=>l,wL:()=>u});var n=r(95155),s=r(12115),o=r(53999);let a=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});a.displayName="Card";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...s})});l.displayName="CardHeader";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});i.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...s})});d.displayName="CardContent";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...s})});u.displayName="CardFooter"},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>i});var n=r(95155),s=r(12115),o=r(99708),a=r(74466),l=r(53999);let i=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:r,variant:s,size:a,asChild:c=!1,...d}=e,u=c?o.DX:"button";return(0,n.jsx)(u,{className:(0,l.cn)(i({variant:s,size:a,className:r})),ref:t,...d})});c.displayName="Button"},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>c,TL:()=>a});var n=r(12115),s=r(6101),o=r(95155);function a(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var a;let e,l,i=(a=r,(l=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(l=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),c=function(e,t){let r={...t};for(let n in t){let s=e[n],o=t[n];/^on[A-Z]/.test(n)?s&&o?r[n]=(...e)=>{let t=o(...e);return s(...e),t}:s&&(r[n]=s):"style"===n?r[n]={...s,...o}:"className"===n&&(r[n]=[s,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,s.t)(t,i):i),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:s,...a}=e,l=n.Children.toArray(s),i=l.find(d);if(i){let e=i.props.children,s=l.map(t=>t!==i?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,o.jsx)(t,{...a,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var l=a("Slot"),i=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=i,t}function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}}},e=>{e.O(0,[4277,8441,5964,7358],()=>e(e.s=37099)),_N_E=e.O()}]);