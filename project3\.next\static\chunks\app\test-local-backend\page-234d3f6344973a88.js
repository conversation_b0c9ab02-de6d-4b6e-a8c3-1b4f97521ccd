(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3100],{5742:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c});var o=t(95155),n=t(12115),r=t(49509);let a=new Map;async function i(e,s){try{console.log("\uD83D\uDD27 Verification store - Starting request:",{phoneNumber:e,codeLength:s.length});let t="".concat(function(){let e=r.env.ADMIN_BASE_URL||"https://admin.codemedicalapps.com/";return e.endsWith("/")?e.slice(0,-1):e}(),"/api/v1/verification/store");console.log("\uD83D\uDD27 Using API URL:",t);let o=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({PhoneNumber:e,VerificationCode:s,ExpirationMinutes:10})});if(console.log("\uD83D\uDCE5 Verification store response status:",o.status),console.log("\uD83D\uDCE5 Verification store response headers:",Object.fromEntries(o.headers.entries())),!o.ok){let e=await o.text();return console.error("❌ Verification store HTTP error:",o.status,o.statusText),console.error("❌ Verification store error response body:",e),!1}let n=await o.json();if(console.log("✅ Verification store response:",n),n.success)return console.log("✅ Verification code stored successfully"),!0;return console.error("❌ Verification store failed:",n.error||"Unknown error"),!1}catch(e){return console.error("❌ Error storing verification code:",e),console.error("❌ Error details:",e instanceof Error?e.message:"Unknown error"),!1}}function c(){let[e,s]=(0,n.useState)(""),[t,r]=(0,n.useState)(""),[a,c]=(0,n.useState)(!1),l=async()=>{c(!0),s("Testing...");try{await i("+9647858021300","123456")?s("✅ SUCCESS: Verification code stored successfully!"):s("❌ FAILED: Could not store verification code")}catch(t){let e=t instanceof Error?t.message:"Unknown error occurred";s("❌ ERROR: ".concat(e))}finally{c(!1)}},d=async()=>{c(!0),r("Testing...");try{let e={requestParameters:{CouponCode:"954B50",cartJsonData:JSON.stringify([{ProductId:1,ProductName:"Test Product",Price:100,Quantity:1,IsDiscountAllowed:!0}])}},s=await fetch("".concat("https://admin.codemedicalapps.com/","api/v1/dynamic/dataoperation/get-coupon-code-data"),{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify(e)});if(s.ok){let e=await s.json();if(e.data&&!e.errorMessage){let s;if(s="string"==typeof e.data?JSON.parse(e.data):e.data,Array.isArray(s)&&s.length>0){let e=s[0];r('✅ SUCCESS: Coupon "'.concat(e.Title,'" found! ').concat(e.DiscountValue,"% discount"))}else r("❌ FAILED: No coupon data returned")}else r("❌ FAILED: ".concat(e.errorMessage||"No data returned"))}else r("❌ HTTP ERROR: ".concat(s.status," ").concat(s.statusText))}catch(s){let e=s instanceof Error?s.message:"Unknown error occurred";r("❌ ERROR: ".concat(e))}finally{c(!1)}};return(0,o.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,o.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Remote Backend API Test"}),(0,o.jsxs)("div",{className:"space-y-8",children:[(0,o.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Environment Configuration"}),(0,o.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Backend URL:"})," ","https://admin.codemedicalapps.com/"]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Domain:"})," ","codemedicalapps.com"]})]})]}),(0,o.jsxs)("div",{className:"bg-green-50 p-6 rounded-lg",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Verification Code Storage Test"}),(0,o.jsx)("button",{onClick:l,disabled:a,className:"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50 mb-4",children:a?"Testing...":"Test Verification Code Storage"}),e&&(0,o.jsx)("div",{className:"mt-4 p-4 bg-white rounded border",children:(0,o.jsx)("p",{className:"font-mono text-sm",children:e})})]}),(0,o.jsxs)("div",{className:"bg-yellow-50 p-6 rounded-lg",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Coupon Code Validation Test"}),(0,o.jsx)("button",{onClick:d,disabled:a,className:"bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 disabled:opacity-50 mb-4",children:a?"Testing...":"Test Coupon Code (954B50)"}),t&&(0,o.jsx)("div",{className:"mt-4 p-4 bg-white rounded border",children:(0,o.jsx)("p",{className:"font-mono text-sm",children:t})})]}),(0,o.jsxs)("div",{className:"bg-gray-50 p-6 rounded-lg",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Instructions"}),(0,o.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-sm",children:[(0,o.jsxs)("li",{children:["Make sure the backend is accessible: ",(0,o.jsx)("code",{className:"bg-gray-200 px-2 py-1 rounded",children:"https://admin.codemedicalapps.com"})]}),(0,o.jsxs)("li",{children:["Make sure the frontend is running: ",(0,o.jsx)("code",{className:"bg-gray-200 px-2 py-1 rounded",children:"npm run dev"})]}),(0,o.jsx)("li",{children:"Click the test buttons above to verify the APIs are working"}),(0,o.jsx)("li",{children:"If tests pass, the signup verification and coupon functionality should work"})]})]})]})]})}setInterval(()=>{let e=Date.now();a.forEach((s,t)=>{s.resetTime<e&&a.delete(t)})},3e5)},25082:(e,s,t)=>{Promise.resolve().then(t.bind(t,5742))}},e=>{e.O(0,[8441,5964,7358],()=>e(e.s=25082)),_N_E=e.O()}]);