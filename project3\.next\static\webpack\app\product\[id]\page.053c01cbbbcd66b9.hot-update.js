"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/product-details-client.tsx":
/*!*****************************************************!*\
  !*** ./app/product/[id]/product-details-client.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Clock,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_modern_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/modern-toast */ \"(app-pages-browser)/./components/ui/modern-toast.tsx\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _contexts_currency_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/currency-context */ \"(app-pages-browser)/./contexts/currency-context.tsx\");\n/* harmony import */ var _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/settings-context */ \"(app-pages-browser)/./contexts/settings-context.tsx\");\n/* harmony import */ var _components_products_product_specifications__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/products/product-specifications */ \"(app-pages-browser)/./components/products/product-specifications.tsx\");\n/* harmony import */ var _components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/products/product-media-gallery */ \"(app-pages-browser)/./components/products/product-media-gallery.tsx\");\n/* harmony import */ var _components_ui_countdown__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/countdown */ \"(app-pages-browser)/./components/ui/countdown.tsx\");\n/* harmony import */ var _product_loading__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./product-loading */ \"(app-pages-browser)/./app/product/[id]/product-loading.tsx\");\n/* harmony import */ var _product_error__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./product-error */ \"(app-pages-browser)/./app/product/[id]/product-error.tsx\");\n/* harmony import */ var _components_ui_product_reviews_display__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/product-reviews-display */ \"(app-pages-browser)/./components/ui/product-reviews-display.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to construct image URL\nconst constructImageUrl = (attachmentUrl)=>{\n    if (!attachmentUrl) return \"/placeholder.svg?height=400&width=400\";\n    if (attachmentUrl.startsWith(\"http\")) {\n        return attachmentUrl;\n    }\n    const baseUrl = \"https://admin.codemedicalapps.com\";\n    // Normalize path (ensure it starts with exactly one slash)\n    let normalizedPath = attachmentUrl.startsWith(\"/\") ? attachmentUrl : \"/\".concat(attachmentUrl);\n    // Remove any double slashes in the path\n    normalizedPath = normalizedPath.replace(/\\/+/g, '/');\n    return \"\".concat(baseUrl).concat(normalizedPath);\n};\nfunction ProductDetailsClient(param) {\n    let { productId } = param;\n    var _product_Rating;\n    _s();\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__.useWishlist)();\n    const { rate } = (0,_contexts_currency_context__WEBPACK_IMPORTED_MODULE_11__.useCurrency)();\n    const { primaryColor, primaryTextColor } = (0,_contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings)();\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [activeImage, setActiveImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoLinks, setVideoLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideoIndex, setSelectedVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingToWishlist, setAddingToWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationType, setAnimationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ProductDetailsClient.useState\": ()=>{\n            // Initialize with first option selected for each attribute if none selected\n            const initial = {};\n            if (product === null || product === void 0 ? void 0 : product.AttributesJson) {\n                product.AttributesJson.forEach({\n                    \"ProductDetailsClient.useState\": (attr)=>{\n                        const key = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        initial[key] = true; // Select first option by default\n                    }\n                }[\"ProductDetailsClient.useState\"]);\n            }\n            return initial;\n        }\n    }[\"ProductDetailsClient.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetailsClient.useEffect\": ()=>{\n            fetchProduct();\n        }\n    }[\"ProductDetailsClient.useEffect\"], [\n        productId\n    ]);\n    const fetchProduct = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Try direct API call first, then fallback to proxy if CORS issues\n            const requestBody = {\n                requestParameters: {\n                    ProductId: Number.parseInt(productId, 10),\n                    recordValueJson: \"[]\"\n                }\n            };\n            console.log(\"Fetching product with ID:\", productId, \"Request body:\", requestBody);\n            let response;\n            try {\n                // Import MakeApiCallAsync for JWT token handling\n                const { MakeApiCallAsync } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api-helper */ \"(app-pages-browser)/./lib/api-helper.ts\"));\n                // Use API helper which automatically handles JWT tokens and removes UserID\n                response = await MakeApiCallAsync(\"get-product_detail\", null, requestBody, {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                }, \"POST\", true);\n                console.log(\"API helper response:\", response.data);\n            } catch (apiHelperError) {\n                console.log(\"API helper failed, trying proxy route:\", apiHelperError);\n                // Fallback to proxy route\n                response = await axios__WEBPACK_IMPORTED_MODULE_19__[\"default\"].post(\"/api/product-detail\", requestBody, {\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                console.log(\"Proxy API response:\", response.data);\n            }\n            if (response.data) {\n                // Check if response.data has a nested data property (from API proxy)\n                const responseData = response.data.data ? response.data : response.data;\n                if (responseData && responseData.data) {\n                    try {\n                        // Parse the response data\n                        const parsedData = JSON.parse(responseData.data);\n                        console.log(\"Parsed product data:\", parsedData);\n                        if (parsedData) {\n                            // The API might return an array with one item or a single object\n                            const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;\n                            if (productData) {\n                                // Ensure AttributesJson is properly parsed if it's a string\n                                if (productData.AttributesJson && typeof productData.AttributesJson === \"string\") {\n                                    try {\n                                        productData.AttributesJson = JSON.parse(productData.AttributesJson);\n                                    } catch (e) {\n                                        console.error(\"Error parsing AttributesJson:\", e);\n                                        productData.AttributesJson = [];\n                                    }\n                                } else if (!productData.AttributesJson) {\n                                    productData.AttributesJson = [];\n                                }\n                                console.log(\"Product data with attributes:\", productData);\n                                console.log(\"CategoryName in product data:\", productData.CategoryName);\n                                console.log(\"CategoryID in product data:\", productData.CategoryID);\n                                setProduct(productData);\n                                // Set active image\n                                if (productData.ProductImagesJson && productData.ProductImagesJson.length > 0) {\n                                    const primaryImage = productData.ProductImagesJson.find((img)=>img.IsPrimary) || productData.ProductImagesJson[0];\n                                    setActiveImage(constructImageUrl(primaryImage.AttachmentURL));\n                                }\n                                // Handle comma-separated video links\n                                if (productData.VideoLink) {\n                                    console.log(\"Video links found:\", productData.VideoLink);\n                                    const links = productData.VideoLink.split(\",\").map((link)=>link.trim());\n                                    const processedLinks = links.map((link)=>constructVideoUrl(link));\n                                    setVideoLinks(processedLinks);\n                                    setSelectedVideoIndex(0);\n                                }\n                                // Set initial quantity based on product minimum order quantity\n                                if (productData.OrderMinimumQuantity > 0) {\n                                    setQuantity(productData.OrderMinimumQuantity);\n                                }\n                            } else {\n                                console.error(\"No product data found in parsed response\");\n                                setError(\"Product with ID \".concat(productId, \" not found. Please check if this product exists.\"));\n                            }\n                        } else {\n                            console.error(\"Invalid product data format - parsedData is null/undefined\");\n                            setError(\"Invalid product data format\");\n                        }\n                    } catch (parseError) {\n                        console.error(\"Error parsing product data:\", parseError, \"Raw data:\", responseData.data);\n                        setError(\"Error parsing product data\");\n                    }\n                } else {\n                    console.error(\"No data property in API response:\", response.data);\n                    setError(\"No data in API response\");\n                }\n            } else {\n                console.error(\"Empty response from API\");\n                setError(\"Empty response from server\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching product:\", error);\n            // More detailed error handling\n            if (error.response) {\n                var _error_response_data;\n                // Server responded with error status\n                console.error(\"Server error:\", error.response.status, error.response.data);\n                setError(\"Server error: \".concat(error.response.status, \" - \").concat(((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Unknown error\"));\n            } else if (error.request) {\n                // Request was made but no response received\n                console.error(\"Network error:\", error.request);\n                setError(\"Network error - please check your connection\");\n            } else {\n                // Something else happened\n                console.error(\"Request setup error:\", error.message);\n                setError(\"Error: \".concat(error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const constructVideoUrl = (videoLink)=>{\n        if (!videoLink) return \"\";\n        if (videoLink.includes(\"youtube.com\") || videoLink.includes(\"youtu.be\")) {\n            return videoLink;\n        }\n        // For MP4 videos, use a proxy URL to handle CORS\n        if (videoLink.startsWith(\"http\")) {\n            return \"/api/video-proxy?url=\".concat(encodeURIComponent(videoLink));\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedVideoLink = videoLink.startsWith(\"/\") ? videoLink : \"/\".concat(videoLink);\n        return \"/api/video-proxy?url=\".concat(encodeURIComponent(\"\".concat(baseUrl).concat(normalizedVideoLink)));\n    };\n    // Group attributes by ProductAttributeID\n    const groupedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[groupedAttributes]\": ()=>{\n            if (!(product === null || product === void 0 ? void 0 : product.AttributesJson)) return {};\n            return product.AttributesJson.reduce({\n                \"ProductDetailsClient.useMemo[groupedAttributes]\": (groups, attr)=>{\n                    const groupId = attr.ProductAttributeID;\n                    if (!groups[groupId]) {\n                        groups[groupId] = [];\n                    }\n                    groups[groupId].push(attr);\n                    return groups;\n                }\n            }[\"ProductDetailsClient.useMemo[groupedAttributes]\"], {});\n        }\n    }[\"ProductDetailsClient.useMemo[groupedAttributes]\"], [\n        product === null || product === void 0 ? void 0 : product.AttributesJson\n    ]);\n    // Handle attribute selection with conditional behavior\n    const handleAttributeChange = (attr, isChecked, isRadioGroup)=>{\n        setSelectedAttributes((prev)=>{\n            const newState = {\n                ...prev\n            };\n            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n            // For radio groups, uncheck all other attributes in the same group\n            if (isRadioGroup && isChecked) {\n                Object.keys(prev).forEach((key)=>{\n                    if (key.startsWith(\"\".concat(attr.ProductAttributeID, \"_\")) && key !== attrKey) {\n                        newState[key] = false;\n                    }\n                });\n            }\n            // Set the selected attribute\n            // For checkboxes, toggle the state\n            // For radio buttons, always set to true (since we already unset others if needed)\n            newState[attrKey] = isRadioGroup ? true : !prev[attrKey];\n            return newState;\n        });\n    };\n    // Render price with all price-related information\n    const renderPrice = ()=>{\n        if (!product) return null;\n        const showDiscount = product.DiscountPrice && product.DiscountPrice < product.Price;\n        const adjustedPrice = calculateAdjustedPrice();\n        const showAdjustedPrice = adjustedPrice !== product.Price && adjustedPrice !== (product.DiscountPrice || product.Price);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-baseline gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-3xl font-bold text-primary\",\n                            children: [\n                                \"$\",\n                                showDiscount ? (product.DiscountPrice || 0).toFixed(2) : adjustedPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 445,\n                            columnNumber: 13\n                        }, this),\n                        showAdjustedPrice && !showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 13\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded\",\n                            children: [\n                                Math.round((product.Price - (product.DiscountPrice || 0)) / product.Price * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 435,\n                    columnNumber: 9\n                }, this),\n                product.PriceIQD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-lg font-medium text-gray-600\",\n                    children: [\n                        product.PriceIQD.toLocaleString(),\n                        \" IQD\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 472,\n                    columnNumber: 11\n                }, this),\n                product.PointNo && product.PointNo > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                        children: [\n                            \"Buy & Earn \",\n                            product.PointNo,\n                            \" $ credit\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 479,\n                    columnNumber: 11\n                }, this),\n                product.OldPrice && product.OldPrice > product.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"line-through\",\n                            children: [\n                                \"$\",\n                                product.OldPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-green-600\",\n                            children: [\n                                Math.round((product.OldPrice - (product.DiscountPrice || product.Price)) / product.OldPrice * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 488,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 433,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate adjusted price based on selected attributes\n    const calculateAdjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProductDetailsClient.useCallback[calculateAdjustedPrice]\": ()=>{\n            if (!product) return 0;\n            let adjustedPrice = product.Price;\n            if (product.AttributesJson && product.AttributesJson.length > 0) {\n                product.AttributesJson.forEach({\n                    \"ProductDetailsClient.useCallback[calculateAdjustedPrice]\": (attr)=>{\n                        const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        if (selectedAttributes[attrKey] && typeof attr.PriceAdjustment === \"number\" && typeof attr.PriceAdjustmentType === \"number\") {\n                            switch(attr.PriceAdjustmentType){\n                                case 1:\n                                    adjustedPrice += attr.PriceAdjustment;\n                                    break;\n                                case 2:\n                                    adjustedPrice += product.Price * attr.PriceAdjustment / 100;\n                                    break;\n                            }\n                        }\n                    }\n                }[\"ProductDetailsClient.useCallback[calculateAdjustedPrice]\"]);\n            }\n            return Math.max(0, adjustedPrice); // Ensure price doesn't go below 0\n        }\n    }[\"ProductDetailsClient.useCallback[calculateAdjustedPrice]\"], [\n        product,\n        selectedAttributes\n    ]);\n    const adjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[adjustedPrice]\": ()=>calculateAdjustedPrice()\n    }[\"ProductDetailsClient.useMemo[adjustedPrice]\"], [\n        calculateAdjustedPrice\n    ]);\n    // Render product badges\n    const renderBadges = ()=>{\n        if (!product) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute top-4 left-4 z-10 flex flex-col gap-2\",\n            children: [\n                product.IsDiscountAllowed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-red-500 hover:bg-red-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"SALE\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 545,\n                    columnNumber: 11\n                }, this),\n                product.MarkAsNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-green-500 hover:bg-green-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"NEW\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 550,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 543,\n            columnNumber: 7\n        }, this);\n    };\n    // Combine images and videos into a single media array for the gallery\n    const mediaItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[mediaItems]\": ()=>{\n            var _product_ProductImagesJson;\n            const items = [];\n            // Add product images\n            if (product === null || product === void 0 ? void 0 : (_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : _product_ProductImagesJson.length) {\n                product.ProductImagesJson.forEach({\n                    \"ProductDetailsClient.useMemo[mediaItems]\": (img)=>{\n                        items.push({\n                            type: \"image\",\n                            url: constructImageUrl(img.AttachmentURL),\n                            alt: (product === null || product === void 0 ? void 0 : product.ProductName) || \"Product image\",\n                            thumbnail: constructImageUrl(img.AttachmentURL)\n                        });\n                    }\n                }[\"ProductDetailsClient.useMemo[mediaItems]\"]);\n            }\n            // Add videos\n            videoLinks.forEach({\n                \"ProductDetailsClient.useMemo[mediaItems]\": (videoUrl, index)=>{\n                    items.push({\n                        type: \"video\",\n                        url: videoUrl,\n                        alt: \"\".concat((product === null || product === void 0 ? void 0 : product.ProductName) || \"Product\", \" - Video \").concat(index + 1),\n                        thumbnail: activeImage || \"\"\n                    });\n                }\n            }[\"ProductDetailsClient.useMemo[mediaItems]\"]);\n            return items;\n        }\n    }[\"ProductDetailsClient.useMemo[mediaItems]\"], [\n        product,\n        videoLinks,\n        activeImage\n    ]);\n    const animateCounter = (type)=>{\n        setAnimationType(type);\n        setIsAnimating(true);\n        setTimeout(()=>setIsAnimating(false), 300);\n    };\n    const incrementQuantity = ()=>{\n        if (product) {\n            const maxQuantity = product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity;\n            if (quantity < maxQuantity) {\n                setQuantity((prev)=>prev + 1);\n                animateCounter(\"increment\");\n            } else {\n                // Visual feedback when max quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Maximum quantity of \".concat(maxQuantity, \" reached\"));\n            }\n        }\n    };\n    const decrementQuantity = ()=>{\n        if (product) {\n            const minQuantity = product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1;\n            if (quantity > minQuantity) {\n                setQuantity((prev)=>prev - 1);\n                animateCounter(\"decrement\");\n            } else {\n                // Visual feedback when min quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Minimum quantity is \".concat(minQuantity));\n            }\n        }\n    };\n    // Dynamic button styles based on state\n    const getButtonStyles = (type)=>{\n        const baseStyles = \"flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 rounded-full transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-offset-1\";\n        const disabledStyles = \"bg-gray-100 text-gray-400 cursor-not-allowed\";\n        if (type === \"increment\") {\n            const isMax = product && quantity >= (product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity);\n            return \"\".concat(baseStyles, \" \").concat(isMax ? disabledStyles : \"bg-primary text-white hover:bg-primary/90 focus:ring-primary/50\");\n        } else {\n            const isMin = product && quantity <= (product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1);\n            return \"\".concat(baseStyles, \" \").concat(isMin ? disabledStyles : \"bg-primary text-white hover:bg-primary/90 focus:ring-primary/50\");\n        }\n    };\n    // Counter display with animation\n    const CounterDisplay = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center justify-center w-10 sm:w-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm sm:text-base font-medium transition-all duration-200 \".concat(isAnimating ? \"scale-125 text-primary\" : \"scale-100\"),\n                    children: quantity\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 657,\n                    columnNumber: 7\n                }, this),\n                isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"absolute text-xs font-bold text-primary transition-all duration-200 \".concat(animationType === \"increment\" ? \"-top-4 sm:-top-5\" : \"top-4 sm:top-5\"),\n                    children: animationType === \"increment\" ? \"+1\" : \"-1\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 665,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 656,\n            columnNumber: 5\n        }, this);\n    // Early return if product is not loaded yet\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_loading__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 680,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            error: error,\n            retry: fetchProduct\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 684,\n            columnNumber: 12\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Product Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 690,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: [\n                        'The product with ID \"',\n                        productId,\n                        '\" could not be found. It may not exist in the database.'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 691,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/products\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"View All Products\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 696,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Check the products list to find available product IDs\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 702,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 695,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 689,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.Breadcrumb, {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbList, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 716,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 720,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products?category=\".concat(product.CategoryID || \"all\"),\n                                    children: product.CategoryName || \"Medical Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 723,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 722,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 721,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 728,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 731,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 734,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbPage, {\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 736,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 735,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 714,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 713,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_14__.ProductMediaGallery, {\n                            media: mediaItems,\n                            className: \"w-full rounded-lg overflow-hidden\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 744,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 743,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:w-1/2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-2\",\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 752,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            ...Array(5)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(i < Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 758,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 756,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 ml-2\",\n                                        children: [\n                                            \"(\",\n                                            product.Rating || 0,\n                                            \") \",\n                                            product.TotalReviews || 0,\n                                            \" reviews\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 768,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 755,\n                                columnNumber: 11\n                            }, this),\n                            renderPrice(),\n                            (()=>{\n                                // Only show countdown if both sale start and end dates are provided\n                                if (!product.SellStartDatetimeUTC || !product.SellEndDatetimeUTC) {\n                                    return null;\n                                }\n                                const now = new Date();\n                                const saleStart = new Date(product.SellStartDatetimeUTC);\n                                const saleEnd = new Date(product.SellEndDatetimeUTC);\n                                // Check if sale is currently active or upcoming\n                                const isSaleActive = now >= saleStart && now <= saleEnd;\n                                const isSaleUpcoming = now < saleStart;\n                                const isSaleExpired = now > saleEnd;\n                                // Don't show timer if sale has expired\n                                if (isSaleExpired) {\n                                    return null;\n                                }\n                                // Determine the countdown target date\n                                const countdownEndDate = isSaleUpcoming ? product.SellStartDatetimeUTC : product.SellEndDatetimeUTC;\n                                // Determine the message based on sale state\n                                let timerMessage = \"🔥 Limited Time Sale!\";\n                                let urgencyMessage = \"Sale ends soon - grab yours now!\";\n                                if (isSaleUpcoming) {\n                                    timerMessage = \"⏰ Sale Starts Soon!\";\n                                    urgencyMessage = \"Get ready for an amazing deal!\";\n                                } else if (isSaleActive) {\n                                    timerMessage = \"🔥 Limited Time Sale!\";\n                                    urgencyMessage = \"Sale ends soon - don't miss out!\";\n                                }\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 p-4 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center gap-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-6 w-6 text-red-500 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 815,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-lg font-bold text-red-600\",\n                                                    children: timerMessage\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 816,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center mb-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_countdown__WEBPACK_IMPORTED_MODULE_15__.Countdown, {\n                                                endDate: countdownEndDate,\n                                                className: \"transform hover:scale-105 transition-transform duration-200\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 821,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-center text-sm font-medium text-gray-700\",\n                                            children: urgencyMessage\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 828,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 813,\n                                    columnNumber: 15\n                                }, this);\n                            })(),\n                            product.ShortDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-sm max-w-none mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: product.ShortDescription\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 838,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 837,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 border-t border-gray-200 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 846,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Choose your preferences from the options below.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 849,\n                                        columnNumber: 13\n                                    }, this),\n                                    Object.entries(groupedAttributes).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: Object.entries(groupedAttributes).map((param)=>{\n                                            let [groupId, attributes] = param;\n                                            var _attributes_, _attributes_1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            ((_attributes_ = attributes[0]) === null || _attributes_ === void 0 ? void 0 : _attributes_.DisplayName) || ((_attributes_1 = attributes[0]) === null || _attributes_1 === void 0 ? void 0 : _attributes_1.AttributeName),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 pl-4\",\n                                                        children: attributes.map((attr)=>{\n                                                            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                                                            const isSelected = !!selectedAttributes[attrKey];\n                                                            const isRadioGroup = attributes.length > 1;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center h-5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: isRadioGroup ? \"radio\" : \"checkbox\",\n                                                                            id: \"attr-\".concat(attrKey),\n                                                                            name: \"attr-group-\".concat(groupId),\n                                                                            className: \"h-4 w-4 \".concat(isRadioGroup ? \"rounded-full\" : \"rounded\", \" border-gray-300 text-primary focus:ring-primary\"),\n                                                                            checked: isSelected,\n                                                                            onChange: (e)=>handleAttributeChange(attr, e.target.checked, isRadioGroup)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                            lineNumber: 871,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 870,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-3 text-sm\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"attr-\".concat(attrKey),\n                                                                            className: \"font-medium \".concat(isSelected ? \"text-primary\" : \"text-gray-700\"),\n                                                                            children: [\n                                                                                attr.AttributeValueText,\n                                                                                (attr.PriceAdjustment || attr.PriceAdjustment === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-2 text-sm font-normal text-green-600\",\n                                                                                    children: [\n                                                                                        \"(\",\n                                                                                        attr.PriceAdjustmentType === 1 ? \"+\" : \"\",\n                                                                                        \"$\",\n                                                                                        attr.PriceAdjustment,\n                                                                                        \" \",\n                                                                                        attr.PriceAdjustmentType === 2 ? \"%\" : \"\",\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                                    lineNumber: 900,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                            lineNumber: 889,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 888,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, attrKey, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 869,\n                                                                columnNumber: 29\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 862,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, \"attr-group-\".concat(groupId), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 856,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 853,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No additional product details available.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 923,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 845,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Quantity:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 932,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between sm:justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: decrementQuantity,\n                                                            className: getButtonStyles(\"decrement\"),\n                                                            disabled: quantity <= (product.OrderMinimumQuantity || 1),\n                                                            \"aria-label\": \"Decrease quantity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"h-4 w-4 sm:h-5 sm:w-5\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                fill: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                    lineNumber: 947,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 935,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CounterDisplay, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 955,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: incrementQuantity,\n                                                            className: getButtonStyles(\"increment\"),\n                                                            disabled: product.OrderMaximumQuantity > 0 ? quantity >= Math.min(product.OrderMaximumQuantity, product.StockQuantity) : quantity >= product.StockQuantity,\n                                                            \"aria-label\": \"Increase quantity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"h-4 w-4 sm:h-5 sm:w-5\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                fill: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                    lineNumber: 977,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 971,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 957,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 934,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row sm:items-center gap-2 sm:ml-4\",\n                                                    children: [\n                                                        product.OrderMinimumQuantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Min: \",\n                                                                product.OrderMinimumQuantity\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.OrderMaximumQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Max:\",\n                                                                \" \",\n                                                                Math.min(product.OrderMaximumQuantity, product.StockQuantity)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 994,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 986,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 933,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 931,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 930,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-3 sm:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none text-sm sm:text-base\",\n                                        disabled: product.StockQuantity <= 0 || addingToCart,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToCart(true);\n                                            try {\n                                                var _product_ProductImagesJson_, _product_ProductImagesJson;\n                                                // Get the first product image or use a placeholder\n                                                const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0 ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL) : \"/placeholder.jpg\";\n                                                // Get selected attributes\n                                                const selectedAttrs = (product.AttributesJson || []).filter((attr)=>selectedAttributes[\"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID)]);\n                                                // Add to cart using the cart context with attributes and adjusted price\n                                                cart.addToCart({\n                                                    id: product.ProductId,\n                                                    name: product.ProductName,\n                                                    price: product.DiscountPrice || product.Price,\n                                                    discountPrice: product.DiscountPrice,\n                                                    image: productImage,\n                                                    originalPrice: product.Price\n                                                }, quantity, selectedAttrs, product.PriceIQD, rate // Pass currency rate as the fifth parameter\n                                                );\n                                                // Show modern toast notification\n                                                (0,_components_ui_modern_toast__WEBPACK_IMPORTED_MODULE_8__.showModernAddToCartToast)({\n                                                    productName: product.ProductName,\n                                                    quantity,\n                                                    productImage: ((_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : (_product_ProductImagesJson_ = _product_ProductImagesJson[0]) === null || _product_ProductImagesJson_ === void 0 ? void 0 : _product_ProductImagesJson_.AttachmentURL) || \"/placeholder.svg\",\n                                                    onViewCart: ()=>{\n                                                        window.location.href = \"/cart\";\n                                                    }\n                                                });\n                                            } catch (error) {\n                                                console.error(\"Error adding to cart:\", error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Failed to add product to cart. Please try again.\");\n                                            } finally{\n                                                setAddingToCart(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1074,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1076,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: addingToCart ? \"Adding...\" : \"Add to Cart\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1078,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1010,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none text-sm sm:text-base sm:flex-initial sm:min-w-[120px]\",\n                                        disabled: addingToWishlist,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToWishlist(true);\n                                            try {\n                                                // Check if product is already in wishlist\n                                                const isAlreadyInWishlist = wishlist.isInWishlist(product.ProductId);\n                                                if (isAlreadyInWishlist) {\n                                                    // Remove from wishlist if already there\n                                                    wishlist.removeFromWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(product.ProductName, \" removed from wishlist\"));\n                                                } else {\n                                                    var _product_ProductImagesJson_, _product_ProductImagesJson;\n                                                    // Add to wishlist\n                                                    const productUrl = \"/product/\".concat(product.ProductId);\n                                                    const imageUrl = ((_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : (_product_ProductImagesJson_ = _product_ProductImagesJson[0]) === null || _product_ProductImagesJson_ === void 0 ? void 0 : _product_ProductImagesJson_.AttachmentURL) || \"/placeholder.svg\";\n                                                    const price = product.DiscountPrice || product.Price;\n                                                    wishlist.addToWishlist(product.ProductId, product.ProductName, productUrl, imageUrl, price);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(product.ProductName, \" added to wishlist\"));\n                                                }\n                                            } catch (error) {\n                                                console.error(\"Error updating wishlist:\", error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Failed to update wishlist. Please try again.\");\n                                            } finally{\n                                                setAddingToWishlist(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1126,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-5 w-5\",\n                                                fill: product && wishlist.isInWishlist(product.ProductId) ? \"currentColor\" : \"none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1128,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: addingToWishlist ? \"Updating...\" : product && wishlist.isInWishlist(product.ProductId) ? \"Remove\" : \"Wishlist\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1137,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1082,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground text-sm sm:text-base sm:flex-initial sm:min-w-[100px]\",\n                                        onClick: ()=>{\n                                            if (navigator.share) {\n                                                navigator.share({\n                                                    title: (product === null || product === void 0 ? void 0 : product.MetaTitle) || (product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    text: (product === null || product === void 0 ? void 0 : product.MetaDescription) || \"Check out this product: \".concat(product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    url: window.location.href\n                                                }).catch((err)=>console.error(\"Error sharing:\", err));\n                                            } else {\n                                                // Fallback - copy to clipboard\n                                                navigator.clipboard.writeText(window.location.href);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Product link copied to clipboard\");\n                                            }\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1168,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: \"Share\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1169,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1147,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1008,\n                                columnNumber: 11\n                            }, this),\n                            product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-3\",\n                                        children: \"Product Tags\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1176,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs bg-white/70 hover:bg-white transition-colors\",\n                                                children: keyword.trim()\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1179,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1177,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1175,\n                                columnNumber: 13\n                            }, this),\n                            product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1195,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"About This Product\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 leading-relaxed\",\n                                        children: product.MetaDescription\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1193,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 751,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 741,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                    defaultValue: \"description\",\n                    className: \"w-full\",\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                            className: \"grid w-full grid-cols-3 mb-6 gap-2 bg-transparent p-0 h-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"description\",\n                                    className: \"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === \"description\" ? primaryColor : \"rgb(209 213 219)\",\n                                        color: activeTab === \"description\" ? primaryTextColor : \"rgb(55 65 81)\",\n                                        transform: activeTab === \"description\" ? \"scale(1.05)\" : \"scale(1)\",\n                                        boxShadow: activeTab === \"description\" ? \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\" : \"none\",\n                                        borderColor: activeTab === \"description\" ? primaryColor : \"transparent\"\n                                    },\n                                    children: \"Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"reviews\",\n                                    className: \"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === \"reviews\" ? primaryColor : \"rgb(209 213 219)\",\n                                        color: activeTab === \"reviews\" ? primaryTextColor : \"rgb(55 65 81)\",\n                                        transform: activeTab === \"reviews\" ? \"scale(1.05)\" : \"scale(1)\",\n                                        boxShadow: activeTab === \"reviews\" ? \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\" : \"none\",\n                                        borderColor: activeTab === \"reviews\" ? primaryColor : \"transparent\"\n                                    },\n                                    children: \"Specifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"shipping\",\n                                    className: \"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === \"shipping\" ? primaryColor : \"rgb(209 213 219)\",\n                                        color: activeTab === \"shipping\" ? primaryTextColor : \"rgb(55 65 81)\",\n                                        transform: activeTab === \"shipping\" ? \"scale(1.05)\" : \"scale(1)\",\n                                        boxShadow: activeTab === \"shipping\" ? \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\" : \"none\",\n                                        borderColor: activeTab === \"shipping\" ? primaryColor : \"transparent\"\n                                    },\n                                    children: \"Reviews\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1258,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"description\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Product Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1285,\n                                        columnNumber: 15\n                                    }, this),\n                                    product.FullDescription ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none\",\n                                        dangerouslySetInnerHTML: {\n                                            __html: product.FullDescription\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1287,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: product.ShortDescription || \"This is a high-quality medical product designed to meet professional standards.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1293,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900 mb-2\",\n                                                                children: \"Key Features\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1299,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-sm text-gray-700 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Professional grade quality\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1303,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Durable construction\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1304,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Easy to use\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1305,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Reliable performance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1306,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1302,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1298,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900 mb-2\",\n                                                                children: \"Benefits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1310,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-sm text-gray-700 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Enhanced efficiency\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1314,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Cost-effective solution\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1315,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Long-lasting durability\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1316,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Professional results\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1317,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1313,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1309,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1297,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1292,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1284,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1280,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"reviews\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Product Specifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1331,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_specifications__WEBPACK_IMPORTED_MODULE_13__.ProductSpecifications, {\n                                        attributes: product.AttributesJson || []\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1334,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1330,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1326,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"shipping\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Customer Reviews\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            1,\n                                                            2,\n                                                            3,\n                                                            4,\n                                                            5\n                                                        ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Clock_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"w-6 h-6 \".concat(star <= Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                            }, star, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1350,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1348,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: ((_product_Rating = product.Rating) === null || _product_Rating === void 0 ? void 0 : _product_Rating.toFixed(1)) || \"0.0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1361,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            \"out of 5\",\n                                                            product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \" \",\n                                                                    \"• \",\n                                                                    product.TotalReviews,\n                                                                    \" review\",\n                                                                    product.TotalReviews !== 1 ? \"s\" : \"\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1366,\n                                                                columnNumber: 23\n                                                            }, this) : null\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1360,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1347,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_product_reviews_display__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    productId: product.ProductId,\n                                                    showTitle: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1376,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1375,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1346,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1344,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1340,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 1208,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 1207,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n        lineNumber: 711,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDetailsClient, \"fhF87DY2zm3u0DvUJErcoKedGAA=\", false, function() {\n    return [\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__.useWishlist,\n        _contexts_currency_context__WEBPACK_IMPORTED_MODULE_11__.useCurrency,\n        _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings\n    ];\n});\n_c = ProductDetailsClient;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductDetailsClient);\nvar _c;\n$RefreshReg$(_c, \"ProductDetailsClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/product-details-client.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/countdown.tsx":
/*!*************************************!*\
  !*** ./components/ui/countdown.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Countdown: () => (/* binding */ Countdown)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ Countdown auto */ \nvar _s = $RefreshSig$();\n\nfunction Countdown(param) {\n    let { endDate, className } = param;\n    _s();\n    const [countdown, setCountdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        days: 0,\n        hours: 0,\n        minutes: 0,\n        seconds: 0,\n        completed: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Countdown.useEffect\": ()=>{\n            const targetDate = new Date(endDate).getTime();\n            const interval = setInterval({\n                \"Countdown.useEffect.interval\": ()=>{\n                    const now = new Date().getTime();\n                    const difference = targetDate - now;\n                    if (difference < 0) {\n                        clearInterval(interval);\n                        setCountdown({\n                            \"Countdown.useEffect.interval\": (prev)=>({\n                                    ...prev,\n                                    completed: true\n                                })\n                        }[\"Countdown.useEffect.interval\"]);\n                        return;\n                    }\n                    setCountdown({\n                        days: Math.floor(difference / (1000 * 60 * 60 * 24)),\n                        hours: Math.floor(difference % (1000 * 60 * 60 * 24) / (1000 * 60 * 60)),\n                        minutes: Math.floor(difference % (1000 * 60 * 60) / (1000 * 60)),\n                        seconds: Math.floor(difference % (1000 * 60) / 1000),\n                        completed: false\n                    });\n                }\n            }[\"Countdown.useEffect.interval\"], 1000);\n            return ({\n                \"Countdown.useEffect\": ()=>clearInterval(interval)\n            })[\"Countdown.useEffect\"];\n        }\n    }[\"Countdown.useEffect\"], [\n        endDate\n    ]);\n    if (countdown.completed) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            className: className,\n            children: \"Offer expired\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n            lineNumber: 55,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"inline-flex items-center gap-0.5 sm:gap-2 bg-white rounded-lg p-1.5 sm:p-3 shadow-sm border \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-6 h-6 sm:w-12 sm:h-12 bg-white border border-red-400 sm:border-2 rounded sm:rounded-lg flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-[10px] sm:text-xl font-bold text-red-500\",\n                            children: countdown.days.toString().padStart(2, \"0\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-[8px] sm:text-xs font-medium text-red-500 mt-0.5 sm:mt-1\",\n                        children: \"Days\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-6 h-6 sm:w-12 sm:h-12 bg-white border border-red-400 sm:border-2 rounded sm:rounded-lg flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-[10px] sm:text-xl font-bold text-red-500\",\n                            children: countdown.hours.toString().padStart(2, \"0\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-[8px] sm:text-xs font-medium text-red-500 mt-0.5 sm:mt-1\",\n                        children: \"Hrs\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-6 h-6 sm:w-12 sm:h-12 bg-white border border-red-400 sm:border-2 rounded sm:rounded-lg flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-[10px] sm:text-xl font-bold text-red-500\",\n                            children: countdown.minutes.toString().padStart(2, \"0\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-[8px] sm:text-xs font-medium text-red-500 mt-0.5 sm:mt-1\",\n                        children: \"Min\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-6 h-6 sm:w-12 sm:h-12 bg-white border border-red-400 sm:border-2 rounded sm:rounded-lg flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-[10px] sm:text-xl font-bold text-red-500\",\n                            children: countdown.seconds.toString().padStart(2, \"0\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-[8px] sm:text-xs font-medium text-red-500 mt-0.5 sm:mt-1\",\n                        children: \"Sec\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\ui\\\\countdown.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n_s(Countdown, \"1UfaAP6t/hUI4ZLjOPlQm4IcCuM=\");\n_c = Countdown;\nvar _c;\n$RefreshReg$(_c, \"Countdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/countdown.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/clock.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Clock)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.446.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Clock = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Clock\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"polyline\",\n        {\n            points: \"12 6 12 12 16 14\",\n            key: \"68esgv\"\n        }\n    ]\n]);\n //# sourceMappingURL=clock.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\n"));

/***/ })

});