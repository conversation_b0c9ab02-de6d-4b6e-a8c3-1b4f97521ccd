(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1778],{27737:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var a=r(95155),s=r(53999);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,s.cn)("animate-pulse rounded-md bg-muted",t),...r})}},29799:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},34964:(e,t,r)=>{"use strict";r.d(t,{Xi:()=>i,av:()=>c,j7:()=>d,tU:()=>o});var a=r(95155),s=r(12115),n=r(30064),l=r(53999);let o=n.bL,d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.B8,{ref:t,className:(0,l.cn)("inline-flex h-8 items-center justify-center rounded-md bg-muted p-0.5 text-muted-foreground",r),...s})});d.displayName=n.B8.displayName;let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.l9,{ref:t,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-2 py-1 text-xs font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...s})});i.displayName=n.l9.displayName;let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.UC,{ref:t,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...s})});c.displayName=n.UC.displayName},40646:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},47924:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},50477:()=>{},53904:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var a=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},54807:(e,t,r)=>{"use strict";r.d(t,{DW:()=>i,PA:()=>d,Rb:()=>l,pN:()=>o});var a=r(32109),s=r.n(a);let n=r(49509).env.NEXT_PUBLIC_ENCRYPTION_KEY||"your-secret-key-change-this-in-production";function l(e){try{let t=e.toString();return s().AES.encrypt(t,n).toString().replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}catch(t){return console.error("Encryption error:",t),e.toString()}}function o(e){try{let t=e.replace(/-/g,"+").replace(/_/g,"/");for(;t.length%4;)t+="=";let r=s().AES.decrypt(t,n).toString(s().enc.Utf8);if(!r)throw Error("Failed to decrypt value");return r}catch(t){return console.error("Decryption error:",t),e}}function d(e){return l(e)}function i(e){return o(e)}},55436:(e,t,r)=>{Promise.resolve().then(r.bind(r,68603))},68603:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var a=r(95155),s=r(12115),n=r(84995),l=r(88482),o=r(97168),d=r(34964),i=r(27737),c=r(6874),m=r.n(c),u=r(79891),x=r(98816),g=r(61204),p=r(37108),f=r(29799),h=r(40646),b=r(53904),y=r(85339),v=r(47924);let N=(0,r(19946).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var j=r(13052),w=r(54807);function A(){let{t:e,primaryColor:t,primaryTextColor:r}=(0,u.t)(),{user:c,isLoggedIn:A,isLoading:C,token:O}=(0,x.J)(),[S,I]=(0,s.useState)("all"),[k,D]=(0,s.useState)([]),[P,T]=(0,s.useState)(!0),[U,L]=(0,s.useState)(null),[E,M]=(0,s.useState)([]),[R,B]=(0,s.useState)(!1),[H,F]=(0,s.useState)(""),[_,q]=(0,s.useState)("date-desc"),z=(0,s.useCallback)(async()=>{T(!0);try{let e={"Content-Type":"application/json",Accept:"application/json"};O&&(e.Authorization="Bearer ".concat(O));let t=await fetch("/api/orders/history",{method:"POST",headers:e,body:JSON.stringify({requestParameters:{recordValueJson:"[]"}})}),r=await t.json();if(console.log("Orders API response:",r),r&&r.data){let e;if(e="string"==typeof r.data?JSON.parse(r.data):r.data,console.log("Parsed order data:",e),Array.isArray(e)){console.log("First order structure:",e[0]);let t=e.map(e=>({id:e.OrderId,OrderID:e.OrderId,OrderNumber:e.OrderNumber,date:e.OrderDateUTC?new Date(e.OrderDateUTC).toLocaleDateString():"N/A",OrderDate:e.OrderDateUTC,total:e.OrderTotal||0,OrderTotal:e.OrderTotal||0,TotalAmount:e.OrderTotal||0,status:e.LatestStatusName||"Active",StatusID:"Active"===e.LatestStatusName?1:"In Progress"===e.LatestStatusName?2:"Completed"===e.LatestStatusName?3:"Returned"===e.LatestStatusName?4:"Refunded"===e.LatestStatusName?5:"Cancelled"===e.LatestStatusName?6:1,ItemCount:e.TotalItems||0,items:e.OrderItems||[],OrderItems:e.OrderItems||[]})).sort((e,t)=>new Date(t.OrderDate||0).getTime()-new Date(e.OrderDate||0).getTime());D(t),console.log("Mapped and sorted orders:",t)}else D([])}}catch(e){console.error("Error fetching order history:",e)}finally{T(!1)}},[O]);if((0,s.useCallback)(async e=>{B(!0);try{let t={"Content-Type":"application/json",Accept:"application/json"};O&&(t.Authorization="Bearer ".concat(O));let r=await fetch("/api/orders/details",{method:"POST",headers:t,body:JSON.stringify({requestParameters:{OrderId:e,recordValueJson:"[]"}})}),a=await r.json();if(a&&a.data){let e=JSON.parse(a.data);Array.isArray(e)&&M(e)}}catch(e){console.error("Error fetching order details:",e)}finally{B(!1)}},[O]),(0,s.useEffect)(()=>{let e=(null==c?void 0:c.UserID)||(null==c?void 0:c.UserId);A&&e&&z()},[A,c,z]),(0,s.useEffect)(()=>{C||A||(window.location.href="/login?redirect=/orders")},[C,A]),C)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-lg text-muted-foreground",children:"Loading..."})]})});if(!A)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{className:"text-lg text-muted-foreground",children:"Redirecting to login..."})})});let J=P?[]:k,X="all"===S?J:J.filter(e=>(e.StatusID||e.OrderStatusId||e.StateId||1).toString()===S),V=[...""===H.trim()?X:X.filter(e=>{let t=H.toLowerCase(),r=(e.OrderNumber||"OR#".concat(String(e.OrderId||e.OrderID).padStart(8,"0"))).toLowerCase(),a=(e.items||e.OrderItems||[]).map(e=>(e.name||e.ProductName||e.ItemName||"").toLowerCase()).join(" ");return r.includes(t)||a.includes(t)})].sort((e,t)=>{switch(_){case"date-desc":return new Date(t.OrderDate||0).getTime()-new Date(e.OrderDate||0).getTime();case"date-asc":return new Date(e.OrderDate||0).getTime()-new Date(t.OrderDate||0).getTime();case"total-desc":return(t.total||t.OrderTotal||t.TotalAmount||0)-(e.total||e.OrderTotal||e.TotalAmount||0);case"total-asc":return(e.total||e.OrderTotal||e.TotalAmount||0)-(t.total||t.OrderTotal||t.TotalAmount||0);default:return 0}});return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,a.jsx)(n.Qp,{className:"mb-6",children:(0,a.jsxs)(n.AB,{children:[(0,a.jsx)(n.J5,{children:(0,a.jsx)(n.w1,{asChild:!0,children:(0,a.jsx)(m(),{href:"/",children:e("home")})})}),(0,a.jsx)(n.tH,{}),(0,a.jsx)(n.J5,{children:(0,a.jsx)(n.tJ,{children:e("orders")})})]})}),(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:e("orders")}),(0,a.jsxs)(o.$,{variant:"outline",size:"sm",onClick:z,disabled:P,className:"flex items-center gap-2",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 ".concat(P?"animate-spin":"")}),P?"Loading...":"Refresh"]})]}),(0,a.jsx)(d.tU,{defaultValue:"all",className:"mb-6",onValueChange:I,children:(0,a.jsxs)(d.j7,{className:"grid w-full grid-cols-5 mb-6 gap-2 bg-transparent p-0 h-auto",children:[(0,a.jsx)(d.Xi,{value:"all",className:"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"all"===S?t:"rgb(209 213 219)",color:"all"===S?r:"rgb(55 65 81)",transform:"all"===S?"scale(1.05)":"scale(1)",boxShadow:"all"===S?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"all"===S?t:"transparent"},children:"All Orders"}),(0,a.jsx)(d.Xi,{value:"1",className:"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"1"===S?t:"rgb(209 213 219)",color:"1"===S?r:"rgb(55 65 81)",transform:"1"===S?"scale(1.05)":"scale(1)",boxShadow:"1"===S?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"1"===S?t:"transparent"},children:"Active"}),(0,a.jsx)(d.Xi,{value:"2",className:"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"2"===S?t:"rgb(209 213 219)",color:"2"===S?r:"rgb(55 65 81)",transform:"2"===S?"scale(1.05)":"scale(1)",boxShadow:"2"===S?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"2"===S?t:"transparent"},children:"In Progress"}),(0,a.jsx)(d.Xi,{value:"3",className:"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"3"===S?t:"rgb(209 213 219)",color:"3"===S?r:"rgb(55 65 81)",transform:"3"===S?"scale(1.05)":"scale(1)",boxShadow:"3"===S?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"3"===S?t:"transparent"},children:"Completed"}),(0,a.jsx)(d.Xi,{value:"6",className:"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"6"===S?t:"rgb(209 213 219)",color:"6"===S?r:"rgb(55 65 81)",transform:"6"===S?"scale(1.05)":"scale(1)",boxShadow:"6"===S?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"6"===S?t:"transparent"},children:"Cancelled"})]})}),(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"relative w-full md:w-64",children:[(0,a.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)("input",{type:"text",placeholder:"Search orders...",value:H,onChange:e=>F(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Sort by:"}),(0,a.jsxs)("select",{value:_,onChange:e=>q(e.target.value),className:"border border-input rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,a.jsx)("option",{value:"date-desc",children:"Date (Newest)"}),(0,a.jsx)("option",{value:"date-asc",children:"Date (Oldest)"}),(0,a.jsx)("option",{value:"total-desc",children:"Amount (High to Low)"}),(0,a.jsx)("option",{value:"total-asc",children:"Amount (Low to High)"})]})]})]}),P?(0,a.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((e,t)=>(0,a.jsx)(l.Zp,{className:"overflow-hidden",children:(0,a.jsx)("div",{className:"border-b border-border p-4",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(i.E,{className:"h-5 w-32"}),(0,a.jsx)(i.E,{className:"h-6 w-20 rounded-full"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground mb-3",children:[(0,a.jsx)(i.E,{className:"h-4 w-24"}),(0,a.jsx)(i.E,{className:"h-4 w-16"}),(0,a.jsx)(i.E,{className:"h-4 w-20"})]}),(0,a.jsx)(i.E,{className:"h-16 w-full rounded-md"})]}),(0,a.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,a.jsx)(i.E,{className:"h-6 w-20"}),(0,a.jsx)(i.E,{className:"h-8 w-24"})]})]})})},t))}):V.length>0?(0,a.jsx)("div",{className:"space-y-4",children:V.map((e,t)=>{var r,s,n,d,i,c;return(0,a.jsxs)(l.Zp,{className:"overflow-hidden",children:[(0,a.jsx)("div",{className:"border-b border-border p-4",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)("h3",{className:"font-medium",children:e.OrderNumber||"OR#".concat(String(e.OrderId||e.OrderID).padStart(8,"0"))}),(()=>{let t=e.LatestStatusName;if(t){let e="bg-blue-100 text-blue-800",r=(0,a.jsx)(p.A,{className:"h-5 w-5 text-blue-500"});switch(t.toLowerCase()){case"active":e="bg-blue-100 text-blue-800",r=(0,a.jsx)(p.A,{className:"h-5 w-5 text-blue-500"});break;case"in progress":case"processing":e="bg-orange-100 text-orange-800",r=(0,a.jsx)(f.A,{className:"h-5 w-5 text-orange-500"});break;case"completed":case"delivered":e="bg-green-100 text-green-800",r=(0,a.jsx)(h.A,{className:"h-5 w-5 text-green-500"});break;case"cancelled":e="bg-red-100 text-red-800",r=(0,a.jsx)(y.A,{className:"h-5 w-5 text-red-500"});break;default:e="bg-gray-100 text-gray-800",r=(0,a.jsx)(p.A,{className:"h-5 w-5 text-gray-500"})}return(0,a.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 rounded-full text-xs ".concat(e),children:[r,(0,a.jsx)("span",{children:t})]})}else{let t=(e=>{switch(parseInt((null==e?void 0:e.toString())||"1")){case 1:return{name:"Active",icon:(0,a.jsx)(p.A,{className:"h-5 w-5 text-blue-500"}),color:"bg-blue-100 text-blue-800"};case 2:return{name:"In Progress",icon:(0,a.jsx)(f.A,{className:"h-5 w-5 text-orange-500"}),color:"bg-orange-100 text-orange-800"};case 3:return{name:"Completed",icon:(0,a.jsx)(h.A,{className:"h-5 w-5 text-green-500"}),color:"bg-green-100 text-green-800"};case 4:return{name:"Returned",icon:(0,a.jsx)(b.A,{className:"h-5 w-5 text-purple-500"}),color:"bg-purple-100 text-purple-800"};case 5:return{name:"Refunded",icon:(0,a.jsx)(b.A,{className:"h-5 w-5 text-indigo-500"}),color:"bg-indigo-100 text-indigo-800"};case 6:return{name:"Cancelled",icon:(0,a.jsx)(y.A,{className:"h-5 w-5 text-red-500"}),color:"bg-red-100 text-red-800"};default:return{name:"Unknown",icon:(0,a.jsx)(p.A,{className:"h-5 w-5 text-gray-500"}),color:"bg-gray-100 text-gray-800"}}})(e.StatusID||e.OrderStatusId||e.StateId||1);return(0,a.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 rounded-full text-xs ".concat(t.color),children:[t.icon,(0,a.jsx)("span",{children:t.name})]})}})()]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mb-2",children:["Ordered on ",(()=>{let t=e.OrderDateUTC||e.date||e.OrderDate||e.CreatedOn;if(t)try{return new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}catch(e){return t}return"N/A"})()]}),(e.items||e.OrderItems||[]).length>0&&(0,a.jsxs)("div",{className:"bg-blue-50 px-3 py-2 rounded-md",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-blue-900 mb-1",children:"Products in this order:"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800 space-y-1",children:[(e.items||e.OrderItems||[]).slice(0,3).map((t,r)=>{let s=t.name||t.ProductName||t.ItemName||"Medical Product",n=t.ProductID||t.ProductId;return(0,a.jsxs)("span",{children:[n?(0,a.jsx)(m(),{href:"/product/".concat(n),className:"text-blue-600 hover:text-blue-800 hover:underline font-medium",target:"_blank",rel:"noopener noreferrer",children:s}):(0,a.jsx)("span",{className:"font-medium",children:s}),r<Math.min(2,(e.items||e.OrderItems||[]).length-1)&&" • "]},r)}),(e.items||e.OrderItems||[]).length>3&&(0,a.jsxs)("span",{className:"text-blue-600",children:[" • +",(e.items||e.OrderItems||[]).length-3," more items"]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 mt-4 md:mt-0",children:[(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"font-medium",children:["$",(e.total||e.OrderTotal||e.TotalAmount||0).toFixed(2)]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.TotalItems||(null==(r=e.items)?void 0:r.length)||(null==(s=e.OrderItems)?void 0:s.length)||e.ItemCount||0," item(s)"]})]}),(0,a.jsx)(o.$,{variant:"outline",size:"sm",className:"flex items-center gap-1",asChild:!0,children:(0,a.jsxs)(m(),{href:(e=>{let t=e.OrderId||e.OrderID||e.id,r=(0,w.PA)(t),a=(e.total||e.OrderTotal||e.TotalAmount||0).toFixed(2),s=(()=>{let t=e.OrderDateUTC||e.date||e.OrderDate||e.CreatedOn;if(t)try{return new Date(t).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}catch(e){}return"N/A"})(),n=(0,w.Rb)(a),l=(0,w.Rb)(s);return"/orders/".concat(r,"?t=").concat(n,"&d=").concat(l)})(e),children:[(0,a.jsx)(N,{className:"h-4 w-4"}),"Details",(0,a.jsx)(j.A,{className:"h-4 w-4"})]})})]})]})}),(0,a.jsxs)("div",{className:"p-4 bg-muted/30",children:[(0,a.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Order Items Preview"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(e.items||e.OrderItems||[]).slice(0,3).map((e,t)=>{let r=e.ProductImageUrl||e.ImageUrl||e.ProductImage;return(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-muted rounded-md overflow-hidden flex items-center justify-center",children:r?(0,a.jsx)("img",{src:r.startsWith("http")?r:"".concat(g.T.ADMIN_BASE_URL).concat(r),alt:e.name||e.ProductName||"Product",className:"w-full h-full object-cover",onError:e=>{let t=e.target;t.style.display="none";let r=t.parentElement;r&&(r.innerHTML='<svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>')}}):(0,a.jsx)(p.A,{className:"h-6 w-6 text-gray-400"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name||e.ProductName||e.ItemName||"Medical Product"}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Qty: ",e.quantity||e.Quantity||e.OrderQuantity||1]}),e.ProductDescription&&(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1 line-clamp-1",children:e.ProductDescription})]})]}),(0,a.jsxs)("p",{className:"text-sm font-medium",children:["$",(e.price||e.Price||e.UnitPrice||e.ItemPrice||0).toFixed(2)]})]},t)}),((null==(n=e.items)?void 0:n.length)||(null==(d=e.OrderItems)?void 0:d.length)||0)>3&&(0,a.jsxs)("div",{className:"text-xs text-muted-foreground text-center pt-2",children:["+",((null==(i=e.items)?void 0:i.length)||(null==(c=e.OrderItems)?void 0:c.length)||0)-3," more items"]})]})]})]},e.OrderId||e.OrderID||e.id||t)})}):(0,a.jsx)(l.Zp,{children:(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(p.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No orders found"}),(0,a.jsxs)("p",{className:"text-muted-foreground mb-4",children:["You don't have any ","all"!==S?S:""," orders yet."]}),(0,a.jsx)(o.$,{asChild:!0,children:(0,a.jsx)(m(),{href:"/",children:"Continue Shopping"})})]})})]})]})}},79891:(e,t,r)=>{"use strict";r.d(t,{Z:()=>d,t:()=>i});var a=r(95155),s=r(12115);let n={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us",searchProducts:"Search products..."},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",searchProducts:"ابحث عن المنتجات...",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}};var l=r(94213);let o=(0,s.createContext)(void 0);function d(e){let{children:t}=e,[r,d]=(0,s.useState)("light"),[i,c]=(0,s.useState)("en"),[m,u]=(0,s.useState)("#0074b2"),[x,g]=(0,s.useState)("#ffffff");return(0,s.useEffect)(()=>{let e=(0,l.N)(m);g(e),document.documentElement.style.setProperty("--primary",m),document.documentElement.style.setProperty("--primary-foreground",e)},[m]),(0,a.jsx)(o.Provider,{value:{theme:r,language:i,primaryColor:m,primaryTextColor:x,toggleTheme:()=>{d("light"===r?"dark":"light")},setLanguage:e=>{c(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{u(e);let t=(0,l.N)(e);g(t),document.documentElement.style.setProperty("--primary",e),document.documentElement.style.setProperty("--primary-foreground",t)},t:e=>(function(e,t){let r=n[t];return e in r?r[e]:"en"!==t&&e in n.en?n.en[e]:e})(e,i)},children:t})}function i(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},84995:(e,t,r)=>{"use strict";r.d(t,{AB:()=>i,J5:()=>c,Qp:()=>d,tH:()=>x,tJ:()=>u,w1:()=>m});var a=r(95155),s=r(12115),n=r(99708),l=r(13052),o=(r(5623),r(53999));let d=s.forwardRef((e,t)=>{let{...r}=e;return(0,a.jsx)("nav",{ref:t,"aria-label":"breadcrumb",...r})});d.displayName="Breadcrumb";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("ol",{ref:t,className:(0,o.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",r),...s})});i.displayName="BreadcrumbList";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("li",{ref:t,className:(0,o.cn)("inline-flex items-center gap-1.5",r),...s})});c.displayName="BreadcrumbItem";let m=s.forwardRef((e,t)=>{let{asChild:r,className:s,...l}=e,d=r?n.DX:"a";return(0,a.jsx)(d,{ref:t,className:(0,o.cn)("transition-colors hover:text-foreground",s),...l})});m.displayName="BreadcrumbLink";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,o.cn)("font-normal text-foreground",r),...s})});u.displayName="BreadcrumbPage";let x=e=>{let{children:t,className:r,...s}=e;return(0,a.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,o.cn)("[&>svg]:size-3.5",r),...s,children:null!=t?t:(0,a.jsx)(l.A,{})})};x.displayName="BreadcrumbSeparator"},85339:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>d,Zp:()=>l,aR:()=>o,wL:()=>m});var a=r(95155),s=r(12115),n=r(53999);let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});l.displayName="Card";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...s})});o.displayName="CardHeader";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});d.displayName="CardTitle";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...s})});i.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...s})});c.displayName="CardContent";let m=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...s})});m.displayName="CardFooter"},94213:(e,t,r)=>{"use strict";function a(e,t){let r=e=>{let t=e.replace("#",""),r=parseInt(t.slice(0,2),16)/255,a=[r,parseInt(t.slice(2,4),16)/255,parseInt(t.slice(4,6),16)/255].map(e=>e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4));return .2126*a[0]+.7152*a[1]+.0722*a[2]},a=r(e),s=r(t);return(Math.max(a,s)+.05)/(Math.min(a,s)+.05)}function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"AA",r=a(e,"#ffffff"),s=a(e,"#000000"),n="AAA"===t?7:4.5;return r>=n&&s>=n?r>s?"#ffffff":"#000000":r>=n?"#ffffff":s>=n?"#000000":r>s?"#ffffff":"#000000"}r.d(t,{N:()=>s})},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>i,r:()=>d});var a=r(95155),s=r(12115),n=r(99708),l=r(74466),o=r(53999);let d=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=s.forwardRef((e,t)=>{let{className:r,variant:s,size:l,asChild:i=!1,...c}=e,m=i?n.DX:"button";return(0,a.jsx)(m,{className:(0,o.cn)(d({variant:s,size:l,className:r})),ref:t,...c})});i.displayName="Button"}},e=>{e.O(0,[4277,3464,4706,3942,5371,9637,8816,8441,5964,7358],()=>e(e.s=55436)),_N_E=e.O()}]);