'use client';

import { useState, useEffect } from 'react';
import { AxiosError } from 'axios';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Define the error response type
interface ErrorResponse {
  error: string;
  [key: string]: any;
}

interface AxiosErrorResponse {
  data?: ErrorResponse;
  status?: number;
  statusText?: string;
  headers?: any;
  config?: any;
}

interface CustomAxiosError extends Error {
  isAxiosError: boolean;
  response?: AxiosErrorResponse;
  config?: any;
  code?: string;
  request?: any;
}
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useSettings } from '@/contexts/settings-context';
import { useCart } from '@/contexts/cart-context';
import { useWishlist } from '@/contexts/wishlist-context';
import { Heart, ShoppingCart, Trash2, Eye, Loader2, ChevronRight } from 'lucide-react';
import axios from 'axios';
import { toast } from 'sonner';
import { showModernAddToCartToast } from '@/components/ui/modern-toast';

// Define the type for wishlist display items
type WishlistDisplayItem = {
  id: number;
  name: string;
  price: number;
  originalPrice?: number; // Added to support displaying original price
  imageUrl: string;
  inStock: boolean;
};

// Product type from API
type Product = {
  ProductId: number;
  ProductName: string;
  ProductPrice: number;
  ProductImagesJson: string;
  ProductQuantity: number;
  ProductDescription?: string;
  CategoryName?: string;
  ManufacturerName?: string;
};

// Helper function to parse product images
const parseProductImages = (productImagesJson: string) => {
  if (!productImagesJson) return [];
  
  try {
    // Try to parse as JSON first
    if (productImagesJson.startsWith('[') || productImagesJson.startsWith('{')) {
      const parsed = JSON.parse(productImagesJson);
      if (Array.isArray(parsed)) return parsed;
      if (parsed && typeof parsed === 'object') return [parsed];
    }
    
    // Handle as string path
    const trimmedPath = productImagesJson.trim();
    if (trimmedPath) {
      return [{
        AttachmentName: trimmedPath.split('/').pop() || 'image',
        AttachmentURL: trimmedPath,
        IsPrimary: true
      }];
    }
  } catch (error) {
    console.error('Error parsing product images:', error);
  }
  
  return [];
};

// Helper function to construct image URL with improved fallback handling
const constructImageUrl = (attachmentURL: string): string => {
  if (!attachmentURL || typeof attachmentURL !== 'string') {
    return '/placeholder-image.jpg';
  }
  
  try {
    // Clean the URL string
    const cleanUrl = attachmentURL.trim();
    
    // If it's already a full URL, normalize it (remove duplicate slashes in pathname)
    if (cleanUrl.startsWith('http://') || cleanUrl.startsWith('https://')) {
      try {
        const u = new URL(cleanUrl);
        u.pathname = u.pathname.replace(/\/+/g, '/');
        return u.toString();
      } catch {
        // Fallback-safe normalization without affecting protocol
        const match = cleanUrl.match(/^(https?:\/\/[^/]+)(\/.*)?$/);
        if (match) {
          const origin = match[1];
          const path = (match[2] || '/').replace(/\/+/g, '/');
          return `${origin}${path}`;
        }
        return cleanUrl;
      }
    }
    
    // Use environment variable for admin base URL
    const baseUrl = process.env.NEXT_PUBLIC_ADMIN_BASE_URL || 'https://admin.codemedicalapps.com';
    
    // Normalize base URL (remove trailing slash if present)
    const normalizedBaseUrl = baseUrl.replace(/\/$/, '');
    
    // Normalize path - first remove any leading/trailing slashes, then add exactly one leading slash
    let normalizedPath = cleanUrl.replace(/^\/+|\/+$/g, '');
    normalizedPath = `/${normalizedPath}`;
    
    // Remove any double slashes within the path
    normalizedPath = normalizedPath.replace(/\/+/g, '/');
    
    // Construct final URL
    const finalUrl = `${normalizedBaseUrl}${normalizedPath}`;
    return finalUrl;
  } catch (error) {
    console.error('Error constructing image URL:', error, 'URL:', attachmentURL);
    return '/placeholder-image.jpg';
  }
};

// Helper function to preload images for better caching
const preloadImage = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
  });
};

// Function to preload all wishlist images
const preloadWishlistImages = async (items: WishlistDisplayItem[]) => {
  const imageCache = JSON.parse(localStorage.getItem('wishlist_image_cache') || '{}');
  const now = Date.now();
  const cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours
  
  for (const item of items) {
    const cacheKey = item.id;
    const cached = imageCache[cacheKey];
    
    // Skip if recently cached and successful
    if (cached && cached.success && (now - cached.timestamp) < cacheExpiry) {
      continue;
    }
    
    // Preload the image
     const success = await preloadImage(item.imageUrl);
     imageCache[cacheKey] = {
       url: item.imageUrl,
       timestamp: now,
       success
     };
  }
  
  localStorage.setItem('wishlist_image_cache', JSON.stringify(imageCache));
};

export default function WishlistPage() {
  const { t } = useSettings();
  const cart = useCart();
  const { wishlistItems, removeFromWishlist, isHydrated } = useWishlist();
  
  // State to hold the display items (products with details)
  const [displayItems, setDisplayItems] = useState<WishlistDisplayItem[]>([]);
  const [loading, setLoading] = useState(false);
  


  // Define types for API response
  interface ProductResponse {
    id?: number;
    ProductId?: number;
    ProductName?: string;
    Name?: string;
    Price?: number;
    ProductPrice?: number;
    OldPrice?: number;
    OriginalPrice?: number;
    ProductImagesJson?: string;
    StockQuantity?: number;
    Quantity?: number;
    [key: string]: any;
  }

  interface ApiResponse {
    data?: ProductResponse | ProductResponse[] | { data: ProductResponse | ProductResponse[] };
    products?: ProductResponse | ProductResponse[];
    [key: string]: any;
  }

  // Function to process wishlist items and fetch additional details if needed
  const processWishlistItems = async (wishlistItems: any[]) => {
    if (!wishlistItems || wishlistItems.length === 0) {
      setDisplayItems([]);
      return;
    }

    // Check if items are in new format (objects) or old format (numbers)
    const isNewFormat = wishlistItems.length > 0 && typeof wishlistItems[0] === 'object';
    
    if (isNewFormat) {
      // New format: items already contain full details
      const itemsToDisplay = wishlistItems.map(item => {
        // Properly construct the image URL from the stored imageUrl
        let processedImageUrl = '/placeholder-image.jpg';
        if (item.imageUrl) {
          // If the imageUrl is already a full URL, use it as is
          if (item.imageUrl.startsWith('http://') || item.imageUrl.startsWith('https://')) {
            processedImageUrl = item.imageUrl;
          } else {
            // If it's a relative path, construct the full URL
            processedImageUrl = constructImageUrl(item.imageUrl);
          }
        }

        return {
          id: item.productId,
          name: item.productName || 'Unnamed Product',
          price: item.price || 0,
          originalPrice: item.price || 0,
          imageUrl: processedImageUrl,
          inStock: true // Default to true since we don't have stock info in wishlist
        };
      });

      setDisplayItems(itemsToDisplay);
      return;
    }
    
    // Old format: items are just product IDs, need to fetch details
    const productIds = wishlistItems.filter(id => id && !isNaN(Number(id)));
    console.log('Valid product IDs after filtering:', productIds);
    
    if (productIds.length === 0) {
      console.log('No valid product IDs found, setting empty display items');
      setDisplayItems([]);
      return;
    }

    console.log('Starting to fetch product details for:', productIds.length, 'products');
    setLoading(true);
    
    try {
      console.log('Fetching products for IDs:', productIds);
      
      // Check if we have cached products
      const cachedProducts = localStorage.getItem('cachedProducts');
      if (cachedProducts) {
        try {
          const allProducts: ProductResponse[] = JSON.parse(cachedProducts);
          const wishlistProducts = allProducts.filter(product =>
            productIds.includes(product.ProductID || product.ProductId || product.id || 0)
          );
          
          if (wishlistProducts.length > 0) {
            console.log('Using cached products:', wishlistProducts.length);
            
            const itemsToDisplay = wishlistProducts.map(product => {
              let imageUrl = '';

              try {
                // Try to parse ProductImagesJson if it exists and is a string
                if (product.ProductImagesJson && typeof product.ProductImagesJson === 'string') {
                  const images = parseProductImages(product.ProductImagesJson);
                  const primaryImage = images.find((img: any) => img.IsPrimary) || images[0];
                  if (primaryImage) {
                    imageUrl = constructImageUrl(primaryImage.AttachmentURL || primaryImage.url || primaryImage);
                  }
                }
                // Fallback to ImagePath if available
                if (!imageUrl && product.ImagePath) {
                  imageUrl = constructImageUrl(product.ImagePath);
                }
                // Additional fallback to ImageUrl
                if (!imageUrl && product.ImageUrl) {
                  imageUrl = constructImageUrl(product.ImageUrl);
                }
                // Try DefaultImage property
                if (!imageUrl && product.DefaultImage) {
                  imageUrl = constructImageUrl(product.DefaultImage);
                }
              } catch (error) {
                console.error('Error processing cached product images:', error);
              }

              return {
                  id: product.ProductID || product.ProductId || product.id || 0,
                  name: product.ProductName || product.Name || 'Unnamed Product',
                  price: product.Price || product.ProductPrice || 0,
                  originalPrice: product.OldPrice || product.OriginalPrice || product.Price || product.ProductPrice || 0,
                  imageUrl: imageUrl || '/placeholder-image.jpg',
                  inStock: (product.StockQuantity || product.Quantity || 0) > 0
                };
            });
            
            setDisplayItems(itemsToDisplay);
            return;
          }
        } catch (cacheError) {
          console.error('Error reading from cache:', cacheError);
          // Continue to fetch from API if cache read fails
        }
      }

      // If not in cache, fetch from API using product detail API for each product
      console.log('Fetching products from API...');

      // Fetch each product individually using the product detail API
      const productPromises = productIds.map(async (productId) => {
        try {
          const response = await axios.post('/api/product-detail', {
            requestParameters: {
              ProductId: productId,
              recordValueJson: "[]",
            },
          });

          if (response.data && response.data.data) {
            const parsedData = JSON.parse(response.data.data);
            return Array.isArray(parsedData) ? parsedData[0] : parsedData;
          }
          return null;
        } catch (error) {
          console.error(`Error fetching product ${productId}:`, error);
          return null;
        }
      });

      const productResults = await Promise.all(productPromises);
      const products = productResults.filter(product => product !== null);

      console.log('Fetched products:', products.length);
      
      console.log('Total products extracted from response:', products.length);
      
      // If no products found, log the structure and set empty array
      if (products.length === 0) {
        console.warn('No products found in the API response.');
        setDisplayItems([]);
        return;
      }
      
      // Convert to display format
      const itemsToDisplay = products.map((product: any) => {
        console.log('Processing product:', {
          id: product.ProductId || product.id,
          name: product.ProductName || product.Name,
          images: product.ProductImagesJson,
          imagePath: product.ImagePath,
          imageUrl: product.ImageUrl
        });
        
        // Handle different possible image properties with improved logic
        let imageUrl = '';
        
        try {
          // Try to parse ProductImagesJson if it exists
          if (product.ProductImagesJson) {
            try {
              const images = parseProductImages(
                typeof product.ProductImagesJson === 'string' 
                  ? product.ProductImagesJson 
                  : JSON.stringify(product.ProductImagesJson)
              );
              

              
              // Find primary image or first available
              const primaryImage = Array.isArray(images) && images.length > 0
                ? images.find((img: any) => img.IsPrimary) || images[0]
                : images;
                
              if (primaryImage) {
                const imgSrc = primaryImage.AttachmentURL || primaryImage.url || primaryImage.src || primaryImage;
                imageUrl = constructImageUrl(imgSrc);
              }
            } catch (e) {
              console.error('Error parsing product images:', e);
            }
          }
          
          // Fallback to ImagePath if no image found yet
          if (!imageUrl && product.ImagePath) {

            imageUrl = constructImageUrl(product.ImagePath);
          }
          
          // Additional fallback to ImageUrl if available
          if (!imageUrl && product.ImageUrl) {

            imageUrl = constructImageUrl(product.ImageUrl);
          }
          
          // Try DefaultImage property
          if (!imageUrl && product.DefaultImage) {

            imageUrl = constructImageUrl(product.DefaultImage);
          }
          
          // Try ProductImage property
          if (!imageUrl && product.ProductImage) {

            imageUrl = constructImageUrl(product.ProductImage);
          }
          
          // Final fallback to placeholder
          if (!imageUrl) {
            console.warn('No valid image found for product:', product.ProductId || product.id, product);
            imageUrl = '/placeholder-image.jpg';
          }
          

        } catch (error) {
          console.error('Error processing product images:', error, 'for product:', product.ProductId || product.id);
          imageUrl = '/placeholder-image.jpg';
        }
        

        return {
          id: product.ProductId || product.ProductID || product.id,
          name: product.ProductName || product.Name || 'Unnamed Product',
          price: product.Price || product.ProductPrice || 0,
          originalPrice: product.OldPrice || product.OriginalPrice || product.Price || product.ProductPrice || 0,
          imageUrl: imageUrl || '/placeholder-image.jpg',
          inStock: (product.StockQuantity || product.Quantity || 0) > 0
        };
      });
      
      console.log('Display items prepared:', itemsToDisplay.length);
      setDisplayItems(itemsToDisplay);
      
      // Cache the products for future use
      try {
        localStorage.setItem('cachedProducts', JSON.stringify(products));
      } catch (error) {
        console.error('Error caching products:', error);
      }
      
    } catch (error) {
      console.error('Error in processWishlistItems:', error);
      
      let errorMessage = 'An unknown error occurred';
      
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (error && typeof error === 'object' && 'message' in error) {
        errorMessage = String(error.message);
      }
      
      // Log detailed error information
      if (error && typeof error === 'object') {
        const errorObj = error as Record<string, unknown>;
        const axiosError = error as any;
        
        console.error('Error details:', {
          message: errorMessage,
          response: (axiosError as any)?.response?.data || 'No response data',
          status: (axiosError as any)?.response?.status,
          statusText: (axiosError as any)?.response?.statusText,
          config: {
            url: axiosError?.config?.url,
            method: axiosError?.config?.method,
            params: axiosError?.config?.params
          }
        });
      }
      
      // Extract error message from Axios response if available
      const axiosError = error && 
                        typeof error === 'object' && 
                        'isAxiosError' in error && 
                        (error as CustomAxiosError).response?.data?.error 
                          ? (error as CustomAxiosError).response?.data?.error 
                          : errorMessage;
      
      toast.error('Failed to load wishlist: ' + (axiosError || 'Unknown error'));
      setDisplayItems([]);
    } finally {
      setLoading(false);
    }
  };

  // Process wishlist items when they change (only after hydration)
  useEffect(() => {
    console.log('Process effect triggered - isHydrated:', isHydrated, 'wishlistItems:', wishlistItems.length);
    if (isHydrated) {
      processWishlistItems(wishlistItems);
    }
  }, [wishlistItems, isHydrated]);

  // Preload images when display items change
  useEffect(() => {
    if (displayItems.length > 0) {
      preloadWishlistImages(displayItems);
    }
  }, [displayItems]);

  const handleRemoveFromWishlist = (id: number) => {
    removeFromWishlist(id);
    toast.success('Product removed from wishlist');
  };

  // Show loading state while context is hydrating or while fetching data
  if (!isHydrated || loading) {
    return (
      <div className="container mx-auto py-12 flex flex-col items-center justify-center min-h-[50vh]">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">
          {!isHydrated ? 'Initializing wishlist...' : 'Loading your wishlist...'}
        </p>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Your Wishlist</h1>
            <p className="text-gray-600 mt-2">
              {displayItems.length > 0 
                ? `${displayItems.length} ${displayItems.length === 1 ? 'item' : 'items'} in your wishlist`
                : 'Your wishlist is empty'
              }
            </p>
          </div>
        </div>

        <Breadcrumb className="mb-6">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbPage>Wishlist</BreadcrumbPage>
          </BreadcrumbList>
        </Breadcrumb>
      
      {displayItems.length > 0 ? (
        <div className="grid gap-3 grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {displayItems.map((item) => (
            <Card key={item.id} className="overflow-hidden">
              <div className="relative aspect-square">
                <div className="w-full h-full flex items-center justify-center bg-gray-100">
                  <img
                    src={item.imageUrl || '/placeholder-image.jpg'}
                    alt={item.name}
                    className="w-full h-full object-cover transition-opacity duration-300"
                    loading="lazy"
                    crossOrigin="anonymous"
                    referrerPolicy="no-referrer"
                    data-original-src={item.imageUrl || ''}
                    data-fallback-attempts="0"

                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      const currentSrc = target.src;
                      target.onerror = null; // Prevent infinite loop
                      
                      // Silently handle image load failures with fallbacks
                      
                      // Track fallback attempts to prevent infinite loops
                      const fallbackAttempts = parseInt(target.dataset.fallbackAttempts || '0');
                      target.dataset.fallbackAttempts = String(fallbackAttempts + 1);
                      
                      // First fallback: try normalized/admin URL if not already using admin domain
                      if (fallbackAttempts === 0) {
                        const originalUrl = target.dataset.originalSrc || item.imageUrl;
                        if (originalUrl && !currentSrc.includes('admin.codemedicalapps.com')) {
                          const newUrl = constructImageUrl(originalUrl);
                          target.src = newUrl;
                          return;
                        }
                      }
                      
                      // Second fallback: try placeholder-image.jpg
                      if (fallbackAttempts === 1 || fallbackAttempts === 0) {
                        if (!currentSrc.includes('placeholder-image.jpg')) {
                          target.src = '/placeholder-image.jpg';
                          return;
                        }
                      }
                      
                      // Third fallback: try placeholder-image.jpg (use a visible placeholder)
                      if (fallbackAttempts === 2 || fallbackAttempts <= 1) {
                        if (!currentSrc.includes('placeholder-image.jpg')) {
                          target.src = '/placeholder-image.jpg';
                          return;
                        }
                      }
                      
                      // Final fallback: use placeholder-image.jpg instead of SVG data URL
                      // This ensures a more visible placeholder image
                      target.src = '/placeholder-image.jpg';
                      console.log('Using final fallback image for:', item.id, item.name);
                      
                      // Add a text fallback when all image attempts fail
                      const parentDiv = target.closest('.aspect-square')?.querySelector('div');
                      if (parentDiv) {
                        // Add a text fallback only if it doesn't exist yet
                        if (!parentDiv.querySelector('.fallback-text')) {
                          const fallbackText = document.createElement('span');
                          fallbackText.className = 'fallback-text absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-500 text-sm';
                          fallbackText.textContent = 'Image unavailable';
                          parentDiv.appendChild(fallbackText);
                          // Hide the img element
                          target.style.display = 'none';
                        }
                      }
                    }}
                    onLoad={() => {
                      console.log('Image loaded successfully:', item.imageUrl);
                      // Reset fallback attempts on successful load
                      const target = document.querySelector(`img[data-original-src="${item.imageUrl}"]`) as HTMLImageElement;
                      if (target) {
                        target.dataset.fallbackAttempts = '0';
                        // Remove any fallback text if it exists
                        const fallbackText = target.closest('.aspect-square')?.querySelector('.fallback-text');
                        if (fallbackText) {
                          fallbackText.remove();
                        }
                        // Make sure the image is visible
                        target.style.display = '';
                      }
                      // Cache successful image loads
                      if (typeof window !== 'undefined') {
                        const imageCache = JSON.parse(localStorage.getItem('wishlist_image_cache') || '{}');
                        imageCache[item.id] = {
                          url: item.imageUrl,
                          timestamp: Date.now(),
                          success: true
                        };
                        localStorage.setItem('wishlist_image_cache', JSON.stringify(imageCache));
                      }
                    }}
                />
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2 rounded-full bg-background/80 backdrop-blur-sm hover:bg-background/60 min-h-[36px] min-w-[36px] sm:min-h-[32px] sm:min-w-[32px]"
                  onClick={() => handleRemoveFromWishlist(item.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
              <div className="p-3 sm:p-4">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <h3 className="text-sm sm:text-base font-semibold truncate">{item.name}</h3>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>{item.name}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <div className="flex items-center gap-2 mb-3 sm:mb-4">
                  <span className="text-base sm:text-lg font-bold">${item.price.toFixed(2)}</span>
                  {item.originalPrice && item.originalPrice > item.price && (
                    <span className="text-xs sm:text-sm text-muted-foreground line-through">
                      ${item.originalPrice.toFixed(2)}
                    </span>
                  )}
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 min-h-[40px] text-xs sm:text-sm"
                    asChild
                  >
                    <Link href={`/product/${item.id}`}>
                      <Eye className="h-4 w-4 mr-1 sm:mr-2" />
                      <span className="hidden xs:inline">View</span>
                      <span className="xs:hidden">👁</span>
                    </Link>
                  </Button>
                  <Button
                    size="sm"
                    className="flex-1 min-h-[40px] text-xs sm:text-sm"
                    disabled={!item.inStock}
                    onClick={() => {
                      cart.addToCart(
                        {
                          id: item.id,
                          name: item.name,
                          price: item.price,
                          discountPrice: item.originalPrice && item.originalPrice > item.price ? item.price : undefined,
                          originalPrice: item.originalPrice || item.price,
                          image: item.imageUrl
                        },
                        1,
                        [], // No attributes by default
                        undefined // No IQD price
                      );
                      // Show modern toast notification
                      showModernAddToCartToast({
                        productName: item.name,
                        quantity: 1,
                        productImage: item.imageUrl || '/placeholder.svg',
                        onViewCart: () => {
                          window.location.href = '/cart';
                        }
                      });
                    }}
                  >
                    <ShoppingCart className="h-4 w-4 mr-1 sm:mr-2" />
                    <span className="hidden xs:inline">{item.inStock ? 'Add to Cart' : 'Out of Stock'}</span>
                    <span className="xs:hidden">{item.inStock ? '🛒' : '❌'}</span>
                  </Button>
                </div>
              </div>
            </Card>
          ))}
        </div>
      ) : (
        <Card className="p-8 text-center">
          <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
            <Heart className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-medium mb-2">Your wishlist is empty</h3>
          <p className="text-muted-foreground mb-4">
            You haven&apos;t added any products to your wishlist yet.
          </p>
          <p className="text-sm text-muted-foreground mb-6">
            💡 <strong>How to add items:</strong> Browse products and click the heart icon (♡) on any product to add it to your wishlist.
          </p>
          <div className="space-y-3">
            <Button asChild>
              <Link href="/products">
                Browse Products
                <ChevronRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>
        </Card>
      )}
      </div>
    </div>
  );
}