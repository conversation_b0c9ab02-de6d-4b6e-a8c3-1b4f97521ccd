(()=>{var a={};a.id=1778,a.ids=[1778],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31263:(a,b,c)=>{"use strict";c.d(b,{DW:()=>j,PA:()=>i,Rb:()=>g,pN:()=>h});var d=c(4765),e=c.n(d);let f=process.env.NEXT_PUBLIC_ENCRYPTION_KEY||"your-secret-key-change-this-in-production";function g(a){try{let b=a.toString();return e().AES.encrypt(b,f).toString().replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}catch(b){return console.error("Encryption error:",b),a.toString()}}function h(a){try{let b=a.replace(/-/g,"+").replace(/_/g,"/");for(;b.length%4;)b+="=";let c=e().AES.decrypt(b,f).toString(e().enc.Utf8);if(!c)throw Error("Failed to decrypt value");return c}catch(b){return console.error("Decryption error:",b),a}}function i(a){return g(a)}function j(a){return h(a)}},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},54084:(a,b,c)=>{Promise.resolve().then(c.bind(c,76297))},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},62265:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\orders\\page.tsx","default")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71463:(a,b,c)=>{"use strict";c.d(b,{E:()=>f});var d=c(60687),e=c(96241);function f({className:a,...b}){return(0,d.jsx)("div",{className:(0,e.cn)("animate-pulse rounded-md bg-muted",a),...b})}},72644:(a,b,c)=>{Promise.resolve().then(c.bind(c,62265))},74075:a=>{"use strict";a.exports=require("zlib")},76297:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>y});var d=c(60687),e=c(43210),f=c(34993),g=c(55192),h=c(24934),i=c(99294),j=c(71463),k=c(85814),l=c.n(k),m=c(77080),n=c(832),o=c(60796),p=c(19080),q=c(88059),r=c(5336),s=c(78122),t=c(93613),u=c(99270);let v=(0,c(62688).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var w=c(14952),x=c(31263);function y(){let{t:a,primaryColor:b,primaryTextColor:c}=(0,m.t)(),{user:k,isLoggedIn:y,isLoading:z,token:A}=(0,n.J)(),[B,C]=(0,e.useState)("all"),[D,E]=(0,e.useState)([]),[F,G]=(0,e.useState)(!0),[H,I]=(0,e.useState)(null),[J,K]=(0,e.useState)([]),[L,M]=(0,e.useState)(!1),[N,O]=(0,e.useState)(""),[P,Q]=(0,e.useState)("date-desc"),R=(0,e.useCallback)(async()=>{G(!0);try{let a={"Content-Type":"application/json",Accept:"application/json"};A&&(a.Authorization=`Bearer ${A}`);let b=await fetch("/api/orders/history",{method:"POST",headers:a,body:JSON.stringify({requestParameters:{recordValueJson:"[]"}})}),c=await b.json();if(console.log("Orders API response:",c),c&&c.data){let a;if(a="string"==typeof c.data?JSON.parse(c.data):c.data,console.log("Parsed order data:",a),Array.isArray(a)){console.log("First order structure:",a[0]);let b=a.map(a=>({id:a.OrderId,OrderID:a.OrderId,OrderNumber:a.OrderNumber,date:a.OrderDateUTC?new Date(a.OrderDateUTC).toLocaleDateString():"N/A",OrderDate:a.OrderDateUTC,total:a.OrderTotal||0,OrderTotal:a.OrderTotal||0,TotalAmount:a.OrderTotal||0,status:a.LatestStatusName||"Active",StatusID:"Active"===a.LatestStatusName?1:"In Progress"===a.LatestStatusName?2:"Completed"===a.LatestStatusName?3:"Returned"===a.LatestStatusName?4:"Refunded"===a.LatestStatusName?5:"Cancelled"===a.LatestStatusName?6:1,ItemCount:a.TotalItems||0,items:a.OrderItems||[],OrderItems:a.OrderItems||[]})).sort((a,b)=>new Date(b.OrderDate||0).getTime()-new Date(a.OrderDate||0).getTime());E(b),console.log("Mapped and sorted orders:",b)}else E([])}}catch(a){console.error("Error fetching order history:",a)}finally{G(!1)}},[A]);if((0,e.useCallback)(async a=>{M(!0);try{let b={"Content-Type":"application/json",Accept:"application/json"};A&&(b.Authorization=`Bearer ${A}`);let c=await fetch("/api/orders/details",{method:"POST",headers:b,body:JSON.stringify({requestParameters:{OrderId:a,recordValueJson:"[]"}})}),d=await c.json();if(d&&d.data){let a=JSON.parse(d.data);Array.isArray(a)&&K(a)}}catch(a){console.error("Error fetching order details:",a)}finally{M(!1)}},[A]),z)return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-lg text-muted-foreground",children:"Loading..."})]})});if(!y)return(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,d.jsx)("div",{className:"text-center",children:(0,d.jsx)("p",{className:"text-lg text-muted-foreground",children:"Redirecting to login..."})})});let S=F?[]:D,T="all"===B?S:S.filter(a=>(a.StatusID||a.OrderStatusId||a.StateId||1).toString()===B),U=[...""===N.trim()?T:T.filter(a=>{let b=N.toLowerCase(),c=(a.OrderNumber||`OR#${String(a.OrderId||a.OrderID).padStart(8,"0")}`).toLowerCase(),d=(a.items||a.OrderItems||[]).map(a=>(a.name||a.ProductName||a.ItemName||"").toLowerCase()).join(" ");return c.includes(b)||d.includes(b)})].sort((a,b)=>{switch(P){case"date-desc":return new Date(b.OrderDate||0).getTime()-new Date(a.OrderDate||0).getTime();case"date-asc":return new Date(a.OrderDate||0).getTime()-new Date(b.OrderDate||0).getTime();case"total-desc":return(b.total||b.OrderTotal||b.TotalAmount||0)-(a.total||a.OrderTotal||a.TotalAmount||0);case"total-asc":return(a.total||a.OrderTotal||a.TotalAmount||0)-(b.total||b.OrderTotal||b.TotalAmount||0);default:return 0}});return(0,d.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,d.jsx)(f.Qp,{className:"mb-6",children:(0,d.jsxs)(f.AB,{children:[(0,d.jsx)(f.J5,{children:(0,d.jsx)(f.w1,{asChild:!0,children:(0,d.jsx)(l(),{href:"/",children:a("home")})})}),(0,d.jsx)(f.tH,{}),(0,d.jsx)(f.J5,{children:(0,d.jsx)(f.tJ,{children:a("orders")})})]})}),(0,d.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:a("orders")}),(0,d.jsxs)(h.$,{variant:"outline",size:"sm",onClick:R,disabled:F,className:"flex items-center gap-2",children:[(0,d.jsx)(s.A,{className:`h-4 w-4 ${F?"animate-spin":""}`}),F?"Loading...":"Refresh"]})]}),(0,d.jsx)(i.tU,{defaultValue:"all",className:"mb-6",onValueChange:C,children:(0,d.jsxs)(i.j7,{className:"grid w-full grid-cols-5 mb-6 gap-2 bg-transparent p-0 h-auto",children:[(0,d.jsx)(i.Xi,{value:"all",className:"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"all"===B?b:"rgb(209 213 219)",color:"all"===B?c:"rgb(55 65 81)",transform:"all"===B?"scale(1.05)":"scale(1)",boxShadow:"all"===B?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"all"===B?b:"transparent"},children:"All Orders"}),(0,d.jsx)(i.Xi,{value:"1",className:"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"1"===B?b:"rgb(209 213 219)",color:"1"===B?c:"rgb(55 65 81)",transform:"1"===B?"scale(1.05)":"scale(1)",boxShadow:"1"===B?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"1"===B?b:"transparent"},children:"Active"}),(0,d.jsx)(i.Xi,{value:"2",className:"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"2"===B?b:"rgb(209 213 219)",color:"2"===B?c:"rgb(55 65 81)",transform:"2"===B?"scale(1.05)":"scale(1)",boxShadow:"2"===B?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"2"===B?b:"transparent"},children:"In Progress"}),(0,d.jsx)(i.Xi,{value:"3",className:"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"3"===B?b:"rgb(209 213 219)",color:"3"===B?c:"rgb(55 65 81)",transform:"3"===B?"scale(1.05)":"scale(1)",boxShadow:"3"===B?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"3"===B?b:"transparent"},children:"Completed"}),(0,d.jsx)(i.Xi,{value:"6",className:"rounded-lg px-4 py-3 text-base font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"6"===B?b:"rgb(209 213 219)",color:"6"===B?c:"rgb(55 65 81)",transform:"6"===B?"scale(1.05)":"scale(1)",boxShadow:"6"===B?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"6"===B?b:"transparent"},children:"Cancelled"})]})}),(0,d.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6",children:[(0,d.jsxs)("div",{className:"relative w-full md:w-64",children:[(0,d.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,d.jsx)("input",{type:"text",placeholder:"Search orders...",value:N,onChange:a=>O(a.target.value),className:"w-full pl-10 pr-4 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("span",{className:"text-sm text-muted-foreground",children:"Sort by:"}),(0,d.jsxs)("select",{value:P,onChange:a=>Q(a.target.value),className:"border border-input rounded-md px-3 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,d.jsx)("option",{value:"date-desc",children:"Date (Newest)"}),(0,d.jsx)("option",{value:"date-asc",children:"Date (Oldest)"}),(0,d.jsx)("option",{value:"total-desc",children:"Amount (High to Low)"}),(0,d.jsx)("option",{value:"total-asc",children:"Amount (Low to High)"})]})]})]}),F?(0,d.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0].map((a,b)=>(0,d.jsx)(g.Zp,{className:"overflow-hidden",children:(0,d.jsx)("div",{className:"border-b border-border p-4",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)(j.E,{className:"h-5 w-32"}),(0,d.jsx)(j.E,{className:"h-6 w-20 rounded-full"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground mb-3",children:[(0,d.jsx)(j.E,{className:"h-4 w-24"}),(0,d.jsx)(j.E,{className:"h-4 w-16"}),(0,d.jsx)(j.E,{className:"h-4 w-20"})]}),(0,d.jsx)(j.E,{className:"h-16 w-full rounded-md"})]}),(0,d.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,d.jsx)(j.E,{className:"h-6 w-20"}),(0,d.jsx)(j.E,{className:"h-8 w-24"})]})]})})},b))}):U.length>0?(0,d.jsx)("div",{className:"space-y-4",children:U.map((a,b)=>(0,d.jsxs)(g.Zp,{className:"overflow-hidden",children:[(0,d.jsx)("div",{className:"border-b border-border p-4",children:(0,d.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)("h3",{className:"font-medium",children:a.OrderNumber||`OR#${String(a.OrderId||a.OrderID).padStart(8,"0")}`}),(()=>{let b=a.LatestStatusName;if(b){let a="bg-blue-100 text-blue-800",c=(0,d.jsx)(p.A,{className:"h-5 w-5 text-blue-500"});switch(b.toLowerCase()){case"active":a="bg-blue-100 text-blue-800",c=(0,d.jsx)(p.A,{className:"h-5 w-5 text-blue-500"});break;case"in progress":case"processing":a="bg-orange-100 text-orange-800",c=(0,d.jsx)(q.A,{className:"h-5 w-5 text-orange-500"});break;case"completed":case"delivered":a="bg-green-100 text-green-800",c=(0,d.jsx)(r.A,{className:"h-5 w-5 text-green-500"});break;case"cancelled":a="bg-red-100 text-red-800",c=(0,d.jsx)(t.A,{className:"h-5 w-5 text-red-500"});break;default:a="bg-gray-100 text-gray-800",c=(0,d.jsx)(p.A,{className:"h-5 w-5 text-gray-500"})}return(0,d.jsxs)("div",{className:`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${a}`,children:[c,(0,d.jsx)("span",{children:b})]})}else{let b=(a=>{switch(parseInt(a?.toString()||"1")){case 1:return{name:"Active",icon:(0,d.jsx)(p.A,{className:"h-5 w-5 text-blue-500"}),color:"bg-blue-100 text-blue-800"};case 2:return{name:"In Progress",icon:(0,d.jsx)(q.A,{className:"h-5 w-5 text-orange-500"}),color:"bg-orange-100 text-orange-800"};case 3:return{name:"Completed",icon:(0,d.jsx)(r.A,{className:"h-5 w-5 text-green-500"}),color:"bg-green-100 text-green-800"};case 4:return{name:"Returned",icon:(0,d.jsx)(s.A,{className:"h-5 w-5 text-purple-500"}),color:"bg-purple-100 text-purple-800"};case 5:return{name:"Refunded",icon:(0,d.jsx)(s.A,{className:"h-5 w-5 text-indigo-500"}),color:"bg-indigo-100 text-indigo-800"};case 6:return{name:"Cancelled",icon:(0,d.jsx)(t.A,{className:"h-5 w-5 text-red-500"}),color:"bg-red-100 text-red-800"};default:return{name:"Unknown",icon:(0,d.jsx)(p.A,{className:"h-5 w-5 text-gray-500"}),color:"bg-gray-100 text-gray-800"}}})(a.StatusID||a.OrderStatusId||a.StateId||1);return(0,d.jsxs)("div",{className:`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${b.color}`,children:[b.icon,(0,d.jsx)("span",{children:b.name})]})}})()]}),(0,d.jsxs)("p",{className:"text-sm text-muted-foreground mb-2",children:["Ordered on ",(()=>{let b=a.OrderDateUTC||a.date||a.OrderDate||a.CreatedOn;if(b)try{return new Date(b).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}catch(a){return b}return"N/A"})()]}),(a.items||a.OrderItems||[]).length>0&&(0,d.jsxs)("div",{className:"bg-blue-50 px-3 py-2 rounded-md",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-blue-900 mb-1",children:"Products in this order:"}),(0,d.jsxs)("div",{className:"text-sm text-blue-800 space-y-1",children:[(a.items||a.OrderItems||[]).slice(0,3).map((b,c)=>{let e=b.name||b.ProductName||b.ItemName||"Medical Product",f=b.ProductID||b.ProductId;return(0,d.jsxs)("span",{children:[f?(0,d.jsx)(l(),{href:`/product/${f}`,className:"text-blue-600 hover:text-blue-800 hover:underline font-medium",target:"_blank",rel:"noopener noreferrer",children:e}):(0,d.jsx)("span",{className:"font-medium",children:e}),c<Math.min(2,(a.items||a.OrderItems||[]).length-1)&&" • "]},c)}),(a.items||a.OrderItems||[]).length>3&&(0,d.jsxs)("span",{className:"text-blue-600",children:[" • +",(a.items||a.OrderItems||[]).length-3," more items"]})]})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4 mt-4 md:mt-0",children:[(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("p",{className:"font-medium",children:["$",(a.total||a.OrderTotal||a.TotalAmount||0).toFixed(2)]}),(0,d.jsxs)("p",{className:"text-sm text-muted-foreground",children:[a.TotalItems||a.items?.length||a.OrderItems?.length||a.ItemCount||0," item(s)"]})]}),(0,d.jsx)(h.$,{variant:"outline",size:"sm",className:"flex items-center gap-1",asChild:!0,children:(0,d.jsxs)(l(),{href:(a=>{let b=a.OrderId||a.OrderID||a.id,c=(0,x.PA)(b),d=(a.total||a.OrderTotal||a.TotalAmount||0).toFixed(2),e=(()=>{let b=a.OrderDateUTC||a.date||a.OrderDate||a.CreatedOn;if(b)try{return new Date(b).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}catch(a){}return"N/A"})(),f=(0,x.Rb)(d),g=(0,x.Rb)(e);return`/orders/${c}?t=${f}&d=${g}`})(a),children:[(0,d.jsx)(v,{className:"h-4 w-4"}),"Details",(0,d.jsx)(w.A,{className:"h-4 w-4"})]})})]})]})}),(0,d.jsxs)("div",{className:"p-4 bg-muted/30",children:[(0,d.jsx)("h4",{className:"text-sm font-medium mb-2",children:"Order Items Preview"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(a.items||a.OrderItems||[]).slice(0,3).map((a,b)=>{let c=a.ProductImageUrl||a.ImageUrl||a.ProductImage;return(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-muted rounded-md overflow-hidden flex items-center justify-center",children:c?(0,d.jsx)("img",{src:c.startsWith("http")?c:`${o.T.ADMIN_BASE_URL}${c}`,alt:a.name||a.ProductName||"Product",className:"w-full h-full object-cover",onError:a=>{let b=a.target;b.style.display="none";let c=b.parentElement;c&&(c.innerHTML='<svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>')}}):(0,d.jsx)(p.A,{className:"h-6 w-6 text-gray-400"})}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium text-gray-900",children:a.name||a.ProductName||a.ItemName||"Medical Product"}),(0,d.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Qty: ",a.quantity||a.Quantity||a.OrderQuantity||1]}),a.ProductDescription&&(0,d.jsx)("p",{className:"text-xs text-gray-500 mt-1 line-clamp-1",children:a.ProductDescription})]})]}),(0,d.jsxs)("p",{className:"text-sm font-medium",children:["$",(a.price||a.Price||a.UnitPrice||a.ItemPrice||0).toFixed(2)]})]},b)}),(a.items?.length||a.OrderItems?.length||0)>3&&(0,d.jsxs)("div",{className:"text-xs text-muted-foreground text-center pt-2",children:["+",(a.items?.length||a.OrderItems?.length||0)-3," more items"]})]})]})]},a.OrderId||a.OrderID||a.id||b))}):(0,d.jsx)(g.Zp,{children:(0,d.jsxs)("div",{className:"p-8 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(p.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No orders found"}),(0,d.jsxs)("p",{className:"text-muted-foreground mb-4",children:["You don't have any ","all"!==B?B:""," orders yet."]}),(0,d.jsx)(h.$,{asChild:!0,children:(0,d.jsx)(l(),{href:"/",children:"Continue Shopping"})})]})})]})]})}},78122:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:a=>{"use strict";a.exports=require("url")},81489:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,62265)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\orders\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\orders\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/orders/page",pathname:"/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/orders/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},88059:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},93613:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,1697,9500,9822,6085],()=>b(b.s=81489));module.exports=c})();