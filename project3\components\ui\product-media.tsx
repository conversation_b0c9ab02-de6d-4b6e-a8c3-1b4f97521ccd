'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Config } from '@/lib/config';

type ProductMediaProps = {
  images: string[];
  video?: string;
  productId?: number;
};

export function ProductMedia({ images, video, productId }: ProductMediaProps) {
  const [selectedMedia, setSelectedMedia] = useState<number>(0);

  // Log the received props for debugging
  console.log('ProductMedia props:', { images, video, productId });

  // Ensure we have at least one image
  const safeImages = images && images.length > 0 ? images : ['/images/no-image.jpg'];

  // Combine video and images
  const allMedia = video ? [video, ...safeImages] : safeImages;

  return (
    <div className="space-y-4">
      <div className="aspect-square relative rounded-lg overflow-hidden">
        {selectedMedia === 0 && video ? (
          <video
            src={video}
            controls
            className="w-full h-full object-cover"
            poster={images[0]}
          />
        ) : (
          <img
            src={formatImageUrl(allMedia[selectedMedia])}
            alt="Product image"
            className="w-full h-full object-cover"
            onError={(e) => {
              // Fallback to local image if remote image fails to load
              const target = e.target as HTMLImageElement;
              console.error('Image load error for:', target.src);

              // Store the original src for debugging
              const originalSrc = target.src;

              // Prevent infinite loop
              target.onerror = null;

              // Special handling for /images/no-image.jpg
              if (originalSrc.includes('/images/no-image.jpg')) {
                console.log('No-image placeholder failed to load, using absolute URL');
                // Use absolute URL for the placeholder
                target.src = `${Config.ADMIN_BASE_URL}/images/no-image.jpg`;
                return;
              }

              // Try with the admin base URL if it's a relative path
              if (!originalSrc.startsWith('http') && !originalSrc.includes(Config.ADMIN_BASE_URL)) {
                const newSrc = `${Config.ADMIN_BASE_URL.replace(/\/$/, '')}${originalSrc.startsWith('/') ? '' : '/'}${originalSrc}`;
                console.log('Trying with admin base URL:', newSrc);
                target.src = newSrc;

                // Add another error handler for this attempt
                target.onerror = () => {
                  console.error('Admin base URL attempt also failed');
                  target.onerror = null; // Prevent infinite loop
                  target.src = `${Config.ADMIN_BASE_URL}/images/no-image.jpg`;
                };
              } else {
                // If that doesn't work, use a placeholder
                console.log('Using final fallback placeholder');
                target.src = `${Config.ADMIN_BASE_URL}/images/no-image.jpg`;
              }
            }}
          />
        )}
      </div>
      <div className="grid grid-cols-6 gap-2">
        {allMedia.map((media, index) => (
          <button
            key={index}
            className={`aspect-square relative rounded-lg overflow-hidden border-2 ${selectedMedia === index ? 'border-primary' : 'border-transparent'}`}
            onClick={() => setSelectedMedia(index)}
          >
            {index === 0 && video ? (
              <video
                src={video}
                className="w-full h-full object-cover"
                poster={images[0]}
              />
            ) : (
              <img
                src={formatImageUrl(media)}
                alt={`Product thumbnail ${index + 1}`}
                className="w-full h-full object-cover"
                onError={(e) => {
                  // Fallback to local image if remote image fails to load
                  const target = e.target as HTMLImageElement;
                  console.error('Thumbnail image load error for:', target.src);

                  // Store the original src for debugging
                  const originalSrc = target.src;

                  // Prevent infinite loop
                  target.onerror = null;

                  // Special handling for /images/no-image.jpg
                  if (originalSrc.includes('/images/no-image.jpg')) {
                    console.log('Thumbnail no-image placeholder failed to load, using absolute URL');
                    // Use absolute URL for the placeholder
                    target.src = `${Config.ADMIN_BASE_URL}/images/no-image.jpg`;
                    return;
                  }

                  // Try with the admin base URL if it's a relative path
                  if (!originalSrc.startsWith('http') && !originalSrc.includes(Config.ADMIN_BASE_URL)) {
                    const newSrc = `${Config.ADMIN_BASE_URL.replace(/\/$/, '')}${originalSrc.startsWith('/') ? '' : '/'}${originalSrc}`;
                    console.log('Trying thumbnail with admin base URL:', newSrc);
                    target.src = newSrc;

                    // Add another error handler for this attempt
                    target.onerror = () => {
                      console.error('Thumbnail admin base URL attempt also failed');
                      target.onerror = null; // Prevent infinite loop
                      target.src = `${Config.ADMIN_BASE_URL}/images/no-image.jpg`;
                    };
                  } else {
                    // If that doesn't work, use a placeholder
                    console.log('Using final fallback placeholder for thumbnail');
                    target.src = `${Config.ADMIN_BASE_URL}/images/no-image.jpg`;
                  }
                }}
              />
            )}
          </button>
        ))}
      </div>
    </div>
  );

  // Helper function to format image URLs correctly
  function formatImageUrl(url: string): string {
    console.log('Formatting image URL:', url);

    if (!url) {
      console.log('Empty URL, using default placeholder');
      return `${Config.ADMIN_BASE_URL}/images/no-image.jpg`;
    }

    // If it's a video URL, return as is
    if (url.includes('youtube.com') || url.includes('vimeo.com')) {
      console.log('Video URL, using as is:', url);
      return url;
    }

    // If it's already a full URL, return it as is
    if (url.startsWith('http')) {
      console.log('URL already has domain, using as is:', url);
      return url;
    }

    // Normalize path (ensure it starts with exactly one slash)
    let normalizedPath = url.startsWith('/') ? url : `/${url}`;
    // Remove any double slashes in the path
    normalizedPath = normalizedPath.replace(/\/+/g, '/');

    const formattedUrl = `${Config.ADMIN_BASE_URL}${normalizedPath}`;
    console.log('Formatted URL:', formattedUrl);
    return formattedUrl;
  }
}