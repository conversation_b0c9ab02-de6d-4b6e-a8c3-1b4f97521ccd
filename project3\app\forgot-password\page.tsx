'use client';

import { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowRight, CheckCircle2, Smartphone, Lock, RefreshCw, Eye, EyeOff, KeyRound } from 'lucide-react';
import { useSettings } from '@/contexts/settings-context';
import { MakeApiCallAsync, Config } from '@/lib/api-helper';
import { useToast } from '@/components/ui/use-toast';

type FormStep = 'phone' | 'verification' | 'reset';

export default function ForgotPasswordPage() {
  const [formStep, setFormStep] = useState<FormStep>('phone');
  const [phone, setPhone] = useState('964');
  const [userCountry, setUserCountry] = useState('iq');
  const [verificationCode, setVerificationCode] = useState('');
  const [confirmationResult, setConfirmationResult] = useState<any>(null);
  const [resendTimer, setResendTimer] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [userEmail, setUserEmail] = useState('');
  const [passwords, setPasswords] = useState({
    newPassword: '',
    confirmPassword: ''
  });
  
  const { t } = useSettings();
  const { toast } = useToast();
  const router = useRouter();

  useEffect(() => {
    fetch('https://ipapi.co/json/')
      .then(res => res.json())
      .then(data => {
        if (data.country_code) {
          setUserCountry(data.country_code.toLowerCase());
          setPhone(data.country_calling_code.replace('+', ''));
        }
      })
      .catch(() => {
        setUserCountry('iq');
        setPhone('964');
      });
  }, []);

  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => setResendTimer(resendTimer - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [resendTimer]);

  const startResendTimer = () => {
    setResendTimer(60);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handlePhoneSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Enhanced phone number validation
    if (!phone) {
      setError('Phone number is required');
      setLoading(false);
      return;
    }

    // Clean phone number for validation
    const cleanPhone = phone.replace(/[^+\d]/g, '');
    
    if (cleanPhone.length < 8) {
      setError('Phone number must be at least 8 digits');
      setLoading(false);
      return;
    }

    if (!/^\+?[1-9]\d{7,14}$/.test(cleanPhone)) {
      setError('Please enter a valid phone number');
      setLoading(false);
      return;
    }

    // Format phone number with country code if not already present
    const formattedPhone = cleanPhone.startsWith('+') ? cleanPhone : `+${cleanPhone}`;
    
    try {
      // STEP 1: First check if user exists in database before attempting Firebase verification
      console.log('Step 1: Checking if user exists with phone number:', formattedPhone);
      
      const checkUserParams = {
        requestParameters: {
          PhoneNumber: formattedPhone
        }
      };

      const headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      };
      
      const userCheckResponse = await MakeApiCallAsync(Config.END_POINT_NAMES.GET_USER_BY_PHONE, null, checkUserParams, headers, "POST", true);
      
      console.log('User check response:', userCheckResponse);
      console.log('User check response data:', userCheckResponse.data);
      console.log('User check response data.data:', userCheckResponse.data?.data);
      console.log('User check response data.data type:', typeof userCheckResponse.data?.data);
      
      // Check for various error conditions
      if (userCheckResponse.data?.errorMessage || 
          userCheckResponse.data?.error || 
          !userCheckResponse.data || 
          !userCheckResponse.data.data ||
          userCheckResponse.data.data === "[]" ||
          (Array.isArray(userCheckResponse.data.data) && userCheckResponse.data.data.length === 0)) {
        setError('No account found with this phone number. Please verify your phone number or create a new account.');
        setLoading(false);
        return;
      }

      // STEP 2: If user exists, proceed with SMS verification
      console.log('Step 2: User found, proceeding with SMS verification');
      
      // Send verification code via Twilio WhatsApp
      const smsResponse = await fetch('/api/sms/send-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          phoneNumber: formattedPhone,
          useWhatsApp: true  // Enable WhatsApp verification
        }),
      });

      const smsData = await smsResponse.json();

      if (smsResponse.ok) {
        setConfirmationResult({ phoneNumber: formattedPhone });
        setFormStep('verification');
        startResendTimer();
      } else {
        throw new Error(smsData.error || 'Failed to send verification code');
      }
      
      toast({
        title: "Verification Code Sent",
        description: `We've sent a verification code to ${formattedPhone}`,
      });
    } catch (err: any) {
      console.error('Error in phone submission:', err);
      
      // Handle specific Firebase errors
      if (err.code === 'auth/too-many-requests') {
        setError('Too many verification attempts. Please wait a few minutes before trying again.');
      } else if (err.code === 'auth/invalid-phone-number') {
        setError('Invalid phone number format. Please check your phone number and try again.');
      } else if (err.code === 'auth/quota-exceeded') {
        setError('SMS quota exceeded. Please try again later.');
      } else {
        setError(err.message || 'Failed to send verification code. Please try again.');
      }
      
      toast({
        title: "Error",
        description: err.message || 'Failed to send verification code',
        type: "error"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResendCode = async () => {
    if (resendTimer > 0) return;
    setLoading(true);
    setError('');

    try {
      // Clean and format phone number
      const cleanPhone = phone.replace(/[^+\d]/g, '');
      const formattedPhone = cleanPhone.startsWith('+') ? cleanPhone : `+${cleanPhone}`;
      
      // Resend verification code via Twilio WhatsApp
      const response = await fetch('/api/sms/send-verification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          phoneNumber: formattedPhone,
          useWhatsApp: true  // Enable WhatsApp verification
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setConfirmationResult({ phoneNumber: formattedPhone });
        startResendTimer();
      } else {
        throw new Error(data.error || 'Failed to resend verification code');
      }
      
      toast({
        title: "Verification code resent",
        description: "A new verification code has been sent to your phone number."
      });
    } catch (err: any) {
      console.error('Error resending verification code:', err);
      
      // Handle specific Firebase errors for resend
      if (err.code === 'auth/too-many-requests') {
        setError('Too many verification attempts. Please wait a few minutes before trying again.');
      } else if (err.code === 'auth/invalid-phone-number') {
        setError('Invalid phone number format. Please check your phone number and try again.');
      } else if (err.code === 'auth/quota-exceeded') {
        setError('SMS quota exceeded. Please try again later.');
      } else {
        setError(err.message || 'Failed to resend verification code. Please try again.');
      }
      
      toast({
        title: "Error",
        description: err.message || 'Failed to resend verification code',
        type: "error"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleVerificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Verify the SMS code
      const response = await fetch('/api/sms/verify-code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          phoneNumber: confirmationResult.phoneNumber, 
          code: verificationCode 
        }),
      });

      const data = await response.json();

      if (response.ok) {
        // Phone verified successfully, now get user email for password reset
        // Use the same formatted phone number
        const phoneNumber = confirmationResult.phoneNumber;
        const getUserParams = {
          requestParameters: {
            PhoneNumber: phoneNumber
          }
        };

        const headers = {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
        };

        // Get user details by phone number
        const userResponse = await MakeApiCallAsync(Config.END_POINT_NAMES.GET_USER_BY_PHONE, null, getUserParams, headers, "POST", true);

        if (userResponse.data && !userResponse.data.errorMessage && userResponse.data.data !== "[]") {
          // Parse user data if it's a string
          let userData;
          if (typeof userResponse.data.data === 'string') {
            try {
              userData = JSON.parse(userResponse.data.data);
            } catch (e) {
              console.error('Error parsing user data:', e);
              userData = [];
            }
          } else {
            userData = userResponse.data.data;
          }

          if (Array.isArray(userData) && userData.length > 0) {
            setUserEmail(userData[0].EmailAddress || userData[0].Email || '');
          }
        }

        setFormStep('reset');
        
        toast({
          title: "Phone Verified",
          description: "Your phone number has been verified. You can now reset your password.",
        });
      }
    } catch (err: any) {
      setError(err.message || 'Invalid verification code');
      console.error('Error:', err);
      toast({
        title: "Verification Failed",
        description: err.message || 'Invalid verification code',
        type: "error"
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Validate passwords
    if (!passwords.newPassword || passwords.newPassword.length < 6) {
      setError('Password must be at least 6 characters long');
      setLoading(false);
      return;
    }

    if (passwords.newPassword !== passwords.confirmPassword) {
      setError('Passwords do not match');
      setLoading(false);
      return;
    }

    try {
      // Call API to reset password
      // Use the same formatted phone number
      const cleanPhoneReset = phone.replace(/[^+\d]/g, '');
      const phoneNumber = cleanPhoneReset.startsWith('+') ? cleanPhoneReset : `+${cleanPhoneReset}`;
      const resetParams = {
        requestParameters: {
          PhoneNumber: phoneNumber,
          Password: passwords.newPassword
        }
      };

      const headers = {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      };

      console.log('Password reset request:', {
        phoneNumber,
        email: userEmail,
        endpoint: Config.END_POINT_NAMES.RESET_PASSWORD_BY_PHONE
      });

      // Call the password reset API
      const response = await MakeApiCallAsync(Config.END_POINT_NAMES.RESET_PASSWORD_BY_PHONE, null, resetParams, headers, "POST", true);
      
      console.log('Password reset response:', response);

      // Check for successful response
      if (response.data && response.data.statusCode === 200 && response.data.statusMessage === "Ok") {
        toast({
          title: "Password Reset Successful",
          description: response.data.message || "Your password has been reset successfully. You can now log in with your new password.",
        });

        // Redirect to login page
        setTimeout(() => {
          router.push('/login');
        }, 2000);
        return;
      }
      
      // Handle various error scenarios
      let errorMessage = 'Failed to reset password. Please try again.';
      
      if (response.data?.errorMessage) {
        // Check if the error message contains specific backend validation errors
        const backendError = response.data.errorMessage;
        if (backendError.includes('Phone number is required')) {
          errorMessage = 'Phone number is required. Please try again.';
        } else if (backendError.includes('Password is required')) {
          errorMessage = 'Password is required. Please try again.';
        } else if (backendError.includes('User not found with the provided phone number')) {
          errorMessage = 'No account found with this phone number. Please verify your phone number.';
        } else if (backendError.includes('Database error occurred')) {
          errorMessage = 'A database error occurred. Please try again later or contact support.';
        } else if (backendError.includes('Operation error')) {
          errorMessage = 'A system error occurred. Please try again later.';
        } else if (backendError.includes('An unexpected error occurred')) {
          errorMessage = 'An unexpected error occurred. Please try again or contact support.';
        } else {
          errorMessage = backendError;
        }
      } else if (response.data?.statusCode === 501) {
        errorMessage = 'Password reset service is currently unavailable. Please try again later or contact support.';
      } else if (response.data?.statusCode === 404) {
        errorMessage = 'User not found. Please verify your phone number.';
      } else if (response.data?.statusCode === 400) {
        errorMessage = 'Invalid request. Please check your information and try again.';
      }
      
      setError(errorMessage);
      
      // Show toast notification for better user experience
      toast({
        title: "Password Reset Failed",
        description: errorMessage,
        type: "error"
      });
      
    } catch (err: any) {
      setError(err.message || 'Failed to reset password');
      console.error('Error:', err);
      toast({
        title: "Reset Failed",
        description: err.message || 'Failed to reset password',
        type: "error"
      });
    } finally {
      setLoading(false);
    }
  };

  const formVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 }
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h2 className="mt-6 text-3xl font-extrabold">
            {formStep === 'phone' && 'Reset Password'}
            {formStep === 'verification' && 'Verify Your Phone'}
            {formStep === 'reset' && 'Create New Password'}
          </h2>
          <p className="mt-2 text-sm text-muted-foreground">
            {formStep === 'phone' && 'Enter your phone number to reset your password'}
            {formStep === 'verification' && 'Enter the code we sent to your phone'}
            {formStep === 'reset' && 'Enter your new password'}
          </p>
        </div>

        <Card className="mt-8 p-8 shadow-xl bg-card/100">
          <div className="flex justify-center mb-8">
            <div className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${formStep === 'phone' ? 'bg-primary text-primary-foreground' : 'bg-primary/20 text-primary'}`}>
                <Smartphone className="w-4 h-4" />
              </div>
              <div className={`w-16 h-1 ${formStep === 'phone' ? 'bg-primary/20' : formStep === 'verification' ? 'bg-primary' : 'bg-primary'}`} />
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${formStep === 'verification' ? 'bg-primary text-primary-foreground' : formStep === 'reset' ? 'bg-primary' : 'bg-primary/20 text-primary'}`}>
                <CheckCircle2 className="w-4 h-4" />
              </div>
              <div className={`w-16 h-1 ${formStep === 'reset' ? 'bg-primary' : 'bg-primary/20'}`} />
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${formStep === 'reset' ? 'bg-primary text-primary-foreground' : 'bg-primary/20 text-primary'}`}>
                <KeyRound className="w-4 h-4" />
              </div>
            </div>
          </div>

          <motion.div
            key={formStep}
            variants={formVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            transition={{ duration: 0.3 }}
          >
            {formStep === 'phone' && (
              <form onSubmit={handlePhoneSubmit} className="space-y-6">
                <div>
                  <Label className="block text-sm font-medium mb-2 text-center">Phone Number</Label>
                  <div className="flex flex-col items-center gap-2">
                    <div className="w-full max-w-[300px]">
                      <PhoneInput
                        country={userCountry}
                        value={phone}
                        onChange={(value) => {
                          setPhone(value);
                          setError('');
                        }}
                        enableSearch
                        searchPlaceholder="Search country..."
                        containerClass="w-full"
                        inputClass={`w-full p-4 border rounded-lg focus:ring-2 focus:ring-primary/50 ${error ? 'border-destructive' : ''}`}
                        buttonClass="!border-input !bg-background hover:!bg-accent"
                        dropdownClass="!bg-background !border-input"
                        disabled={loading}
                        countryCodeEditable={false}
                        isValid={(value, country) => {
                          if (!value) return false;
                          if (value.length < 8) return false;
                          if (!/^\+?[1-9]\d{1,14}$/.test(value)) return false;
                          return true;
                        }}
                      />
                    </div>
                    {error && (
                      <p className="text-sm text-destructive">{error}</p>
                    )}
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2"
                  disabled={loading}
                >
                  {loading ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <>Send Code <ArrowRight className="w-4 h-4" /></>
                  )}
                </Button>

                <div className="text-center text-sm mt-4">
                  <span className="text-muted-foreground">Remember your password? </span>
                  <Link href="/login" className="text-primary hover:text-primary/80 hover:underline transition-colors">
                    Back to Login
                  </Link>
                </div>
              </form>
            )}

            {formStep === 'verification' && (
              <form onSubmit={handleVerificationSubmit} className="space-y-6">
                <div>
                  <Label className="block text-sm font-medium mb-4 text-center">Verification Code</Label>
                  <div className="flex justify-center items-center gap-3">
                    {[...Array(6)].map((_, i) => (
                      <Input
                        key={i}
                        
                        type="number"
                        maxLength={1}
                        className="w-10 h-10 sm:w-12 sm:h-12 text-center text-lg sm:text-2xl font-semibold rounded-lg focus:ring-2 focus:ring-primary/50 transition-all"
                         value={verificationCode[i] || ''}
                        onChange={(e) => {
                          const newCode = verificationCode.split('');
                          newCode[i] = e.target.value;
                          setVerificationCode(newCode.join(''));
                          if (e.target.value && e.target.nextElementSibling) {
                            (e.target.nextElementSibling as HTMLInputElement).focus();
                          }
                          setError('');
                        }}
                        disabled={loading}
                      />
                    ))}
                  </div>
                  <div className="mt-4 text-center">
                    <button
                      type="button"
                      onClick={handleResendCode}
                      className={`text-sm ${resendTimer > 0 ? 'text-muted-foreground' : 'text-primary hover:underline'}`}
                      disabled={resendTimer > 0 || loading}
                    >
                      {resendTimer > 0 ? `Resend code in ${formatTime(resendTimer)}` : 'Resend code'}
                    </button>
                  </div>
                  {error && (
                    <p className="text-sm text-destructive text-center mt-2">{error}</p>
                  )}
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center justify-center gap-2"
                  disabled={loading || verificationCode.length !== 6}
                >
                  {loading ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <>Verify <CheckCircle2 className="w-4 h-4" /></>
                  )}
                </Button>
              </form>
            )}

            {formStep === 'reset' && (
              <form onSubmit={handlePasswordReset} className="space-y-6">
                {userEmail && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                    <div className="flex items-center">
                      <CheckCircle2 className="w-5 h-5 text-green-600 mr-2" />
                      <div>
                        <p className="text-sm font-medium text-green-800">Account Found</p>
                        <p className="text-sm text-green-600">{userEmail}</p>
                      </div>
                    </div>
                  </div>
                )}

                <div>
                  <Label className="block text-sm font-medium mb-2">New Password</Label>
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      value={passwords.newPassword}
                      onChange={(e) => {
                        setPasswords({...passwords, newPassword: e.target.value});
                        setError('');
                      }}
                      className="pl-10 pr-10"
                      placeholder="Enter new password"
                      required
                      minLength={6}
                      disabled={loading}
                    />
                    <Lock className="w-4 h-4 absolute left-3 top-3 text-muted-foreground" />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={loading}
                    >
                      {showPassword ? (
                        <Eye className="w-4 h-4 text-muted-foreground" />
                      ) : (
                        <EyeOff className="w-4 h-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                </div>

                <div>
                  <Label className="block text-sm font-medium mb-2">Confirm New Password</Label>
                  <div className="relative">
                    <Input
                      type={showConfirmPassword ? "text" : "password"}
                      value={passwords.confirmPassword}
                      onChange={(e) => {
                        setPasswords({...passwords, confirmPassword: e.target.value});
                        setError('');
                      }}
                      className="pl-10 pr-10"
                      placeholder="Confirm new password"
                      required
                      minLength={6}
                      disabled={loading}
                    />
                    <Lock className="w-4 h-4 absolute left-3 top-3 text-muted-foreground" />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      disabled={loading}
                    >
                      {showConfirmPassword ? (
                        <Eye className="w-4 h-4 text-muted-foreground" />
                      ) : (
                        <EyeOff className="w-4 h-4 text-muted-foreground" />
                      )}
                    </Button>
                  </div>
                </div>

                {error && (
                  <p className="text-sm text-destructive text-center">{error}</p>
                )}

                <Button
                  type="submit"
                  className="w-full h-12 text-lg bg-primary text-primary-foreground hover:bg-primary/90 transition-colors"
                  disabled={loading || !passwords.newPassword || !passwords.confirmPassword}
                >
                  {loading ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    'Reset Password'
                  )}
                </Button>
              </form>
            )}
          </motion.div>
        </Card>
      </div>
    </div>
  );
}
