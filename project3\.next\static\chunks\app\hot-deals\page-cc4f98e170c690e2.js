(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1416],{1287:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>u});var s=t(95155),r=t(12115),l=t(61204),c=t(6874),o=t.n(c),i=t(13052),d=t(55607),n=t(79891),p=t(52355),m=t(27737);function u(){let{primaryColor:e}=(0,n.t)(),[a,c]=(0,r.useState)([]),[u,h]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{(async()=>{h(!0);try{var e;let{MakeApiCallAsync:a}=await Promise.all([t.e(3464),t.e(7790)]).then(t.bind(t,65409)),s=await a("get-popular-products-list",null,{requestParameters:{PageNo:1,PageSize:12}},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(null==s||null==(e=s.data)?void 0:e.data)try{let e=JSON.parse(s.data.data);if(console.log("Popular products data:",e),Array.isArray(e)&&e.length>0){let a=e.map(e=>{let a=e.ProductImagesUrl||e.ProductImageUrl,t=null;try{if(a){let e=a;if("string"==typeof a&&(a.startsWith("[")||a.startsWith('"')))try{let t=JSON.parse(a);Array.isArray(t)&&t.length>0?e=t[0].AttachmentURL||t[0]:"string"==typeof t&&(e=t)}catch(t){e=a.replace(/^"|"/g,"")}if("string"==typeof e&&""!==e.trim()&&(e=e.replace(/^"|"$/g,"").trim())){let a=decodeURIComponent(e),s=a.startsWith("/")||a.startsWith("http")?a:"/".concat(a);t=s.startsWith("http")?s:"".concat(l.T.ADMIN_BASE_URL).concat(s)}}}catch(a){console.error("Error processing URL for product",e.ProductId,":",a)}return{ProductId:e.ProductId||e.ProductID||0,ProductName:e.ProductName||"Popular Product",Price:parseFloat(e.Price)||0,OldPrice:e.OldPrice?parseFloat(e.OldPrice):void 0,DiscountPrice:e.DiscountPrice?parseFloat(e.DiscountPrice):void 0,Rating:parseFloat(e.Rating)||0,ProductImageUrl:t||void 0,CategoryName:e.CategoryName||"Popular",StockQuantity:parseInt(e.StockQuantity,10)||0,ProductTypeName:e.ProductTypeName,IQDPrice:parseFloat(e.IQDPrice)||void 0,IsDiscountAllowed:!!e.IsDiscountAllowed,MarkAsNew:!!e.MarkAsNew,SellStartDatetimeUTC:e.SellStartDatetimeUTC,SellEndDatetimeUTC:e.SellEndDatetimeUTC}});c(a)}else console.log("No popular products found"),c([])}catch(e){console.error("Error parsing popular products data:",e),c([])}else console.error("Invalid or empty response from API"),c([])}catch(e){console.error("Error fetching popular products:",e),c([])}finally{h(!1)}})()},[]),(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"flex items-center text-sm mb-6",children:[(0,s.jsx)(o(),{href:"/",className:"text-gray-500 hover:text-primary",children:"Home"}),(0,s.jsx)(i.A,{className:"h-4 w-4 mx-2 text-gray-400"}),(0,s.jsx)("span",{className:"font-medium",children:"Hot Deals"})]}),(0,s.jsxs)("div",{className:"mb-8 flex items-center",children:[(0,s.jsx)(d.A,{className:"h-8 w-8 mr-3",style:{color:e}}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",style:{color:e},children:"Hot Deals"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Special offers and discounts on our best products"})]})]}),u?(0,s.jsx)("div",{className:"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-4",children:Array.from({length:12}).map((e,a)=>(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,s.jsx)("div",{className:"aspect-square",children:(0,s.jsx)(m.E,{className:"h-full w-full"})}),(0,s.jsxs)("div",{className:"p-4 space-y-2",children:[(0,s.jsx)(m.E,{className:"h-4 w-full"}),(0,s.jsx)(m.E,{className:"h-4 w-3/4"}),(0,s.jsx)(m.E,{className:"h-6 w-1/3"})]}),(0,s.jsx)("div",{className:"p-4 pt-0",children:(0,s.jsxs)("div",{className:"flex w-full gap-2",children:[(0,s.jsx)(m.E,{className:"h-10 flex-1"}),(0,s.jsx)(m.E,{className:"h-10 w-10"})]})})]},a))}):a.length>0?(0,s.jsx)("div",{className:"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-4",children:a.map(e=>(0,s.jsx)(p.A,{product:e},e.ProductId))}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[(0,s.jsx)(d.A,{className:"h-8 w-8 text-red-500"}),(0,s.jsx)("h2",{className:"text-2xl font-bold",children:"No Hot Deals Available"})]}),(0,s.jsx)("p",{className:"text-gray-500",children:"Check back later for amazing deals!"})]})]})}},7948:(e,a,t)=>{Promise.resolve().then(t.bind(t,1287))},55607:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]])},61204:(e,a,t)=>{"use strict";t.d(a,{T:()=>s});let s={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",COMMON_CONTROLLER_SUB_URL:"api/v1/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile"}}},92657:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(19946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{e.O(0,[4277,4706,6774,6220,2616,2443,8441,5964,7358],()=>e(e.s=7948)),_N_E=e.O()}]);