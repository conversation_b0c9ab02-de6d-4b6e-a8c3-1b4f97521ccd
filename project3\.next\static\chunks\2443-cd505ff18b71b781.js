"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2443],{14186:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},27737:(e,t,r)=>{r.d(t,{E:()=>l});var s=r(95155),a=r(53999);function l(e){let{className:t,...r}=e;return(0,s.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",t),...r})}},27809:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},38564:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},40646:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},52355:(e,t,r)=>{r.d(t,{A:()=>y});var s=r(95155),a=r(12115),l=r(6874),i=r.n(l),c=r(66766),d=r(14186),n=r(38564),o=r(92657),u=r(51976),m=r(97168),h=r(88482),x=r(88145),f=r(78067),g=r(59268),p=r(53580);r(73339);var v=r(79891);function j(e){let{endDate:t}=e,[r,l]=(0,a.useState)(null);(0,a.useEffect)(()=>{let e=()=>{let e=new Date().getTime(),r=new Date(t).getTime()-e;return r>0?{days:Math.floor(r/864e5),hours:Math.floor(r%864e5/36e5),minutes:Math.floor(r%36e5/6e4),seconds:Math.floor(r%6e4/1e3)}:null},r=setInterval(()=>{l(e())},1e3);return l(e()),()=>clearInterval(r)},[t]);let i=e=>{let{value:t,label:r}=e;return(0,s.jsxs)("div",{className:"flex flex-col items-center mx-0.5",children:[(0,s.jsx)("div",{className:"relative w-8 h-8 sm:w-9 sm:h-9 flex items-center justify-center bg-white rounded-md shadow-sm border border-red-500",children:(0,s.jsx)("span",{className:"text-red-500 font-bold text-xs sm:text-sm",children:String(t).padStart(2,"0")})}),(0,s.jsx)("span",{className:"text-[10px] sm:text-xs text-red-500 mt-0.5 font-medium",children:r})]})};return r?(0,s.jsx)("div",{className:"bg-white/90 backdrop-blur-sm p-1.5 sm:p-2 rounded-lg shadow-md border border-gray-200 max-w-[90%] mx-auto",children:(0,s.jsxs)("div",{className:"flex justify-center items-center space-x-1",children:[r.days>0&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(i,{value:r.days,label:"Days"})}),(0,s.jsx)(i,{value:r.hours,label:"Hrs"}),(0,s.jsx)(i,{value:r.minutes,label:"Min"}),(0,s.jsx)(i,{value:r.seconds,label:"Sec"})]})}):(0,s.jsxs)("div",{className:"px-2 py-1 bg-white/90 backdrop-blur-sm rounded-lg border border-gray-300 flex items-center justify-center shadow-md max-w-[90%] mx-auto",children:[(0,s.jsx)(d.A,{className:"w-3 h-3 mr-1 text-gray-500 animate-pulse"}),(0,s.jsx)("span",{className:"text-xs font-semibold text-gray-500",children:"Sale Ended"})]})}function y(e){let{product:t}=e;(0,f._)();let r=(0,g.n)(),{toast:l}=(0,p.dj)(),{primaryColor:d}=(0,v.t)(),[y,N]=(0,a.useState)(!1),[b,w]=(0,a.useState)(!1),P=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return null==e||isNaN(e)?"IQD"===t?"IQD 0":"$0.00":"IQD"===t?"IQD ".concat(e.toLocaleString()):"$".concat(e.toFixed(2))};t.SellStartDatetimeUTC&&t.SellEndDatetimeUTC;let S=new Date,I=t.SellStartDatetimeUTC?new Date(t.SellStartDatetimeUTC):null,A=t.SellEndDatetimeUTC?new Date(t.SellEndDatetimeUTC):null,D=I&&A&&S>=I&&S<=A;return(0,s.jsxs)(h.Zp,{className:"overflow-hidden flex flex-col h-full relative",children:[(0,s.jsxs)("div",{className:"absolute top-2 left-2 z-10 flex flex-col gap-1",children:[t.MarkAsNew&&(0,s.jsx)(x.E,{variant:"secondary",className:"bg-blue-500 text-white text-xs",children:"New"}),t.DiscountPrice&&t.DiscountPrice>0&&(0,s.jsx)(x.E,{variant:"destructive",className:"bg-red-500 text-white text-xs",children:"Sale"}),D&&!t.DiscountPrice&&(0,s.jsx)(x.E,{variant:"destructive",className:"bg-red-500 text-white text-xs",children:"Sale"})]}),(0,s.jsx)(i(),{href:"/product/".concat(t.ProductId),children:(0,s.jsxs)("div",{className:"aspect-square overflow-hidden relative",children:[(0,s.jsx)("div",{className:"h-full w-full relative",children:(0,s.jsx)(c.default,{src:t.ProductImageUrl||"/placeholder.svg?height=300&width=300",alt:t.ProductName||"Product",fill:!0,className:"object-cover transition-transform hover:scale-105",onError:e=>{e.target.src="/placeholder.svg?height=300&width=300"},priority:!1,loading:"lazy"})}),D&&t.SellEndDatetimeUTC&&(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-2 flex justify-center",children:(0,s.jsx)(j,{endDate:t.SellEndDatetimeUTC})})]})}),(0,s.jsxs)(h.Wu,{className:"pt-4 flex-grow",children:[(0,s.jsxs)("div",{className:"flex items-center mb-2",children:[(0,s.jsx)("div",{className:"flex",children:[void 0,void 0,void 0,void 0,void 0].map((e,r)=>(0,s.jsx)(n.A,{className:"w-4 h-4 ".concat(r<Math.floor(t.Rating||0)?"text-yellow-400 fill-yellow-400":"text-gray-300")},r))}),(0,s.jsxs)("span",{className:"text-xs text-gray-500 ml-1",children:["(",t.Rating||0,")"]})]}),(0,s.jsx)(i(),{href:"/product/".concat(t.ProductId),className:"hover:underline",children:(0,s.jsx)("h3",{className:"font-semibold text-lg line-clamp-2 mb-2",children:t.ProductName||"Unnamed Product"})}),t.ProductTypeName&&(0,s.jsxs)("p",{className:"text-sm text-gray-500 mb-2",children:["Type: ",t.ProductTypeName]}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("div",{className:"flex items-center gap-2",children:t.DiscountPrice?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:"text-lg font-bold text-red-500",children:P(t.DiscountPrice)}),(0,s.jsx)("span",{className:"text-xs text-gray-500 line-through",children:P(t.Price||0)})]}):t.OldPrice&&t.OldPrice>t.Price?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("span",{className:"text-lg font-bold text-red-500",children:P(t.Price||0)}),(0,s.jsx)("span",{className:"text-xs text-gray-500 line-through",children:P(t.OldPrice)})]}):(0,s.jsx)("span",{className:"text-lg font-bold text-primary",children:P(t.Price||0)})}),t.IQDPrice&&(0,s.jsx)("span",{className:"text-sm font-medium text-green-600 mt-0.5",children:P(t.IQDPrice,"IQD")})]})]}),(0,s.jsx)(h.wL,{className:"p-3 pt-1 mt-auto",children:(0,s.jsx)("div",{className:"w-full",children:(0,s.jsxs)("div",{className:"flex items-center justify-between gap-2",children:[(0,s.jsx)(m.$,{variant:"ghost",size:"sm",className:"h-8 px-2 text-xs font-medium flex-1 gap-1.5 text-white hover:opacity-90",style:{backgroundColor:d},asChild:!0,children:(0,s.jsxs)(i(),{href:"/product/".concat(t.ProductId),children:[(0,s.jsx)(o.A,{className:"h-3.5 w-3.5"}),(0,s.jsx)("span",{children:"View"})]})}),(0,s.jsx)(m.$,{variant:"ghost",size:"icon",className:"h-8 w-8 rounded-full",onClick:()=>{if(r.isHydrated){w(!0);try{if(r.isInWishlist(t.ProductId))r.removeFromWishlist(t.ProductId),l.success("".concat(t.ProductName," removed from wishlist"));else{let e="/product/".concat(t.ProductId),s=t.ProductImageUrl||"/placeholder.svg",a=t.DiscountPrice||t.Price;r.addToWishlist(t.ProductId,t.ProductName,e,s,a),l.success("".concat(t.ProductName," added to wishlist"))}}catch(e){console.error("Error updating wishlist:",e),l.error("Failed to update wishlist")}finally{setTimeout(()=>{w(!1)},500)}}},disabled:b,children:(0,s.jsx)(u.A,{className:"h-4 w-4 ".concat(r.isInWishlist(t.ProductId)?"fill-red-500 text-red-500":"")})})]})})})]})}},54416:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},59268:(e,t,r)=>{r.d(t,{Z:()=>i,n:()=>c});var s=r(95155),a=r(12115);let l=(0,a.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,a.useState)([]),[c,d]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let e=localStorage.getItem("wishlist");if(e)try{let t=JSON.parse(e);if(Array.isArray(t)&&t.length>0)if("number"==typeof t[0]){let e=t.map(e=>({productId:e,productName:"Product ".concat(e),productUrl:"/product/".concat(e),addedAt:new Date().toISOString()}));i(e),localStorage.setItem("wishlist",JSON.stringify(e))}else i(t)}catch(e){console.error("Failed to parse wishlist from localStorage:",e)}d(!0)},[]),(0,a.useEffect)(()=>{localStorage.setItem("wishlist",JSON.stringify(r))},[r]),(0,s.jsx)(l.Provider,{value:{wishlistItems:r,addToWishlist:(e,t,s,a,l)=>{r.some(t=>t.productId===e)||i([...r,{productId:e,productName:t,productUrl:s,imageUrl:a,price:l,addedAt:new Date().toISOString()}])},removeFromWishlist:e=>{i(r.filter(t=>t.productId!==e))},isInWishlist:e=>r.some(t=>t.productId===e),getWishlistItem:e=>r.find(t=>t.productId===e),totalItems:r.length,isHydrated:c},children:t})}function c(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useWishlist must be used within a WishlistProvider");return e}},73339:(e,t,r)=>{r.d(t,{S:()=>o,k:()=>n});var s=r(95155),a=r(56671),l=r(40646),i=r(27809),c=r(54416),d=r(97168);let n=e=>{let{productName:t,quantity:r,productImage:n,onViewCart:o}=e;return a.oR.custom(e=>(0,s.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-md w-full",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,s.jsx)(l.A,{className:"w-5 h-5 text-green-600"})})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-900 mb-1",children:"Added to cart"}),(0,s.jsxs)("p",{className:"text-sm text-gray-600 line-clamp-2",children:[r," \xd7 ",t]})]}),n&&(0,s.jsx)("div",{className:"flex-shrink-0 ml-3",children:(0,s.jsx)("img",{src:(e=>{if(!e)return"/placeholder.svg";if(e.startsWith("http://")||e.startsWith("https://")||e.startsWith("/placeholder")||e.startsWith("/images/")||e.startsWith("/assets/"))return e;let t="https://admin.codemedicalapps.com/".replace(/\/$/,""),r=e.startsWith("/")?e:"/".concat(e);return r=r.replace(/\/+/g,"/"),"".concat(t).concat(r)})(n),alt:t,className:"w-12 h-12 rounded-md object-cover border border-gray-200",onError:e=>{let t=e.target;t.onerror=null,t.src="/placeholder.svg"}})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-3",children:[(0,s.jsxs)(d.$,{size:"sm",variant:"outline",onClick:()=>{a.oR.dismiss(e),null==o||o()},className:"h-8 text-xs",children:[(0,s.jsx)(i.A,{className:"w-3 h-3 mr-1"}),"View Cart"]}),(0,s.jsx)(d.$,{size:"sm",variant:"ghost",onClick:()=>a.oR.dismiss(e),className:"h-8 text-xs text-gray-500 hover:text-gray-700",children:"Continue Shopping"})]})]}),(0,s.jsx)("button",{onClick:()=>a.oR.dismiss(e),className:"flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)(c.A,{className:"w-4 h-4"})})]})}),{duration:5e3,position:"top-right"})},o=e=>{let{productName:t,quantity:r}=e;return a.oR.success((0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(l.A,{className:"w-4 h-4 text-green-600"}),(0,s.jsxs)("span",{children:[r," \xd7 ",t," added to cart"]})]}),{duration:3e3})}},78067:(e,t,r)=>{r.d(t,{_:()=>c,e:()=>i});var s=r(95155),a=r(12115);let l=(0,a.createContext)(void 0);function i(e){let{children:t}=e,[r,i]=(0,a.useState)([]),[c,d]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=localStorage.getItem("cart");if(e)try{i(JSON.parse(e))}catch(e){console.error("Failed to parse cart from localStorage:",e)}d(!0)},[]),(0,a.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(r))},[r]);let n=e=>{i(t=>t.filter(t=>t.id!==e))},o=r.reduce((e,t)=>e+t.quantity,0);(0,a.useEffect)(()=>{localStorage.setItem("cart",JSON.stringify(r))},[r]);let u=r.reduce((e,t)=>e+(t.discountPrice?Math.min(t.discountPrice,t.adjustedPrice):t.adjustedPrice)*t.quantity,0),m=r.reduce((e,t)=>e+(t.adjustedIqdPrice||t.iqdPrice||0)*t.quantity,0);return(0,s.jsx)(l.Provider,{value:{items:r,addToCart:function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],s=arguments.length>3?arguments[3]:void 0,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1500;i(l=>{let i=e.price,c=s||Math.round(e.price*a),d=c;r.forEach(t=>{if(t.PriceAdjustment&&t.PriceAdjustmentType){let r=e.originalPrice||e.price;switch(t.PriceAdjustmentType){case 1:i+=t.PriceAdjustment,d+=Math.round(t.PriceAdjustment*a);break;case 2:let s=r*t.PriceAdjustment/100;i+=s,d+=Math.round(s*a)}}});let n=l.findIndex(t=>{var s;return t.id===e.id&&JSON.stringify(null==(s=t.attributes)?void 0:s.sort((e,t)=>e.ProductAttributeID-t.ProductAttributeID))===JSON.stringify(null==r?void 0:r.sort((e,t)=>e.ProductAttributeID-t.ProductAttributeID))});if(!(n>=0))return[...l,{...e,iqdPrice:c,adjustedIqdPrice:Math.max(0,d),quantity:t,attributes:r,adjustedPrice:Math.max(0,i),originalPrice:e.originalPrice}];{let e=[...l];return e[n].quantity+=t,e}})},removeFromCart:n,updateQuantity:(e,t)=>{if(t<=0)return void n(e);i(r=>r.map(r=>r.id===e?{...r,quantity:t}:r))},clearCart:()=>{i([])},totalItems:o,subtotal:u,subtotalIQD:m,total:u,totalIQD:m,isHydrated:c},children:t})}function c(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}},88145:(e,t,r)=>{r.d(t,{E:()=>c});var s=r(95155),a=r(74466),l=r(53999);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{className:(0,l.cn)(i({variant:r}),t),...a})}}}]);