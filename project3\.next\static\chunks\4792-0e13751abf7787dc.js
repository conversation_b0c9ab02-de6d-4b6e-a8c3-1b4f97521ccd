"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4792],{5196:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},19420:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},24792:(e,a,t)=>{t.d(a,{Y:()=>g});var r=t(95155),s=t(12115),n=t(97168),i=t(88482),l=t(81586),d=t(19420),o=t(19946);let c=(0,o.A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]),m=(0,o.A)("Gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]]);var h=t(29799),p=t(5196);let x=(0,o.A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),u=(0,o.A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var y=t(56671);let f={"Zain cash (Iraq)":{id:1,name:"Zain cash (Iraq)",image:"/Zaincash iraq.png",number:"***********",type:"phone",instructions:"Send payment to the Zain Cash number below and keep the transaction receipt for your records.",location:"Inside Iraq"},"Zain Cash":{id:1,name:"Zain Cash",image:"/Zaincash iraq.png",number:"***********",type:"phone",instructions:"Send payment to the Zain Cash number below and keep the transaction receipt for your records.",location:"Inside Iraq"},"Rafidain Bank":{id:2,name:"Rafidain Bank",image:"/Qicard iraq.png",number:"**********",accountNumber:"**********",bankName:"Rafidain Bank",type:"account",instructions:"Transfer the payment amount to the Rafidain Bank account number below.",location:"Inside Iraq"},"Asia Pay":{id:3,name:"Asia Pay",image:"/Asia pay.png",number:"***********",type:"phone",instructions:"Send payment to the Asia Pay number below and save the transaction confirmation.",location:"Inside Iraq"},PayPal:{id:4,name:"PayPal",image:"/Paypal.png",link:"https://paypal.me/FatimahNaser?country.x=JO&locale.x=en_US",type:"link",instructions:"Click the PayPal button below to complete your payment securely through PayPal.",location:"Outside Iraq"},"Amazon Gift":{id:5,name:"Amazon Gift",image:"/Amazon gift card.png",link:"https://www.amazon.com/Amazon-eGift-Card-Logo-Animated/dp/B07PCMWTSG/ref=mp_s_a_1_1?adgrpid=************&dib=eyJ2IjoiMSJ9.y343JC2nqCCCtAt_MaFdYdSEoDk1IL8C8OVn3MADfESEozTH6jWzFIJ4WqlXn7_W-n2IrnPR-rfE3Spk4QYVuOOL7cvbuK9Esy0CXQivH6v0c4KW6RfZeH8pYn15Gdj-s62p0V-fiHzAE12D4YOgeY2zQf3sUuAQF30eHiR7nSfSyvGj9P0M79Suz3VRAqqxS64beG-r2SJhB_Y_apq-6Q.gbfTjpxr2hWpO9dWg-U8dthgvZM21cwxR6PrsZBpG38&dib_tag=se&hvadid=************&hvdev=m&hvlocphy=9211521&hvnetw=g&hvqmt=e&hvrand=15003258399388157606&hvtargid=kwd-2389411675177&hydadcr=22339_13333066&keywords=amazon%27+gift+card&mcid=90ecf431d9b83733b420d0f87065fc78&qid=1748814128&sr=8-1",email:"<EMAIL>",type:"gift_card",instructions:"Purchase an Amazon eGift Card using the link below and send it to the specified email address.",location:"Outside Iraq"},"Amazon Gift Card":{id:5,name:"Amazon Gift Card",image:"/Amazon gift card.png",link:"https://www.amazon.com/Amazon-eGift-Card-Logo-Animated/dp/B07PCMWTSG/ref=mp_s_a_1_1?adgrpid=************&dib=eyJ2IjoiMSJ9.y343JC2nqCCCtAt_MaFdYdSEoDk1IL8C8OVn3MADfESEozTH6jWzFIJ4WqlXn7_W-n2IrnPR-rfE3Spk4QYVuOOL7cvbuK9Esy0CXQivH6v0c4KW6RfZeH8pYn15Gdj-s62p0V-fiHzAE12D4YOgeY2zQf3sUuAQF30eHiR7nSfSyvGj9P0M79Suz3VRAqqxS64beG-r2SJhB_Y_apq-6Q.gbfTjpxr2hWpO9dWg-U8dthgvZM21cwxR6PrsZBpG38&dib_tag=se&hvadid=************&hvdev=m&hvlocphy=9211521&hvnetw=g&hvqmt=e&hvrand=15003258399388157606&hvtargid=kwd-2389411675177&hydadcr=22339_13333066&keywords=amazon%27+gift+card&mcid=90ecf431d9b83733b420d0f87065fc78&qid=1748814128&sr=8-1",email:"<EMAIL>",type:"gift_card",instructions:"Purchase an Amazon eGift Card using the link below and send it to the specified email address.",location:"Outside Iraq"},"Cash on delivery":{id:6,name:"Cash on delivery",image:"/Cash on delivery.png",type:"cash",instructions:"Pay in cash when your order is delivered. Available for all provinces within Iraq.",location:"Inside Iraq"},"Cash on Delivery":{id:6,name:"Cash on Delivery",image:"/Cash on delivery.png",type:"cash",instructions:"Pay in cash when your order is delivered. Available for all provinces within Iraq.",location:"Inside Iraq"}};function g(e){let{paymentMethodName:a,paymentMethodId:t}=e,[o,g]=(0,s.useState)({}),b=f[a]||Object.values(f).find(e=>e.name.toLowerCase().includes(a.toLowerCase())||a.toLowerCase().includes(e.name.toLowerCase()));if(!b)return(0,r.jsx)(i.Zp,{className:"mt-4 p-4 bg-gray-50 border border-gray-200",children:(0,r.jsxs)("div",{className:"text-center py-4",children:[(0,r.jsx)(l.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:['Payment method details for "',a,'" are not available.']}),(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Please contact support for payment instructions."})]})});let v=async(e,a)=>{try{await navigator.clipboard.writeText(e),g(e=>({...e,[a]:!0})),y.oR.success("".concat(a," copied to clipboard!"),{duration:2e3,style:{background:"#10B981",color:"white",border:"none"}}),setTimeout(()=>{g(e=>({...e,[a]:!1}))},2e3)}catch(t){try{let t=document.createElement("textarea");t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),g(e=>({...e,[a]:!0})),y.oR.success("".concat(a," copied to clipboard!")),setTimeout(()=>{g(e=>({...e,[a]:!1}))},2e3)}catch(e){y.oR.error("Failed to copy to clipboard. Please copy manually.")}}},j=e=>{switch(e){case"phone":return(0,r.jsx)(d.A,{className:"h-4 w-4"});case"account":return(0,r.jsx)(c,{className:"h-4 w-4"});case"gift_card":return(0,r.jsx)(m,{className:"h-4 w-4"});case"cash":return(0,r.jsx)(h.A,{className:"h-4 w-4"});default:return(0,r.jsx)(l.A,{className:"h-4 w-4"})}};return(0,r.jsx)(i.Zp,{className:"mt-4 p-6 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border border-blue-200 shadow-lg",children:(0,r.jsxs)("div",{className:"flex items-start gap-6",children:[(0,r.jsx)("div",{className:"flex-shrink-0",style:{display:"none"},children:(0,r.jsx)("div",{className:"w-20 h-20 rounded-xl bg-white border border-gray-200 shadow-sm flex items-center justify-center",children:j(b.type)})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("h4",{className:"font-bold text-xl text-blue-900",children:b.name}),j(b.type)]}),(0,r.jsx)("span",{className:"text-xs bg-blue-100 text-blue-700 px-3 py-1 rounded-full font-medium",children:b.location})]}),(0,r.jsx)("p",{className:"text-sm text-blue-700 mb-6 leading-relaxed",children:b.instructions}),(0,r.jsxs)("div",{className:"space-y-4",children:[b.number&&(0,r.jsx)("div",{className:"group",children:(0,r.jsxs)("div",{className:"flex items-center gap-3 p-4 bg-white rounded-xl border border-blue-200 shadow-sm hover:shadow-md transition-all duration-200",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:["phone"===b.type?(0,r.jsx)(d.A,{className:"h-4 w-4 text-blue-600"}):(0,r.jsx)(c,{className:"h-4 w-4 text-blue-600"}),(0,r.jsx)("p",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide",children:"phone"===b.type?"Phone Number":"account"===b.type?"Account Number":"Number"})]}),(0,r.jsx)("p",{className:"font-mono text-xl font-bold text-gray-900 tracking-wider",children:b.number}),b.bankName&&(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:["Bank: ",b.bankName]})]}),(0,r.jsx)(n.$,{type:"button",variant:"outline",size:"sm",onClick:e=>{e.preventDefault(),e.stopPropagation(),v(b.number,"phone"===b.type?"Phone Number":"Account Number")},className:"flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300 transition-colors",children:o["phone"===b.type?"Phone Number":"Account Number"]?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-green-600 font-medium",children:"Copied!"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Copy"})]})})]})}),b.email&&(0,r.jsx)("div",{className:"group",children:(0,r.jsxs)("div",{className:"flex items-center gap-3 p-4 bg-white rounded-xl border border-blue-200 shadow-sm hover:shadow-md transition-all duration-200",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsxs)("svg",{className:"h-4 w-4 text-blue-600",fill:"currentColor",viewBox:"0 0 20 20",children:[(0,r.jsx)("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),(0,r.jsx)("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]}),(0,r.jsx)("p",{className:"text-xs font-medium text-gray-600 uppercase tracking-wide",children:"Email Address"})]}),(0,r.jsx)("p",{className:"font-mono text-lg font-semibold text-gray-900 break-all",children:b.email})]}),(0,r.jsx)(n.$,{type:"button",variant:"outline",size:"sm",onClick:e=>{e.preventDefault(),e.stopPropagation(),v(b.email,"Email Address")},className:"flex items-center gap-2 hover:bg-blue-50 hover:border-blue-300 transition-colors",children:o["Email Address"]?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"h-4 w-4 text-green-600"}),(0,r.jsx)("span",{className:"text-green-600 font-medium",children:"Copied!"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Copy"})]})})]})}),b.link&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)(n.$,{type:"button",onClick:e=>{var a;e.preventDefault(),e.stopPropagation(),a=b.link,window.open(a,"_blank","noopener,noreferrer")},className:"w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white flex items-center justify-center gap-3 py-3 text-base font-semibold shadow-lg hover:shadow-xl transition-all duration-200",children:[(0,r.jsx)(u,{className:"h-5 w-5"}),b.name.includes("PayPal")?"Pay with PayPal":b.name.includes("Amazon")?"Buy Amazon Gift Card":"Open Payment Link"]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl border border-gray-200 p-3",children:[(0,r.jsx)("p",{className:"text-xs font-medium text-gray-600 mb-2 uppercase tracking-wide",children:"Payment Link"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("input",{type:"text",value:b.link,readOnly:!0,className:"flex-1 px-3 py-2 text-xs bg-gray-50 border border-gray-200 rounded-lg font-mono text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500"}),(0,r.jsx)(n.$,{type:"button",variant:"outline",size:"sm",onClick:e=>{e.preventDefault(),e.stopPropagation(),v(b.link,"Payment Link")},className:"flex items-center gap-1 hover:bg-blue-50 hover:border-blue-300",children:o["Payment Link"]?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"h-3 w-3 text-green-600"}),(0,r.jsx)("span",{className:"text-green-600 text-xs font-medium",children:"Copied!"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x,{className:"h-3 w-3"}),(0,r.jsx)("span",{className:"text-xs",children:"Copy"})]})})]})]})]}),"cash"===b.type&&(0,r.jsx)("div",{className:"p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(h.A,{className:"h-4 w-4 text-green-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-semibold text-green-700 mb-1",children:"✅ No advance payment required"}),(0,r.jsx)("p",{className:"text-sm text-green-700",children:"Pay in cash when your order is delivered to your doorstep."}),(0,r.jsx)("p",{className:"text-xs text-green-600 mt-2",children:"\uD83D\uDCCD Available for all provinces within Iraq. Additional delivery fees may apply based on your location."})]})]})})]}),(0,r.jsx)("div",{className:"mt-6 p-4 bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-xl",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"h-4 w-4 text-amber-600",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-semibold text-amber-700 mb-1",children:"Important Notice"}),(0,r.jsx)("p",{className:"text-sm text-amber-700",children:"After completing your payment, we will contact you within 24 hours to confirm your order and arrange delivery details."}),(0,r.jsx)("p",{className:"text-xs text-amber-600 mt-1",children:"Please keep your payment receipt/confirmation for reference."})]})]})})]})]})})}},29799:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},81586:(e,a,t)=>{t.d(a,{A:()=>r});let r=(0,t(19946).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},88482:(e,a,t)=>{t.d(a,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>l,wL:()=>m});var r=t(95155),s=t(12115),n=t(53999);let i=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...s})});i.displayName="Card";let l=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...s})});l.displayName="CardHeader";let d=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("h3",{ref:a,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...s})});d.displayName="CardTitle";let o=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("p",{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",t),...s})});o.displayName="CardDescription";let c=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,n.cn)("p-6 pt-0",t),...s})});c.displayName="CardContent";let m=s.forwardRef((e,a)=>{let{className:t,...s}=e;return(0,r.jsx)("div",{ref:a,className:(0,n.cn)("flex items-center p-6 pt-0",t),...s})});m.displayName="CardFooter"}}]);