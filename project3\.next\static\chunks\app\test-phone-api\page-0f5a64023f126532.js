(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2023,5409,7790],{6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>a,t:()=>s});var o=t(12115);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function s(...e){return r=>{let t=!1,o=e.map(e=>{let o=n(e,r);return t||"function"!=typeof o||(t=!0),o});if(t)return()=>{for(let r=0;r<o.length;r++){let t=o[r];"function"==typeof t?t():n(e[r],null)}}}}function a(...e){return o.useCallback(s(...e),e)}},11461:(e,r,t)=>{Promise.resolve().then(t.bind(t,77566))},53999:(e,r,t)=>{"use strict";t.d(r,{cn:()=>s});var o=t(52596),n=t(39688);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,o.$)(r))}},61204:(e,r,t)=>{"use strict";t.d(r,{T:()=>o});let o={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",COMMON_CONTROLLER_SUB_URL:"api/v1/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile"}}},65409:(e,r,t)=>{"use strict";t.d(r,{$g:()=>u,Config:()=>s,MakeApiCallAsync:()=>i,XX:()=>c,k6:()=>d});var o=t(23464),n=t(61204);o.A.defaults.timeout=3e4,"https:"===window.location.protocol&&n.T.ADMIN_BASE_URL.includes("localhost")&&(o.A.defaults.httpsAgent={rejectUnauthorized:!1});let s={ADMIN_BASE_URL:n.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...n.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",RESET_PASSWORD_FIREBASE:"reset-password-firebase",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile",INSERT_PRODUCT_REVIEW:"Insert-Product-Review",GET_PRODUCT_REVIEWS:"get-product-reviews"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},a=async()=>{try{{try{let e=await fetch("/api/auth/get-token",{method:"GET",credentials:"include"});if(e.ok){let r=await e.json();if(r.token)return console.log("\uD83D\uDD10 Retrieved JWT token from secure HttpOnly cookie"),r.token}}catch(e){console.log("API token retrieval failed, trying client-side cookies:",e)}for(let e of document.cookie.split(";")){let[r,t]=e.trim().split("=");if("auth_token"===r)return console.log("\uD83D\uDD10 Retrieved JWT token from client-side cookie"),decodeURIComponent(t)}let e=localStorage.getItem("token")||localStorage.getItem("authToken");if(e)return console.log("⚠️ Using token from localStorage (migrating to secure cookies)"),localStorage.removeItem("token"),localStorage.removeItem("authToken"),e}return null}catch(e){return console.error("Error getting token for header:",e),null}},l=async()=>{try{{for(let t of document.cookie.split(";")){let[o,n]=t.trim().split("=");if("auth_user"===o)try{var e,r;let t=JSON.parse(decodeURIComponent(n)),o=(null==(e=t.UserId)?void 0:e.toString())||(null==(r=t.UserID)?void 0:r.toString());if(o)return console.log("\uD83D\uDD10 Retrieved User ID from secure cookie"),o}catch(e){console.warn("Failed to parse user data from cookie:",e)}}let t=localStorage.getItem("userId")||localStorage.getItem("userID");if(t)return console.log("⚠️ Using User ID from localStorage (migrating to secure cookies)"),localStorage.removeItem("userId"),localStorage.removeItem("userID"),t}return null}catch(e){return console.error("Error getting user ID for header:",e),null}},i=async function(e,r,t,n,i){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let d=(e=>{if(!e)return e;let r=JSON.parse(JSON.stringify(e));return r.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from request body (will use JWT token instead)"),delete r.UserId),r.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from request body (will use JWT token instead)"),delete r.UserID),r.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from request body (will use JWT token instead)"),delete r.user_id),r.requestParameters&&(r.requestParameters.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from requestParameters (will use JWT token instead)"),delete r.requestParameters.UserId),r.requestParameters.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from requestParameters (will use JWT token instead)"),delete r.requestParameters.UserID),r.requestParameters.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from requestParameters (will use JWT token instead)"),delete r.requestParameters.user_id)),r})(t),c={...n};if(!c.hasOwnProperty("Authorization")){let e=await a();e&&(c.Authorization="Bearer "+e,console.log("\uD83D\uDD10 Added JWT token to Authorization header"))}if(!c.hasOwnProperty("Token")){let e=await a();c.Token=null!=e?e:"",e&&console.log("\uD83D\uDD10 Added JWT token to Token header (backward compatibility)")}if(!c.hasOwnProperty("UserID")){let e=await l();c.UserID=null!=e?e:""}c.hasOwnProperty("Accept")||(c.Accept="application/json"),c.hasOwnProperty("Content-Type")||(c["Content-Type"]="application/json");let u=s.ADMIN_BASE_URL+(null===r||void 0==r?s.DYNAMIC_METHOD_SUB_URL:r)+e;i=null!=i?i:"POST";let p={headers:c,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===i)return await o.A.post(u,d,p);if("GET"==i)return p.params=d,await o.A.get(u,p);return{data:{errorMessage:"Unsupported method type: ".concat(i),status:"method_not_supported"}}}catch(r){console.error("API call failed:",r);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(r&&"object"==typeof r&&"response"in r&&r.response){var d,c;let t=null==(d=r.response)?void 0:d.data;e.data={errorMessage:(null==t?void 0:t.errorMessage)||"An error occurred while processing your request.",status:null==(c=r.response)?void 0:c.status}}else if(r&&"object"==typeof r&&"request"in r){let t="Network error: No response received from server.";r.message&&r.message.includes("Network Error")&&(t="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+s.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:t,status:"network_error"}}else e.data={errorMessage:r&&"object"==typeof r&&"message"in r?r.message:"An unexpected error occurred",status:"request_error"};return e}},d=async()=>{try{return console.log("Using default currency rate (1430) - API endpoint not available"),1430}catch(e){return console.error("Error fetching currency rate:",e),1430}},c=(e,r)=>Math.round(e*r),u=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return null==e||isNaN(e)?"IQD"===r?"0 IQD":"$0.00":"IQD"===r?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>a});var o=t(52596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=o.$,a=(e,r)=>t=>{var o;if((null==r?void 0:r.variants)==null)return s(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:a,defaultVariants:l}=r,i=Object.keys(a).map(e=>{let r=null==t?void 0:t[e],o=null==l?void 0:l[e];if(null===r)return null;let s=n(r)||n(o);return a[e][s]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,o]=r;return void 0===o||(e[t]=o),e},{});return s(e,i,null==r||null==(o=r.compoundVariants)?void 0:o.reduce((e,r)=>{let{class:t,className:o,...n}=r;return Object.entries(n).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...l,...d}[r]):({...l,...d})[r]===t})?[...e,t,o]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},77566:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var o=t(95155),n=t(12115),s=t(97168),a=t(89852),l=t(88482),i=t(65409);function d(){var e,r,t,d,c,u,p;let[f,g]=(0,n.useState)("+65464646"),[m,h]=(0,n.useState)(null),[_,v]=(0,n.useState)(!1),y=async()=>{v(!0);try{let e={requestParameters:{PhoneNumber:f}};console.log("Testing phone API with:",e);let r=await (0,i.MakeApiCallAsync)(i.Config.END_POINT_NAMES.GET_USER_BY_PHONE,null,e,{Accept:"application/json","Content-Type":"application/json"},"POST",!0);console.log("API Response:",r),h(r)}catch(e){console.error("Error testing phone API:",e),h({error:e instanceof Error?e.message:String(e)})}finally{v(!1)}};return(0,o.jsx)("div",{className:"min-h-screen bg-background p-8",children:(0,o.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Test Phone API"}),(0,o.jsx)(l.Zp,{className:"p-6 mb-6",children:(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Phone Number"}),(0,o.jsx)(a.p,{type:"text",value:f,onChange:e=>g(e.target.value),placeholder:"+65464646"})]}),(0,o.jsx)(s.$,{onClick:y,disabled:_,className:"w-full",children:_?"Testing...":"Test API"})]})}),m&&(0,o.jsxs)(l.Zp,{className:"p-6",children:[(0,o.jsx)("h2",{className:"text-lg font-semibold mb-4",children:"API Response:"}),(0,o.jsx)("pre",{className:"bg-gray-100 p-4 rounded-lg overflow-auto text-sm",children:JSON.stringify(m,null,2)}),(0,o.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Status Code:"})," ",null==(e=m.data)?void 0:e.statusCode]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Status Message:"})," ",null==(r=m.data)?void 0:r.statusMessage]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Data:"})," ",null==(t=m.data)?void 0:t.data]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Data Type:"})," ",typeof(null==(d=m.data)?void 0:d.data)]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Error Message:"})," ",(null==(c=m.data)?void 0:c.errorMessage)||"None"]}),(null==(u=m.data)?void 0:u.data)==="[]"&&(0,o.jsx)("p",{className:"text-red-600 font-semibold",children:"✓ User not found (empty array as string)"}),(null==(p=m.data)?void 0:p.data)&&"[]"!==m.data.data&&(0,o.jsx)("p",{className:"text-green-600 font-semibold",children:"✓ User found"})]})]})]})})}},88482:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>a,aR:()=>l,wL:()=>u});var o=t(95155),n=t(12115),s=t(53999);let a=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,o.jsx)("div",{ref:r,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...n})});a.displayName="Card";let l=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,o.jsx)("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",t),...n})});l.displayName="CardHeader";let i=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,o.jsx)("h3",{ref:r,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",t),...n})});i.displayName="CardTitle";let d=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,o.jsx)("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",t),...n})});d.displayName="CardDescription";let c=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,o.jsx)("div",{ref:r,className:(0,s.cn)("p-6 pt-0",t),...n})});c.displayName="CardContent";let u=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,o.jsx)("div",{ref:r,className:(0,s.cn)("flex items-center p-6 pt-0",t),...n})});u.displayName="CardFooter"},89852:(e,r,t)=>{"use strict";t.d(r,{p:()=>a});var o=t(95155),n=t(12115),s=t(53999);let a=n.forwardRef((e,r)=>{let{className:t,type:n,...a}=e;return(0,o.jsx)("input",{type:n,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...a})});a.displayName="Input"},97168:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>i});var o=t(95155),n=t(12115),s=t(99708),a=t(74466),l=t(53999);let i=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,r)=>{let{className:t,variant:n,size:a,asChild:d=!1,...c}=e,u=d?s.DX:"button";return(0,o.jsx)(u,{className:(0,l.cn)(i({variant:n,size:a,className:t})),ref:r,...c})});d.displayName="Button"},99708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>l,Dc:()=>d,TL:()=>a});var o=t(12115),n=t(6101),s=t(95155);function a(e){let r=function(e){let r=o.forwardRef((e,r)=>{let{children:t,...s}=e;if(o.isValidElement(t)){var a;let e,l,i=(a=t,(l=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(l=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),d=function(e,r){let t={...r};for(let o in r){let n=e[o],s=r[o];/^on[A-Z]/.test(o)?n&&s?t[o]=(...e)=>{let r=s(...e);return n(...e),r}:n&&(t[o]=n):"style"===o?t[o]={...n,...s}:"className"===o&&(t[o]=[n,s].filter(Boolean).join(" "))}return{...e,...t}}(s,t.props);return t.type!==o.Fragment&&(d.ref=r?(0,n.t)(r,i):i),o.cloneElement(t,d)}return o.Children.count(t)>1?o.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=o.forwardRef((e,t)=>{let{children:n,...a}=e,l=o.Children.toArray(n),i=l.find(c);if(i){let e=i.props.children,n=l.map(r=>r!==i?r:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,s.jsx)(r,{...a,ref:t,children:o.isValidElement(e)?o.cloneElement(e,void 0,n):null})}return(0,s.jsx)(r,{...a,ref:t,children:n})});return t.displayName=`${e}.Slot`,t}var l=a("Slot"),i=Symbol("radix.slottable");function d(e){let r=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=i,r}function c(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}}},e=>{e.O(0,[4277,3464,8441,5964,7358],()=>e(e.s=11461)),_N_E=e.O()}]);