"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5409,7790,8816],{61204:(e,o,r)=>{r.d(o,{T:()=>t});let t={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",COMMON_CONTROLLER_SUB_URL:"api/v1/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile"}}},65409:(e,o,r)=>{r.d(o,{$g:()=>u,Config:()=>a,MakeApiCallAsync:()=>i,XX:()=>d,k6:()=>c});var t=r(23464),s=r(61204);t.A.defaults.timeout=3e4,"https:"===window.location.protocol&&s.T.ADMIN_BASE_URL.includes("localhost")&&(t.A.defaults.httpsAgent={rejectUnauthorized:!1});let a={ADMIN_BASE_URL:s.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...s.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",RESET_PASSWORD_FIREBASE:"reset-password-firebase",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile",INSERT_PRODUCT_REVIEW:"Insert-Product-Review",GET_PRODUCT_REVIEWS:"get-product-reviews"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},n=async()=>{try{{try{let e=await fetch("/api/auth/get-token",{method:"GET",credentials:"include"});if(e.ok){let o=await e.json();if(o.token)return console.log("\uD83D\uDD10 Retrieved JWT token from secure HttpOnly cookie"),o.token}}catch(e){console.log("API token retrieval failed, trying client-side cookies:",e)}for(let e of document.cookie.split(";")){let[o,r]=e.trim().split("=");if("auth_token"===o)return console.log("\uD83D\uDD10 Retrieved JWT token from client-side cookie"),decodeURIComponent(r)}let e=localStorage.getItem("token")||localStorage.getItem("authToken");if(e)return console.log("⚠️ Using token from localStorage (migrating to secure cookies)"),localStorage.removeItem("token"),localStorage.removeItem("authToken"),e}return null}catch(e){return console.error("Error getting token for header:",e),null}},l=async()=>{try{{for(let r of document.cookie.split(";")){let[t,s]=r.trim().split("=");if("auth_user"===t)try{var e,o;let r=JSON.parse(decodeURIComponent(s)),t=(null==(e=r.UserId)?void 0:e.toString())||(null==(o=r.UserID)?void 0:o.toString());if(t)return console.log("\uD83D\uDD10 Retrieved User ID from secure cookie"),t}catch(e){console.warn("Failed to parse user data from cookie:",e)}}let r=localStorage.getItem("userId")||localStorage.getItem("userID");if(r)return console.log("⚠️ Using User ID from localStorage (migrating to secure cookies)"),localStorage.removeItem("userId"),localStorage.removeItem("userID"),r}return null}catch(e){return console.error("Error getting user ID for header:",e),null}},i=async function(e,o,r,s,i){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let c=(e=>{if(!e)return e;let o=JSON.parse(JSON.stringify(e));return o.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from request body (will use JWT token instead)"),delete o.UserId),o.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from request body (will use JWT token instead)"),delete o.UserID),o.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from request body (will use JWT token instead)"),delete o.user_id),o.requestParameters&&(o.requestParameters.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from requestParameters (will use JWT token instead)"),delete o.requestParameters.UserId),o.requestParameters.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from requestParameters (will use JWT token instead)"),delete o.requestParameters.UserID),o.requestParameters.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from requestParameters (will use JWT token instead)"),delete o.requestParameters.user_id)),o})(r),d={...s};if(!d.hasOwnProperty("Authorization")){let e=await n();e&&(d.Authorization="Bearer "+e,console.log("\uD83D\uDD10 Added JWT token to Authorization header"))}if(!d.hasOwnProperty("Token")){let e=await n();d.Token=null!=e?e:"",e&&console.log("\uD83D\uDD10 Added JWT token to Token header (backward compatibility)")}if(!d.hasOwnProperty("UserID")){let e=await l();d.UserID=null!=e?e:""}d.hasOwnProperty("Accept")||(d.Accept="application/json"),d.hasOwnProperty("Content-Type")||(d["Content-Type"]="application/json");let u=a.ADMIN_BASE_URL+(null===o||void 0==o?a.DYNAMIC_METHOD_SUB_URL:o)+e;i=null!=i?i:"POST";let g={headers:d,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===i)return await t.A.post(u,c,g);if("GET"==i)return g.params=c,await t.A.get(u,g);return{data:{errorMessage:"Unsupported method type: ".concat(i),status:"method_not_supported"}}}catch(o){console.error("API call failed:",o);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(o&&"object"==typeof o&&"response"in o&&o.response){var c,d;let r=null==(c=o.response)?void 0:c.data;e.data={errorMessage:(null==r?void 0:r.errorMessage)||"An error occurred while processing your request.",status:null==(d=o.response)?void 0:d.status}}else if(o&&"object"==typeof o&&"request"in o){let r="Network error: No response received from server.";o.message&&o.message.includes("Network Error")&&(r="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+a.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:r,status:"network_error"}}else e.data={errorMessage:o&&"object"==typeof o&&"message"in o?o.message:"An unexpected error occurred",status:"request_error"};return e}},c=async()=>{try{return console.log("Using default currency rate (1430) - API endpoint not available"),1430}catch(e){return console.error("Error fetching currency rate:",e),1430}},d=(e,o)=>Math.round(e*o),u=function(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return null==e||isNaN(e)?"IQD"===o?"0 IQD":"$0.00":"IQD"===o?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},98816:(e,o,r)=>{r.d(o,{J:()=>i,v:()=>l});var t=r(95155),s=r(12115),a=r(65409);let n=(0,s.createContext)(void 0);function l(e){let{children:o}=e,[r,l]=(0,s.useState)(null),[i,c]=(0,s.useState)(null),[d,u]=(0,s.useState)(!0);s.useEffect(()=>{(async()=>{try{let e=await fetch("/api/auth/get-token",{method:"GET",credentials:"include",headers:{"Content-Type":"application/json"}});if(!e.ok)throw Error("HTTP error! status: ".concat(e.status));let o=await e.json();o.token&&"null"!==o.token&&c(o.token);let r=document.cookie.split(";"),t=null;for(let e of r){let[o,r]=e.trim().split("=");if("auth_user_public"===o)try{t=JSON.parse(decodeURIComponent(r)),console.log("✅ UserProvider: Found user data in public cookie:",t),console.log("\uD83D\uDD0D UserProvider Debug - IsActive in cookie:",null==t?void 0:t.IsActive,"(type:",typeof(null==t?void 0:t.IsActive),")"),console.log("\uD83D\uDD0D UserProvider Debug - CreatedOn in cookie:",null==t?void 0:t.CreatedOn,"(type:",typeof(null==t?void 0:t.CreatedOn),")"),console.log("\uD83D\uDD0D UserProvider Debug - All cookie keys:",t?Object.keys(t):"null");break}catch(e){console.error("Error parsing user public cookie:",e)}}if(!t)for(let e of r){let[o,r]=e.trim().split("=");if("auth_user"===o)try{t=JSON.parse(decodeURIComponent(r)),console.log("✅ UserProvider: Found user data in HttpOnly cookie:",t);break}catch(e){console.error("Error parsing user HttpOnly cookie:",e)}}t&&o.token&&"null"!==o.token?(console.log("✅ UserProvider: Setting user and token from cookies"),l(t)):(console.log("❌ UserProvider: No valid user data or token found",{hasUserData:!!t,hasToken:!!(o.token&&"null"!==o.token)}),l(null),c(null))}catch(e){console.error("UserProvider: Error fetching auth data:",e),l(null),c(null)}finally{u(!1)}})()},[]);let g=async(e,o,r)=>{try{var t,s;u(!0);let n={requestParameters:{Email:e,Password:o,...r&&{RecaptchaToken:r}}},i=await (0,a.MakeApiCallAsync)(a.Config.END_POINT_NAMES.GET_USER_LOGIN,null,n,{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(console.log("=== LOGIN API RESPONSE DEBUG ==="),console.log("Full response:",i),console.log("response.data:",i.data),console.log("response.data.data:",i.data.data),console.log("isAuthorized:",null==(t=i.data)?void 0:t.isAuthorized),console.log("errorMessage:",null==(s=i.data)?void 0:s.errorMessage),!i||!i.data)return{success:!1,message:"Login failed. Please try again."};{let e;if(i.data.errorMessage)return{success:!1,message:i.data.errorMessage||"Login failed. Please try again."};try{if("string"==typeof i.data.data){let o=JSON.parse(i.data.data);if(console.log("Outer data:",o),Array.isArray(o)&&o.length>0&&o[0].DATA){let r=o[0].DATA;console.log("Inner DATA string:",r);let t=JSON.parse(r);if(console.log("Parsed inner data:",t),Array.isArray(t)&&0===t.length)return console.log("Login failed - empty DATA array"),{success:!1,message:"Invalid email or password. Please check your credentials and try again."};e=t}else e=o}else e=i.data.data;if(console.log("Final userData:",e),!Array.isArray(e)||!(e.length>0))return console.log("Empty user data - login failed"),{success:!1,message:"Invalid email or password. Please check your credentials and try again."};{let o=e[0];if(console.log("Checking userInfo:",o),console.log("Available fields in userInfo:",o?Object.keys(o):"null"),console.log("UserInfo Gender:",null==o?void 0:o.Gender),console.log("UserInfo CategoryId:",null==o?void 0:o.CategoryId),console.log("UserInfo CategoryID:",null==o?void 0:o.CategoryID),!o||!o.UserID&&!o.UserId&&!o.ID&&!o.Id)return{success:!1,message:o.ResponseMsg||"Invalid credentials. Please try again."};{if(!1===o.IsActive||0===o.IsActive||"false"===o.IsActive||"0"===o.IsActive)return console.log("Login failed - account is inactive"),{success:!1,message:"Your account is inactive. Please contact support for assistance."};let e=o.UserID||o.UserId||o.ID||o.Id;console.log("Found user ID:",e);let r={...o,UserId:e,UserID:e,UserName:((o.FirstName||"")+" "+(o.LastName||"")).trim()||o.UserName||o.Name,Email:o.EmailAddress||o.Email||o.email,EmailAddress:o.EmailAddress||o.Email||o.email,FirstName:o.FirstName||o.firstname,LastName:o.LastName||o.lastname,PhoneNumber:o.PhoneNo||o.MobileNo||o.PhoneNumber,PhoneNo:o.PhoneNo||o.MobileNo,MobileNo:o.MobileNo||o.PhoneNo,ResponseMsg:o.ResponseMsg||"Login successful!",Pointno:o.Pointno||o.Points||0,Gender:o.Gender||o.gender||"",CategoryID:o.CategoryID||o.CategoryId||o.category_id||o.categoryId||"",CategoryId:o.CategoryId||o.CategoryID||o.category_id||o.categoryId||"",SpecialistId:o.SpecialistId||o.specialist_id||o.CategoryId||o.CategoryID||"",CatID:o.CatID||o.CategoryId||o.CategoryID||"",IsActive:o.IsActive,CreatedOn:o.CreatedOn||o.createdOn||o.created_on};console.log("Mapped user:",r);try{l(r);let e=i.data.token||null;console.log("Extracted token:",e?"".concat(e.substring(0,20),"..."):"No token found"),e?c(e):(console.warn("No JWT token found in login response"),c(null));try{(await fetch("/api/auth/set-cookies",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({user:r,token:e||null})})).ok?console.log("\uD83D\uDD10 Secure HttpOnly cookies set successfully"):console.warn("Failed to set secure cookies")}catch(e){console.warn("Cookie API failed:",e)}return console.log("User data saved successfully to secure cookies"),{success:!0,message:"Login successful!"}}catch(e){return console.error("Error saving user data:",e),{success:!1,message:"Login successful but failed to save user data"}}}}}catch(e){return console.error("Error parsing login response:",e),{success:!1,message:"Error processing login response!"}}}}catch(e){return console.error("Login error:",e),{success:!1,message:"An error occurred. Please try again!"}}finally{u(!1)}},p=async()=>{l(null),c(null);try{(await fetch("/api/auth/clear-cookies",{method:"POST",credentials:"include"})).ok?console.log("\uD83D\uDD10 Secure cookies cleared successfully"):console.warn("Failed to clear secure cookies via API")}catch(e){console.warn("Cookie clearing API failed:",e)}},I=async e=>{if(r){l({...r,...e});try{(await fetch("/api/auth/update-user-cookies",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify(e)})).ok?console.log("✅ User context: Profile updated in cookies successfully"):console.warn("⚠️ User context: Failed to update profile in cookies")}catch(e){console.warn("⚠️ User context: Cookie update API failed:",e)}}},f=null!==r&&(r.UserId&&r.UserId>0||r.UserID&&r.UserID>0);return(0,t.jsx)(n.Provider,{value:{user:r,isLoggedIn:f,isLoading:d,login:g,logout:p,token:i,updateProfile:I},children:o})}function i(){let e=(0,s.useContext)(n);if(void 0===e)throw Error("useUser must be used within a UserProvider");return e}}}]);