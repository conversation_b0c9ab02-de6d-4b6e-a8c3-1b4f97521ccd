(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9139],{94664:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var o=t(95155),n=t(12115);function i(){let[e,s]=(0,n.useState)(""),[t,i]=(0,n.useState)(!1),r=async()=>{i(!0),s("Testing...");try{console.log("\uD83E\uDDEA Testing verification API...");let e={phoneNumber:"+9647858021300",verificationCode:"123456",expirationMinutes:10};console.log("\uD83D\uDCE4 Sending request to /api/verification/store"),console.log("\uD83D\uDCE4 Request data:",e);let t=await fetch("/api/verification/store",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});console.log("\uD83D\uDCE5 Response status:",t.status),console.log("\uD83D\uDCE5 Response headers:",Object.fromEntries(t.headers.entries()));let o=await t.text();if(console.log("\uD83D\uDCE5 Response body:",o),t.ok){let e=JSON.parse(o);e.success?s("✅ SUCCESS: Verification code stored successfully!"):s("❌ FAILED: ".concat(e.error))}else s("❌ HTTP ERROR: ".concat(t.status," - ").concat(o))}catch(t){console.error("❌ Error:",t);let e=t instanceof Error?t.message:"Unknown error occurred";s("❌ ERROR: ".concat(e))}finally{i(!1)}},c=async()=>{i(!0),s("Testing direct backend...");try{console.log("\uD83E\uDDEA Testing direct backend API...");let e={PhoneNumber:"+9647858021300",VerificationCode:"123456",ExpirationMinutes:10,IPAddress:"127.0.0.1",UserAgent:"Test-Browser"},t="https://admin.codemedicalapps.com/api/v1/verification/store";console.log("\uD83D\uDCE4 Sending request to:",t),console.log("\uD83D\uDCE4 Request data:",e);let o=await fetch(t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});console.log("\uD83D\uDCE5 Response status:",o.status),console.log("\uD83D\uDCE5 Response headers:",Object.fromEntries(o.headers.entries()));let n=await o.text();if(console.log("\uD83D\uDCE5 Response body:",n),o.ok){let e=JSON.parse(n);e.success?s("✅ SUCCESS: Direct backend call worked!"):s("❌ FAILED: ".concat(e.error))}else s("❌ HTTP ERROR: ".concat(o.status," - ").concat(n))}catch(t){console.error("❌ Error:",t);let e=t instanceof Error?t.message:"Unknown error occurred";s("❌ ERROR: ".concat(e))}finally{i(!1)}};return(0,o.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,o.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Verification Debug Test"}),(0,o.jsxs)("div",{className:"space-y-6",children:[(0,o.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Environment Configuration"}),(0,o.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"NEXT_PUBLIC_ADMIN_BASE_URL:"})," ","https://admin.codemedicalapps.com/"]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"NEXT_PUBLIC_API_BASE_URL:"})," ","https://admin.codemedicalapps.com"]})]})]}),(0,o.jsxs)("div",{className:"bg-green-50 p-6 rounded-lg",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Frontend API Route"}),(0,o.jsx)("button",{onClick:r,disabled:t,className:"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 disabled:opacity-50 mb-4 mr-4",children:t?"Testing...":"Test /api/verification/store"}),(0,o.jsx)("button",{onClick:c,disabled:t,className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50 mb-4",children:t?"Testing...":"Test Direct Backend"}),e&&(0,o.jsx)("div",{className:"mt-4 p-4 bg-white rounded border",children:(0,o.jsx)("p",{className:"font-mono text-sm whitespace-pre-wrap",children:e})})]}),(0,o.jsxs)("div",{className:"bg-yellow-50 p-6 rounded-lg",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Debug Instructions"}),(0,o.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-sm",children:[(0,o.jsx)("li",{children:"Open browser console (F12) to see detailed logs"}),(0,o.jsx)("li",{children:'Click "Test /api/verification/store" to test the frontend API route'}),(0,o.jsx)("li",{children:'Click "Test Direct Backend" to test direct backend connection'}),(0,o.jsx)("li",{children:"Check the console logs for detailed error information"}),(0,o.jsx)("li",{children:"If direct backend works but API route fails, there's an issue in the API route"})]})]})]})]})}},97931:(e,s,t)=>{Promise.resolve().then(t.bind(t,94664))}},e=>{e.O(0,[8441,5964,7358],()=>e(e.s=97931)),_N_E=e.O()}]);