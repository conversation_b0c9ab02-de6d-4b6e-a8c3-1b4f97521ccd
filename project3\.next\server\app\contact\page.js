(()=>{var a={};a.id=977,a.ids=[977],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7710:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\contact\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\contact\\page.tsx","default")},7884:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>y});var d=c(60687),e=c(43210),f=c(34993),g=c(55192),h=c(24934),i=c(68988),j=c(39390),k=c(15616),l=c(85814),m=c.n(l),n=c(77080),o=c(832),p=c(58869),q=c(5336),r=c(27900),s=c(97992),t=c(48340),u=c(41550),v=c(97461),w=c.n(v);c(90895);var x=c(15488);function y(){let{t:a,primaryColor:b}=(0,n.t)(),{user:c,isLoggedIn:l}=(0,o.J)(),[v,y]=(0,e.useState)(!1),[z,A]=(0,e.useState)(!1),[B,C]=(0,e.useState)(""),{executeRecaptcha:D}=(0,x._Y)(),[E,F]=(0,e.useState)(""),[G,H]=(0,e.useState)("iq"),[I,J]=(0,e.useState)({name:"",email:"",phoneNumber:"",subject:"",message:""}),K=a=>{let{name:b,value:c}=a.target;J(a=>({...a,[b]:c}))},L=async a=>{if(a.preventDefault(),y(!0),C(""),!D){C("reCAPTCHA not ready"),y(!1);return}E.startsWith("+");try{let a=await D("contact_us"),b={FullName:I.name,Email:I.email,Subject:I.subject,Message:I.message,RecaptchaToken:a};if(l&&c){let a=c.PhoneNumber||c.PhoneNo||c.MobileNo||c.phone||"";a&&(b.PhoneNumber=a.startsWith("+")?a:`+${a}`)}else b.PhoneNumber=E.startsWith("+")?E:`+${E}`;l&&c?(b.UserId=c.UserId||c.UserID||c.Id,b.UserType=c.UserType||"Customer",b.IsLoggedIn=!0):b.IsLoggedIn=!1;let d=await fetch("https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/contact-us",{method:"POST",headers:{"Content-Type":"application/json",...l&&c?{Authorization:`Bearer ${c.token}`}:{}},body:JSON.stringify({requestParameters:b})}),e=await d.json();if(d.ok){if(A(!0),l&&c){let a=`${c.FirstName||""} ${c.LastName||""}`.trim()||c.UserName||c.Name||"",b=c.Email||c.EmailAddress||c.email||"",d=c.PhoneNumber||c.PhoneNo||c.MobileNo||"";J({name:a,email:b,phoneNumber:d,subject:"",message:""})}else J({name:"",email:"",phoneNumber:"",subject:"",message:""});setTimeout(()=>A(!1),5e3)}else C(e.message||"Failed to send message.")}catch(a){C("An error occurred. Please try again.")}finally{y(!1)}};return(0,d.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,d.jsx)(f.Qp,{className:"mb-6",children:(0,d.jsxs)(f.AB,{children:[(0,d.jsx)(f.J5,{children:(0,d.jsx)(f.w1,{asChild:!0,children:(0,d.jsx)(m(),{href:"/",children:a("home")})})}),(0,d.jsx)(f.tH,{}),(0,d.jsx)(f.J5,{children:(0,d.jsx)(f.tJ,{children:a("contact")})})]})}),(0,d.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold mb-6",children:a("contact")}),(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-[1fr_400px] gap-8 mb-12",children:[(0,d.jsx)(g.Zp,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,d.jsx)("h2",{className:"text-2xl font-semibold",children:"Get in Touch"}),l&&(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,d.jsx)(p.A,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:["Logged in as ",c?.FirstName," ",c?.LastName]})]})]}),z?(0,d.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(q.A,{className:"h-8 w-8 text-green-600"})}),(0,d.jsx)("h3",{className:"text-xl font-medium text-green-800 mb-2",children:"Message Sent!"}),(0,d.jsx)("p",{className:"text-green-700 mb-4",children:"Thank you for contacting us. We'll get back to you as soon as possible."}),(0,d.jsx)(h.$,{onClick:()=>A(!1),children:"Send Another Message"})]}):(0,d.jsxs)("form",{onSubmit:L,children:[l&&(0,d.jsxs)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 text-green-600"}),(0,d.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Your Information"})]}),(0,d.jsxs)("div",{className:"text-sm text-green-700",children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Name:"})," ",c?.FirstName," ",c?.LastName]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Email:"})," ",c?.Email||c?.EmailAddress]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Phone:"})," ",c?.PhoneNumber||c?.PhoneNo||c?.MobileNo]})]})]}),!l&&(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(j.J,{htmlFor:"name",children:"Your Name"}),(0,d.jsx)(i.p,{id:"name",name:"name",value:I.name,onChange:K,required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(j.J,{htmlFor:"email",children:"Email Address"}),(0,d.jsx)(i.p,{id:"email",name:"email",type:"email",value:I.email,onChange:K,required:!0})]}),(0,d.jsxs)("div",{className:"w-full",children:[(0,d.jsx)(j.J,{children:a("phonenumber")}),(0,d.jsx)(w(),{country:G,value:E,onChange:(a,b)=>{F(a);let c=a.startsWith("+")?a:`+${a}`;J(a=>({...a,phoneNumber:c})),b&&b.countryCode&&H(b.countryCode.toLowerCase())},enableSearch:!0,searchPlaceholder:"Search country...",containerClass:"w-full",inputClass:"w-full p-2 border rounded-md focus:ring-2 focus:ring-primary/50",buttonClass:"!border-input !bg-background hover:!bg-accent",dropdownClass:"!bg-background !border-input",countryCodeEditable:!1,inputProps:{name:"phoneNumber",required:!0,id:"phoneNumber"}})]})]}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)(j.J,{htmlFor:"subject",children:"Subject"}),(0,d.jsx)(i.p,{id:"subject",name:"subject",value:I.subject,onChange:K,required:!0})]}),(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsx)(j.J,{htmlFor:"message",children:"Message"}),(0,d.jsx)(k.T,{id:"message",name:"message",value:I.message,onChange:K,rows:6,required:!0})]}),B&&(0,d.jsx)("p",{className:"text-red-500 text-sm mb-4",children:B}),(0,d.jsx)(h.$,{type:"submit",disabled:v,className:"w-full",children:v?(0,d.jsxs)("span",{className:"flex items-center gap-2",children:[(0,d.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sending..."]}):(0,d.jsxs)("span",{className:"flex items-center gap-2",children:[(0,d.jsx)(r.A,{className:"h-4 w-4"}),"Send Message"]})})]})]})}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(g.Zp,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Contact Information"}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-4",style:{backgroundColor:`${b}20`},children:(0,d.jsx)(s.A,{className:"h-5 w-5",style:{color:b}})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium",children:"Address"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Iraq"})]})]}),(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-4",style:{backgroundColor:`${b}20`},children:(0,d.jsx)(t.A,{className:"h-5 w-5",style:{color:b}})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium",children:"Phone"}),(0,d.jsx)(m(),{href:`tel:${a("phone")}`,className:"text-muted-foreground hover:text-primary transition-colors",children:a("phone")})]})]}),(0,d.jsxs)("div",{className:"flex items-start",children:[(0,d.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-4",style:{backgroundColor:`${b}20`},children:(0,d.jsx)(u.A,{className:"h-5 w-5",style:{color:b}})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium",children:"Email"}),(0,d.jsx)(m(),{href:`mailto:${a("email")}`,className:"text-muted-foreground hover:text-primary transition-colors",children:a("email")})]})]})]})]})}),(0,d.jsx)(g.Zp,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Business Hours"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"Monday - Friday"}),(0,d.jsx)("span",{children:"9:00 AM - 11:00 PM"})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"Saturday"}),(0,d.jsx)("span",{children:"10:00 AM - 10:00 PM"})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{children:"Sunday"}),(0,d.jsx)("span",{children:"Closed"})," "]}),(0,d.jsx)("div",{className:"flex justify-between",children:(0,d.jsx)("p",{children:"We’re available 24/7 for orders and inquiries. Our team will get back to you during our regular working hours."})})]})]})})]})]}),(0,d.jsxs)("div",{className:"mb-12",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Frequently Asked Questions"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsx)(g.Zp,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"How can I track my order?"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"You can track your order by logging into your account and visiting the Orders section. Alternatively, you can use the tracking number provided in your order confirmation email."})]})}),(0,d.jsx)(g.Zp,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"What payment methods do you accept?"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"We accept various payment methods including credit/debit cards, PayPal, and bank transfers. For more information, please visit our Payment Methods page."})]})}),(0,d.jsx)(g.Zp,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"How long does shipping take?"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Shipping times vary depending on your location. Domestic orders typically take 3-5 business days, while international orders may take 7-14 business days."})]})}),(0,d.jsx)(g.Zp,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"What is your return policy?"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"We offer a 30-day return policy for most products. Please visit our Returns page for detailed information on our return process and eligibility criteria."})]})})]})]})]})]})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},14952:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15616:(a,b,c)=>{"use strict";c.d(b,{T:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,f.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...b}));g.displayName="Textarea"},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19523:(a,b,c)=>{Promise.resolve().then(c.bind(c,7710))},21820:a=>{"use strict";a.exports=require("os")},24411:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,7710)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\contact\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\contact\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/contact/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27900:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33595:(a,b,c)=>{Promise.resolve().then(c.bind(c,7884))},33873:a=>{"use strict";a.exports=require("path")},34993:(a,b,c)=>{"use strict";c.d(b,{AB:()=>j,J5:()=>k,Qp:()=>i,tH:()=>n,tJ:()=>m,w1:()=>l});var d=c(60687),e=c(43210),f=c(8730),g=c(14952),h=(c(93661),c(96241));let i=e.forwardRef(({...a},b)=>(0,d.jsx)("nav",{ref:b,"aria-label":"breadcrumb",...a}));i.displayName="Breadcrumb";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("ol",{ref:c,className:(0,h.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",a),...b}));j.displayName="BreadcrumbList";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("li",{ref:c,className:(0,h.cn)("inline-flex items-center gap-1.5",a),...b}));k.displayName="BreadcrumbItem";let l=e.forwardRef(({asChild:a,className:b,...c},e)=>{let g=a?f.DX:"a";return(0,d.jsx)(g,{ref:e,className:(0,h.cn)("transition-colors hover:text-foreground",b),...c})});l.displayName="BreadcrumbLink";let m=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("span",{ref:c,role:"link","aria-disabled":"true","aria-current":"page",className:(0,h.cn)("font-normal text-foreground",a),...b}));m.displayName="BreadcrumbPage";let n=({children:a,className:b,...c})=>(0,d.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,h.cn)("[&>svg]:size-3.5",b),...c,children:a??(0,d.jsx)(g.A,{})});n.displayName="BreadcrumbSeparator"},39390:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(96241);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55192:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h,wL:()=>l});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b}));l.displayName="CardFooter"},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68988:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"},74075:a=>{"use strict";a.exports=require("zlib")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93661:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,1697,3338,9822],()=>b(b.s=24411));module.exports=c})();