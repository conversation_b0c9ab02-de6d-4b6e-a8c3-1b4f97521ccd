(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7764],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>o});var n=r(12115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=s(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():s(e[t],null)}}}}function i(...e){return n.useCallback(o(...e),e)}},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var n=r(12115),s=r(63655),o=r(95155),i=n.forwardRef((e,t)=>(0,o.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var n=r(52596),s=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,n.$)(t))}},63655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>a,sG:()=>l});var n=r(12115),s=r(47650),o=r(99708),i=r(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,o.TL)(`Primitive.${t}`),s=n.forwardRef((e,n)=>{let{asChild:s,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(s?r:t,{...o,ref:n})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function a(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var n=r(52596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:l}=t,a=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let o=s(t)||s(n);return i[e][o]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,a,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},82714:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var n=r(95155),s=r(12115),o=r(40968),i=r(74466),l=r(53999);let a=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(o.b,{ref:t,className:(0,l.cn)(a(),r),...s})});c.displayName=o.b.displayName},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>a,Zp:()=>i,aR:()=>l,wL:()=>u});var n=r(95155),s=r(12115),o=r(53999);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});i.displayName="Card";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...s})});l.displayName="CardHeader";let a=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});a.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...s})});d.displayName="CardContent";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...s})});u.displayName="CardFooter"},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var n=r(95155),s=r(12115),o=r(53999);let i=s.forwardRef((e,t)=>{let{className:r,type:s,...i}=e;return(0,n.jsx)("input",{type:s,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...i})});i.displayName="Input"},90465:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var n=r(95155),s=r(12115),o=r(88482),i=r(97168),l=r(89852),a=r(82714);function c(){let[e,t]=(0,s.useState)("+9647858021300"),[r,c]=(0,s.useState)(""),[d,u]=(0,s.useState)(""),[f,p]=(0,s.useState)(!1),[m,h]=(0,s.useState)(1),x=async()=>{p(!0),u("Step 1: Sending verification code...");try{console.log("\uD83D\uDCE4 Sending verification code to:",e);let t=await fetch("/api/sms/send-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:e,useWhatsApp:!0})});console.log("\uD83D\uDCE5 Send response status:",t.status);let r=await t.text();if(console.log("\uD83D\uDCE5 Send response body:",r),t.ok)try{let e=JSON.parse(r);u("✅ Step 1 SUCCESS: Verification code sent!\n\nResponse: ".concat(JSON.stringify(e,null,2))),h(2)}catch(e){u("✅ Step 1 SUCCESS: Verification code sent!\n\nResponse: ".concat(r)),h(2)}else u("❌ Step 1 FAILED: ".concat(t.status,"\n\nResponse: ").concat(r))}catch(t){console.error("❌ Error:",t);let e=t instanceof Error?t.message:"Unknown error occurred";u("❌ Step 1 ERROR: ".concat(e))}finally{p(!1)}},g=async()=>{if(!r)return void u("❌ Please enter a verification code");p(!0),u("Step 2: Verifying code...");try{console.log("\uD83D\uDCE4 Verifying code:",r,"for phone:",e);let t=await fetch("/api/sms/verify-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:e,code:r})});console.log("\uD83D\uDCE5 Verify response status:",t.status);let n=await t.text();if(console.log("\uD83D\uDCE5 Verify response body:",n),t.ok)try{let e=JSON.parse(n);e.success?(u("✅ Step 2 SUCCESS: Code verified!\n\nResponse: ".concat(JSON.stringify(e,null,2))),h(3)):u("❌ Step 2 FAILED: Invalid code\n\nResponse: ".concat(JSON.stringify(e,null,2)))}catch(e){u("✅ Step 2 SUCCESS: Code verified!\n\nResponse: ".concat(n)),h(3)}else u("❌ Step 2 FAILED: ".concat(t.status,"\n\nResponse: ").concat(n))}catch(t){console.error("❌ Error:",t);let e=t instanceof Error?t.message:"Unknown error occurred";u("❌ Step 2 ERROR: ".concat(e))}finally{p(!1)}},y=async()=>{if(!r)return void u("❌ Please enter a verification code to store");p(!0),u("Testing direct code storage...");try{console.log("\uD83D\uDCE4 Storing code:",r,"for phone:",e);let t=await fetch("/api/verification/store",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:e,verificationCode:r,expirationMinutes:10})});console.log("\uD83D\uDCE5 Store response status:",t.status);let n=await t.text();if(console.log("\uD83D\uDCE5 Store response body:",n),t.ok)try{let e=JSON.parse(n);u("✅ STORE SUCCESS: Code stored!\n\nResponse: ".concat(JSON.stringify(e,null,2)))}catch(e){u("✅ STORE SUCCESS: Code stored!\n\nResponse: ".concat(n))}else u("❌ STORE FAILED: ".concat(t.status,"\n\nResponse: ").concat(n))}catch(t){console.error("❌ Error:",t);let e=t instanceof Error?t.message:"Unknown error occurred";u("❌ STORE ERROR: ".concat(e))}finally{p(!1)}};return(0,n.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Verification Flow Test"}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)(o.Zp,{className:"p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Configuration"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(a.J,{htmlFor:"phone",children:"Phone Number"}),(0,n.jsx)(l.p,{id:"phone",value:e,onChange:e=>t(e.target.value),placeholder:"+9647858021300"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(a.J,{htmlFor:"code",children:"Verification Code (for testing)"}),(0,n.jsx)(l.p,{id:"code",value:r,onChange:e=>c(e.target.value),placeholder:"123456"})]})]})]}),(0,n.jsxs)(o.Zp,{className:"p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Steps"}),(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,n.jsx)(i.$,{onClick:x,disabled:f,variant:m>=2?"outline":"default",children:f&&1===m?"Sending...":"Step 1: Send Code"}),(0,n.jsx)(i.$,{onClick:g,disabled:f||!r,variant:m>=3?"outline":"default",children:f&&2===m?"Verifying...":"Step 2: Verify Code"}),(0,n.jsx)(i.$,{onClick:y,disabled:f||!r,variant:"secondary",children:f?"Storing...":"Test: Store Code"}),(0,n.jsx)(i.$,{onClick:()=>{h(1),c(""),u("")},disabled:f,variant:"outline",children:"Reset"})]})})]}),d&&(0,n.jsxs)(o.Zp,{className:"p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Results"}),(0,n.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-sm overflow-auto whitespace-pre-wrap",children:d})]}),(0,n.jsxs)(o.Zp,{className:"p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Instructions"}),(0,n.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-sm",children:[(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Step 1:"}),' Click "Send Code" to send a verification code via WhatsApp']}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Step 2:"}),' Enter the code you received and click "Verify Code"']}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Alternative:"}),' Use "Test: Store Code" to manually store a code for testing']}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Debug:"})," Open browser console (F12) to see detailed logs"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Production:"})," This same flow should work in production"]})]})]}),(0,n.jsxs)(o.Zp,{className:"p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Environment"}),(0,n.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Current URL:"})," ",window.location.origin]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Environment:"})," ","production"]})]})]})]})]})}},96562:(e,t,r)=>{Promise.resolve().then(r.bind(r,90465))},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>a});var n=r(95155),s=r(12115),o=r(99708),i=r(74466),l=r(53999);let a=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:r,variant:s,size:i,asChild:c=!1,...d}=e,u=c?o.DX:"button";return(0,n.jsx)(u,{className:(0,l.cn)(a({variant:s,size:i,className:r})),ref:t,...d})});c.displayName="Button"},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>c,TL:()=>i});var n=r(12115),s=r(6101),o=r(95155);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var i;let e,l,a=(i=r,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let r={...t};for(let n in t){let s=e[n],o=t[n];/^on[A-Z]/.test(n)?s&&o?r[n]=(...e)=>{let t=o(...e);return s(...e),t}:s&&(r[n]=s):"style"===n?r[n]={...s,...o}:"className"===n&&(r[n]=[s,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,s.t)(t,a):a),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:s,...i}=e,l=n.Children.toArray(s),a=l.find(d);if(a){let e=a.props.children,s=l.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,o.jsx)(t,{...i,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var l=i("Slot"),a=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=a,t}function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}}},e=>{e.O(0,[4277,8441,5964,7358],()=>e(e.s=96562)),_N_E=e.O()}]);