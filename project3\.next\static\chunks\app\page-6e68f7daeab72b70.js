(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5409,7790,8974],{37108:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},42355:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},49131:(e,t,a)=>{Promise.resolve().then(a.bind(a,99101))},55607:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("Flame",[["path",{d:"M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z",key:"96xj49"}]])},61204:(e,t,a)=>{"use strict";a.d(t,{T:()=>r});let r={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",COMMON_CONTROLLER_SUB_URL:"api/v1/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile"}}},65409:(e,t,a)=>{"use strict";a.d(t,{$g:()=>u,Config:()=>l,MakeApiCallAsync:()=>n,XX:()=>d,k6:()=>c});var r=a(23464),s=a(61204);r.A.defaults.timeout=3e4,"https:"===window.location.protocol&&s.T.ADMIN_BASE_URL.includes("localhost")&&(r.A.defaults.httpsAgent={rejectUnauthorized:!1});let l={ADMIN_BASE_URL:s.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...s.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",RESET_PASSWORD_FIREBASE:"reset-password-firebase",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile",INSERT_PRODUCT_REVIEW:"Insert-Product-Review",GET_PRODUCT_REVIEWS:"get-product-reviews"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},i=async()=>{try{{try{let e=await fetch("/api/auth/get-token",{method:"GET",credentials:"include"});if(e.ok){let t=await e.json();if(t.token)return console.log("\uD83D\uDD10 Retrieved JWT token from secure HttpOnly cookie"),t.token}}catch(e){console.log("API token retrieval failed, trying client-side cookies:",e)}for(let e of document.cookie.split(";")){let[t,a]=e.trim().split("=");if("auth_token"===t)return console.log("\uD83D\uDD10 Retrieved JWT token from client-side cookie"),decodeURIComponent(a)}let e=localStorage.getItem("token")||localStorage.getItem("authToken");if(e)return console.log("⚠️ Using token from localStorage (migrating to secure cookies)"),localStorage.removeItem("token"),localStorage.removeItem("authToken"),e}return null}catch(e){return console.error("Error getting token for header:",e),null}},o=async()=>{try{{for(let a of document.cookie.split(";")){let[r,s]=a.trim().split("=");if("auth_user"===r)try{var e,t;let a=JSON.parse(decodeURIComponent(s)),r=(null==(e=a.UserId)?void 0:e.toString())||(null==(t=a.UserID)?void 0:t.toString());if(r)return console.log("\uD83D\uDD10 Retrieved User ID from secure cookie"),r}catch(e){console.warn("Failed to parse user data from cookie:",e)}}let a=localStorage.getItem("userId")||localStorage.getItem("userID");if(a)return console.log("⚠️ Using User ID from localStorage (migrating to secure cookies)"),localStorage.removeItem("userId"),localStorage.removeItem("userID"),a}return null}catch(e){return console.error("Error getting user ID for header:",e),null}},n=async function(e,t,a,s,n){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let c=(e=>{if(!e)return e;let t=JSON.parse(JSON.stringify(e));return t.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from request body (will use JWT token instead)"),delete t.UserId),t.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from request body (will use JWT token instead)"),delete t.UserID),t.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from request body (will use JWT token instead)"),delete t.user_id),t.requestParameters&&(t.requestParameters.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from requestParameters (will use JWT token instead)"),delete t.requestParameters.UserId),t.requestParameters.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from requestParameters (will use JWT token instead)"),delete t.requestParameters.UserID),t.requestParameters.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from requestParameters (will use JWT token instead)"),delete t.requestParameters.user_id)),t})(a),d={...s};if(!d.hasOwnProperty("Authorization")){let e=await i();e&&(d.Authorization="Bearer "+e,console.log("\uD83D\uDD10 Added JWT token to Authorization header"))}if(!d.hasOwnProperty("Token")){let e=await i();d.Token=null!=e?e:"",e&&console.log("\uD83D\uDD10 Added JWT token to Token header (backward compatibility)")}if(!d.hasOwnProperty("UserID")){let e=await o();d.UserID=null!=e?e:""}d.hasOwnProperty("Accept")||(d.Accept="application/json"),d.hasOwnProperty("Content-Type")||(d["Content-Type"]="application/json");let u=l.ADMIN_BASE_URL+(null===t||void 0==t?l.DYNAMIC_METHOD_SUB_URL:t)+e;n=null!=n?n:"POST";let m={headers:d,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===n)return await r.A.post(u,c,m);if("GET"==n)return m.params=c,await r.A.get(u,m);return{data:{errorMessage:"Unsupported method type: ".concat(n),status:"method_not_supported"}}}catch(t){console.error("API call failed:",t);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(t&&"object"==typeof t&&"response"in t&&t.response){var c,d;let a=null==(c=t.response)?void 0:c.data;e.data={errorMessage:(null==a?void 0:a.errorMessage)||"An error occurred while processing your request.",status:null==(d=t.response)?void 0:d.status}}else if(t&&"object"==typeof t&&"request"in t){let a="Network error: No response received from server.";t.message&&t.message.includes("Network Error")&&(a="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+l.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:a,status:"network_error"}}else e.data={errorMessage:t&&"object"==typeof t&&"message"in t?t.message:"An unexpected error occurred",status:"request_error"};return e}},c=async()=>{try{return console.log("Using default currency rate (1430) - API endpoint not available"),1430}catch(e){return console.error("Error fetching currency rate:",e),1430}},d=(e,t)=>Math.round(e*t),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return null==e||isNaN(e)?"IQD"===t?"0 IQD":"$0.00":"IQD"===t?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},74783:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},99101:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>z});var r=a(95155),s=a(12115),l=a(65409),i=a(42355),o=a(13052),n=a(97168),c=a(53999);function d(e){let{images:t,autoPlayInterval:a=5e3,className:l,onSlideChange:d,initialIndex:u=0}=e,[m,p]=(0,s.useState)(u),[g,h]=(0,s.useState)(!0),[f,x]=(0,s.useState)(!1),v=(0,s.useCallback)(()=>{if(f)return;x(!0);let e=m===t.length-1?0:m+1;p(e),d&&d(e),setTimeout(()=>x(!1),500)},[t.length,m,d,f]);return(0,s.useEffect)(()=>{let e;return g&&!f&&(e=setInterval(v,a)),()=>{e&&clearInterval(e)}},[g,v,a,f]),(0,r.jsxs)("div",{className:(0,c.cn)("relative w-full overflow-hidden rounded-xl",l),onMouseEnter:()=>h(!1),onMouseLeave:()=>h(!0),children:[(0,r.jsx)("div",{className:"flex transition-transform duration-500 ease-out",style:{transform:"translateX(-".concat(100*m,"%)")},children:t.map((e,t)=>(0,r.jsx)("div",{className:"w-full h-full flex-shrink-0 relative",children:(0,r.jsx)("div",{className:"w-full h-full",children:(0,r.jsx)("img",{src:e.url,alt:e.alt,className:"w-full h-full object-cover",style:{objectFit:"cover",width:"100%",height:"100%"}})})},t))}),(0,r.jsx)(n.$,{variant:"ghost",size:"icon",className:"absolute left-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 z-20",onClick:()=>{if(f)return;x(!0);let e=0===m?t.length-1:m-1;p(e),d&&d(e),setTimeout(()=>x(!1),500)},disabled:f,children:(0,r.jsx)(i.A,{className:"h-6 w-6"})}),(0,r.jsx)(n.$,{variant:"ghost",size:"icon",className:"absolute right-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white rounded-full p-2 z-20",onClick:v,disabled:f,children:(0,r.jsx)(o.A,{className:"h-6 w-6"})}),(0,r.jsx)("div",{className:"absolute bottom-6 left-1/2 -translate-x-1/2 flex gap-3 z-20",children:t.map((e,t)=>(0,r.jsx)("button",{className:(0,c.cn)("w-3 h-3 rounded-full transition-all",m===t?"bg-white scale-125":"bg-white/50 hover:bg-white/75"),onClick:()=>{f||(x(!0),p(t),d&&d(t),setTimeout(()=>x(!1),500))},disabled:f},t))})]})}function u(e){let{className:t}=e,[a,i]=(0,s.useState)([]),[o,c]=(0,s.useState)(!0),[u,m]=(0,s.useState)(null),[p,g]=(0,s.useState)(0),[h,f]=(0,s.useState)({transform:"translateX(0%) translateY(0%)"});if((0,s.useEffect)(()=>{(async()=>{try{c(!0),m(null);let e=await (0,l.MakeApiCallAsync)(l.Config.END_POINT_NAMES.GET_HOME_SCREEN_BANNER,null,{PageNumber:1,PageSize:100,SortColumn:"BannerID",SortOrder:"ASC"},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(console.log("API Response:",e),null==e?void 0:e.data)try{let t=e.data;if("string"==typeof t&&(t=JSON.parse(t)),t.data&&"string"==typeof t.data&&(t=JSON.parse(t.data)),console.log("Parsed banner data:",t),Array.isArray(t)){let e=t.map(e=>{var t;return{...e,BannerImgUrl:(null==(t=e.BannerImgUrl)?void 0:t.startsWith("http"))?e.BannerImgUrl:"https://admin.codemedicalapps.com".concat(e.BannerImgUrl||"")}});i(e)}else if(t.data&&Array.isArray(t.data)){let e=t.data.map(e=>{var t;return{...e,BannerImgUrl:(null==(t=e.BannerImgUrl)?void 0:t.startsWith("http"))?e.BannerImgUrl:"https://admin.codemedicalapps.com".concat(e.BannerImgUrl||"")}});i(e)}else console.error("Banner data is not in expected format:",t),i([])}catch(e){console.error("Error processing banner data:",e),m("Failed to process banner data"),i([])}else console.error("Invalid or empty response from API"),m("Failed to load banners"),i([])}catch(e){console.error("Error fetching banners:",e),m("Failed to fetch banners"),i([])}finally{c(!1)}})()},[]),o)return(0,r.jsx)("div",{className:"relative w-full h-[250px] md:h-[386px] overflow-hidden rounded-xl bg-accent/10 animate-pulse ".concat(t),children:(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"})})});if((u||0===a.length)&&(i([{BannerID:1,TopTitle:"TOP TITLE",MainTitle:"main title",BottomTitle:"bottom title",LeftButtonText:"test",RightButtonText:"test right",BannerImgUrl:"https://placehold.co/1200x450/333333/FFFFFF?text=Banner+Image",LeftButtonUrl:"#",ThemeTypeID:1}]),u))return(0,r.jsx)("div",{className:"relative w-full h-[250px] md:h-[386px] overflow-hidden rounded-xl bg-accent/5 flex items-center justify-center ".concat(t),children:(0,r.jsxs)("div",{className:"text-center p-8",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Failed to load banners"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:u}),(0,r.jsx)(n.$,{onClick:()=>window.location.reload(),children:"Retry"})]})});let x=a.map(e=>({url:e.BannerImgUrl,alt:e.MainTitle||"Banner",id:e.BannerID,leftButtonUrl:e.LeftButtonUrl}));return(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("div",{className:"relative w-full h-full",children:(0,r.jsx)(d,{images:x,className:"h-[250px] md:h-[386px] ".concat(t),onSlideChange:e=>{g(e)},initialIndex:p,autoPlayInterval:5e3})})})}var m=a(74783),p=a(37108),g=a(54416),h=a(79891),f=a(6874),x=a.n(f),v=a(1978),y=a(90869),N=a(82885),j=a(80845),w=a(51508);class P extends s.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function T(e){let{children:t,isPresent:a}=e,l=(0,s.useId)(),i=(0,s.useRef)(null),o=(0,s.useRef)({width:0,height:0,top:0,left:0}),{nonce:n}=(0,s.useContext)(w.Q);return(0,s.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:s}=o.current;if(a||!i.current||!e||!t)return;i.current.dataset.motionPopId=l;let c=document.createElement("style");return n&&(c.nonce=n),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(l,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            top: ").concat(r,"px !important;\n            left: ").concat(s,"px !important;\n          }\n        ")),()=>{document.head.removeChild(c)}},[a]),(0,r.jsx)(P,{isPresent:a,childRef:i,sizeRef:o,children:s.cloneElement(t,{ref:i})})}let I=e=>{let{children:t,initial:a,isPresent:l,onExitComplete:i,custom:o,presenceAffectsLayout:n,mode:c}=e,d=(0,N.M)(C),u=(0,s.useId)(),m=(0,s.useCallback)(e=>{for(let t of(d.set(e,!0),d.values()))if(!t)return;i&&i()},[d,i]),p=(0,s.useMemo)(()=>({id:u,initial:a,isPresent:l,custom:o,onExitComplete:m,register:e=>(d.set(e,!1),()=>d.delete(e))}),n?[Math.random(),m]:[l,m]);return(0,s.useMemo)(()=>{d.forEach((e,t)=>d.set(t,!1))},[l]),s.useEffect(()=>{l||d.size||!i||i()},[l]),"popLayout"===c&&(t=(0,r.jsx)(T,{isPresent:l,children:t})),(0,r.jsx)(j.t.Provider,{value:p,children:t})};function C(){return new Map}var b=a(32082);let S=e=>e.key||"";function E(e){let t=[];return s.Children.forEach(e,e=>{(0,s.isValidElement)(e)&&t.push(e)}),t}var A=a(97494);let U=e=>{let{children:t,custom:a,initial:l=!0,onExitComplete:i,presenceAffectsLayout:o=!0,mode:n="sync",propagate:c=!1}=e,[d,u]=(0,b.xQ)(c),m=(0,s.useMemo)(()=>E(t),[t]),p=c&&!d?[]:m.map(S),g=(0,s.useRef)(!0),h=(0,s.useRef)(m),f=(0,N.M)(()=>new Map),[x,v]=(0,s.useState)(m),[j,w]=(0,s.useState)(m);(0,A.E)(()=>{g.current=!1,h.current=m;for(let e=0;e<j.length;e++){let t=S(j[e]);p.includes(t)?f.delete(t):!0!==f.get(t)&&f.set(t,!1)}},[j,p.length,p.join("-")]);let P=[];if(m!==x){let e=[...m];for(let t=0;t<j.length;t++){let a=j[t],r=S(a);p.includes(r)||(e.splice(t,0,a),P.push(a))}"wait"===n&&P.length&&(e=P),w(E(e)),v(m);return}let{forceRender:T}=(0,s.useContext)(y.L);return(0,r.jsx)(r.Fragment,{children:j.map(e=>{let t=S(e),s=(!c||!!d)&&(m===j||p.includes(t));return(0,r.jsx)(I,{isPresent:s,initial:(!g.current||!!l)&&void 0,custom:s?void 0:a,presenceAffectsLayout:o,mode:n,onExitComplete:s?void 0:()=>{if(!f.has(t))return;f.set(t,!0);let e=!0;f.forEach(t=>{t||(e=!1)}),e&&(null==T||T(),w(h.current),c&&(null==u||u()),i&&i())},children:e},t)})})};function D(){let[e,t]=(0,s.useState)([]),[a,i]=(0,s.useState)(!0),[d,u]=(0,s.useState)([]),[f,y]=(0,s.useState)(!0),[N,j]=(0,s.useState)(null),{t:w,primaryColor:P}=(0,h.t)();return((0,s.useEffect)(()=>{(async()=>{try{var e,a;let r={"Content-Type":"application/json",Accept:"application/json",Authorization:"Bearer "+localStorage.getItem("token")},s=await (0,l.MakeApiCallAsync)(l.Config.END_POINT_NAMES.GET_CATEGORIES_LIST,null,{PageNumber:1,PageSize:100,SortColumn:"Name",SortOrder:"ASC"},r,"POST",!0);if(null==s||null==(e=s.data)?void 0:e.data)try{let e=JSON.parse(s.data.data);if(Array.isArray(e)){let a=e.filter(e=>!e.ParentCategoryID),r=e.filter(e=>e.ParentCategoryID),s=a.map(e=>({id:e.CategoryID,name:e.Name,subcategories:r.filter(t=>t.ParentCategoryID===e.CategoryID).map(e=>({id:e.CategoryID,name:e.Name}))}));t(s)}else console.error("Categories data is not an array:",e),t([])}catch(e){console.error("Error parsing categories data:",e),t([])}else(null==s||null==(a=s.data)?void 0:a.errorMessage)?console.error("API Error:",s.data.errorMessage):console.error("Invalid or empty response from API"),t([])}catch(e){console.error("Error fetching categories:",e),t([])}finally{i(!1)}})()},[]),a)?(0,r.jsx)("div",{className:"p-6 space-y-4 bg-gradient-to-b from-background to-accent/5 min-h-screen border-r",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-8 bg-accent/10 animate-pulse rounded-lg w-3/4"}),(0,r.jsx)("div",{className:"h-6 bg-accent/5 animate-pulse rounded-lg w-1/2 ml-4"})]},t))}):(0,r.jsxs)("div",{className:"hidden md:block",children:[(0,r.jsx)(n.$,{variant:"ghost",size:"icon",className:"absolute left-4 top-4 z-50 md:hidden",onClick:()=>y(!f),children:(0,r.jsx)(m.A,{className:"h-6 w-6"})}),(0,r.jsx)(v.P.div,{className:"bg-background md:relative w-64 shadow-sm",style:{background:"linear-gradient(135deg, ".concat(P,"30, ").concat(P,"20, ").concat(P,"10)")},initial:{x:0},animate:{x:f?0:"-100%"},transition:{duration:.3,ease:"easeInOut"},children:(0,r.jsxs)("div",{className:"p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(p.A,{className:"h-6 w-6",style:{color:P}}),(0,r.jsx)("h2",{className:"text-xl font-semibold",style:{color:P},children:w("categories")})]}),N&&(0,r.jsx)(n.$,{variant:"ghost",size:"icon",onClick:()=>{j(null)},className:"hover:bg-accent/80",children:(0,r.jsx)(g.A,{className:"h-5 w-5"})})]}),(0,r.jsx)(U,{mode:"wait",children:N?(0,r.jsx)(v.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.2},className:"space-y-2",children:N.subcategories.map((e,t)=>(0,r.jsx)(v.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{delay:.05*t},children:(0,r.jsx)(x(),{href:"/products?category=".concat(e.id),className:(0,c.cn)("block px-4 py-3 text-sm transition-all duration-200","bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] rounded-lg","relative overflow-hidden group"),children:(0,r.jsxs)("span",{className:"relative z-10 flex items-center gap-2",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"}),e.name]})})},e.id))},"subcategory-list"):(0,r.jsx)(v.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.2},className:"space-y-2",children:[...e].reverse().map(e=>(0,r.jsx)(v.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.2},children:e.subcategories.length>0?(0,r.jsxs)("button",{onClick:()=>{j(e)},className:(0,c.cn)("w-full flex items-center justify-between p-4 transition-all duration-200 rounded-lg","bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] relative overflow-hidden"),children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:e.name}),(0,r.jsx)(o.A,{className:"h-4 w-4"})]}):(0,r.jsx)(x(),{href:"/products?category=".concat(e.id),className:(0,c.cn)("w-full flex items-center justify-between p-4 transition-all duration-200 rounded-lg","bg-background/90 hover:bg-accent/50 hover:shadow-lg hover:scale-[1.02] relative overflow-hidden"),children:(0,r.jsx)("span",{className:"text-sm font-medium",children:e.name})})},e.id))},"parent-list")})]})})]})}function k(){let e=e=>({popularCategories:"Popular Categories"})[e]||e,[t,l]=(0,s.useState)([]),[n,c]=(0,s.useState)(!0),[d,u]=(0,s.useState)(0),m=(0,s.useRef)(null),[p,g]=(0,s.useState)(!0),[h]=(0,s.useState)(window.innerWidth>=768?6e3:4e3),f=(0,s.useRef)(null),x=()=>{{let e=window.innerWidth;return e>=1280?10:e>=1024?8:6}},v=x(),y=Math.ceil(t.length/v),N=()=>{d<y-1?u(d+1):u(0)},j=()=>{f.current&&clearInterval(f.current),f.current=setInterval(()=>{N()},h)},w=()=>{f.current&&(clearInterval(f.current),f.current=null)},P=()=>{w(),setTimeout(()=>{p&&j()},1e4)};return((0,s.useEffect)(()=>{(async()=>{try{var e;c(!0);let t=(await a.e(9362).then(a.bind(a,59362))).default,r=await t({method:"post",url:"".concat("https://admin.codemedicalapps.com/","api/v1/dynamic/dataoperation/get-popular-categories"),headers:{Accept:"application/json","Content-Type":"application/json"},data:{requestParameters:{recordValueJson:"[]"}}});if(null==r||null==(e=r.data)?void 0:e.data)try{let e=JSON.parse(r.data.data);if(Array.isArray(e)){let t=e.map(e=>({id:e.CategoryID,title:e.Name,image:(e=>{if(!e)return"".concat("https://admin.codemedicalapps.com/","images/no-image.jpg");if(e.startsWith("http"))return e;let t=e.startsWith("/")?e:"/".concat(e);return t=t.replace(/\/+/g,"/"),"".concat("https://admin.codemedicalapps.com/").concat(t)})(e.AttachmentURL),parentId:e.ParentCategoryID||void 0,parentName:e.ParentCategoryName||void 0})).sort((e,t)=>e.title.localeCompare(t.title));l(t)}else console.error("Categories data is not an array:",e),l([])}catch(e){console.error("Error parsing data:",e),l([])}else console.error("No data returned from API"),l([])}catch(e){console.error("Error fetching categories:",e),l([])}finally{c(!1)}})()},[]),(0,s.useEffect)(()=>{let e,a=()=>{clearTimeout(e),e=setTimeout(()=>{let e=x(),a=Math.ceil(t.length/e);d>=a&&u(Math.max(0,a-1))},100)};return window.addEventListener("resize",a),()=>{clearTimeout(e),window.removeEventListener("resize",a)}},[t.length,d]),(0,s.useEffect)(()=>{let e,t=()=>{e&&clearInterval(e)};return p&&y>1&&(t(),e=setInterval(()=>{u(e=>e<y-1?e+1:0)},h)),()=>{t()}},[p,h,y]),n)?(0,r.jsx)("section",{className:"py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:e("popularCategories")}),(0,r.jsx)("div",{className:"w-full flex justify-center items-center py-12",children:(0,r.jsx)("div",{className:"animate-pulse text-lg",children:"Loading popular categories..."})})]})}):t.length?(0,r.jsx)("section",{className:"py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsx)("h2",{className:"text-2xl font-bold text-center",children:e("popularCategories")})}),(0,r.jsxs)("div",{className:"w-full relative overflow-hidden",onMouseEnter:()=>w(),onMouseLeave:()=>p&&j(),children:[(0,r.jsx)("button",{onClick:()=>{d>0?u(d-1):u(y-1),P()},disabled:y<=1,className:"hidden sm:block absolute left-2 top-1/2 -translate-y-1/2 z-10 p-3 rounded-full bg-white shadow-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:scale-105","aria-label":"Previous categories",children:(0,r.jsx)(i.A,{className:"h-6 w-6 text-gray-600"})}),(0,r.jsx)("button",{onClick:()=>{N(),P()},disabled:y<=1,className:"hidden sm:block absolute right-2 top-1/2 -translate-y-1/2 z-10 p-3 rounded-full bg-white shadow-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 hover:scale-105","aria-label":"Next categories",children:(0,r.jsx)(o.A,{className:"h-6 w-6 text-gray-600"})}),(0,r.jsx)("div",{ref:m,className:"w-full transition-transform duration-700 ease-in-out md:duration-1000",style:{transform:"translateX(-".concat(100*d,"%)")},children:(0,r.jsx)("div",{className:"flex flex-nowrap",style:{width:"".concat(100*y,"%")},children:Array.from({length:y}).map((e,a)=>(0,r.jsx)("div",{className:"w-full flex-shrink-0 grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 sm:gap-6 md:gap-8",style:{width:"".concat(100/y,"%")},children:t.slice(a*v,(a+1)*v).map(e=>(0,r.jsx)("div",{className:"flex flex-col items-center px-2",children:(0,r.jsxs)("a",{href:"/products?category=".concat(e.id),className:"group flex flex-col items-center cursor-pointer transition-all duration-300 hover:scale-105",children:[(0,r.jsx)("div",{className:"w-20 h-20 sm:w-24 sm:h-24 md:w-28 md:h-28 lg:w-32 lg:h-32 xl:w-36 xl:h-36 mb-3 relative",children:(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 rounded-full p-1 group-hover:shadow-lg transition-all duration-300",children:(0,r.jsx)("div",{className:"w-full h-full rounded-full overflow-hidden border-2 border-white bg-white",children:(0,r.jsx)("img",{src:e.image||"/placeholder.svg?height=150&width=150",alt:e.title,width:144,height:144,className:"w-full h-full object-cover",onError:t=>{let a=t.target;if(console.error("Image load error for:",e.image),"https://admin.codemedicalapps.com/images/no-image.jpg"===e.image||e.image.includes("no-image"))a.src="/placeholder.svg?height=150&width=150";else{let e="".concat("https://admin.codemedicalapps.com","/images/no-image.jpg");console.log("Trying fallback URL:",e),a.src=e,a.onerror=()=>{console.error("Fallback URL also failed, using simple placeholder"),a.src="/placeholder.svg?height=150&width=150",a.onerror=null}}}})})})}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h3",{className:"text-xs sm:text-sm font-medium text-gray-800 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2",children:e.title}),e.parentName&&(0,r.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:e.parentName})]})]})},e.id))},"page-".concat(a)))})})]}),(0,r.jsx)("div",{className:"flex justify-center mt-4 space-x-2",children:Array.from({length:y}).map((e,t)=>(0,r.jsx)("button",{className:"h-2 rounded-full transition-all ".concat(d===t?"w-6 bg-primary":"w-2 bg-gray-300"),onClick:()=>{u(t),P()},"aria-label":"Go to page ".concat(t+1)},"indicator-".concat(t)))})]})}):(0,r.jsx)("section",{className:"py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:e("popularCategories")}),(0,r.jsx)("div",{className:"w-full flex justify-center items-center py-12",children:(0,r.jsx)("div",{className:"text-lg text-gray-500",children:"No categories available at the moment. Please check back later."})})]})})}var _=a(27737),M=a(52355),O=a(61204);function R(e){let{effect:t}=e,{t:l}=(0,h.t)(),[i,o]=(0,s.useState)([]),[n,c]=(0,s.useState)(!0),[d,u]=(0,s.useState)(null),m=async()=>{c(!0);try{var e,t;let{MakeApiCallAsync:r}=await Promise.resolve().then(a.bind(a,65409)),s=await r("get-recents-products-list",null,{requestParameters:{PageNo:1,PageSize:100,recordValueJson:"[]"}},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(null==s||null==(e=s.data)?void 0:e.data)try{let e=JSON.parse(s.data.data);if(console.log("New products data:",e),Array.isArray(e)){let t=e.map(e=>{let t=e.ProductImagesUrl||e.ProductImageUrl,a=null;try{if(t){let e=t;if("string"==typeof t&&(t.startsWith("[")||t.startsWith('"')))try{let a=JSON.parse(t);Array.isArray(a)&&a.length>0?e=a[0].AttachmentURL||a[0]:"string"==typeof a&&(e=a)}catch(a){e=t.replace(/^"|"/g,"")}if("string"==typeof e&&""!==e.trim()&&(e=e.replace(/^"|"$/g,"").trim())){let t=decodeURIComponent(e),r=t.startsWith("/")||t.startsWith("http")?t:"/".concat(t);a=r.startsWith("http")?r:"".concat(O.T.ADMIN_BASE_URL).concat(r)}}}catch(t){console.error("Error processing URL for product",e.ProductId,":",t)}return{ProductId:e.ProductId||e.ProductID||0,ProductName:e.ProductName||"Unnamed Product",Price:parseFloat(e.Price)||0,OldPrice:e.OldPrice?parseFloat(e.OldPrice):void 0,DiscountPrice:e.DiscountPrice?parseFloat(e.DiscountPrice):void 0,Rating:parseFloat(e.Rating)||0,ProductImageUrl:a||void 0,CategoryName:e.CategoryName||"Uncategorized",StockQuantity:parseInt(e.StockQuantity,10)||0,ProductTypeName:e.ProductTypeName,IQDPrice:parseFloat(e.IQDPrice)||void 0,IsDiscountAllowed:!!e.IsDiscountAllowed,MarkAsNew:!!e.MarkAsNew,SellStartDatetimeUTC:e.SellStartDatetimeUTC,SellEndDatetimeUTC:e.SellEndDatetimeUTC}});o(t)}else console.error("Products data is not an array:",e),o([]),u("Invalid data format received from server")}catch(e){console.error("Error parsing products data:",e),o([]),u("Error processing product data")}else(null==s||null==(t=s.data)?void 0:t.errorMessage)?(console.error("API Error:",s.data.errorMessage),o([]),u(s.data.errorMessage||"An error occurred while fetching products")):(console.error("Invalid or empty response from API"),o([]),u("No data received from server"))}catch(e){console.error("Error fetching products:",e),o([]),e&&"object"==typeof e&&"message"in e?u(e.message):u("An unexpected error occurred while fetching products")}finally{c(!1)}};return((0,s.useEffect)(()=>{m()},[]),d)?(0,r.jsx)("div",{className:"w-full p-4 mb-4 text-center",children:(0,r.jsxs)("div",{className:"p-4 bg-red-50 border border-red-200 rounded-md",children:[(0,r.jsx)("p",{className:"text-red-600",children:d}),(0,r.jsx)("button",{onClick:()=>{u(null),m()},className:"mt-2 px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-md text-sm transition-colors",children:l("tryAgain")})]})}):(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6",children:n?Array.from({length:6}).map((e,t)=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsx)("div",{className:"aspect-square",children:(0,r.jsx)(_.E,{className:"h-full w-full"})}),(0,r.jsxs)("div",{className:"p-4 space-y-2",children:[(0,r.jsx)(_.E,{className:"h-4 w-full"}),(0,r.jsx)(_.E,{className:"h-4 w-3/4"}),(0,r.jsx)(_.E,{className:"h-6 w-1/3"})]}),(0,r.jsx)("div",{className:"p-4 pt-0",children:(0,r.jsxs)("div",{className:"flex w-full gap-2",children:[(0,r.jsx)(_.E,{className:"h-10 flex-1"}),(0,r.jsx)(_.E,{className:"h-10 w-10"})]})})]},t)):i.length>0?i.map(e=>(0,r.jsx)(M.A,{product:e},e.ProductId)):(0,r.jsx)("div",{className:"col-span-full text-center py-8",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:l("noProductsFound")})})})})}var L=a(66766);let B=()=>{let[e,t]=(0,s.useState)([]),[l]=(0,s.useState)("https://admin.codemedicalapps.com/");return(0,s.useEffect)(()=>{(async()=>{try{var e;let{MakeApiCallAsync:r}=await Promise.resolve().then(a.bind(a,65409)),s=await r("get-web-campaign-list",null,{requestParameters:{PageNo:1,PageSize:12,recordValueJson:"[]"}},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(null==s||null==(e=s.data)?void 0:e.data)try{let e=JSON.parse(s.data.data);if(console.log("Campaign data:",e),Array.isArray(e)&&e.length>0){let a=e.map(e=>{var t;return{CampaignId:(null==(t=e.CampaignId)?void 0:t.toString())||"",MainTitle:e.MainTitle||e.Title||"",DiscountTitle:e.DiscountTitle||e.SubTitle||"",CoverPictureUrl:e.CoverPictureUrl||e.ImageUrl||"/images/campaign/placeholder.jpg"}});t(a)}else t([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"},{CampaignId:"4",MainTitle:"Medical Courses",DiscountTitle:"Best Prices",CoverPictureUrl:"/images/campaign/medical.jpg"},{CampaignId:"5",MainTitle:"E-Books Collection",DiscountTitle:"Digital Library",CoverPictureUrl:"/images/campaign/ebooks.jpg"},{CampaignId:"6",MainTitle:"Printed Books",DiscountTitle:"Physical Copies",CoverPictureUrl:"/images/campaign/books.jpg"},{CampaignId:"7",MainTitle:"Cardiology Special",DiscountTitle:"Heart Health",CoverPictureUrl:"/images/campaign/cardiology.jpg"},{CampaignId:"8",MainTitle:"Surgery Guides",DiscountTitle:"Expert Techniques",CoverPictureUrl:"/images/campaign/surgery.jpg"},{CampaignId:"9",MainTitle:"Pediatrics Course",DiscountTitle:"Child Care",CoverPictureUrl:"/images/campaign/pediatrics.jpg"}])}catch(e){console.error("Error parsing campaign data:",e),t([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"},{CampaignId:"4",MainTitle:"Medical Courses",DiscountTitle:"Best Prices",CoverPictureUrl:"/images/campaign/medical.jpg"},{CampaignId:"5",MainTitle:"E-Books Collection",DiscountTitle:"Digital Library",CoverPictureUrl:"/images/campaign/ebooks.jpg"},{CampaignId:"6",MainTitle:"Printed Books",DiscountTitle:"Physical Copies",CoverPictureUrl:"/images/campaign/books.jpg"}])}else console.error("Invalid or empty response from API"),t([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"},{CampaignId:"4",MainTitle:"Medical Courses",DiscountTitle:"Best Prices",CoverPictureUrl:"/images/campaign/medical.jpg"},{CampaignId:"5",MainTitle:"E-Books Collection",DiscountTitle:"Digital Library",CoverPictureUrl:"/images/campaign/ebooks.jpg"},{CampaignId:"6",MainTitle:"Printed Books",DiscountTitle:"Physical Copies",CoverPictureUrl:"/images/campaign/books.jpg"}])}catch(e){console.error("Error fetching campaign data:",e),t([{CampaignId:"1",MainTitle:"Summer Collection",DiscountTitle:"Up to 50% Off",CoverPictureUrl:"/images/campaign/summer.jpg"},{CampaignId:"2",MainTitle:"Winter Essentials",DiscountTitle:"Save 30%",CoverPictureUrl:"/images/campaign/winter.jpg"},{CampaignId:"3",MainTitle:"New Arrivals",DiscountTitle:"Fresh Styles",CoverPictureUrl:"/images/campaign/new.jpg"},{CampaignId:"4",MainTitle:"Medical Courses",DiscountTitle:"Best Prices",CoverPictureUrl:"/images/campaign/medical.jpg"},{CampaignId:"5",MainTitle:"E-Books Collection",DiscountTitle:"Digital Library",CoverPictureUrl:"/images/campaign/ebooks.jpg"},{CampaignId:"6",MainTitle:"Printed Books",DiscountTitle:"Physical Copies",CoverPictureUrl:"/images/campaign/books.jpg"}])}})()},[]),(0,r.jsx)("section",{className:"py-12",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:e&&e.map((e,t)=>(0,r.jsx)(x(),{href:"/".concat("en","/campaign/").concat(e.CampaignId,"/").concat(e.MainTitle),className:"relative overflow-hidden rounded-lg group block shadow-md hover:shadow-lg transition-shadow duration-300",children:(0,r.jsxs)("div",{className:"aspect-[4/3] relative",children:[(0,r.jsx)(L.default,{src:l+e.CoverPictureUrl,className:"object-cover transition-transform duration-300 group-hover:scale-105",alt:e.MainTitle,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw",onError:e=>{e.target.src="https://placehold.co/400x300/cccccc/666666?text=Campaign+Image"}}),(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,r.jsxs)("div",{className:"absolute bottom-4 left-4 right-4 text-white",children:[(0,r.jsx)("h3",{className:"text-lg font-bold mb-1",children:e.MainTitle}),(0,r.jsx)("p",{className:"text-sm opacity-90",children:e.DiscountTitle})]})})]})},e.CampaignId||t))})})})};var F=a(55607);function W(){let[e,t]=(0,s.useState)([]),[l,i]=(0,s.useState)(!0);return((0,s.useEffect)(()=>{(async()=>{i(!0);try{var e;let{MakeApiCallAsync:r}=await Promise.resolve().then(a.bind(a,65409)),s=await r("get-popular-products-list",null,{requestParameters:{PageNo:1,PageSize:3}},{"Content-Type":"application/json",Accept:"application/json"},"POST",!0);if(null==s||null==(e=s.data)?void 0:e.data)try{let e=JSON.parse(s.data.data);if(console.log("Popular products data:",e),Array.isArray(e)&&e.length>0){let a=e.map(e=>{let t=e.ProductImagesUrl||e.ProductImageUrl,a=null;try{if(t){let e=t;if("string"==typeof t&&(t.startsWith("[")||t.startsWith('"')))try{let a=JSON.parse(t);Array.isArray(a)&&a.length>0?e=a[0].AttachmentURL||a[0]:"string"==typeof a&&(e=a)}catch(a){e=t.replace(/^"|"/g,"")}if("string"==typeof e&&""!==e.trim()&&(e=e.replace(/^"|"$/g,"").trim())){let t=decodeURIComponent(e),r=t.startsWith("/")||t.startsWith("http")?t:"/".concat(t);a=r.startsWith("http")?r:"".concat(O.T.ADMIN_BASE_URL).concat(r)}}}catch(t){console.error("Error processing URL for product",e.ProductId,":",t)}return{ProductId:e.ProductId||e.ProductID||0,ProductName:e.ProductName||"Popular Product",Price:parseFloat(e.Price)||0,OldPrice:e.OldPrice?parseFloat(e.OldPrice):void 0,DiscountPrice:e.DiscountPrice?parseFloat(e.DiscountPrice):void 0,Rating:parseFloat(e.Rating)||0,ProductImageUrl:a||void 0,CategoryName:e.CategoryName||"Popular",StockQuantity:parseInt(e.StockQuantity,10)||0,ProductTypeName:e.ProductTypeName,IQDPrice:parseFloat(e.IQDPrice)||void 0,IsDiscountAllowed:!!e.IsDiscountAllowed,MarkAsNew:!!e.MarkAsNew,SellStartDatetimeUTC:e.SellStartDatetimeUTC,SellEndDatetimeUTC:e.SellEndDatetimeUTC}});t(a)}else console.log("No popular products found"),t([])}catch(e){console.error("Error parsing popular products data:",e),t([])}else console.error("Invalid or empty response from API"),t([])}catch(e){console.error("Error fetching popular products:",e),t([])}finally{i(!1)}})()},[]),l)?(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Today's Hot Deals"}),(0,r.jsx)(F.A,{className:"h-6 w-6 text-red-500"})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-4",children:Array.from({length:4}).map((e,t)=>(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:[(0,r.jsx)("div",{className:"aspect-square",children:(0,r.jsx)(_.E,{className:"h-full w-full"})}),(0,r.jsxs)("div",{className:"p-4 space-y-2",children:[(0,r.jsx)(_.E,{className:"h-4 w-full"}),(0,r.jsx)(_.E,{className:"h-4 w-3/4"}),(0,r.jsx)(_.E,{className:"h-6 w-1/3"})]}),(0,r.jsx)("div",{className:"p-4 pt-0",children:(0,r.jsxs)("div",{className:"flex w-full gap-2",children:[(0,r.jsx)(_.E,{className:"h-10 flex-1"}),(0,r.jsx)(_.E,{className:"h-10 w-10"})]})})]},t))})]}):0===e.length?(0,r.jsxs)("div",{className:"w-full text-center py-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-4",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Today's Hot Deals"}),(0,r.jsx)(F.A,{className:"h-6 w-6 text-red-500"})]}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"No hot deals available at the moment"})]}):(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold",children:"Today's Hot Deals"}),(0,r.jsx)(F.A,{className:"h-6 w-6 text-red-500"})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4 sm:gap-6 sm:grid-cols-2 lg:grid-cols-4",children:e.slice(0,4).map(e=>(0,r.jsx)(M.A,{product:e},e.ProductId))})]})}function z(){return(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row min-h-screen bg-background",children:[(0,r.jsx)("div",{className:"w-full lg:w-64 lg:flex-shrink-0 px-4 sm:px-6 lg:px-0 py-4 lg:py-0",children:(0,r.jsx)(D,{})}),(0,r.jsxs)("div",{className:"flex-1 p-4 sm:p-6",children:[(0,r.jsx)(u,{}),(0,r.jsx)(k,{}),(0,r.jsxs)("div",{className:"space-y-12 mt-8",children:[(0,r.jsxs)("section",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"New Products"}),(0,r.jsx)(R,{effect:"icon-inline"})]}),(0,r.jsx)("section",{children:(0,r.jsx)(B,{})}),(0,r.jsx)("section",{children:(0,r.jsx)(W,{})})]})]})]})}}},e=>{e.O(0,[4277,3464,4706,6774,4042,6220,2616,2443,8441,5964,7358],()=>e(e.s=49131)),_N_E=e.O()}]);