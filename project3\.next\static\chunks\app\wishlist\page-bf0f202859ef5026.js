(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1720],{490:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k});var s=r(95155),a=r(12115),o=r(84995),l=r(89613),i=r(53999);let n=l.Kq,c=l.bL,d=l.l9,m=a.forwardRef((e,t)=>{let{className:r,sideOffset:a=4,...o}=e;return(0,s.jsx)(l.UC,{ref:t,sideOffset:a,className:(0,i.cn)("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...o})});m.displayName=l.UC.displayName;var u=r(88482),g=r(97168),f=r(6874),p=r.n(f),h=r(66766),x=r(79891),y=r(59268),b=r(51154),v=r(62525),N=r(92657),j=r(51976),w=r(13052),P=r(23464),I=r(56671);let S=e=>{if(!e)return[];try{if(e.startsWith("[")||e.startsWith("{")){let t=JSON.parse(e);if(Array.isArray(t))return t;if(t&&"object"==typeof t)return[t]}let t=e.trim();if(t)return[{AttachmentName:t.split("/").pop()||"image",AttachmentURL:t,IsPrimary:!0}]}catch(e){console.error("Error parsing product images:",e)}return[]},U=e=>{if(!e||"string"!=typeof e)return"/placeholder-image.jpg";try{let t=e.trim();if(t.startsWith("http://")||t.startsWith("https://"))return t.replace(/([^:]\/)\/+/g,"$1");let r="https://admin.codemedicalapps.com/".replace(/\/$/,""),s=t;return s.startsWith("/")||(s="/".concat(s)),s=s.replace(/\/+/g,"/"),"".concat(r).concat(s)}catch(t){return console.error("Error constructing image URL:",t,"URL:",e),"/placeholder-image.jpg"}},A=e=>new Promise(t=>{let r=new h.default;r.onload=()=>t(!0),r.onerror=()=>t(!1),r.src=e}),C=async e=>{let t=JSON.parse(localStorage.getItem("wishlist_image_cache")||"{}"),r=Date.now();for(let s of e){let e=s.id,a=t[e];if(a&&a.success&&r-a.timestamp<864e5)continue;let o=await A(s.imageUrl);t[e]={url:s.imageUrl,timestamp:r,success:o}}localStorage.setItem("wishlist_image_cache",JSON.stringify(t))};function k(){let{t:e}=(0,x.t)(),{wishlistItems:t,removeFromWishlist:r,isHydrated:l}=(0,y.n)(),[i,f]=(0,a.useState)([]),[h,A]=(0,a.useState)(!1),k=async e=>{if(!e||0===e.length)return void f([]);if(e.length>0&&"object"==typeof e[0])return void f(e.map(e=>{let t="/placeholder-image.jpg";return e.imageUrl&&(t=e.imageUrl.startsWith("http://")||e.imageUrl.startsWith("https://")?e.imageUrl:U(e.imageUrl)),{id:e.productId,name:e.productName||"Unnamed Product",price:e.price||0,originalPrice:e.price||0,imageUrl:t,inStock:!0}}));let t=e.filter(e=>e&&!isNaN(Number(e)));if(console.log("Valid product IDs after filtering:",t),0===t.length){console.log("No valid product IDs found, setting empty display items"),f([]);return}console.log("Starting to fetch product details for:",t.length,"products"),A(!0);try{console.log("Fetching products for IDs:",t);let e=localStorage.getItem("cachedProducts");if(e)try{let r=JSON.parse(e).filter(e=>t.includes(e.ProductID||e.ProductId||e.id||0));if(r.length>0){console.log("Using cached products:",r.length);let e=r.map(e=>{let t="";try{if(e.ProductImagesJson&&"string"==typeof e.ProductImagesJson){let r=S(e.ProductImagesJson),s=r.find(e=>e.IsPrimary)||r[0];s&&(t=U(s.AttachmentURL||s.url||s))}!t&&e.ImagePath&&(t=U(e.ImagePath)),!t&&e.ImageUrl&&(t=U(e.ImageUrl)),!t&&e.DefaultImage&&(t=U(e.DefaultImage))}catch(e){console.error("Error processing cached product images:",e)}return{id:e.ProductID||e.ProductId||e.id||0,name:e.ProductName||e.Name||"Unnamed Product",price:e.Price||e.ProductPrice||0,originalPrice:e.OldPrice||e.OriginalPrice||e.Price||e.ProductPrice||0,imageUrl:t||"/placeholder-image.jpg",inStock:(e.StockQuantity||e.Quantity||0)>0}});f(e);return}}catch(e){console.error("Error reading from cache:",e)}console.log("Fetching products from API...");let r=t.map(async e=>{try{let t=await P.A.post("/api/product-detail",{requestParameters:{ProductId:e,recordValueJson:"[]"}});if(t.data&&t.data.data){let e=JSON.parse(t.data.data);return Array.isArray(e)?e[0]:e}return null}catch(t){return console.error("Error fetching product ".concat(e,":"),t),null}}),s=(await Promise.all(r)).filter(e=>null!==e);if(console.log("Fetched products:",s.length),console.log("Total products extracted from response:",s.length),0===s.length){console.warn("No products found in the API response."),f([]);return}let a=s.map(e=>{console.log("Processing product:",{id:e.ProductId||e.id,name:e.ProductName||e.Name,images:e.ProductImagesJson,imagePath:e.ImagePath,imageUrl:e.ImageUrl});let t="";try{if(e.ProductImagesJson)try{let r=S("string"==typeof e.ProductImagesJson?e.ProductImagesJson:JSON.stringify(e.ProductImagesJson)),s=Array.isArray(r)&&r.length>0?r.find(e=>e.IsPrimary)||r[0]:r;if(s){let e=s.AttachmentURL||s.url||s.src||s;t=U(e)}}catch(e){console.error("Error parsing product images:",e)}!t&&e.ImagePath&&(t=U(e.ImagePath)),!t&&e.ImageUrl&&(t=U(e.ImageUrl)),!t&&e.DefaultImage&&(t=U(e.DefaultImage)),!t&&e.ProductImage&&(t=U(e.ProductImage)),t||(console.warn("No valid image found for product:",e.ProductId||e.id,e),t="/placeholder-image.jpg")}catch(r){console.error("Error processing product images:",r,"for product:",e.ProductId||e.id),t="/placeholder-image.jpg"}return{id:e.ProductId||e.ProductID||e.id,name:e.ProductName||e.Name||"Unnamed Product",price:e.Price||e.ProductPrice||0,originalPrice:e.OldPrice||e.OriginalPrice||e.Price||e.ProductPrice||0,imageUrl:t||"/placeholder-image.jpg",inStock:(e.StockQuantity||e.Quantity||0)>0}});console.log("Display items prepared:",a.length),f(a);try{localStorage.setItem("cachedProducts",JSON.stringify(s))}catch(e){console.error("Error caching products:",e)}}catch(u){var r,s,a,o,l,i,n,c,d,m;console.error("Error in processWishlistItems:",u);let e="An unknown error occurred";u instanceof Error?e=u.message:u&&"object"==typeof u&&"message"in u&&(e=String(u.message)),u&&"object"==typeof u&&console.error("Error details:",{message:e,response:(null==u||null==(l=u.response)?void 0:l.data)||"No response data",status:null==u||null==(i=u.response)?void 0:i.status,statusText:null==u||null==(n=u.response)?void 0:n.statusText,config:{url:null==u||null==(c=u.config)?void 0:c.url,method:null==u||null==(d=u.config)?void 0:d.method,params:null==u||null==(m=u.config)?void 0:m.params}});let t=u&&"object"==typeof u&&"isAxiosError"in u&&(null==(s=u.response)||null==(r=s.data)?void 0:r.error)?null==(o=u.response)||null==(a=o.data)?void 0:a.error:e;I.oR.error("Failed to load wishlist: "+(t||"Unknown error")),f([])}finally{A(!1)}};return((0,a.useEffect)(()=>{l&&k(t)},[t,l]),(0,a.useEffect)(()=>{i.length>0&&C(i)},[i]),!l||h)?(0,s.jsxs)("div",{className:"container mx-auto py-12 flex flex-col items-center justify-center min-h-[50vh]",children:[(0,s.jsx)(b.A,{className:"h-12 w-12 animate-spin text-primary mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:l?"Loading your wishlist...":"Initializing wishlist..."})]}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsx)("div",{className:"flex items-center justify-between mb-8",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Your Wishlist"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:i.length>0?"".concat(i.length," ").concat(1===i.length?"item":"items"," in your wishlist"):"Your wishlist is empty"})]})}),(0,s.jsx)(o.Qp,{className:"mb-6",children:(0,s.jsxs)(o.AB,{children:[(0,s.jsx)(o.J5,{children:(0,s.jsx)(o.w1,{asChild:!0,children:(0,s.jsx)(p(),{href:"/",children:"Home"})})}),(0,s.jsx)(o.tH,{}),(0,s.jsx)(o.tJ,{children:"Wishlist"})]})}),i.length>0?(0,s.jsx)("div",{className:"grid gap-3 grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:i.map(e=>(0,s.jsxs)(u.Zp,{className:"overflow-hidden",children:[(0,s.jsxs)("div",{className:"relative aspect-square",children:[(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-gray-100",children:(0,s.jsx)("img",{src:e.imageUrl||"/placeholder-image.jpg",alt:e.name,className:"w-full h-full object-cover transition-opacity duration-300",loading:"lazy","data-original-src":e.imageUrl||"","data-fallback-attempts":"0",onError:t=>{var r;let s=t.target,a=s.src;s.onerror=null;let o=parseInt(s.dataset.fallbackAttempts||"0");if(s.dataset.fallbackAttempts=String(o+1),0===o){let t=s.dataset.originalSrc||e.imageUrl;if(t&&!a.includes("admin.codemedicalapps.com")){s.src=U(t);return}}if((1===o||0===o)&&!a.includes("placeholder-image.jpg")||(2===o||o<=1)&&!a.includes("placeholder-image.jpg")){s.src="/placeholder-image.jpg";return}s.src="/placeholder-image.jpg",console.log("Using final fallback image for:",e.id,e.name);let l=null==(r=s.closest(".aspect-square"))?void 0:r.querySelector("div");if(l&&!l.querySelector(".fallback-text")){let e=document.createElement("span");e.className="fallback-text absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-500 text-sm",e.textContent="Image unavailable",l.appendChild(e),s.style.display="none"}},onLoad:()=>{console.log("Image loaded successfully:",e.imageUrl);let t=document.querySelector('img[data-original-src="'.concat(e.imageUrl,'"]'));if(t){var r;t.dataset.fallbackAttempts="0";let e=null==(r=t.closest(".aspect-square"))?void 0:r.querySelector(".fallback-text");e&&e.remove(),t.style.display=""}{let t=JSON.parse(localStorage.getItem("wishlist_image_cache")||"{}");t[e.id]={url:e.imageUrl,timestamp:Date.now(),success:!0},localStorage.setItem("wishlist_image_cache",JSON.stringify(t))}}},"wishlist-img-".concat(e.id,"-").concat(e.imageUrl))}),(0,s.jsx)(g.$,{variant:"ghost",size:"icon",className:"absolute top-2 right-2 rounded-full bg-background/80 backdrop-blur-sm hover:bg-background/60 min-h-[36px] min-w-[36px] sm:min-h-[32px] sm:min-w-[32px]",onClick:()=>{r(e.id),I.oR.success("Product removed from wishlist")},children:(0,s.jsx)(v.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"p-3 sm:p-4",children:[(0,s.jsx)(n,{children:(0,s.jsxs)(c,{children:[(0,s.jsx)(d,{children:(0,s.jsx)("h3",{className:"text-sm sm:text-base font-semibold truncate",children:e.name})}),(0,s.jsx)(m,{children:(0,s.jsx)("p",{children:e.name})})]})}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-3 sm:mb-4",children:[(0,s.jsxs)("span",{className:"text-base sm:text-lg font-bold",children:["$",e.price.toFixed(2)]}),e.originalPrice&&e.originalPrice>e.price&&(0,s.jsxs)("span",{className:"text-xs sm:text-sm text-muted-foreground line-through",children:["$",e.originalPrice.toFixed(2)]})]}),(0,s.jsx)("div",{className:"flex flex-col sm:flex-row gap-2",children:(0,s.jsx)(g.$,{variant:"outline",size:"sm",className:"w-full min-h-[40px] text-xs sm:text-sm",asChild:!0,children:(0,s.jsxs)(p(),{href:"/product/".concat(e.id),children:[(0,s.jsx)(N.A,{className:"h-4 w-4 mr-1 sm:mr-2"}),(0,s.jsx)("span",{className:"hidden xs:inline",children:"View"}),(0,s.jsx)("span",{className:"xs:hidden",children:"\uD83D\uDC41"})]})})})]})]},e.id))}):(0,s.jsxs)(u.Zp,{className:"p-8 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(j.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,s.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Your wishlist is empty"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"You haven't added any products to your wishlist yet."}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground mb-6",children:["\uD83D\uDCA1 ",(0,s.jsx)("strong",{children:"How to add items:"})," Browse products and click the heart icon (♡) on any product to add it to your wishlist."]}),(0,s.jsx)("div",{className:"space-y-3",children:(0,s.jsx)(g.$,{asChild:!0,children:(0,s.jsxs)(p(),{href:"/products",children:["Browse Products",(0,s.jsx)(w.A,{className:"ml-2 h-4 w-4"})]})})})]})]})})}},35882:(e,t,r)=>{Promise.resolve().then(r.bind(r,490))},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(52596),a=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},59268:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l,n:()=>i});var s=r(95155),a=r(12115);let o=(0,a.createContext)(void 0);function l(e){let{children:t}=e,[r,l]=(0,a.useState)([]),[i,n]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let e=localStorage.getItem("wishlist");if(e)try{let t=JSON.parse(e);if(Array.isArray(t)&&t.length>0)if("number"==typeof t[0]){let e=t.map(e=>({productId:e,productName:"Product ".concat(e),productUrl:"/product/".concat(e),addedAt:new Date().toISOString()}));l(e),localStorage.setItem("wishlist",JSON.stringify(e))}else l(t)}catch(e){console.error("Failed to parse wishlist from localStorage:",e)}n(!0)},[]),(0,a.useEffect)(()=>{localStorage.setItem("wishlist",JSON.stringify(r))},[r]),(0,s.jsx)(o.Provider,{value:{wishlistItems:r,addToWishlist:(e,t,s,a,o)=>{r.some(t=>t.productId===e)||l([...r,{productId:e,productName:t,productUrl:s,imageUrl:a,price:o,addedAt:new Date().toISOString()}])},removeFromWishlist:e=>{l(r.filter(t=>t.productId!==e))},isInWishlist:e=>r.some(t=>t.productId===e),getWishlistItem:e=>r.find(t=>t.productId===e),totalItems:r.length,isHydrated:i},children:t})}function i(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useWishlist must be used within a WishlistProvider");return e}},79891:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n,t:()=>c});var s=r(95155),a=r(12115);let o={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us",searchProducts:"Search products..."},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",searchProducts:"ابحث عن المنتجات...",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}};var l=r(94213);let i=(0,a.createContext)(void 0);function n(e){let{children:t}=e,[r,n]=(0,a.useState)("light"),[c,d]=(0,a.useState)("en"),[m,u]=(0,a.useState)("#0074b2"),[g,f]=(0,a.useState)("#ffffff");return(0,a.useEffect)(()=>{let e=(0,l.N)(m);f(e),document.documentElement.style.setProperty("--primary",m),document.documentElement.style.setProperty("--primary-foreground",e)},[m]),(0,s.jsx)(i.Provider,{value:{theme:r,language:c,primaryColor:m,primaryTextColor:g,toggleTheme:()=>{n("light"===r?"dark":"light")},setLanguage:e=>{d(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{u(e);let t=(0,l.N)(e);f(t),document.documentElement.style.setProperty("--primary",e),document.documentElement.style.setProperty("--primary-foreground",t)},t:e=>(function(e,t){let r=o[t];return e in r?r[e]:"en"!==t&&e in o.en?o.en[e]:e})(e,c)},children:t})}function c(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},84995:(e,t,r)=>{"use strict";r.d(t,{AB:()=>c,J5:()=>d,Qp:()=>n,tH:()=>g,tJ:()=>u,w1:()=>m});var s=r(95155),a=r(12115),o=r(99708),l=r(13052),i=(r(5623),r(53999));let n=a.forwardRef((e,t)=>{let{...r}=e;return(0,s.jsx)("nav",{ref:t,"aria-label":"breadcrumb",...r})});n.displayName="Breadcrumb";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("ol",{ref:t,className:(0,i.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",r),...a})});c.displayName="BreadcrumbList";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("li",{ref:t,className:(0,i.cn)("inline-flex items-center gap-1.5",r),...a})});d.displayName="BreadcrumbItem";let m=a.forwardRef((e,t)=>{let{asChild:r,className:a,...l}=e,n=r?o.DX:"a";return(0,s.jsx)(n,{ref:t,className:(0,i.cn)("transition-colors hover:text-foreground",a),...l})});m.displayName="BreadcrumbLink";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,i.cn)("font-normal text-foreground",r),...a})});u.displayName="BreadcrumbPage";let g=e=>{let{children:t,className:r,...a}=e;return(0,s.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,i.cn)("[&>svg]:size-3.5",r),...a,children:null!=t?t:(0,s.jsx)(l.A,{})})};g.displayName="BreadcrumbSeparator"},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>n,Zp:()=>l,aR:()=>i,wL:()=>m});var s=r(95155),a=r(12115),o=r(53999);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});l.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});n.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent";let m=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...a})});m.displayName="CardFooter"},94213:(e,t,r)=>{"use strict";function s(e,t){let r=e=>{let t=e.replace("#",""),r=parseInt(t.slice(0,2),16)/255,s=[r,parseInt(t.slice(2,4),16)/255,parseInt(t.slice(4,6),16)/255].map(e=>e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4));return .2126*s[0]+.7152*s[1]+.0722*s[2]},s=r(e),a=r(t);return(Math.max(s,a)+.05)/(Math.min(s,a)+.05)}function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"AA",r=s(e,"#ffffff"),a=s(e,"#000000"),o="AAA"===t?7:4.5;return r>=o&&a>=o?r>a?"#ffffff":"#000000":r>=o?"#ffffff":a>=o?"#000000":r>a?"#ffffff":"#000000"}r.d(t,{N:()=>a})},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>n});var s=r(95155),a=r(12115),o=r(99708),l=r(74466),i=r(53999);let n=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:c=!1,...d}=e,m=c?o.DX:"button";return(0,s.jsx)(m,{className:(0,i.cn)(n({variant:a,size:l,className:r})),ref:t,...d})});c.displayName="Button"}},e=>{e.O(0,[4277,3464,4706,6774,5725,6220,3164,8441,5964,7358],()=>e(e.s=35882)),_N_E=e.O()}]);