(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>n,t:()=>l});var a=r(12115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,a=e.map(e=>{let a=s(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():s(e[t],null)}}}}function n(...e){return a.useCallback(l(...e),e)}},18160:(e,t,r)=>{Promise.resolve().then(r.bind(r,18591))},18591:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(95155),s=r(12115),l=r(98816),n=r(97168),i=r(89852),d=r(88482),o=r(82714),c=r(95784),u=r(65409),f=r(35695),m=r(24752),p=r.n(m);function x(){let{user:e,isLoggedIn:t,updateProfile:r}=(0,l.J)(),m=(0,f.useRouter)(),[x,h]=(0,s.useState)(!1),[g,y]=(0,s.useState)([]),[v,N]=(0,s.useState)([]),[j,b]=(0,s.useState)([]),[C,w]=(0,s.useState)({FirstName:"",LastName:"",AddressLineOne:"",CityId:"-999",StateProvinceId:"-999",PostalCode:"",CategoryId:"1024"});(0,s.useEffect)(()=>{if(!t)return void m.push("/login")},[t,m]),(0,s.useEffect)(()=>{e&&w(t=>{var r,a,s;return{...t,FirstName:e.FirstName||"",LastName:e.LastName||"",AddressLineOne:e.AddressLineOne||"",CityId:(null==(r=e.CityId)?void 0:r.toString())||"-999",StateProvinceId:(null==(a=e.StateProvinceId)?void 0:a.toString())||"-999",PostalCode:e.PostalCode||"",CategoryId:(null==(s=e.CategoryId)?void 0:s.toString())||"1024"}})},[e]),(0,s.useEffect)(()=>{(async()=>{try{let e=await (0,u.MakeApiCallAsync)(u.Config.END_POINT_NAMES.GET_CATEGORIES_LIST,null,{},{},"POST");if((null==e?void 0:e.data)&&!e.data.errorMessage){let t=e.data;if("string"==typeof t&&(t=JSON.parse(t)),Array.isArray(t)&&t.length>0&&t[0].DATA){let e=JSON.parse(t[0].DATA);Array.isArray(e)&&b(e)}}}catch(e){console.error("Error loading categories:",e)}})()},[]);let I=(e,t)=>{w(r=>({...r,[e]:t}))},P=async()=>{if(!(null==e?void 0:e.UserId))return void p().fire({icon:"error",title:"Error",text:"User ID not found. Please log in again."});h(!0);try{let e={requestParameters:{FirstName:C.FirstName,LastName:C.LastName,AddressLineOne:C.AddressLineOne,CityId:C.CityId,StateProvinceId:C.StateProvinceId,PostalCode:C.PostalCode,CategoryId:C.CategoryId}},a=await (0,u.MakeApiCallAsync)(u.Config.END_POINT_NAMES.UPDATE_PROFILE,null,e,{Accept:"application/json","Content-Type":"application/json"},"POST");if((null==a?void 0:a.data)&&!a.data.errorMessage)r({FirstName:C.FirstName,LastName:C.LastName,AddressLineOne:C.AddressLineOne,CityId:parseInt(C.CityId),StateProvinceId:parseInt(C.StateProvinceId),PostalCode:C.PostalCode,CategoryId:parseInt(C.CategoryId),UserName:"".concat(C.FirstName," ").concat(C.LastName).trim()}),p().fire({icon:"success",title:"Success!",text:"Profile updated successfully!",timer:2e3,showConfirmButton:!1});else{var t;throw Error((null==a||null==(t=a.data)?void 0:t.errorMessage)||"Failed to update profile")}}catch(e){console.error("Profile update error:",e),p().fire({icon:"error",title:"Update Failed",text:e instanceof Error?e.message:"An error occurred while updating your profile"})}finally{h(!1)}};return t&&e?(0,a.jsx)("div",{className:"container mx-auto p-8",children:(0,a.jsxs)(d.Zp,{className:"max-w-2xl mx-auto p-6",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Update Profile"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"firstName",children:"First Name"}),(0,a.jsx)(i.p,{id:"firstName",type:"text",value:C.FirstName,onChange:e=>I("FirstName",e.target.value),placeholder:"Enter your first name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"lastName",children:"Last Name"}),(0,a.jsx)(i.p,{id:"lastName",type:"text",value:C.LastName,onChange:e=>I("LastName",e.target.value),placeholder:"Enter your last name"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"address",children:"Address"}),(0,a.jsx)(i.p,{id:"address",type:"text",value:C.AddressLineOne,onChange:e=>I("AddressLineOne",e.target.value),placeholder:"Enter your address"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"cityId",children:"City"}),(0,a.jsxs)(c.l6,{value:C.CityId,onValueChange:e=>I("CityId",e),children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Select city"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"-999",children:"Select City"}),g.map(e=>(0,a.jsx)(c.eb,{value:e.CityId.toString(),children:e.CityName},e.CityId))]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"stateId",children:"State/Province"}),(0,a.jsxs)(c.l6,{value:C.StateProvinceId,onValueChange:e=>I("StateProvinceId",e),children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Select state"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"-999",children:"Select State"}),v.map(e=>(0,a.jsx)(c.eb,{value:e.StateProvinceId.toString(),children:e.StateProvinceName},e.StateProvinceId))]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"postalCode",children:"Postal Code"}),(0,a.jsx)(i.p,{id:"postalCode",type:"text",value:C.PostalCode,onChange:e=>I("PostalCode",e.target.value),placeholder:"Enter postal code"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(o.J,{htmlFor:"category",children:"Category"}),(0,a.jsxs)(c.l6,{value:C.CategoryId,onValueChange:e=>I("CategoryId",e),children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Select category"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"1024",children:"Default Category"}),j.map(e=>(0,a.jsx)(c.eb,{value:e.CategoryId.toString(),children:e.CategoryName},e.CategoryId))]})]})]}),(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsx)(n.$,{onClick:P,disabled:x,className:"w-full",children:x?"Updating...":"Update Profile"})})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-gray-50 rounded",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"Current User Info:"}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Email:"})," ",e.Email]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"User ID:"})," ",e.UserId]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Phone:"})," ",e.PhoneNumber]})]})]})}):(0,a.jsx)("div",{className:"container mx-auto p-8",children:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("p",{children:"Please log in to access your profile."})})})}},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(12115);let s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:i=2,absoluteStrokeWidth:d,className:o="",children:c,iconNode:u,...f}=e;return(0,a.createElement)("svg",{ref:t,...l,width:n,height:n,stroke:r,strokeWidth:d?24*Number(i)/Number(n):i,className:s("lucide",o),...f},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),i=(e,t)=>{let r=(0,a.forwardRef)((r,l)=>{let{className:i,...d}=r;return(0,a.createElement)(n,{ref:l,iconNode:t,className:s("lucide-".concat(e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),i),...d})});return r.displayName="".concat(e),r}},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var a=r(12115),s=r(63655),l=r(95155),n=a.forwardRef((e,t)=>(0,l.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=n},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var a=r(52596),s=r(39688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});var a=r(52596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=a.$,n=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:i}=t,d=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],a=null==i?void 0:i[e];if(null===t)return null;let l=s(t)||s(a);return n[e][l]}),o=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return l(e,d,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:r,className:a,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...o}[t]):({...i,...o})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},82714:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var a=r(95155),s=r(12115),l=r(40968),n=r(74466),i=r(53999);let d=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.b,{ref:t,className:(0,i.cn)(d(),r),...s})});o.displayName=l.b.displayName},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>u});var a=r(95155),s=r(12115),l=r(53999);let n=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});n.displayName="Card";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...s})});i.displayName="CardHeader";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});d.displayName="CardTitle";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...s})});o.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",r),...s})});c.displayName="CardContent";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",r),...s})});u.displayName="CardFooter"},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(95155),s=r(12115),l=r(53999);let n=s.forwardRef((e,t)=>{let{className:r,type:s,...n}=e;return(0,a.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...n})});n.displayName="Input"},95784:(e,t,r)=>{"use strict";r.d(t,{bq:()=>f,eb:()=>h,gC:()=>x,l6:()=>c,yv:()=>u});var a=r(95155),s=r(12115),l=r(14582),n=r(66474),i=r(47863),d=r(5196),o=r(53999);let c=l.bL;l.YJ;let u=l.WT,f=s.forwardRef((e,t)=>{let{className:r,children:s,...i}=e;return(0,a.jsxs)(l.l9,{ref:t,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...i,children:[s,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});f.displayName=l.l9.displayName;let m=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.PP,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});m.displayName=l.PP.displayName;let p=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.wn,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});p.displayName=l.wn.displayName;let x=s.forwardRef((e,t)=>{let{className:r,children:s,position:n="popper",...i}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:t,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-white text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:n,...i,children:[(0,a.jsx)(m,{}),(0,a.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(p,{})]})})});x.displayName=l.UC.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.JU,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...s})}).displayName=l.JU.displayName;let h=s.forwardRef((e,t)=>{let{className:r,children:s,...n}=e;return(0,a.jsxs)(l.q7,{ref:t,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:s})]})});h.displayName=l.q7.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(l.wv,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",r),...s})}).displayName=l.wv.displayName},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>d});var a=r(95155),s=r(12115),l=r(99708),n=r(74466),i=r(53999);let d=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=s.forwardRef((e,t)=>{let{className:r,variant:s,size:n,asChild:o=!1,...c}=e,u=o?l.DX:"button";return(0,a.jsx)(u,{className:(0,i.cn)(d({variant:s,size:n,className:r})),ref:t,...c})});o.displayName="Button"},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i,Dc:()=>o,TL:()=>n});var a=r(12115),s=r(6101),l=r(95155);function n(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...l}=e;if(a.isValidElement(r)){var n;let e,i,d=(n=r,(i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(i=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),o=function(e,t){let r={...t};for(let a in t){let s=e[a],l=t[a];/^on[A-Z]/.test(a)?s&&l?r[a]=(...e)=>{let t=l(...e);return s(...e),t}:s&&(r[a]=s):"style"===a?r[a]={...s,...l}:"className"===a&&(r[a]=[s,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==a.Fragment&&(o.ref=t?(0,s.t)(t,d):d),a.cloneElement(r,o)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:s,...n}=e,i=a.Children.toArray(s),d=i.find(c);if(d){let e=d.props.children,s=i.map(t=>t!==d?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...n,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,s):null})}return(0,l.jsx)(t,{...n,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var i=n("Slot"),d=Symbol("radix.slottable");function o(e){let t=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=d,t}function c(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}}},e=>{e.O(0,[8320,4277,3464,3942,5725,5145,655,8816,8441,5964,7358],()=>e(e.s=18160)),_N_E=e.O()}]);