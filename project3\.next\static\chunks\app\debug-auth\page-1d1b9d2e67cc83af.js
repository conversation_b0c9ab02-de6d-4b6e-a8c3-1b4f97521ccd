(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3759],{32010:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>t});var r=l(95155),n=l(98816),i=l(12115);function t(){let{user:e,isLoggedIn:s,isLoading:l,token:t}=(0,n.J)(),[c,d]=(0,i.useState)(null),[o,a]=(0,i.useState)(null);return(0,i.useEffect)(()=>{{let e=document.cookie.split(";"),s=null;for(let l of e){let[e,r]=l.trim().split("=");if("auth_user"===e)try{s=JSON.parse(decodeURIComponent(r));break}catch(e){console.error("Error parsing user cookie:",e)}}d(s)}fetch("/api/auth/get-token",{method:"GET",credentials:"include"}).then(e=>e.json()).then(e=>a(e)).catch(e=>console.error("Token fetch error:",e))},[]),(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Authentication Debug"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"User Context State"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"isLoading:"})," ",l?"true":"false"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"isLoggedIn:"})," ",s?"true":"false"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"token exists:"})," ",t?"yes":"no"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"user exists:"})," ",e?"yes":"no"]}),e&&(0,r.jsxs)("div",{className:"ml-4 space-y-1",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"UserId:"})," ",e.UserId]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"UserID:"})," ",e.UserID]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Email:"})," ",e.Email]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Name:"})," ",e.FirstName," ",e.LastName]}),(0,r.jsx)("p",{children:(0,r.jsx)("strong",{children:"Full user object:"})}),(0,r.jsx)("pre",{className:"bg-white p-2 rounded text-xs overflow-auto",children:JSON.stringify(e,null,2)})]})]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Cookie Data"}),(0,r.jsx)("div",{className:"text-sm",children:c?(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"UserId:"})," ",c.UserId]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"UserID:"})," ",c.UserID]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Email:"})," ",c.Email]}),(0,r.jsx)("p",{children:(0,r.jsx)("strong",{children:"Full cookie object:"})}),(0,r.jsx)("pre",{className:"bg-white p-2 rounded text-xs overflow-auto",children:JSON.stringify(c,null,2)})]}):(0,r.jsx)("p",{children:"No cookie data found"})})]}),(0,r.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Token API Response"}),(0,r.jsx)("div",{className:"text-sm",children:o?(0,r.jsx)("pre",{className:"bg-white p-2 rounded text-xs overflow-auto",children:JSON.stringify(o,null,2)}):(0,r.jsx)("p",{children:"Loading token data..."})})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"Raw Cookies"}),(0,r.jsx)("div",{className:"text-sm",children:(0,r.jsx)("p",{className:"break-all",children:document.cookie||"No cookies found"})})]})]})]})}},44317:(e,s,l)=>{Promise.resolve().then(l.bind(l,32010))}},e=>{e.O(0,[3464,8816,8441,5964,7358],()=>e(e.s=44317)),_N_E=e.O()}]);