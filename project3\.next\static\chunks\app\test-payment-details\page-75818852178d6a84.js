(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5999],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>l,t:()=>i});var n=r(12115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function l(...e){return n.useCallback(i(...e),e)}},10006:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var n=r(95155),a=r(12115),i=r(88482),l=r(97168),s=r(24792);let o=["Zain cash (Iraq)","Rafidain Bank","Asia Pay","PayPal","Amazon Gift","Cash on delivery"];function d(){let[e,t]=(0,a.useState)(null);return(0,n.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Payment Method Details Demo"}),(0,n.jsxs)(i.Zp,{className:"p-6 mb-8",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Select a Payment Method"}),(0,n.jsx)("p",{className:"text-muted-foreground mb-6",children:"Choose a payment method to see the enhanced details that will appear in the checkout page."}),(0,n.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 mb-6",children:o.map(r=>(0,n.jsx)(l.$,{variant:e===r?"default":"outline",onClick:()=>t(r),className:"h-auto p-4 text-center",children:r},r))}),e&&(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Enhanced Payment Details:"}),(0,n.jsx)(s.Y,{paymentMethodName:e,paymentMethodId:1})]}),!e&&(0,n.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"Select a payment method above to see the enhanced details"})]}),(0,n.jsxs)("div",{className:"bg-blue-50 p-6 rounded-lg",children:[(0,n.jsx)("h3",{className:"font-semibold text-blue-900 mb-2",children:"Features:"}),(0,n.jsxs)("ul",{className:"text-blue-800 space-y-1 text-sm",children:[(0,n.jsx)("li",{children:"• Copy phone numbers and account numbers with one click"}),(0,n.jsx)("li",{children:"• Copy email addresses for gift card payments"}),(0,n.jsx)("li",{children:"• Direct buttons to open PayPal and Amazon links"}),(0,n.jsx)("li",{children:"• Copy payment links to clipboard"}),(0,n.jsx)("li",{children:"• Visual feedback when items are copied"}),(0,n.jsx)("li",{children:"• Clear instructions for each payment method"}),(0,n.jsx)("li",{children:"• Location indicators (Inside Iraq / Outside Iraq)"}),(0,n.jsx)("li",{children:"• Special handling for cash on delivery"})]})]})]})})}},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(12115);let a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:l=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:u,...f}=e;return(0,n.createElement)("svg",{ref:t,...i,width:l,height:l,stroke:r,strokeWidth:o?24*Number(s)/Number(l):s,className:a("lucide",d),...f},[...u.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),s=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:s,...o}=r;return(0,n.createElement)(l,{ref:i,iconNode:t,className:a("lucide-".concat(e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),s),...o})});return r.displayName="".concat(e),r}},42485:(e,t,r)=>{Promise.resolve().then(r.bind(r,10006))},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var n=r(52596),a=r(39688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>o});var n=r(95155),a=r(12115),i=r(99708),l=r(74466),s=r(53999);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:d=!1,...c}=e,u=d?i.DX:"button";return(0,n.jsx)(u,{className:(0,s.cn)(o({variant:a,size:l,className:r})),ref:t,...c})});d.displayName="Button"},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>s,Dc:()=>d,TL:()=>l});var n=r(12115),a=r(6101),i=r(95155);function l(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var l;let e,s,o=(l=r,(s=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(s=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),d=function(e,t){let r={...t};for(let n in t){let a=e[n],i=t[n];/^on[A-Z]/.test(n)?a&&i?r[n]=(...e)=>{let t=i(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...i}:"className"===n&&(r[n]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(d.ref=t?(0,a.t)(t,o):o),n.cloneElement(r,d)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...l}=e,s=n.Children.toArray(a),o=s.find(c);if(o){let e=o.props.children,a=s.map(t=>t!==o?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...l,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var s=l("Slot"),o=Symbol("radix.slottable");function d(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{e.O(0,[4277,6774,4792,8441,5964,7358],()=>e(e.s=42485)),_N_E=e.O()}]);