(()=>{var a={};a.id=4560,a.ids=[4560],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20383:(a,b,c)=>{Promise.resolve().then(c.bind(c,59393))},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28559:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31263:(a,b,c)=>{"use strict";c.d(b,{DW:()=>j,PA:()=>i,Rb:()=>g,pN:()=>h});var d=c(4765),e=c.n(d);let f=process.env.NEXT_PUBLIC_ENCRYPTION_KEY||"your-secret-key-change-this-in-production";function g(a){try{let b=a.toString();return e().AES.encrypt(b,f).toString().replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}catch(b){return console.error("Encryption error:",b),a.toString()}}function h(a){try{let b=a.replace(/-/g,"+").replace(/_/g,"/");for(;b.length%4;)b+="=";let c=e().AES.decrypt(b,f).toString(e().enc.Utf8);if(!c)throw Error("Failed to decrypt value");return c}catch(b){return console.error("Decryption error:",b),a}}function i(a){return g(a)}function j(a){return h(a)}},33873:a=>{"use strict";a.exports=require("path")},34993:(a,b,c)=>{"use strict";c.d(b,{AB:()=>j,J5:()=>k,Qp:()=>i,tH:()=>n,tJ:()=>m,w1:()=>l});var d=c(60687),e=c(43210),f=c(8730),g=c(14952),h=(c(93661),c(96241));let i=e.forwardRef(({...a},b)=>(0,d.jsx)("nav",{ref:b,"aria-label":"breadcrumb",...a}));i.displayName="Breadcrumb";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("ol",{ref:c,className:(0,h.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",a),...b}));j.displayName="BreadcrumbList";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("li",{ref:c,className:(0,h.cn)("inline-flex items-center gap-1.5",a),...b}));k.displayName="BreadcrumbItem";let l=e.forwardRef(({asChild:a,className:b,...c},e)=>{let g=a?f.DX:"a";return(0,d.jsx)(g,{ref:e,className:(0,h.cn)("transition-colors hover:text-foreground",b),...c})});l.displayName="BreadcrumbLink";let m=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("span",{ref:c,role:"link","aria-disabled":"true","aria-current":"page",className:(0,h.cn)("font-normal text-foreground",a),...b}));m.displayName="BreadcrumbPage";let n=({children:a,className:b,...c})=>(0,d.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,h.cn)("[&>svg]:size-3.5",b),...c,children:a??(0,d.jsx)(g.A,{})});n.displayName="BreadcrumbSeparator"},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},42989:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["orders",{children:["[orderId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,97595)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\orders\\[orderId]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\orders\\[orderId]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/orders/[orderId]/page",pathname:"/orders/[orderId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/orders/[orderId]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},59393:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>z});var d=c(60687),e=c(43210),f=c(16189),g=c(34993),h=c(55192),i=c(24934),j=c(71463),k=c(85814),l=c.n(k),m=c(77080),n=c(832),o=c(98712),p=c(28559);let q=(0,c(62688).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var r=c(58869),s=c(41550),t=c(48340),u=c(85778),v=c(64398),w=c(19080),x=c(39956),y=c(31263);function z(){let a,b=(0,f.useParams)();(0,f.useRouter)();let c=(0,f.useSearchParams)(),{t:k}=(0,m.t)(),{user:z,isLoggedIn:A,isLoading:B,token:C}=(0,n.J)(),[D,E]=(0,e.useState)([]),[F,G]=(0,e.useState)(!0),[H,I]=(0,e.useState)(null),J=b.orderId,K=null,L=null;try{a=(0,y.DW)(J);let b=c.get("t"),d=c.get("d");if(b)try{K=(0,y.pN)(b),console.log("\uD83D\uDD10 Decrypted order total:",K)}catch(a){console.warn("⚠️ Failed to decrypt order total:",a)}if(d)try{L=(0,y.pN)(d),console.log("\uD83D\uDD10 Decrypted order date:",L)}catch(a){console.warn("⚠️ Failed to decrypt order date:",a)}console.log("\uD83D\uDD10 Order ID Decryption:",{encrypted:J,decrypted:a,isValidNumber:!isNaN(parseInt(a)),hasQuickTotal:!!K,hasQuickDate:!!L}),a===J&&(J.includes("-")||J.includes("_"))&&console.warn("⚠️ Decryption may have failed, encrypted and decrypted values are the same")}catch(b){console.error("❌ Order ID decryption failed:",b),a=J}if(B)return(0,d.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,d.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsx)(h.Zp,{children:(0,d.jsxs)("div",{className:"p-6 space-y-4",children:[(0,d.jsx)(j.E,{className:"h-8 w-64"}),(0,d.jsx)(j.E,{className:"h-4 w-32"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsx)(j.E,{className:"h-20"}),(0,d.jsx)(j.E,{className:"h-20"})]})]})})})})});if(!A)return(0,d.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,d.jsx)(h.Zp,{className:"max-w-md mx-auto",children:(0,d.jsxs)("div",{className:"p-8 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(o.A,{className:"h-8 w-8 text-primary"})}),(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Login Required"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please log in to view order details"}),(0,d.jsx)(i.$,{asChild:!0,className:"w-full",children:(0,d.jsx)(l(),{href:`/login?redirect=/orders/${b.orderId}`,children:"Login to Continue"})})]})})});let M=D[0]||{},N=M.StatusID||M.OrderStatusId||M.StateId||M.LatestStatusID||M.LatestStatusId||1,O=3===parseInt(N.toString()),P=M.LatestStatusName||M.StatusName||null;console.log("\uD83D\uDD0D Order Details: Status debugging",{orderInfo:M,statusId:N,statusName:P,availableFields:Object.keys(M)});let Q=()=>{I(null)};return(0,d.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,d.jsx)(g.Qp,{className:"mb-6",children:(0,d.jsxs)(g.AB,{children:[(0,d.jsx)(g.J5,{children:(0,d.jsx)(g.w1,{asChild:!0,children:(0,d.jsx)(l(),{href:"/",children:"Home"})})}),(0,d.jsx)(g.tH,{}),(0,d.jsx)(g.J5,{children:(0,d.jsx)(g.w1,{asChild:!0,children:(0,d.jsx)(l(),{href:"/orders",children:"Orders"})})}),(0,d.jsx)(g.tH,{}),(0,d.jsx)(g.J5,{children:(0,d.jsxs)(g.tJ,{children:["Order #",a]})})]})}),(0,d.jsx)(i.$,{variant:"outline",className:"mb-6",asChild:!0,children:(0,d.jsxs)(l(),{href:"/orders",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Back to Orders"]})}),(0,d.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Order Details"}),F?(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(h.Zp,{children:(0,d.jsxs)("div",{className:"p-6 space-y-4",children:[(0,d.jsx)(j.E,{className:"h-8 w-64"}),(0,d.jsx)(j.E,{className:"h-4 w-32"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,d.jsx)(j.E,{className:"h-20"}),(0,d.jsx)(j.E,{className:"h-20"})]})]})}),(0,d.jsx)(h.Zp,{children:(0,d.jsxs)("div",{className:"p-6 space-y-4",children:[(0,d.jsx)(j.E,{className:"h-6 w-32"}),Array.from({length:3}).map((a,b)=>(0,d.jsx)(j.E,{className:"h-16"},b))]})})]}):D.length>0?(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsx)(h.Zp,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{className:"text-2xl font-bold",children:["Order #",M.OrderNumber||M.OrderID||a]}),(0,d.jsxs)("p",{className:"text-muted-foreground flex items-center gap-2 mt-1",children:[(0,d.jsx)(q,{className:"h-4 w-4"}),"Placed on ",F?L||"Loading...":M.OrderDate||M.CreatedOn||L||"N/A"]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("p",{className:"text-2xl font-bold",children:["$",F?K?`${K}`:"Loading...":M.OrderTotal||M.TotalAmount||K||"0.00"]}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:F?"Loading items...":`${D.length} item(s)`})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h3",{className:"font-semibold mb-3 flex items-center gap-2",children:[(0,d.jsx)(r.A,{className:"h-4 w-4"}),"Customer Information"]}),(0,d.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Name:"})," ",z?.FirstName," ",z?.LastName]}),(0,d.jsxs)("p",{className:"flex items-center gap-2",children:[(0,d.jsx)(s.A,{className:"h-3 w-3"}),z?.Email||z?.EmailAddress]}),(0,d.jsxs)("p",{className:"flex items-center gap-2",children:[(0,d.jsx)(t.A,{className:"h-3 w-3"}),z?.PhoneNumber||z?.PhoneNo||"N/A"]})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("h3",{className:"font-semibold mb-3 flex items-center gap-2",children:[(0,d.jsx)(u.A,{className:"h-4 w-4"}),"Payment Information"]}),(0,d.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Payment Method:"})," ",M.PaymentMethodName||(6===M.PaymentMethod?"Cash on Delivery":"Online Payment")]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Currency:"})," USD"]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Status:"}),(()=>{if(P){let a="bg-gray-100 text-gray-800";switch(P.toLowerCase()){case"active":a="bg-blue-100 text-blue-800";break;case"in progress":case"inprogress":a="bg-orange-100 text-orange-800";break;case"completed":a="bg-green-100 text-green-800";break;case"returned":a="bg-purple-100 text-purple-800";break;case"refunded":a="bg-indigo-100 text-indigo-800";break;case"cancelled":a="bg-red-100 text-red-800"}return(0,d.jsx)("span",{className:`ml-1 px-2 py-1 rounded-full text-xs ${a}`,children:P})}else{let a=(a=>{switch(parseInt(a?.toString()||"1")){case 1:return{name:"Active",color:"bg-blue-100 text-blue-800"};case 2:return{name:"In Progress",color:"bg-orange-100 text-orange-800"};case 3:return{name:"Completed",color:"bg-green-100 text-green-800"};case 4:return{name:"Returned",color:"bg-purple-100 text-purple-800"};case 5:return{name:"Refunded",color:"bg-indigo-100 text-indigo-800"};case 6:return{name:"Cancelled",color:"bg-red-100 text-red-800"};default:return{name:"Unknown",color:"bg-gray-100 text-gray-800"}}})(N);return(0,d.jsx)("span",{className:`ml-1 px-2 py-1 rounded-full text-xs ${a.color}`,children:a.name})}})()]}),O&&(0,d.jsxs)("p",{className:"text-sm text-green-600 mt-1 flex items-center gap-1",children:[(0,d.jsx)(v.A,{className:"h-3 w-3"}),"You can now write reviews for your products"]})]})]})]})]})}),(0,d.jsx)(h.Zp,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("h3",{className:"font-semibold mb-4 flex items-center gap-2",children:[(0,d.jsx)(w.A,{className:"h-4 w-4"}),"Order Items"]}),(0,d.jsx)("div",{className:"space-y-4",children:D.map((a,b)=>{let c=a.ProductImageUrl||a.ImageUrl||a.ProductImage;return(0,d.jsxs)("div",{className:"p-4 border rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-muted rounded-lg overflow-hidden flex items-center justify-center",children:c?(0,d.jsx)("img",{src:c.startsWith("http")?c:`https://admin.codemedicalapps.com/${c}`,alt:a.ProductName||a.ItemName||"Product",className:"w-full h-full object-cover",onError:a=>{let b=a.target;b.style.display="none";let c=b.parentElement;c&&(c.innerHTML='<svg class="w-8 h-8 text-gray-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path></svg>')}}):(0,d.jsx)(w.A,{className:"h-8 w-8 text-gray-400"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium",children:a.ProductName||a.ItemName||"Medical Product"}),(0,d.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Quantity: ",a.Quantity||a.OrderQuantity||1]}),a.ProductDescription&&(0,d.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:a.ProductDescription})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("p",{className:"font-medium",children:["$",(a.UnitPrice||a.Price||a.ItemPrice||0).toFixed(2)]}),(0,d.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Total: $",((a.UnitPrice||a.Price||0)*(a.Quantity||1)).toFixed(2)]}),O&&(0,d.jsxs)(i.$,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>I(H===a.ProductID?null:a.ProductID),children:[(0,d.jsx)(v.A,{className:"h-3 w-3 mr-1"}),H===a.ProductID?"Cancel":"Write Review"]})]})]}),O&&H===a.ProductID&&(0,d.jsx)("div",{className:"mt-4",children:(0,d.jsx)(x.A,{productId:a.ProductID,productName:a.ProductName||a.ItemName||"Medical Product",onReviewSubmitted:Q})})]},b)})}),(0,d.jsx)("div",{className:"mt-6 pt-4 border-t",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-lg font-semibold",children:"Total Amount:"}),(0,d.jsxs)("span",{className:"text-2xl font-bold",children:["$",F?K?`${K}`:"Loading...":M.OrderTotal||M.TotalAmount||K||"0.00"]})]})})]})})]}):(0,d.jsx)(h.Zp,{children:(0,d.jsxs)("div",{className:"p-8 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(w.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Order Not Found"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:"The order you're looking for doesn't exist or you don't have permission to view it."}),(0,d.jsx)(i.$,{asChild:!0,children:(0,d.jsx)(l(),{href:"/orders",children:"Back to Orders"})})]})})]})]})}},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},85778:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},90119:(a,b,c)=>{Promise.resolve().then(c.bind(c,97595))},94735:a=>{"use strict";a.exports=require("events")},97595:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\orders\\\\[orderId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\orders\\[orderId]\\page.tsx","default")},98712:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]])}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,1697,7567,9500,9822,8026],()=>b(b.s=42989));module.exports=c})();