"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./components/products/product-media-gallery.tsx":
/*!*******************************************************!*\
  !*** ./components/products/product-media-gallery.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductMediaGallery: () => (/* binding */ ProductMediaGallery)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ProductMediaGallery auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Component for generating video thumbnails\nfunction VideoThumbnail(param) {\n    let { src, alt, className } = param;\n    _s();\n    const [thumbnailSrc, setThumbnailSrc] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"VideoThumbnail.useEffect\": ()=>{\n            const generateThumbnail = {\n                \"VideoThumbnail.useEffect.generateThumbnail\": ()=>{\n                    const video = videoRef.current;\n                    const canvas = canvasRef.current;\n                    if (!video || !canvas) return;\n                    const context = canvas.getContext('2d');\n                    if (!context) return;\n                    // Set canvas dimensions to match video\n                    canvas.width = video.videoWidth || 320;\n                    canvas.height = video.videoHeight || 240;\n                    try {\n                        // Draw the current frame to canvas\n                        context.drawImage(video, 0, 0, canvas.width, canvas.height);\n                        // Convert canvas to data URL\n                        const dataURL = canvas.toDataURL('image/jpeg', 0.8);\n                        setThumbnailSrc(dataURL);\n                    } catch (err) {\n                        console.error('Error generating video thumbnail:', err);\n                        setError(true);\n                    }\n                }\n            }[\"VideoThumbnail.useEffect.generateThumbnail\"];\n            const handleLoadedData = {\n                \"VideoThumbnail.useEffect.handleLoadedData\": ()=>{\n                    const video = videoRef.current;\n                    if (video) {\n                        // Seek to 1 second or 10% of video duration, whichever is smaller\n                        const seekTime = Math.min(1, video.duration * 0.1);\n                        video.currentTime = seekTime;\n                    }\n                }\n            }[\"VideoThumbnail.useEffect.handleLoadedData\"];\n            const handleSeeked = {\n                \"VideoThumbnail.useEffect.handleSeeked\": ()=>{\n                    // Small delay to ensure frame is rendered\n                    setTimeout(generateThumbnail, 100);\n                }\n            }[\"VideoThumbnail.useEffect.handleSeeked\"];\n            const video = videoRef.current;\n            if (video) {\n                video.addEventListener('loadeddata', handleLoadedData);\n                video.addEventListener('seeked', handleSeeked);\n                video.addEventListener('error', {\n                    \"VideoThumbnail.useEffect\": ()=>setError(true)\n                }[\"VideoThumbnail.useEffect\"]);\n                return ({\n                    \"VideoThumbnail.useEffect\": ()=>{\n                        video.removeEventListener('loadeddata', handleLoadedData);\n                        video.removeEventListener('seeked', handleSeeked);\n                        video.removeEventListener('error', {\n                            \"VideoThumbnail.useEffect\": ()=>setError(true)\n                        }[\"VideoThumbnail.useEffect\"]);\n                    }\n                })[\"VideoThumbnail.useEffect\"];\n            }\n        }\n    }[\"VideoThumbnail.useEffect\"], [\n        src\n    ]);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"bg-gray-200 flex items-center justify-center\", className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                size: 16,\n                className: \"text-gray-500\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 72,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative\", className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                ref: videoRef,\n                src: src,\n                className: \"hidden\",\n                muted: true,\n                playsInline: true,\n                preload: \"metadata\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            thumbnailSrc ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                src: thumbnailSrc,\n                alt: alt || 'Video thumbnail',\n                className: \"w-full h-full object-cover\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full bg-gray-200 flex items-center justify-center animate-pulse\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    size: 16,\n                    className: \"text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(VideoThumbnail, \"84fzdndoZ7wJDakFY2R8LHgI1fs=\");\n_c = VideoThumbnail;\nfunction ProductMediaGallery(param) {\n    let { media, className } = param;\n    _s1();\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isVideoLoaded, setIsVideoLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isZoomed, setIsZoomed] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [zoomPosition, setZoomPosition] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [zoomLevel, setZoomLevel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1.5);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [activeMediaType, setActiveMediaType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Filter media based on active type\n    const filteredMedia = media.filter((item)=>activeMediaType === 'all' || item.type === activeMediaType);\n    const currentItem = filteredMedia[currentIndex] || media[0];\n    const hasMultipleItems = filteredMedia.length > 1;\n    // Reset index when media changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ProductMediaGallery.useEffect\": ()=>{\n            setCurrentIndex(0);\n            setIsPlaying(false);\n        }\n    }[\"ProductMediaGallery.useEffect\"], [\n        activeMediaType,\n        media\n    ]);\n    const goToPrev = ()=>{\n        setCurrentIndex((prev)=>prev === 0 ? filteredMedia.length - 1 : prev - 1);\n        setIsPlaying(false);\n    };\n    const goToNext = ()=>{\n        setCurrentIndex((prev)=>prev === filteredMedia.length - 1 ? 0 : prev + 1);\n        setIsPlaying(false);\n    };\n    const togglePlayPause = ()=>{\n        if (currentItem.type === 'video') {\n            if (videoRef.current) {\n                if (isPlaying) {\n                    videoRef.current.pause();\n                } else {\n                    videoRef.current.play();\n                }\n                setIsPlaying(!isPlaying);\n            }\n        }\n    };\n    const handleVideoEnded = ()=>{\n        setIsPlaying(false);\n        if (hasMultipleItems) {\n            goToNext();\n        }\n    };\n    const handleThumbnailClick = (index)=>{\n        setCurrentIndex(index);\n        setIsPlaying(false);\n        setIsZoomed(false);\n    };\n    const handleImageMouseMove = (e)=>{\n        if (!isZoomed || !imageRef.current) return;\n        const rect = imageRef.current.getBoundingClientRect();\n        const x = (e.clientX - rect.left) / rect.width * 100;\n        const y = (e.clientY - rect.top) / rect.height * 100;\n        setZoomPosition({\n            x,\n            y\n        });\n    };\n    const handleImageClick = ()=>{\n        if (currentItem.type === 'image') {\n            setIsZoomed(!isZoomed);\n        }\n    };\n    const handleImageDoubleClick = ()=>{\n        if (currentItem.type === 'image') {\n            setIsFullscreen(true);\n        }\n    };\n    const increaseZoom = ()=>{\n        setZoomLevel((prev)=>Math.min(prev + 0.5, 4));\n        setIsZoomed(true);\n    };\n    const decreaseZoom = ()=>{\n        setZoomLevel((prev)=>{\n            const newLevel = Math.max(prev - 0.5, 1);\n            if (newLevel === 1) {\n                setIsZoomed(false);\n            }\n            return newLevel;\n        });\n    };\n    const resetZoom = ()=>{\n        setZoomLevel(1.5);\n        setIsZoomed(false);\n    };\n    // Group media by type for filter buttons\n    const mediaCounts = media.reduce((acc, item)=>{\n        acc[item.type] = (acc[item.type] || 0) + 1;\n        return acc;\n    }, {});\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex flex-col gap-4', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative aspect-square w-full bg-gray-100 rounded-lg overflow-hidden\",\n                children: [\n                    currentItem.type === 'image' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-9ac6182445f1a16e\" + \" \" + \"relative w-full h-full overflow-hidden group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                id: \"9ac6182445f1a16e\",\n                                children: '.cursor-zoom-in.jsx-9ac6182445f1a16e{cursor:url(\\'data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><circle cx=\"11\" cy=\"11\" r=\"8\"/><path d=\"m21 21-4.35-4.35\"/><line x1=\"11\" y1=\"8\" x2=\"11\" y2=\"14\"/><line x1=\"8\" y1=\"11\" x2=\"14\" y2=\"11\"/></svg>\\')12 12,auto}.cursor-zoom-out.jsx-9ac6182445f1a16e{cursor:url(\\'data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><circle cx=\"11\" cy=\"11\" r=\"8\"/><path d=\"m21 21-4.35-4.35\"/><line x1=\"8\" y1=\"11\" x2=\"14\" y2=\"11\"/></svg>\\')12 12,auto}'\n                            }, void 0, false, void 0, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                ref: imageRef,\n                                src: currentItem.url,\n                                alt: currentItem.alt || 'Product image',\n                                style: isZoomed ? {\n                                    transform: \"scale(\".concat(zoomLevel, \")\"),\n                                    transformOrigin: \"\".concat(zoomPosition.x, \"% \").concat(zoomPosition.y, \"%\")\n                                } : {\n                                    transform: 'scale(1)'\n                                },\n                                onClick: handleImageClick,\n                                onDoubleClick: handleImageDoubleClick,\n                                onMouseMove: handleImageMouseMove,\n                                onMouseLeave: ()=>setIsZoomed(false),\n                                className: \"jsx-9ac6182445f1a16e\" + \" \" + ((0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-full h-full object-contain transition-transform duration-300\", isZoomed ? \"cursor-zoom-out\" : \"cursor-zoom-in\") || \"\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"absolute inset-0 flex items-center justify-center pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-white/95 rounded-full p-4 shadow-xl border-2 border-gray-200\",\n                                    children: isZoomed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"absolute top-2 left-2 flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1\",\n                                        children: isZoomed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-9ac6182445f1a16e\",\n                                                    children: [\n                                                        \"Zoom: \",\n                                                        Math.round(zoomLevel * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-9ac6182445f1a16e\",\n                                                    children: \"Click to zoom\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-9ac6182445f1a16e\" + \" \" + \"flex flex-col gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: increaseZoom,\n                                                title: \"Zoom in\",\n                                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: decreaseZoom,\n                                                title: \"Zoom out\",\n                                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: resetZoom,\n                                                title: \"Reset zoom\",\n                                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all text-xs\",\n                                                children: \"1:1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsFullscreen(true),\n                                title: \"View fullscreen\",\n                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"absolute top-2 right-12 bg-white/90 hover:bg-white text-gray-700 rounded p-2 shadow-sm transition-all\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                ref: videoRef,\n                                src: currentItem.url,\n                                className: \"w-full h-full object-contain\",\n                                controls: false,\n                                onEnded: handleVideoEnded,\n                                onPlay: ()=>setIsPlaying(true),\n                                onPause: ()=>setIsPlaying(false),\n                                onLoadedData: ()=>setIsVideoLoaded(true),\n                                playsInline: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            !isVideoLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center bg-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-pulse\",\n                                    children: \"Loading video...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: togglePlayPause,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('absolute inset-0 flex items-center justify-center transition-opacity', isPlaying ? 'opacity-0 hover:opacity-100' : 'opacity-80', !isVideoLoaded && 'hidden'),\n                                \"aria-label\": isPlaying ? 'Pause' : 'Play',\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-black/50 text-white rounded-full p-3\",\n                                    children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 30\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 356,\n                                        columnNumber: 52\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1\",\n                        children: [\n                            currentItem.type === 'image' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 12\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: 12\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: currentItem.type === 'image' ? 'Image' : 'Video'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, this),\n                    hasMultipleItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: goToPrev,\n                                className: \"absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all\",\n                                \"aria-label\": \"Previous media\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 375,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: goToNext,\n                                className: \"absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all\",\n                                \"aria-label\": \"Next media\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 237,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveMediaType('all'),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('px-3 py-1 text-sm rounded-full border', activeMediaType === 'all' ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                        children: [\n                            \"All (\",\n                            media.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, this),\n                    mediaCounts.image > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveMediaType('image'),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('px-3 py-1 text-sm rounded-full border flex items-center gap-1', activeMediaType === 'image' ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 14\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: mediaCounts.image\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 11\n                    }, this),\n                    mediaCounts.video > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveMediaType('video'),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('px-3 py-1 text-sm rounded-full border flex items-center gap-1', activeMediaType === 'video' ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                size: 14\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: mediaCounts.video\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 421,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 394,\n                columnNumber: 7\n            }, this),\n            hasMultipleItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 overflow-x-auto pb-2 -mx-2 px-2\",\n                children: filteredMedia.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleThumbnailClick(index),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('relative flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 transition-all', index === currentIndex ? 'border-blue-600 ring-2 ring-blue-400' : 'border-gray-200 hover:border-gray-400'),\n                        \"aria-label\": \"View \".concat(item.type, \" \").concat(index + 1),\n                        children: item.type === 'image' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: item.thumbnail || item.url,\n                            alt: item.alt || '',\n                            className: \"w-full h-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full h-full overflow-hidden\",\n                            children: [\n                                item.thumbnail ? // Use provided thumbnail if available\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: item.thumbnail,\n                                    alt: item.alt || 'Video thumbnail',\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 21\n                                }, this) : // Generate thumbnail from video\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(VideoThumbnail, {\n                                    src: item.url,\n                                    alt: item.alt,\n                                    className: \"w-full h-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center bg-black/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/90 rounded-full p-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            size: 12,\n                                            className: \"text-gray-700 ml-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 17\n                        }, this)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 438,\n                columnNumber: 9\n            }, this),\n            isFullscreen && currentItem.type === 'image' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/95 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full h-full flex items-center justify-center p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: currentItem.url,\n                            alt: currentItem.alt || 'Product image',\n                            className: \"max-w-full max-h-full object-contain\",\n                            style: {\n                                maxWidth: '95vw',\n                                maxHeight: '95vh'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsFullscreen(false),\n                            className: \"absolute top-4 right-4 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all\",\n                            title: \"Close fullscreen\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-6 w-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 13\n                        }, this),\n                        hasMultipleItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: goToPrev,\n                                    className: \"absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all\",\n                                    title: \"Previous image\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 517,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: goToNext,\n                                    className: \"absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all\",\n                                    title: \"Next image\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/70 text-white px-3 py-1 rounded-full text-sm\",\n                            children: [\n                                currentIndex + 1,\n                                \" of \",\n                                filteredMedia.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 530,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 489,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n        lineNumber: 235,\n        columnNumber: 5\n    }, this);\n}\n_s1(ProductMediaGallery, \"0G3bEXxMzDpxYPHFGofm4PcYc2I=\");\n_c1 = ProductMediaGallery;\nvar _c, _c1;\n$RefreshReg$(_c, \"VideoThumbnail\");\n$RefreshReg$(_c1, \"ProductMediaGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/products/product-media-gallery.tsx\n"));

/***/ })

});