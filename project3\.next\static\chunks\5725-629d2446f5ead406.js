"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5725],{2564:(e,t,n)=>{n.d(t,{Qg:()=>l,bL:()=>s});var r=n(12115),i=n(63655),o=n(95155),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=r.forwardRef((e,t)=>(0,o.jsx)(i.sG.span,{...e,ref:t,style:{...l,...e.style}}));a.displayName="VisuallyHidden";var s=a},11275:(e,t,n)=>{n.d(t,{X:()=>o});var r=n(12115),i=n(52712);function o(e){let[t,n]=r.useState(void 0);return(0,i.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},19178:(e,t,n)=>{n.d(t,{qW:()=>d});var r,i=n(12115),o=n(85185),l=n(63655),a=n(6101),s=n(39033),u=n(95155),f="dismissableLayer.update",c=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=i.forwardRef((e,t)=>{var n,d;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:g,onPointerDownOutside:v,onFocusOutside:y,onInteractOutside:w,onDismiss:x,...b}=e,E=i.useContext(c),[R,A]=i.useState(null),L=null!=(d=null==R?void 0:R.ownerDocument)?d:null==(n=globalThis)?void 0:n.document,[,C]=i.useState({}),P=(0,a.s)(t,e=>A(e)),S=Array.from(E.layers),[O]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),T=S.indexOf(O),D=R?S.indexOf(R):-1,k=E.layersWithOutsidePointerEventsDisabled.size>0,W=D>=T,H=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,s.c)(e),o=i.useRef(!1),l=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));W&&!n&&(null==v||v(e),null==w||w(e),e.defaultPrevented||null==x||x())},L),N=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,s.c)(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==y||y(e),null==w||w(e),e.defaultPrevented||null==x||x())},L);return!function(e,t=globalThis?.document){let n=(0,s.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{D===E.layers.size-1&&(null==g||g(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},L),i.useEffect(()=>{if(R)return m&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=L.body.style.pointerEvents,L.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(R)),E.layers.add(R),p(),()=>{m&&1===E.layersWithOutsidePointerEventsDisabled.size&&(L.body.style.pointerEvents=r)}},[R,L,m,E]),i.useEffect(()=>()=>{R&&(E.layers.delete(R),E.layersWithOutsidePointerEventsDisabled.delete(R),p())},[R,E]),i.useEffect(()=>{let e=()=>C({});return document.addEventListener(f,e),()=>document.removeEventListener(f,e)},[]),(0,u.jsx)(l.sG.div,{...b,ref:P,style:{pointerEvents:k?W?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.m)(e.onFocusCapture,N.onFocusCapture),onBlurCapture:(0,o.m)(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:(0,o.m)(e.onPointerDownCapture,H.onPointerDownCapture)})});function p(){let e=new CustomEvent(f);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:i}=r,o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),i?(0,l.hO)(o,a):o.dispatchEvent(a)}d.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(c),r=i.useRef(null),o=(0,a.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(l.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch"},34378:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(12115),i=n(47650),o=n(63655),l=n(52712),a=n(95155),s=r.forwardRef((e,t)=>{var n,s;let{container:u,...f}=e,[c,d]=r.useState(!1);(0,l.N)(()=>d(!0),[]);let p=u||c&&(null==(s=globalThis)||null==(n=s.document)?void 0:n.body);return p?i.createPortal((0,a.jsx)(o.sG.div,{...f,ref:t}),p):null});s.displayName="Portal"},35152:(e,t,n)=>{n.d(t,{Mz:()=>e1,i3:()=>e2,UC:()=>e5,bL:()=>e0,Bk:()=>ez});var r=n(12115);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,s=Math.floor,u=e=>({x:e,y:e}),f={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}let v=new Set(["top","bottom"]);function y(e){return v.has(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>c[e])}let x=["left","right"],b=["right","left"],E=["top","bottom"],R=["bottom","top"];function A(e){return e.replace(/left|right|bottom|top/g,e=>f[e])}function L(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function C(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function P(e,t,n){let r,{reference:i,floating:o}=e,l=y(t),a=m(y(t)),s=g(a),u=p(t),f="y"===l,c=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,v=i[s]/2-o[s]/2;switch(u){case"top":r={x:c,y:i.y-o.height};break;case"bottom":r={x:c,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(t)){case"start":r[a]-=v*(n&&f?-1:1);break;case"end":r[a]+=v*(n&&f?-1:1)}return r}let S=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),s=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:f,y:c}=P(u,r,s),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:v,data:y,reset:w}=await m({x:f,y:c,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});f=null!=g?g:f,c=null!=v?v:c,p={...p,[o]:{...p[o],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:f,y:c}=P(u,d,s)),n=-1)}return{x:f,y:c,placement:d,strategy:i,middlewareData:p}};async function O(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:s}=e,{boundary:u="clippingAncestors",rootBoundary:f="viewport",elementContext:c="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=L(h),g=a[p?"floating"===c?"reference":"floating":c],v=C(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:f,strategy:s})),y="floating"===c?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),x=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},b=C(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:y,offsetParent:w,strategy:s}):y);return{top:(v.top-b.top+m.top)/x.y,bottom:(b.bottom-v.bottom+m.bottom)/x.y,left:(v.left-b.left+m.left)/x.x,right:(b.right-v.right+m.right)/x.x}}function T(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function D(e){return i.some(t=>e[t]>=0)}let k=new Set(["left","top"]);async function W(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),s="y"===y(n),u=k.has(l)?-1:1,f=o&&s?-1:1,c=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:v}="number"==typeof c?{mainAxis:c,crossAxis:0,alignmentAxis:null}:{mainAxis:c.mainAxis||0,crossAxis:c.crossAxis||0,alignmentAxis:c.alignmentAxis};return a&&"number"==typeof v&&(g="end"===a?-1*v:v),s?{x:g*f,y:m*u}:{x:m*u,y:g*f}}function H(){return"undefined"!=typeof window}function N(e){return z(e)?(e.nodeName||"").toLowerCase():"#document"}function j(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function F(e){var t;return null==(t=(z(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function z(e){return!!H()&&(e instanceof Node||e instanceof j(e).Node)}function B(e){return!!H()&&(e instanceof Element||e instanceof j(e).Element)}function M(e){return!!H()&&(e instanceof HTMLElement||e instanceof j(e).HTMLElement)}function V(e){return!!H()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof j(e).ShadowRoot)}let G=new Set(["inline","contents"]);function _(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!G.has(i)}let X=new Set(["table","td","th"]),I=[":popover-open",":modal"];function Y(e){return I.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let q=["transform","translate","scale","rotate","perspective"],$=["transform","translate","scale","rotate","perspective","filter"],Q=["paint","layout","strict","content"];function U(e){let t=Z(),n=B(e)?ee(e):e;return q.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||$.some(e=>(n.willChange||"").includes(e))||Q.some(e=>(n.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let J=new Set(["html","body","#document"]);function K(e){return J.has(N(e))}function ee(e){return j(e).getComputedStyle(e)}function et(e){return B(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===N(e))return e;let t=e.assignedSlot||e.parentNode||V(e)&&e.host||F(e);return V(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=en(t);return K(n)?t.ownerDocument?t.ownerDocument.body:t.body:M(n)&&_(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=j(i);if(o){let e=ei(l);return t.concat(l,l.visualViewport||[],_(i)?i:[],e&&n?er(e):[])}return t.concat(i,er(i,[],n))}function ei(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eo(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=M(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,s=a(n)!==o||a(r)!==l;return s&&(n=o,r=l),{width:n,height:r,$:s}}function el(e){return B(e)?e:e.contextElement}function ea(e){let t=el(e);if(!M(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=eo(t),l=(o?a(n.width):n.width)/r,s=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),s&&Number.isFinite(s)||(s=1),{x:l,y:s}}let es=u(0);function eu(e){let t=j(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:es}function ef(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=el(e),a=u(1);t&&(r?B(r)&&(a=ea(r)):a=ea(e));let s=(void 0===(i=n)&&(i=!1),r&&(!i||r===j(l))&&i)?eu(l):u(0),f=(o.left+s.x)/a.x,c=(o.top+s.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let e=j(l),t=r&&B(r)?j(r):r,n=e,i=ei(n);for(;i&&r&&t!==n;){let e=ea(i),t=i.getBoundingClientRect(),r=ee(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;f*=e.x,c*=e.y,d*=e.x,p*=e.y,f+=o,c+=l,i=ei(n=j(i))}}return C({width:d,height:p,x:f,y:c})}function ec(e,t){let n=et(e).scrollLeft;return t?t.left+n:ef(F(e)).left+n}function ed(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ec(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function eh(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=j(e),r=F(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,s=0;if(i){o=i.width,l=i.height;let e=Z();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,s=i.offsetTop)}return{width:o,height:l,x:a,y:s}}(e,n);else if("document"===t)r=function(e){let t=F(e),n=et(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ec(e),s=-n.scrollTop;return"rtl"===ee(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:s}}(F(e));else if(B(t))r=function(e,t){let n=ef(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=M(e)?ea(e):u(1),l=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:l,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=eu(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return C(r)}function em(e){return"static"===ee(e).position}function eg(e,t){if(!M(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return F(e)===n&&(n=n.ownerDocument.body),n}function ev(e,t){var n;let r=j(e);if(Y(e))return r;if(!M(e)){let t=en(e);for(;t&&!K(t);){if(B(t)&&!em(t))return t;t=en(t)}return r}let i=eg(e,t);for(;i&&(n=i,X.has(N(n)))&&em(i);)i=eg(i,t);return i&&K(i)&&em(i)&&!U(i)?r:i||function(e){let t=en(e);for(;M(t)&&!K(t);){if(U(t))return t;if(Y(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||ev,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=M(t),i=F(t),o="fixed"===n,l=ef(e,!0,o,t),a={scrollLeft:0,scrollTop:0},s=u(0);if(r||!r&&!o)if(("body"!==N(t)||_(i))&&(a=et(t)),r){let e=ef(t,!0,o,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else i&&(s.x=ec(i));o&&!r&&i&&(s.x=ec(i));let f=!i||r||o?u(0):ed(i,a);return{x:l.left+a.scrollLeft-s.x-f.x,y:l.top+a.scrollTop-s.y-f.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ew={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=F(r),a=!!t&&Y(t.floating);if(r===l||a&&o)return n;let s={scrollLeft:0,scrollTop:0},f=u(1),c=u(0),d=M(r);if((d||!d&&!o)&&(("body"!==N(r)||_(l))&&(s=et(r)),M(r))){let e=ef(r);f=ea(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let p=!l||d||o?u(0):ed(l,s,!0);return{width:n.width*f.x,height:n.height*f.y,x:n.x*f.x-s.scrollLeft*f.x+c.x+p.x,y:n.y*f.y-s.scrollTop*f.y+c.y+p.y}},getDocumentElement:F,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?Y(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>B(e)&&"body"!==N(e)),i=null,o="fixed"===ee(e).position,l=o?en(e):e;for(;B(l)&&!K(l);){let t=ee(l),n=U(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&ep.has(i.position)||_(l)&&!n&&function e(t,n){let r=en(t);return!(r===n||!B(r)||K(r))&&("fixed"===ee(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=en(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],s=a[0],u=a.reduce((e,n)=>{let r=eh(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},eh(t,s,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:ev,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eo(e);return{width:t,height:n}},getScale:ea,isElement:B,isRTL:function(e){return"rtl"===ee(e).direction}};function ex(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eb=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:s,elements:u,middlewareData:f}=t,{element:c,padding:p=0}=d(e,t)||{};if(null==c)return{};let v=L(p),w={x:n,y:r},x=m(y(i)),b=g(x),E=await s.getDimensions(c),R="y"===x,A=R?"clientHeight":"clientWidth",C=a.reference[b]+a.reference[x]-w[x]-a.floating[b],P=w[x]-a.reference[x],S=await (null==s.getOffsetParent?void 0:s.getOffsetParent(c)),O=S?S[A]:0;O&&await (null==s.isElement?void 0:s.isElement(S))||(O=u.floating[A]||a.floating[b]);let T=O/2-E[b]/2-1,D=o(v[R?"top":"left"],T),k=o(v[R?"bottom":"right"],T),W=O-E[b]-k,H=O/2-E[b]/2+(C/2-P/2),N=l(D,o(H,W)),j=!f.arrow&&null!=h(i)&&H!==N&&a.reference[b]/2-(H<D?D:k)-E[b]/2<0,F=j?H<D?H-D:H-W:0;return{[x]:w[x]+F,data:{[x]:N,centerOffset:H-N-F,...j&&{alignmentOffset:F}},reset:j}}});var eE=n(47650),eR="undefined"!=typeof document?r.useLayoutEffect:function(){};function eA(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eA(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!eA(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eL(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eC(e,t){let n=eL(e);return Math.round(t*n)/n}function eP(e){let t=r.useRef(e);return eR(()=>{t.current=e}),t}var eS=n(63655),eO=n(95155),eT=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eO.jsx)(eS.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eO.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eT.displayName="Arrow";var eD=n(6101),ek=n(46081),eW=n(39033),eH=n(52712),eN=n(11275),ej="Popper",[eF,ez]=(0,ek.A)(ej),[eB,eM]=eF(ej),eV=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eO.jsx)(eB,{scope:t,anchor:i,onAnchorChange:o,children:n})};eV.displayName=ej;var eG="PopperAnchor",e_=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=eM(eG,n),a=r.useRef(null),s=(0,eD.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,eO.jsx)(eS.sG.div,{...o,ref:s})});e_.displayName=eG;var eX="PopperContent",[eI,eY]=eF(eX),eq=r.forwardRef((e,t)=>{var n,i,a,u,f,c,v,L;let{__scopePopper:C,side:P="bottom",sideOffset:H=0,align:N="center",alignOffset:j=0,arrowPadding:z=0,avoidCollisions:B=!0,collisionBoundary:M=[],collisionPadding:V=0,sticky:G="partial",hideWhenDetached:_=!1,updatePositionStrategy:X="optimized",onPlaced:I,...Y}=e,q=eM(eX,C),[$,Q]=r.useState(null),U=(0,eD.s)(t,e=>Q(e)),[Z,J]=r.useState(null),K=(0,eN.X)(Z),ee=null!=(v=null==K?void 0:K.width)?v:0,et=null!=(L=null==K?void 0:K.height)?L:0,en="number"==typeof V?V:{top:0,right:0,bottom:0,left:0,...V},ei=Array.isArray(M)?M:[M],eo=ei.length>0,ea={padding:en,boundary:ei.filter(eZ),altBoundary:eo},{refs:es,floatingStyles:eu,placement:ec,isPositioned:ed,middlewareData:ep}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:s=!0,whileElementsMounted:u,open:f}=e,[c,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);eA(p,i)||h(i);let[m,g]=r.useState(null),[v,y]=r.useState(null),w=r.useCallback(e=>{e!==R.current&&(R.current=e,g(e))},[]),x=r.useCallback(e=>{e!==A.current&&(A.current=e,y(e))},[]),b=l||m,E=a||v,R=r.useRef(null),A=r.useRef(null),L=r.useRef(c),C=null!=u,P=eP(u),O=eP(o),T=eP(f),D=r.useCallback(()=>{if(!R.current||!A.current)return;let e={placement:t,strategy:n,middleware:p};O.current&&(e.platform=O.current),((e,t,n)=>{let r=new Map,i={platform:ew,...n},o={...i.platform,_c:r};return S(e,t,{...i,platform:o})})(R.current,A.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};k.current&&!eA(L.current,t)&&(L.current=t,eE.flushSync(()=>{d(t)}))})},[p,t,n,O,T]);eR(()=>{!1===f&&L.current.isPositioned&&(L.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[f]);let k=r.useRef(!1);eR(()=>(k.current=!0,()=>{k.current=!1}),[]),eR(()=>{if(b&&(R.current=b),E&&(A.current=E),b&&E){if(P.current)return P.current(b,E,D);D()}},[b,E,D,P,C]);let W=r.useMemo(()=>({reference:R,floating:A,setReference:w,setFloating:x}),[w,x]),H=r.useMemo(()=>({reference:b,floating:E}),[b,E]),N=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!H.floating)return e;let t=eC(H.floating,c.x),r=eC(H.floating,c.y);return s?{...e,transform:"translate("+t+"px, "+r+"px)",...eL(H.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,s,H.floating,c.x,c.y]);return r.useMemo(()=>({...c,update:D,refs:W,elements:H,floatingStyles:N}),[c,D,W,H,N])}({strategy:"fixed",placement:P+("center"!==N?"-"+N:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:f="function"==typeof ResizeObserver,layoutShift:c="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=el(e),h=a||u?[...p?er(p):[],...er(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&c?function(e,t){let n,r=null,i=F(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(f,c){void 0===f&&(f=!1),void 0===c&&(c=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(f||t(),!m||!g)return;let v=s(h),y=s(i.clientWidth-(p+m)),w={rootMargin:-v+"px "+-y+"px "+-s(i.clientHeight-(h+g))+"px "+-s(p)+"px",threshold:l(0,o(1,c))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==c){if(!x)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||ex(d,e.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...w,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),a}(p,n):null,g=-1,v=null;f&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&v&&(v.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),p&&!d&&v.observe(p),v.observe(t));let y=d?ef(e):null;return d&&function t(){let r=ef(e);y&&!ex(y,r)&&n(),y=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=v)||e.disconnect(),v=null,d&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===X})},elements:{reference:q.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=t,s=await W(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+s.x,y:o+s.y,data:{...s,placement:l}}}}}(e),options:[e,t]}))({mainAxis:H+et,alignmentAxis:j}),B&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:s=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...f}=d(e,t),c={x:n,y:r},h=await O(t,f),g=y(p(i)),v=m(g),w=c[v],x=c[g];if(a){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=w+h[e],r=w-h[t];w=l(n,o(w,r))}if(s){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,o(x,r))}let b=u.fn({...t,[v]:w,[g]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[v]:a,[g]:s}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===G?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:s=!0,crossAxis:u=!0}=d(e,t),f={x:n,y:r},c=y(i),h=m(c),g=f[h],v=f[c],w=d(a,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(s){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+x.mainAxis,n=o.reference[h]+o.reference[e]-x.mainAxis;g<t?g=t:g>n&&(g=n)}if(u){var b,E;let e="y"===h?"width":"height",t=k.has(p(i)),n=o.reference[c]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[c])||0)+(t?0:x.crossAxis),r=o.reference[c]+o.reference[e]+(t?0:(null==(E=l.offset)?void 0:E[c])||0)-(t?x.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[h]:g,[c]:v}}}}(e),options:[e,t]}))():void 0,...ea}),B&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,l;let{placement:a,middlewareData:s,rects:u,initialPlacement:f,platform:c,elements:v}=t,{mainAxis:L=!0,crossAxis:C=!0,fallbackPlacements:P,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:T="none",flipAlignment:D=!0,...k}=d(e,t);if(null!=(n=s.arrow)&&n.alignmentOffset)return{};let W=p(a),H=y(f),N=p(f)===f,j=await (null==c.isRTL?void 0:c.isRTL(v.floating)),F=P||(N||!D?[A(f)]:function(e){let t=A(e);return[w(e),t,w(t)]}(f)),z="none"!==T;!P&&z&&F.push(...function(e,t,n,r){let i=h(e),o=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?b:x;return t?x:b;case"left":case"right":return t?E:R;default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(w)))),o}(f,D,T,j));let B=[f,...F],M=await O(t,k),V=[],G=(null==(r=s.flip)?void 0:r.overflows)||[];if(L&&V.push(M[W]),C){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),i=m(y(e)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=A(l)),[l,A(l)]}(a,u,j);V.push(M[e[0]],M[e[1]])}if(G=[...G,{placement:a,overflows:V}],!V.every(e=>e<=0)){let e=((null==(i=s.flip)?void 0:i.index)||0)+1,t=B[e];if(t&&("alignment"!==C||H===y(t)||G.every(e=>e.overflows[0]>0&&y(e.placement)===H)))return{data:{index:e,overflows:G},reset:{placement:t}};let n=null==(o=G.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(S){case"bestFit":{let e=null==(l=G.filter(e=>{if(z){let t=y(e.placement);return t===H||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=f}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...ea}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a,{placement:s,rects:u,platform:f,elements:c}=t,{apply:m=()=>{},...g}=d(e,t),v=await O(t,g),w=p(s),x=h(s),b="y"===y(s),{width:E,height:R}=u.floating;"top"===w||"bottom"===w?(i=w,a=x===(await (null==f.isRTL?void 0:f.isRTL(c.floating))?"start":"end")?"left":"right"):(a=w,i="end"===x?"top":"bottom");let A=R-v.top-v.bottom,L=E-v.left-v.right,C=o(R-v[i],A),P=o(E-v[a],L),S=!t.middlewareData.shift,T=C,D=P;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(D=L),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(T=A),S&&!x){let e=l(v.left,0),t=l(v.right,0),n=l(v.top,0),r=l(v.bottom,0);b?D=E-2*(0!==e||0!==t?e+t:l(v.left,v.right)):T=R-2*(0!==n||0!==r?n+r:l(v.top,v.bottom))}await m({...t,availableWidth:D,availableHeight:T});let k=await f.getDimensions(c.floating);return E!==k.width||R!==k.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...ea,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),Z&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eb({element:n.current,padding:r}).fn(t):{}:n?eb({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:Z,padding:z}),eJ({arrowWidth:ee,arrowHeight:et}),_&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=d(e,t);switch(r){case"referenceHidden":{let e=T(await O(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:D(e)}}}case"escaped":{let e=T(await O(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:D(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...ea})]}),[eh,em]=eK(ec),eg=(0,eW.c)(I);(0,eH.N)(()=>{ed&&(null==eg||eg())},[ed,eg]);let ev=null==(n=ep.arrow)?void 0:n.x,ey=null==(i=ep.arrow)?void 0:i.y,eT=(null==(a=ep.arrow)?void 0:a.centerOffset)!==0,[ek,ej]=r.useState();return(0,eH.N)(()=>{$&&ej(window.getComputedStyle($).zIndex)},[$]),(0,eO.jsx)("div",{ref:es.setFloating,"data-radix-popper-content-wrapper":"",style:{...eu,transform:ed?eu.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ek,"--radix-popper-transform-origin":[null==(u=ep.transformOrigin)?void 0:u.x,null==(f=ep.transformOrigin)?void 0:f.y].join(" "),...(null==(c=ep.hide)?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eO.jsx)(eI,{scope:C,placedSide:eh,onArrowChange:J,arrowX:ev,arrowY:ey,shouldHideArrow:eT,children:(0,eO.jsx)(eS.sG.div,{"data-side":eh,"data-align":em,...Y,ref:U,style:{...Y.style,animation:ed?void 0:"none"}})})})});eq.displayName=eX;var e$="PopperArrow",eQ={top:"bottom",right:"left",bottom:"top",left:"right"},eU=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eY(e$,n),o=eQ[i.placedSide];return(0,eO.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eO.jsx)(eT,{...r,ref:t,style:{...r.style,display:"block"}})})});function eZ(e){return null!==e}eU.displayName=e$;var eJ=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:a,rects:s,middlewareData:u}=t,f=(null==(n=u.arrow)?void 0:n.centerOffset)!==0,c=f?0:e.arrowWidth,d=f?0:e.arrowHeight,[p,h]=eK(a),m={start:"0%",center:"50%",end:"100%"}[h],g=(null!=(o=null==(r=u.arrow)?void 0:r.x)?o:0)+c/2,v=(null!=(l=null==(i=u.arrow)?void 0:i.y)?l:0)+d/2,y="",w="";return"bottom"===p?(y=f?m:"".concat(g,"px"),w="".concat(-d,"px")):"top"===p?(y=f?m:"".concat(g,"px"),w="".concat(s.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),w=f?m:"".concat(v,"px")):"left"===p&&(y="".concat(s.floating.width+d,"px"),w=f?m:"".concat(v,"px")),{data:{x:y,y:w}}}});function eK(e){let[t,n="center"]=e.split("-");return[t,n]}var e0=eV,e1=e_,e5=eq,e2=eU}}]);