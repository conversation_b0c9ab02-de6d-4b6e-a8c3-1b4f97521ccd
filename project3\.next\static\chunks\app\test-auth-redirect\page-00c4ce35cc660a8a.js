(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2283],{11657:(e,r,t)=>{Promise.resolve().then(t.bind(t,34062))},34062:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var s=t(95155),a=t(49223),l=t(98816),n=t(88482),i=t(97168),d=t(88145),o=t(49026),c=t(35169),u=t(75525),m=t(71007),h=t(34835),x=t(6874),g=t.n(x);function f(){let{isAuthorized:e,isLoading:r}=(0,a.zK)({requireAuth:!0,redirectTo:"/login"}),{user:t,logout:x}=(0,l.J)();return r||null===e?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Checking authentication..."})]})}):e?(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 max-w-4xl",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)(g(),{href:"/",className:"inline-flex items-center text-blue-600 hover:text-blue-800 mb-4",children:[(0,s.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"\uD83D\uDD10 Authentication Test Page"}),(0,s.jsx)("p",{className:"text-gray-600",children:"This page demonstrates the redirect-after-login functionality."})]}),(0,s.jsxs)(o.Fc,{className:"mb-6 border-green-200 bg-green-50",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 text-green-600"}),(0,s.jsxs)(o.TN,{className:"text-green-800",children:[(0,s.jsx)("strong",{children:"Success!"})," You have been successfully authenticated and redirected to this protected page."]})]}),(0,s.jsxs)(n.Zp,{className:"mb-6",children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"w-5 h-5"}),"User Information"]})}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"User ID"}),(0,s.jsx)("p",{className:"text-lg font-semibold",children:(null==t?void 0:t.UserId)||(null==t?void 0:t.UserID)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,s.jsx)("p",{className:"text-lg font-semibold",children:null==t?void 0:t.Email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Name"}),(0,s.jsxs)("p",{className:"text-lg font-semibold",children:[null==t?void 0:t.FirstName," ",null==t?void 0:t.LastName]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Phone"}),(0,s.jsx)("p",{className:"text-lg font-semibold",children:(null==t?void 0:t.PhoneNumber)||"Not provided"})]})]})})]}),(0,s.jsxs)(n.Zp,{className:"mb-6",children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)(n.ZB,{children:"\uD83E\uDDEA How to Test Redirect Functionality"})}),(0,s.jsxs)(n.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h3",{className:"font-semibold text-lg",children:"Test Steps:"}),(0,s.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-gray-700",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Logout"})," using the button below"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Try to access this page directly"})," by going to"," ",(0,s.jsx)("code",{className:"bg-gray-100 px-2 py-1 rounded text-sm",children:"/test-auth-redirect"})]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"You should be redirected"})," to the login page with a redirect parameter"]}),(0,s.jsx)("li",{children:(0,s.jsx)("strong",{children:"Login with your credentials"})}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"You should be redirected back"})," to this page automatically"]})]})]}),(0,s.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-semibold text-blue-900 mb-2",children:"Expected URL Pattern:"}),(0,s.jsx)("code",{className:"text-sm text-blue-800",children:"/login?redirect=%2Ftest-auth-redirect"})]})]})]}),(0,s.jsxs)(n.Zp,{className:"mb-6",children:[(0,s.jsx)(n.aR,{children:(0,s.jsx)(n.ZB,{children:"\uD83D\uDEE1️ Protected Routes"})}),(0,s.jsxs)(n.Wu,{children:[(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"The following routes require authentication and will redirect to login:"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:["/account","/orders","/profile","/checkout","/wishlist","/test-auth-redirect"].map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,s.jsx)("code",{className:"text-sm",children:e}),(0,s.jsx)(d.E,{variant:"secondary",children:"Protected"})]},e))})]})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsxs)(i.$,{onClick:x,variant:"destructive",className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"w-4 h-4"}),"Logout & Test Redirect"]}),(0,s.jsx)(g(),{href:"/account",children:(0,s.jsx)(i.$,{variant:"outline",children:"Go to Account Page"})}),(0,s.jsx)(g(),{href:"/orders",children:(0,s.jsx)(i.$,{variant:"outline",children:"Go to Orders Page"})})]})]})}):null}},34835:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},35169:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,r,t)=>{"use strict";var s=t(18999);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},49026:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>d,TN:()=>o});var s=t(95155),a=t(12115),l=t(74466),n=t(53999);let i=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-red-500 dark:border-destructive [&>svg]:text-red-500"}},defaultVariants:{variant:"default"}}),d=a.forwardRef((e,r)=>{let{className:t,variant:a,...l}=e;return(0,s.jsx)("div",{ref:r,role:"alert",className:(0,n.cn)(i({variant:a}),t),...l})});d.displayName="Alert",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h5",{ref:r,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",t),...a})}).displayName="AlertTitle";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",t),...a})});o.displayName="AlertDescription"},49223:(e,r,t)=>{"use strict";t.d(r,{zK:()=>l}),t(95155);var s=t(35695),a=t(98816);function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{redirectTo:r="/login",requireAuth:t=!0,redirectIfAuthenticated:l=!1}=e,{isLoggedIn:n,isLoading:i}=(0,a.J)();(0,s.useRouter)(),(0,s.usePathname)(),console.log("\uD83D\uDD27 useAuthGuard: Auth state check",{isLoggedIn:n,isLoading:i,requireAuth:t,redirectIfAuthenticated:l});let d=null;return i?(console.log("\uD83D\uDD27 useAuthGuard: Still loading, isAuthorized = null"),d=null):t&&!n?(console.log("\uD83D\uDD12 useAuthGuard: Authentication required but user not logged in, isAuthorized = false"),d=!1):l&&n?(console.log("\uD83D\uDD13 useAuthGuard: User authenticated but should be redirected, isAuthorized = false"),d=!1):(console.log("✅ useAuthGuard: User is authorized, isAuthorized = true"),d=!0),console.log("\uD83D\uDD27 useAuthGuard: Final result",{isAuthorized:d,isLoading:i,isLoggedIn:n}),{isAuthorized:d,isLoading:i,isLoggedIn:n}}},53999:(e,r,t)=>{"use strict";t.d(r,{cn:()=>l});var s=t(52596),a=t(39688);function l(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}},71007:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>n});var s=t(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=s.$,n=(e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return l(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:n,defaultVariants:i}=r,d=Object.keys(n).map(e=>{let r=null==t?void 0:t[e],s=null==i?void 0:i[e];if(null===r)return null;let l=a(r)||a(s);return n[e][l]}),o=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return l(e,d,null==r||null==(s=r.compoundVariants)?void 0:s.reduce((e,r)=>{let{class:t,className:s,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...o}[r]):({...i,...o})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},75525:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},88145:(e,r,t)=>{"use strict";t.d(r,{E:()=>i});var s=t(95155),a=t(74466),l=t(53999);let n=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:r,variant:t,...a}=e;return(0,s.jsx)("div",{className:(0,l.cn)(n({variant:t}),r),...a})}},88482:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i,wL:()=>u});var s=t(95155),a=t(12115),l=t(53999);let n=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});n.displayName="Card";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...a})});i.displayName="CardHeader";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});d.displayName="CardTitle";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...a})});o.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",t),...a})});c.displayName="CardContent";let u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",t),...a})});u.displayName="CardFooter"},97168:(e,r,t)=>{"use strict";t.d(r,{$:()=>o,r:()=>d});var s=t(95155),a=t(12115),l=t(99708),n=t(74466),i=t(53999);let d=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,r)=>{let{className:t,variant:a,size:n,asChild:o=!1,...c}=e,u=o?l.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(d({variant:a,size:n,className:t})),ref:r,...c})});o.displayName="Button"}},e=>{e.O(0,[4277,3464,4706,8816,8441,5964,7358],()=>e(e.s=11657)),_N_E=e.O()}]);