(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1e3,5409,7790],{14781:(e,t,s)=>{"use strict";s.d(t,{default:()=>Z});var r=s(95155),a=s(12115),l=s(6874),i=s.n(l),n=s(23464),o=s(35169),c=s(14186),d=s(38564),m=s(27809),u=s(51976),x=s(66516),h=s(69037),p=s(97168),g=s(84995),f=s(34964),j=s(88145),v=s(56671),b=s(73339),y=s(78067),N=s(59268),w=s(9776),A=s(79891),P=s(88482),S=s(5196);let I=["General","Technical Specifications","Dimensions","Materials","Colors","Features","Warranty","Package Includes"];function C(e){let{attributes:t=[],className:s=""}=e;if(!t||0===t.length)return null;let l=Object.entries(t.reduce((e,t)=>{let s=t.DisplayName||t.AttributeName,r="Specifications",a=I.find(e=>s.toLowerCase().includes(e.toLowerCase()));return a?r=a:s.toLowerCase().includes("dimension")||s.toLowerCase().includes("size")||s.toLowerCase().includes("weight")?r="Dimensions":s.toLowerCase().includes("color")||s.toLowerCase().includes("colour")?r="Colors":s.toLowerCase().includes("material")||s.toLowerCase().includes("fabric")?r="Materials":(s.toLowerCase().includes("feature")||s.toLowerCase().includes("spec"))&&(r="Features"),e[r]||(e[r]=[]),e[r].push(t),e},{})).sort((e,t)=>{let[s]=e,[r]=t,a=I.indexOf(s),l=I.indexOf(r);return -1===a&&-1===l?s.localeCompare(r):-1===a?1:-1===l?-1:a-l});return(0,r.jsxs)("div",{className:s,children:[(0,r.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Product Specifications"}),(0,r.jsx)("div",{className:"space-y-8",children:l.map(e=>{let[t,s]=e;return(0,r.jsxs)(P.Zp,{className:"overflow-hidden",children:[(0,r.jsx)(P.aR,{className:"bg-gray-50 p-4 border-b",children:(0,r.jsx)(P.ZB,{className:"text-lg font-semibold",children:t})}),(0,r.jsx)(P.Wu,{className:"p-0",children:(0,r.jsx)("div",{className:"divide-y",children:s.map((e,s)=>{let l=e.DisplayName||e.AttributeName,i=e.AttributeValueText;return(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 p-4 hover:bg-gray-50 transition-colors",children:[(0,r.jsxs)("div",{className:"font-medium text-gray-900 flex items-center",children:[(0,r.jsx)(S.A,{className:"h-4 w-4 text-green-500 mr-2"}),l,":"]}),(0,r.jsx)("div",{className:"md:col-span-2 text-gray-700 mt-1 md:mt-0",children:"Colors"===t?(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-6 h-6 rounded-full border border-gray-300",style:{backgroundColor:i.toLowerCase(),backgroundImage:["white","#fff","#ffffff","rgb(255,255,255)"].includes(i.toLowerCase())?"linear-gradient(45deg, #e5e7eb 25%, transparent 25%), linear-gradient(-45deg, #e5e7eb 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #e5e7eb 75%), linear-gradient(-45deg, transparent 75%, #e5e7eb 75%)":"none",backgroundSize:"8px 8px"}}),(0,r.jsx)("span",{className:"capitalize",children:i})]}):"Dimensions"===t&&(l.toLowerCase().includes("dimension")||l.toLowerCase().includes("size"))?(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[i.split("x").map((e,t)=>(0,r.jsxs)(a.Fragment,{children:[t>0&&(0,r.jsx)("span",{className:"mx-1 text-gray-400",children:"\xd7"}),(0,r.jsx)("span",{children:e.trim()})]},t)),!isNaN(parseFloat(i))&&(0,r.jsx)("span",{className:"ml-1 text-sm text-gray-500",children:"cm"})]}):(0,r.jsx)("span",{children:(e=>{if(!e)return"N/A";try{let t=new URL(e);return(0,r.jsxs)("a",{href:t.toString(),target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:["View ",t.hostname]})}catch(t){return e}})(i)})})]},"".concat(e.ProductAttributeID,"-").concat(s))})})})]},t)})})]})}var k=s(11518),D=s.n(k),E=s(9803),T=s(6262),R=s(54481),O=s(47924),_=s(82178),U=s(85690),M=s(27213),L=s(42355),J=s(13052),F=s(53999);function W(e){let{src:t,alt:s,className:l}=e,[i,n]=(0,a.useState)(null),[o,c]=(0,a.useState)(!1),d=(0,a.useRef)(null),m=(0,a.useRef)(null);return((0,a.useEffect)(()=>{let e=()=>{let e=d.current,t=m.current;if(!e||!t)return;let s=t.getContext("2d");if(s){t.width=e.videoWidth||320,t.height=e.videoHeight||240;try{s.drawImage(e,0,0,t.width,t.height);let r=t.toDataURL("image/jpeg",.8);n(r)}catch(e){console.error("Error generating video thumbnail:",e),c(!0)}}},t=()=>{let e=d.current;if(e){let t=Math.min(1,.1*e.duration);e.currentTime=t}},s=()=>{setTimeout(e,100)},r=d.current;if(r)return r.addEventListener("loadeddata",t),r.addEventListener("seeked",s),r.addEventListener("error",()=>c(!0)),()=>{r.removeEventListener("loadeddata",t),r.removeEventListener("seeked",s),r.removeEventListener("error",()=>c(!0))}},[t]),o)?(0,r.jsx)("div",{className:(0,F.cn)("bg-gray-200 flex items-center justify-center",l),children:(0,r.jsx)(E.A,{size:16,className:"text-gray-500"})}):(0,r.jsxs)("div",{className:(0,F.cn)("relative",l),children:[(0,r.jsx)("video",{ref:d,src:t,className:"hidden",muted:!0,playsInline:!0,preload:"metadata"}),(0,r.jsx)("canvas",{ref:m,className:"hidden"}),i?(0,r.jsx)("img",{src:i,alt:s||"Video thumbnail",className:"w-full h-full object-cover"}):(0,r.jsx)("div",{className:"w-full h-full bg-gray-200 flex items-center justify-center animate-pulse",children:(0,r.jsx)(E.A,{size:16,className:"text-gray-400"})})]})}function B(e){let{media:t,className:s}=e,[l,i]=(0,a.useState)(0),[n,o]=(0,a.useState)(!1),[c,d]=(0,a.useState)(!1),[m,u]=(0,a.useState)(!1),[x,h]=(0,a.useState)({x:0,y:0}),[p,g]=(0,a.useState)(1.5),[f,j]=(0,a.useState)(!1),v=(0,a.useRef)(null),b=(0,a.useRef)(null),[y,N]=(0,a.useState)("all"),w=t.filter(e=>"all"===y||e.type===y),A=w[l]||t[0],P=w.length>1;(0,a.useEffect)(()=>{i(0),o(!1)},[y,t]);let S=()=>{i(e=>0===e?w.length-1:e-1),o(!1)},I=()=>{i(e=>e===w.length-1?0:e+1),o(!1)},C=t.reduce((e,t)=>(e[t.type]=(e[t.type]||0)+1,e),{});return(0,r.jsxs)("div",{className:(0,F.cn)("flex flex-col gap-4",s),children:[(0,r.jsxs)("div",{className:"relative aspect-square w-full bg-gray-100 rounded-lg overflow-hidden",children:["image"===A.type?(0,r.jsxs)("div",{className:"jsx-9ac6182445f1a16e relative w-full h-full overflow-hidden group",children:[(0,r.jsx)(D(),{id:"9ac6182445f1a16e",children:'.cursor-zoom-in.jsx-9ac6182445f1a16e{cursor:url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/><line x1="11" y1="8" x2="11" y2="14"/><line x1="8" y1="11" x2="14" y2="11"/></svg>\')12 12,auto}.cursor-zoom-out.jsx-9ac6182445f1a16e{cursor:url(\'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"/><path d="m21 21-4.35-4.35"/><line x1="8" y1="11" x2="14" y2="11"/></svg>\')12 12,auto}'}),(0,r.jsx)("img",{ref:b,src:A.url,alt:A.alt||"Product image",style:m?{transform:"scale(".concat(p,")"),transformOrigin:"".concat(x.x,"% ").concat(x.y,"%")}:{transform:"scale(1)"},onClick:()=>{"image"===A.type&&u(!m)},onDoubleClick:()=>{"image"===A.type&&j(!0)},onMouseMove:e=>{if(!m||!b.current)return;let t=b.current.getBoundingClientRect();h({x:(e.clientX-t.left)/t.width*100,y:(e.clientY-t.top)/t.height*100})},onMouseLeave:()=>u(!1),className:"jsx-9ac6182445f1a16e "+((0,F.cn)("w-full h-full object-contain transition-transform duration-300",m?"cursor-zoom-out":"cursor-zoom-in")||"")}),(0,r.jsx)("div",{className:"jsx-9ac6182445f1a16e absolute inset-0 flex items-center justify-center pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,r.jsx)("div",{className:"jsx-9ac6182445f1a16e bg-white/95 rounded-full p-4 shadow-xl border-2 border-gray-200",children:m?(0,r.jsx)(T.A,{className:"h-8 w-8 text-gray-700"}):(0,r.jsx)(R.A,{className:"h-8 w-8 text-gray-700"})})}),(0,r.jsxs)("div",{className:"jsx-9ac6182445f1a16e absolute top-2 left-2 flex flex-col gap-2",children:[(0,r.jsx)("div",{className:"jsx-9ac6182445f1a16e bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1",children:m?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(T.A,{className:"h-3 w-3"}),(0,r.jsxs)("span",{className:"jsx-9ac6182445f1a16e",children:["Zoom: ",Math.round(100*p),"%"]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(R.A,{className:"h-3 w-3"}),(0,r.jsx)("span",{className:"jsx-9ac6182445f1a16e",children:"Click to zoom"})]})}),(0,r.jsxs)("div",{className:"jsx-9ac6182445f1a16e flex flex-col gap-1",children:[(0,r.jsx)("button",{onClick:()=>{g(e=>Math.min(e+.5,4)),u(!0)},title:"Zoom in",className:"jsx-9ac6182445f1a16e bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all",children:(0,r.jsx)(R.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>{g(e=>{let t=Math.max(e-.5,1);return 1===t&&u(!1),t})},title:"Zoom out",className:"jsx-9ac6182445f1a16e bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all",children:(0,r.jsx)(T.A,{className:"h-4 w-4"})}),(0,r.jsx)("button",{onClick:()=>{g(1.5),u(!1)},title:"Reset zoom",className:"jsx-9ac6182445f1a16e bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all text-xs",children:"1:1"})]})]}),(0,r.jsx)("button",{onClick:()=>j(!0),title:"View fullscreen",className:"jsx-9ac6182445f1a16e absolute top-2 right-12 bg-white/90 hover:bg-white text-gray-700 rounded p-2 shadow-sm transition-all",children:(0,r.jsx)(O.A,{className:"h-4 w-4"})})]}):(0,r.jsxs)("div",{className:"relative w-full h-full",children:[(0,r.jsx)("video",{ref:v,src:A.url,className:"w-full h-full object-contain",controls:!1,onEnded:()=>{o(!1),P&&I()},onPlay:()=>o(!0),onPause:()=>o(!1),onLoadedData:()=>d(!0),playsInline:!0}),!c&&(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-200",children:(0,r.jsx)("div",{className:"animate-pulse",children:"Loading video..."})}),(0,r.jsx)("button",{onClick:()=>{"video"===A.type&&v.current&&(n?v.current.pause():v.current.play(),o(!n))},className:(0,F.cn)("absolute inset-0 flex items-center justify-center transition-opacity",n?"opacity-0 hover:opacity-100":"opacity-80",!c&&"hidden"),"aria-label":n?"Pause":"Play",children:(0,r.jsx)("div",{className:"bg-black/50 text-white rounded-full p-3",children:n?(0,r.jsx)(_.A,{size:24}):(0,r.jsx)(U.A,{size:24})})})]}),(0,r.jsxs)("div",{className:"absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1",children:["image"===A.type?(0,r.jsx)(M.A,{size:12}):(0,r.jsx)(E.A,{size:12}),(0,r.jsx)("span",{children:"image"===A.type?"Image":"Video"})]}),P&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:S,className:"absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all","aria-label":"Previous media",children:(0,r.jsx)(L.A,{size:20})}),(0,r.jsx)("button",{onClick:I,className:"absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all","aria-label":"Next media",children:(0,r.jsx)(J.A,{size:20})})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("button",{onClick:()=>N("all"),className:(0,F.cn)("px-3 py-1 text-sm rounded-full border","all"===y?"bg-blue-600 text-white border-blue-600":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"),children:["All (",t.length,")"]}),C.image>0&&(0,r.jsxs)("button",{onClick:()=>N("image"),className:(0,F.cn)("px-3 py-1 text-sm rounded-full border flex items-center gap-1","image"===y?"bg-blue-600 text-white border-blue-600":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"),children:[(0,r.jsx)(M.A,{size:14}),(0,r.jsx)("span",{children:C.image})]}),C.video>0&&(0,r.jsxs)("button",{onClick:()=>N("video"),className:(0,F.cn)("px-3 py-1 text-sm rounded-full border flex items-center gap-1","video"===y?"bg-blue-600 text-white border-blue-600":"bg-white text-gray-700 border-gray-300 hover:bg-gray-50"),children:[(0,r.jsx)(E.A,{size:14}),(0,r.jsx)("span",{children:C.video})]})]}),P&&(0,r.jsx)("div",{className:"flex gap-2 sm:gap-3 overflow-x-auto pb-2 -mx-2 px-2",children:w.map((e,t)=>(0,r.jsx)("button",{onClick:()=>(e=>{let t=w[e];i(e),u(!1),(null==t?void 0:t.type)==="video"?(o(!0),setTimeout(()=>{v.current&&v.current.play().catch(console.error)},100)):o(!1)})(t),className:(0,F.cn)("relative flex-shrink-0 w-20 h-20 sm:w-16 sm:h-16 md:w-20 md:h-20 rounded-md overflow-hidden border-2 transition-all",t===l?"border-blue-600 ring-2 ring-blue-400":"border-gray-200 hover:border-gray-400"),"aria-label":"View ".concat(e.type," ").concat(t+1),children:"image"===e.type?(0,r.jsx)("img",{src:e.thumbnail||e.url,alt:e.alt||"",className:"w-full h-full object-cover"}):(0,r.jsxs)("div",{className:"relative w-full h-full overflow-hidden",children:[e.thumbnail?(0,r.jsx)("img",{src:e.thumbnail,alt:e.alt||"Video thumbnail",className:"w-full h-full object-cover"}):(0,r.jsx)(W,{src:e.url,alt:e.alt,className:"w-full h-full"}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-black/30",children:(0,r.jsx)("div",{className:"bg-white/90 rounded-full p-1 sm:p-1",children:(0,r.jsx)(U.A,{size:14,className:"text-gray-700 ml-0.5 sm:w-3 sm:h-3"})})})]})},t))}),f&&"image"===A.type&&(0,r.jsx)("div",{className:"fixed inset-0 z-50 bg-black/95 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"relative w-full h-full flex items-center justify-center p-4",children:[(0,r.jsx)("img",{src:A.url,alt:A.alt||"Product image",className:"max-w-full max-h-full object-contain",style:{maxWidth:"95vw",maxHeight:"95vh"}}),(0,r.jsx)("button",{onClick:()=>j(!1),className:"absolute top-4 right-4 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all",title:"Close fullscreen",children:(0,r.jsx)("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),P&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{onClick:S,className:"absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all",title:"Previous image",children:(0,r.jsx)(L.A,{className:"h-6 w-6"})}),(0,r.jsx)("button",{onClick:I,className:"absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all",title:"Next image",children:(0,r.jsx)(J.A,{className:"h-6 w-6"})})]}),(0,r.jsxs)("div",{className:"absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/70 text-white px-3 py-1 rounded-full text-sm",children:[l+1," of ",w.length]})]})})]})}function Q(e){let{endDate:t,className:s}=e,[l,i]=(0,a.useState)({days:0,hours:0,minutes:0,seconds:0,completed:!1});return((0,a.useEffect)(()=>{let e=new Date(t).getTime(),s=setInterval(()=>{let t=e-new Date().getTime();if(t<0){clearInterval(s),i(e=>({...e,completed:!0}));return}i({days:Math.floor(t/864e5),hours:Math.floor(t%864e5/36e5),minutes:Math.floor(t%36e5/6e4),seconds:Math.floor(t%6e4/1e3),completed:!1})},1e3);return()=>clearInterval(s)},[t]),l.completed)?(0,r.jsx)("span",{className:s,children:"Offer expired"}):(0,r.jsxs)("div",{className:"inline-flex items-center gap-0.5 sm:gap-2 bg-white rounded-lg p-1.5 sm:p-3 shadow-sm border ".concat(s),children:[(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"w-6 h-6 sm:w-12 sm:h-12 bg-white border border-red-400 sm:border-2 rounded sm:rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-[10px] sm:text-xl font-bold text-red-500",children:l.days.toString().padStart(2,"0")})}),(0,r.jsx)("span",{className:"text-[8px] sm:text-xs font-medium text-red-500 mt-0.5 sm:mt-1",children:"Days"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"w-6 h-6 sm:w-12 sm:h-12 bg-white border border-red-400 sm:border-2 rounded sm:rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-[10px] sm:text-xl font-bold text-red-500",children:l.hours.toString().padStart(2,"0")})}),(0,r.jsx)("span",{className:"text-[8px] sm:text-xs font-medium text-red-500 mt-0.5 sm:mt-1",children:"Hrs"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"w-6 h-6 sm:w-12 sm:h-12 bg-white border border-red-400 sm:border-2 rounded sm:rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-[10px] sm:text-xl font-bold text-red-500",children:l.minutes.toString().padStart(2,"0")})}),(0,r.jsx)("span",{className:"text-[8px] sm:text-xs font-medium text-red-500 mt-0.5 sm:mt-1",children:"Min"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:"w-6 h-6 sm:w-12 sm:h-12 bg-white border border-red-400 sm:border-2 rounded sm:rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-[10px] sm:text-xl font-bold text-red-500",children:l.seconds.toString().padStart(2,"0")})}),(0,r.jsx)("span",{className:"text-[8px] sm:text-xs font-medium text-red-500 mt-0.5 sm:mt-1",children:"Sec"})]})]})}var z=s(27737);function V(){return(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-8",children:[(0,r.jsxs)("div",{className:"md:w-1/2",children:[(0,r.jsx)(z.E,{className:"h-[400px] w-full rounded-lg"}),(0,r.jsx)("div",{className:"flex gap-2 mt-4",children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsx)(z.E,{className:"h-20 w-20 rounded-lg"},t))})]}),(0,r.jsxs)("div",{className:"md:w-1/2 space-y-4",children:[(0,r.jsx)(z.E,{className:"h-8 w-3/4"}),(0,r.jsx)(z.E,{className:"h-6 w-1/2"}),(0,r.jsx)(z.E,{className:"h-4 w-full"}),(0,r.jsx)(z.E,{className:"h-4 w-full"}),(0,r.jsx)(z.E,{className:"h-4 w-3/4"}),(0,r.jsx)(z.E,{className:"h-10 w-1/3"}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)(z.E,{className:"h-12 flex-1"}),(0,r.jsx)(z.E,{className:"h-12 w-12"}),(0,r.jsx)(z.E,{className:"h-12 w-12"})]})]})]})})}var q=s(85339);function G(e){let{error:t,retry:s}=e;return(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center text-center p-8 border rounded-lg bg-red-50",children:[(0,r.jsx)(q.A,{className:"h-12 w-12 text-red-500 mb-4"}),(0,r.jsx)("h1",{className:"text-2xl font-bold mb-2",children:"Error Loading Product"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:t}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(p.$,{onClick:s,children:"Try Again"}),(0,r.jsx)(i(),{href:"/products",children:(0,r.jsxs)(p.$,{variant:"outline",children:[(0,r.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Back to Products"]})})]})]})})}var H=s(49504);let $=e=>{if(!e)return"/placeholder.svg?height=400&width=400";if(e.startsWith("http"))return e;let t=e.startsWith("/")?e:"/".concat(e);return t=t.replace(/\/+/g,"/"),"".concat("https://admin.codemedicalapps.com").concat(t)},Z=function(e){var t;let{productId:l}=e,P=(0,y._)(),S=(0,N.n)(),{rate:I}=(0,w.H)(),{primaryColor:k,primaryTextColor:D}=(0,A.t)(),[E,T]=(0,a.useState)(null),[R,O]=(0,a.useState)(!0),[_,U]=(0,a.useState)(1),[M,L]=(0,a.useState)(""),[J,F]=(0,a.useState)([]),[W,z]=(0,a.useState)(0),[q,Z]=(0,a.useState)(!1),[X,Y]=(0,a.useState)(!1),[K,ee]=(0,a.useState)(null),[et,es]=(0,a.useState)("description"),[er,ea]=(0,a.useState)(!1),[el,ei]=(0,a.useState)(null),[en,eo]=(0,a.useState)(()=>{let e={};return(null==E?void 0:E.AttributesJson)&&E.AttributesJson.forEach(t=>{e["".concat(t.ProductAttributeID,"_").concat(t.AttributeValueID)]=!0}),e});(0,a.useEffect)(()=>{ec()},[l]);let ec=async()=>{O(!0),ee(null);try{let e,t={requestParameters:{ProductId:Number.parseInt(l,10),recordValueJson:"[]"}};console.log("Fetching product with ID:",l,"Request body:",t);try{let{MakeApiCallAsync:r}=await Promise.resolve().then(s.bind(s,65409));e=await r("get-product_detail",null,t,{Accept:"application/json","Content-Type":"application/json"},"POST",!0),console.log("API helper response:",e.data)}catch(s){console.log("API helper failed, trying proxy route:",s),e=await n.A.post("/api/product-detail",t,{headers:{Accept:"application/json","Content-Type":"application/json"}}),console.log("Proxy API response:",e.data)}if(e.data){let t=(e.data.data,e.data);if(t&&t.data)try{let e=JSON.parse(t.data);if(console.log("Parsed product data:",e),e){let t=Array.isArray(e)?e[0]:e;if(t){if(t.AttributesJson&&"string"==typeof t.AttributesJson)try{t.AttributesJson=JSON.parse(t.AttributesJson)}catch(e){console.error("Error parsing AttributesJson:",e),t.AttributesJson=[]}else t.AttributesJson||(t.AttributesJson=[]);if(console.log("Product data with attributes:",t),console.log("CategoryName in product data:",t.CategoryName),console.log("CategoryID in product data:",t.CategoryID),T(t),t.ProductImagesJson&&t.ProductImagesJson.length>0){let e=t.ProductImagesJson.find(e=>e.IsPrimary)||t.ProductImagesJson[0];L($(e.AttachmentURL))}if(t.VideoLink){console.log("Video links found:",t.VideoLink);let e=t.VideoLink.split(",").map(e=>e.trim()).map(e=>ed(e));F(e),z(0)}t.OrderMinimumQuantity>0&&U(t.OrderMinimumQuantity)}else console.error("No product data found in parsed response"),ee("Product with ID ".concat(l," not found. Please check if this product exists."))}else console.error("Invalid product data format - parsedData is null/undefined"),ee("Invalid product data format")}catch(e){console.error("Error parsing product data:",e,"Raw data:",t.data),ee("Error parsing product data")}else console.error("No data property in API response:",e.data),ee("No data in API response")}else console.error("Empty response from API"),ee("Empty response from server")}catch(t){if(console.error("Error fetching product:",t),t.response){var e;console.error("Server error:",t.response.status,t.response.data),ee("Server error: ".concat(t.response.status," - ").concat((null==(e=t.response.data)?void 0:e.message)||"Unknown error"))}else t.request?(console.error("Network error:",t.request),ee("Network error - please check your connection")):(console.error("Request setup error:",t.message),ee("Error: ".concat(t.message)))}finally{O(!1)}},ed=e=>{if(!e)return"";if(e.includes("youtube.com")||e.includes("youtu.be"))return e;if(e.startsWith("http"))return"/api/video-proxy?url=".concat(encodeURIComponent(e));let t=e.startsWith("/")?e:"/".concat(e);return"/api/video-proxy?url=".concat(encodeURIComponent("".concat("https://admin.codemedicalapps.com").concat(t)))},em=(0,a.useMemo)(()=>(null==E?void 0:E.AttributesJson)?E.AttributesJson.reduce((e,t)=>{let s=t.ProductAttributeID;return e[s]||(e[s]=[]),e[s].push(t),e},{}):{},[null==E?void 0:E.AttributesJson]),eu=(0,a.useCallback)(()=>{if(!E)return 0;let e=E.Price;return E.AttributesJson&&E.AttributesJson.length>0&&E.AttributesJson.forEach(t=>{if(en["".concat(t.ProductAttributeID,"_").concat(t.AttributeValueID)]&&"number"==typeof t.PriceAdjustment&&"number"==typeof t.PriceAdjustmentType)switch(t.PriceAdjustmentType){case 1:e+=t.PriceAdjustment;break;case 2:e+=E.Price*t.PriceAdjustment/100}}),Math.max(0,e)},[E,en]);(0,a.useMemo)(()=>eu(),[eu]);let ex=(0,a.useMemo)(()=>{var e;let t=[];return(null==E||null==(e=E.ProductImagesJson)?void 0:e.length)&&E.ProductImagesJson.forEach(e=>{t.push({type:"image",url:$(e.AttachmentURL),alt:(null==E?void 0:E.ProductName)||"Product image",thumbnail:$(e.AttachmentURL)})}),J.forEach((e,s)=>{t.push({type:"video",url:e,alt:"".concat((null==E?void 0:E.ProductName)||"Product"," - Video ").concat(s+1)})}),t},[E,J,M]),eh=e=>{ei(e),ea(!0),setTimeout(()=>ea(!1),300)},ep=e=>{let t="flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 rounded-full transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-offset-1",s="bg-gray-100 text-gray-400 cursor-not-allowed";if("increment"===e){let e=E&&_>=(E.OrderMaximumQuantity>0?Math.min(E.OrderMaximumQuantity,E.StockQuantity):E.StockQuantity);return"".concat(t," ").concat(e?s:"bg-primary text-white hover:bg-primary/90 focus:ring-primary/50")}{let e=E&&_<=(E.OrderMinimumQuantity>0?E.OrderMinimumQuantity:1);return"".concat(t," ").concat(e?s:"bg-primary text-white hover:bg-primary/90 focus:ring-primary/50")}};return R?(0,r.jsx)(V,{}):K?(0,r.jsx)(G,{error:K,retry:ec}):E?(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden",children:[(0,r.jsx)(g.Qp,{className:"mb-6",children:(0,r.jsxs)(g.AB,{children:[(0,r.jsx)(g.J5,{children:(0,r.jsx)(g.w1,{asChild:!0,children:(0,r.jsx)(i(),{href:"/",children:"Home"})})}),(0,r.jsx)(g.tH,{}),(0,r.jsx)(g.J5,{children:(0,r.jsx)(g.w1,{asChild:!0,children:(0,r.jsx)(i(),{href:"/products?category=".concat(E.CategoryID||"all"),children:E.CategoryName||"Medical Products"})})}),(0,r.jsx)(g.tH,{}),(0,r.jsx)(g.J5,{children:(0,r.jsx)(g.w1,{asChild:!0,children:(0,r.jsx)(i(),{href:"/products",children:"Products"})})}),(0,r.jsx)(g.tH,{}),(0,r.jsx)(g.J5,{children:(0,r.jsx)(g.tJ,{children:E.ProductName})})]})}),(0,r.jsx)("div",{className:"lg:hidden mb-6",children:(()=>{if(!E.SellStartDatetimeUTC||!E.SellEndDatetimeUTC)return null;let e=new Date,t=new Date(E.SellStartDatetimeUTC),s=new Date(E.SellEndDatetimeUTC),a=e<t;if(e>s)return null;let l=a?E.SellStartDatetimeUTC:E.SellEndDatetimeUTC,i="\uD83D\uDD25 Limited Time Sale!",n="Sale ends soon - grab yours now!";return a?(i="⏰ Sale Starts Soon!",n="Get ready for an amazing deal!"):e>=t&&e<=s&&(i="\uD83D\uDD25 Limited Time Sale!",n="Sale ends soon - don't miss out!"),(0,r.jsxs)("div",{className:"p-3 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-xl",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 mb-3",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 text-red-500 animate-pulse"}),(0,r.jsx)("span",{className:"text-sm font-bold text-red-600",children:i})]}),(0,r.jsx)("div",{className:"flex justify-center mb-3",children:(0,r.jsx)(Q,{endDate:l,className:"transform hover:scale-105 transition-transform duration-200"})}),(0,r.jsx)("p",{className:"text-center text-xs font-medium text-gray-700",children:n})]})})()}),(0,r.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,r.jsx)("div",{className:"lg:w-1/2",children:(0,r.jsx)(B,{media:ex,className:"w-full rounded-lg overflow-hidden"})}),(0,r.jsxs)("div",{className:"md:w-1/2",children:[(0,r.jsx)("h1",{className:"text-xl font-bold mb-2",children:E.ProductName}),(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[(0,r.jsx)("div",{className:"flex",children:[void 0,void 0,void 0,void 0,void 0].map((e,t)=>(0,r.jsx)(d.A,{className:"w-4 h-4 ".concat(t<Math.floor(E.Rating||0)?"text-yellow-400 fill-yellow-400":"text-gray-300")},t))}),(0,r.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["(",E.Rating||0,") ",E.TotalReviews||0," reviews"]})]}),(()=>{if(!E)return null;let e=E.DiscountPrice&&E.DiscountPrice<E.Price,t=eu(),s=t!==E.Price&&t!==(E.DiscountPrice||E.Price);return(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("div",{className:"flex items-baseline gap-2",children:[(0,r.jsxs)("span",{className:"text-3xl font-bold text-primary",children:["$",e?(E.DiscountPrice||0).toFixed(2):t.toFixed(2)]}),e&&(0,r.jsxs)("span",{className:"text-lg text-gray-400 line-through",children:["$",E.Price.toFixed(2)]}),s&&!e&&(0,r.jsxs)("span",{className:"text-lg text-gray-400 line-through",children:["$",E.Price.toFixed(2)]}),e&&(0,r.jsxs)("span",{className:"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded",children:[Math.round((E.Price-(E.DiscountPrice||0))/E.Price*100),"% OFF"]})]}),E.PriceIQD&&(0,r.jsxs)("div",{className:"mt-1 text-lg font-medium text-gray-600",children:[E.PriceIQD.toLocaleString()," IQD"]}),E.PointNo&&E.PointNo>0&&(0,r.jsx)("div",{className:"mt-2",children:(0,r.jsxs)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800",children:["Buy & Earn ",E.PointNo," $ credit"]})}),E.OldPrice&&E.OldPrice>E.Price&&(0,r.jsxs)("div",{className:"mt-1 text-sm text-gray-500",children:[(0,r.jsxs)("span",{className:"line-through",children:["$",E.OldPrice.toFixed(2)]}),(0,r.jsxs)("span",{className:"ml-2 text-green-600",children:[Math.round((E.OldPrice-(E.DiscountPrice||E.Price))/E.OldPrice*100),"% OFF"]})]})]})})(),(0,r.jsx)("div",{className:"hidden lg:block",children:(()=>{if(!E.SellStartDatetimeUTC||!E.SellEndDatetimeUTC)return null;let e=new Date,t=new Date(E.SellStartDatetimeUTC),s=new Date(E.SellEndDatetimeUTC),a=e<t;if(e>s)return null;let l=a?E.SellStartDatetimeUTC:E.SellEndDatetimeUTC,i="\uD83D\uDD25 Limited Time Sale!",n="Sale ends soon - grab yours now!";return a?(i="⏰ Sale Starts Soon!",n="Get ready for an amazing deal!"):e>=t&&e<=s&&(i="\uD83D\uDD25 Limited Time Sale!",n="Sale ends soon - don't miss out!"),(0,r.jsxs)("div",{className:"mb-6 p-4 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200 rounded-xl",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center gap-3 mb-4",children:[(0,r.jsx)(c.A,{className:"h-6 w-6 text-red-500 animate-pulse"}),(0,r.jsx)("span",{className:"text-lg font-bold text-red-600",children:i})]}),(0,r.jsx)("div",{className:"flex justify-center mb-4",children:(0,r.jsx)(Q,{endDate:l,className:"transform hover:scale-105 transition-transform duration-200"})}),(0,r.jsx)("p",{className:"text-center text-sm font-medium text-gray-700",children:n})]})})()}),E.ShortDescription&&(0,r.jsx)("div",{className:"prose prose-sm max-w-none mb-6",children:(0,r.jsx)("p",{children:E.ShortDescription})}),(0,r.jsxs)("div",{className:"mb-6 border-t border-gray-200 pt-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Product Details"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Choose your preferences from the options below."}),Object.entries(em).length>0?(0,r.jsx)("div",{className:"space-y-6",children:Object.entries(em).map(e=>{var t,s;let[a,l]=e;return(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("h4",{className:"text-sm font-medium text-gray-700",children:[(null==(t=l[0])?void 0:t.DisplayName)||(null==(s=l[0])?void 0:s.AttributeName),":"]}),(0,r.jsx)("div",{className:"space-y-2 pl-4",children:l.map(e=>{let t="".concat(e.ProductAttributeID,"_").concat(e.AttributeValueID),s=!!en[t],i=l.length>1;return(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("div",{className:"flex items-center h-5",children:(0,r.jsx)("input",{type:i?"radio":"checkbox",id:"attr-".concat(t),name:"attr-group-".concat(a),className:"h-4 w-4 ".concat(i?"rounded-full":"rounded"," border-gray-300 text-primary focus:ring-primary"),checked:s,onChange:t=>{var s;return s=t.target.checked,void eo(t=>{let r={...t},a="".concat(e.ProductAttributeID,"_").concat(e.AttributeValueID);return i&&s&&Object.keys(t).forEach(t=>{t.startsWith("".concat(e.ProductAttributeID,"_"))&&t!==a&&(r[t]=!1)}),r[a]=!!i||!t[a],r})}})}),(0,r.jsx)("div",{className:"ml-3 text-sm",children:(0,r.jsxs)("label",{htmlFor:"attr-".concat(t),className:"font-medium ".concat(s?"text-primary":"text-gray-700"),children:[e.AttributeValueText,(e.PriceAdjustment||0===e.PriceAdjustment)&&(0,r.jsxs)("span",{className:"ml-2 text-sm font-normal text-green-600",children:["(",1===e.PriceAdjustmentType?"+":"","$",e.PriceAdjustment," ",2===e.PriceAdjustmentType?"%":"",")"]})]})})]},t)})})]},"attr-group-".concat(a))})}):(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"No additional product details available."})]}),(0,r.jsx)("div",{className:"mb-6",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-3",children:[(0,r.jsx)("span",{className:"text-sm font-medium",children:"Quantity:"}),(0,r.jsxs)("div",{className:"flex items-center justify-between sm:justify-start",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:()=>{if(E){let e=E.OrderMinimumQuantity>0?E.OrderMinimumQuantity:1;_>e?(U(e=>e-1),eh("decrement")):v.oR.info("Minimum quantity is ".concat(e))}},className:ep("decrement"),disabled:_<=(E.OrderMinimumQuantity||1),"aria-label":"Decrease quantity",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 sm:h-5 sm:w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"})})}),(0,r.jsx)(()=>(0,r.jsxs)("div",{className:"relative flex items-center justify-center w-10 sm:w-12",children:[(0,r.jsx)("span",{className:"text-sm sm:text-base font-medium transition-all duration-200 ".concat(er?"scale-125 text-primary":"scale-100"),children:_}),er&&(0,r.jsx)("span",{className:"absolute text-xs font-bold text-primary transition-all duration-200 ".concat("increment"===el?"-top-4 sm:-top-5":"top-4 sm:top-5"),children:"increment"===el?"+1":"-1"})]}),{}),(0,r.jsx)("button",{onClick:()=>{if(E){let e=E.OrderMaximumQuantity>0?Math.min(E.OrderMaximumQuantity,E.StockQuantity):E.StockQuantity;_<e?(U(e=>e+1),eh("increment")):v.oR.info("Maximum quantity of ".concat(e," reached"))}},className:ep("increment"),disabled:E.OrderMaximumQuantity>0?_>=Math.min(E.OrderMaximumQuantity,E.StockQuantity):_>=E.StockQuantity,"aria-label":"Increase quantity",children:(0,r.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 sm:h-5 sm:w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"})})})]}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-2 sm:ml-4",children:[E.OrderMinimumQuantity>1&&(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["Min: ",E.OrderMinimumQuantity]}),E.OrderMaximumQuantity>0&&(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["Max:"," ",Math.min(E.OrderMaximumQuantity,E.StockQuantity)]})]})]})]})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 sm:gap-4",children:[(0,r.jsxs)("button",{type:"button",className:"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none text-sm sm:text-base",disabled:E.StockQuantity<=0||q,onClick:()=>{if(E){Z(!0);try{var e,t;let s=E.ProductImagesJson&&E.ProductImagesJson.length>0?$(E.ProductImagesJson[0].AttachmentURL):"/placeholder.jpg",r=(E.AttributesJson||[]).filter(e=>en["".concat(e.ProductAttributeID,"_").concat(e.AttributeValueID)]);P.addToCart({id:E.ProductId,name:E.ProductName,price:E.DiscountPrice||E.Price,discountPrice:E.DiscountPrice,image:s,originalPrice:E.Price},_,r,E.PriceIQD,I),(0,b.k)({productName:E.ProductName,quantity:_,productImage:(null==(t=E.ProductImagesJson)||null==(e=t[0])?void 0:e.AttachmentURL)||"/placeholder.svg",onViewCart:()=>{window.location.href="/cart"}})}catch(e){console.error("Error adding to cart:",e),v.oR.error("Failed to add product to cart. Please try again.")}finally{Z(!1)}}},children:[q?(0,r.jsx)("div",{className:"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin"}):(0,r.jsx)(m.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:q?"Adding...":"Add to Cart"})]}),(0,r.jsxs)("button",{type:"button",className:"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none text-sm sm:text-base sm:flex-initial sm:min-w-[120px]",disabled:X,onClick:()=>{if(E){Y(!0);try{if(S.isInWishlist(E.ProductId))S.removeFromWishlist(E.ProductId),v.oR.success("".concat(E.ProductName," removed from wishlist"));else{var e,t;let s="/product/".concat(E.ProductId),r=(null==(t=E.ProductImagesJson)||null==(e=t[0])?void 0:e.AttachmentURL)||"/placeholder.svg",a=E.DiscountPrice||E.Price;S.addToWishlist(E.ProductId,E.ProductName,s,r,a),v.oR.success("".concat(E.ProductName," added to wishlist"))}}catch(e){console.error("Error updating wishlist:",e),v.oR.error("Failed to update wishlist. Please try again.")}finally{Y(!1)}}},children:[X?(0,r.jsx)("div",{className:"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin"}):(0,r.jsx)(u.A,{className:"h-5 w-5",fill:E&&S.isInWishlist(E.ProductId)?"currentColor":"none"}),(0,r.jsx)("span",{className:"sr-only md:not-sr-only md:inline",children:X?"Updating...":E&&S.isInWishlist(E.ProductId)?"Remove":"Wishlist"})]}),(0,r.jsxs)("button",{type:"button",className:"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground text-sm sm:text-base sm:flex-initial sm:min-w-[100px]",onClick:()=>{navigator.share?navigator.share({title:(null==E?void 0:E.MetaTitle)||(null==E?void 0:E.ProductName),text:(null==E?void 0:E.MetaDescription)||"Check out this product: ".concat(null==E?void 0:E.ProductName),url:window.location.href}).catch(e=>console.error("Error sharing:",e)):(navigator.clipboard.writeText(window.location.href),v.oR.success("Product link copied to clipboard"))},children:[(0,r.jsx)(x.A,{className:"h-5 w-5"}),(0,r.jsx)("span",{className:"sr-only md:not-sr-only md:inline",children:"Share"})]})]}),E.MetaKeywords&&(0,r.jsxs)("div",{className:"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200",children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 mb-3",children:"Product Tags"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:E.MetaKeywords.split(",").map((e,t)=>(0,r.jsx)(j.E,{variant:"secondary",className:"text-xs bg-white/70 hover:bg-white transition-colors",children:e.trim()},t))})]}),E.MetaDescription&&(0,r.jsxs)("div",{className:"mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200",children:[(0,r.jsxs)("h3",{className:"font-medium text-gray-900 mb-3 flex items-center",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 text-green-600 mr-2"}),"About This Product"]}),(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:E.MetaDescription})]})]})]}),(0,r.jsx)("div",{className:"mt-12",children:(0,r.jsxs)(f.tU,{defaultValue:"description",className:"w-full",value:et,onValueChange:es,children:[(0,r.jsxs)(f.j7,{className:"grid w-full grid-cols-3 mb-6 gap-2 bg-transparent p-0 h-auto",children:[(0,r.jsx)(f.Xi,{value:"description",className:"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"description"===et?k:"rgb(209 213 219)",color:"description"===et?D:"rgb(55 65 81)",transform:"description"===et?"scale(1.05)":"scale(1)",boxShadow:"description"===et?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"description"===et?k:"transparent"},children:"Overview"}),(0,r.jsx)(f.Xi,{value:"reviews",className:"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"reviews"===et?k:"rgb(209 213 219)",color:"reviews"===et?D:"rgb(55 65 81)",transform:"reviews"===et?"scale(1.05)":"scale(1)",boxShadow:"reviews"===et?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"reviews"===et?k:"transparent"},children:"Specifications"}),(0,r.jsx)(f.Xi,{value:"shipping",className:"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"shipping"===et?k:"rgb(209 213 219)",color:"shipping"===et?D:"rgb(55 65 81)",transform:"shipping"===et?"scale(1.05)":"scale(1)",boxShadow:"shipping"===et?"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)":"none",borderColor:"shipping"===et?k:"transparent"},children:"Reviews"})]}),(0,r.jsx)(f.av,{value:"description",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Product Overview"}),E.FullDescription?(0,r.jsx)("div",{className:"prose max-w-none",dangerouslySetInnerHTML:{__html:E.FullDescription}}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-700 leading-relaxed",children:E.ShortDescription||"This is a high-quality medical product designed to meet professional standards."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mt-6",children:[(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Key Features"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1",children:[(0,r.jsx)("li",{children:"• Professional grade quality"}),(0,r.jsx)("li",{children:"• Durable construction"}),(0,r.jsx)("li",{children:"• Easy to use"}),(0,r.jsx)("li",{children:"• Reliable performance"})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Benefits"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-700 space-y-1",children:[(0,r.jsx)("li",{children:"• Enhanced efficiency"}),(0,r.jsx)("li",{children:"• Cost-effective solution"}),(0,r.jsx)("li",{children:"• Long-lasting durability"}),(0,r.jsx)("li",{children:"• Professional results"})]})]})]})]})]})}),(0,r.jsx)(f.av,{value:"reviews",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Product Specifications"}),(0,r.jsx)(C,{attributes:E.AttributesJson||[]})]})}),(0,r.jsx)(f.av,{value:"shipping",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,r.jsxs)("div",{className:"p-8",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4",children:"Customer Reviews"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center gap-4",children:[(0,r.jsx)("div",{className:"flex items-center",children:[1,2,3,4,5].map(e=>(0,r.jsx)(d.A,{className:"w-6 h-6 ".concat(e<=Math.floor(E.Rating||0)?"text-yellow-400 fill-yellow-400":"text-gray-300")},e))}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsx)("span",{className:"font-medium",children:(null==(t=E.Rating)?void 0:t.toFixed(1))||"0.0"})," ","out of 5",E.TotalReviews?(0,r.jsxs)("span",{children:[" ","• ",E.TotalReviews," review",1!==E.TotalReviews?"s":""]}):null]})]}),(0,r.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg",children:(0,r.jsx)(H.A,{productId:E.ProductId,showTitle:!1})})]})]})})]})})]}):(0,r.jsxs)("div",{className:"container mx-auto px-4 py-8 text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Product Not Found"}),(0,r.jsxs)("p",{className:"mb-6",children:['The product with ID "',l,'" could not be found. It may not exist in the database.']}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(i(),{href:"/products",children:(0,r.jsxs)(p.$,{children:[(0,r.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"View All Products"]})}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Check the products list to find available product IDs"})]})]})}},27737:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(95155),a=s(53999);function l(e){let{className:t,...s}=e;return(0,r.jsx)("div",{className:(0,a.cn)("animate-pulse rounded-md bg-muted",t),...s})}},34489:(e,t,s)=>{Promise.resolve().then(s.bind(s,14781))},34964:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>c,av:()=>d,j7:()=>o,tU:()=>n});var r=s(95155),a=s(12115),l=s(30064),i=s(53999);let n=l.bL,o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.B8,{ref:t,className:(0,i.cn)("inline-flex h-8 items-center justify-center rounded-md bg-muted p-0.5 text-muted-foreground",s),...a})});o.displayName=l.B8.displayName;let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.l9,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-2 py-1 text-xs font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",s),...a})});c.displayName=l.l9.displayName;let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)(l.UC,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",s),...a})});d.displayName=l.UC.displayName},49504:(e,t,s)=>{"use strict";s.d(t,{A:()=>u});var r=s(95155),a=s(12115),l=s(88482),i=s(97168),n=s(27737),o=s(38564),c=s(71366),d=s(71007),m=s(70333);function u(e){let{productId:t,showTitle:s=!0}=e,[u,x]=(0,a.useState)([]),[h,p]=(0,a.useState)(!0),[g,f]=(0,a.useState)(null),[j,v]=(0,a.useState)(!1);(0,a.useEffect)(()=>{t&&b()},[t]);let b=async()=>{p(!0),f(null);try{console.log("\uD83D\uDD0D Fetching product reviews for ProductId:",t);let e=await fetch("/api/reviews/get-product-reviews",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({requestParameters:{ProductId:t,recordValueJson:"[]"}})}),s=await e.json();if(console.log("\uD83D\uDD0D Product reviews API response:",s),e.ok&&s&&!s.errorMessage){let e=s.data||s;if(console.log("\uD83D\uDD0D Raw reviews data:",e),"string"==typeof e)try{e=JSON.parse(e),console.log("\uD83D\uDD0D Parsed reviews data from string:",e)}catch(t){console.error("❌ Failed to parse reviews data string:",t),e=[]}if(Array.isArray(e)&&e.length>0&&e[0].DATA)try{let t=JSON.parse(e[0].DATA);console.log("\uD83D\uDD0D Parsed inner DATA:",t),Array.isArray(t)?x(t.filter(e=>!1!==e.IsApproved)):x([])}catch(e){console.error("❌ Failed to parse inner DATA:",e),x([])}else Array.isArray(e)?(console.log("\uD83D\uDD0D Using reviews data as array:",e),e.length>0&&console.log("\uD83D\uDD0D Sample review date fields:",{ReviewDate:e[0].ReviewDate,CreatedOn:e[0].CreatedOn,availableFields:Object.keys(e[0])}),x(e.filter(e=>!1!==e.IsApproved))):(console.log("ℹ️ No valid reviews data found"),x([]))}else console.error("❌ API error:",s),f((null==s?void 0:s.errorMessage)||(null==s?void 0:s.message)||"Failed to load reviews")}catch(e){console.error("❌ Error fetching product reviews:",e),f("Failed to load reviews")}finally{p(!1)}},y=e=>(0,r.jsx)("div",{className:"flex items-center gap-1",children:[1,2,3,4,5].map(t=>(0,r.jsx)(o.A,{className:"h-4 w-4 ".concat(t<=e?"fill-yellow-400 text-yellow-400":"text-gray-300")},t))}),N=j?u:u.slice(0,3),w=u.length>0?u.reduce((e,t)=>e+t.Rating,0)/u.length:0;return h?(0,r.jsxs)("div",{className:"space-y-4",children:[s&&(0,r.jsx)(n.E,{className:"h-6 w-48"}),[1,2,3].map(e=>(0,r.jsx)(l.Zp,{className:"p-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(n.E,{className:"h-4 w-20"}),(0,r.jsx)(n.E,{className:"h-4 w-24"})]}),(0,r.jsx)(n.E,{className:"h-4 w-full"}),(0,r.jsx)(n.E,{className:"h-16 w-full"})]})},e))]}):g?(0,r.jsxs)(l.Zp,{className:"p-6 text-center",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-gray-500",children:g})]}):0===u.length?(0,r.jsxs)(l.Zp,{className:"p-6 text-center",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,r.jsx)("h3",{className:"font-medium mb-1",children:"No Reviews Yet"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"Be the first to review this product!"})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[s&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Customer Reviews"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[y(Math.round(w)),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[w.toFixed(1)," (",u.length," review",1!==u.length?"s":"",")"]})]})]}),(0,r.jsx)("div",{className:"space-y-4",children:N.map(e=>(0,r.jsx)(l.Zp,{className:"p-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:(0,r.jsx)(d.A,{className:"h-4 w-4 text-primary"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.ReviewerName}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[y(e.Rating),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{if(!e)return"N/A";try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}catch(t){return e}})(e.ReviewDate||e.CreatedOn||"")})]})]})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-1",children:e.Title}),(0,r.jsx)("p",{className:"text-gray-700 text-sm leading-relaxed",children:e.Body})]}),e.HelpfulCount&&e.HelpfulCount>0&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[(0,r.jsx)(m.A,{className:"h-3 w-3"}),(0,r.jsxs)("span",{children:[e.HelpfulCount," people found this helpful"]})]})]})},e.ReviewID))}),u.length>3&&(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)(i.$,{variant:"outline",onClick:()=>v(!j),children:j?"Show Less":"Show All ".concat(u.length," Reviews")})})]})}},59268:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i,n:()=>n});var r=s(95155),a=s(12115);let l=(0,a.createContext)(void 0);function i(e){let{children:t}=e,[s,i]=(0,a.useState)([]),[n,o]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let e=localStorage.getItem("wishlist");if(e)try{let t=JSON.parse(e);if(Array.isArray(t)&&t.length>0)if("number"==typeof t[0]){let e=t.map(e=>({productId:e,productName:"Product ".concat(e),productUrl:"/product/".concat(e),addedAt:new Date().toISOString()}));i(e),localStorage.setItem("wishlist",JSON.stringify(e))}else i(t)}catch(e){console.error("Failed to parse wishlist from localStorage:",e)}o(!0)},[]),(0,a.useEffect)(()=>{localStorage.setItem("wishlist",JSON.stringify(s))},[s]),(0,r.jsx)(l.Provider,{value:{wishlistItems:s,addToWishlist:(e,t,r,a,l)=>{s.some(t=>t.productId===e)||i([...s,{productId:e,productName:t,productUrl:r,imageUrl:a,price:l,addedAt:new Date().toISOString()}])},removeFromWishlist:e=>{i(s.filter(t=>t.productId!==e))},isInWishlist:e=>s.some(t=>t.productId===e),getWishlistItem:e=>s.find(t=>t.productId===e),totalItems:s.length,isHydrated:n},children:t})}function n(){let e=(0,a.useContext)(l);if(void 0===e)throw Error("useWishlist must be used within a WishlistProvider");return e}},61204:(e,t,s)=>{"use strict";s.d(t,{T:()=>r});let r={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",COMMON_CONTROLLER_SUB_URL:"api/v1/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile"}}},65409:(e,t,s)=>{"use strict";s.d(t,{$g:()=>m,Config:()=>l,MakeApiCallAsync:()=>o,XX:()=>d,k6:()=>c});var r=s(23464),a=s(61204);r.A.defaults.timeout=3e4,"https:"===window.location.protocol&&a.T.ADMIN_BASE_URL.includes("localhost")&&(r.A.defaults.httpsAgent={rejectUnauthorized:!1});let l={ADMIN_BASE_URL:a.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...a.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",RESET_PASSWORD_FIREBASE:"reset-password-firebase",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile",INSERT_PRODUCT_REVIEW:"Insert-Product-Review",GET_PRODUCT_REVIEWS:"get-product-reviews"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},i=async()=>{try{{try{let e=await fetch("/api/auth/get-token",{method:"GET",credentials:"include"});if(e.ok){let t=await e.json();if(t.token)return console.log("\uD83D\uDD10 Retrieved JWT token from secure HttpOnly cookie"),t.token}}catch(e){console.log("API token retrieval failed, trying client-side cookies:",e)}for(let e of document.cookie.split(";")){let[t,s]=e.trim().split("=");if("auth_token"===t)return console.log("\uD83D\uDD10 Retrieved JWT token from client-side cookie"),decodeURIComponent(s)}let e=localStorage.getItem("token")||localStorage.getItem("authToken");if(e)return console.log("⚠️ Using token from localStorage (migrating to secure cookies)"),localStorage.removeItem("token"),localStorage.removeItem("authToken"),e}return null}catch(e){return console.error("Error getting token for header:",e),null}},n=async()=>{try{{for(let s of document.cookie.split(";")){let[r,a]=s.trim().split("=");if("auth_user"===r)try{var e,t;let s=JSON.parse(decodeURIComponent(a)),r=(null==(e=s.UserId)?void 0:e.toString())||(null==(t=s.UserID)?void 0:t.toString());if(r)return console.log("\uD83D\uDD10 Retrieved User ID from secure cookie"),r}catch(e){console.warn("Failed to parse user data from cookie:",e)}}let s=localStorage.getItem("userId")||localStorage.getItem("userID");if(s)return console.log("⚠️ Using User ID from localStorage (migrating to secure cookies)"),localStorage.removeItem("userId"),localStorage.removeItem("userID"),s}return null}catch(e){return console.error("Error getting user ID for header:",e),null}},o=async function(e,t,s,a,o){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let c=(e=>{if(!e)return e;let t=JSON.parse(JSON.stringify(e));return t.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from request body (will use JWT token instead)"),delete t.UserId),t.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from request body (will use JWT token instead)"),delete t.UserID),t.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from request body (will use JWT token instead)"),delete t.user_id),t.requestParameters&&(t.requestParameters.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from requestParameters (will use JWT token instead)"),delete t.requestParameters.UserId),t.requestParameters.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from requestParameters (will use JWT token instead)"),delete t.requestParameters.UserID),t.requestParameters.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from requestParameters (will use JWT token instead)"),delete t.requestParameters.user_id)),t})(s),d={...a};if(!d.hasOwnProperty("Authorization")){let e=await i();e&&(d.Authorization="Bearer "+e,console.log("\uD83D\uDD10 Added JWT token to Authorization header"))}if(!d.hasOwnProperty("Token")){let e=await i();d.Token=null!=e?e:"",e&&console.log("\uD83D\uDD10 Added JWT token to Token header (backward compatibility)")}if(!d.hasOwnProperty("UserID")){let e=await n();d.UserID=null!=e?e:""}d.hasOwnProperty("Accept")||(d.Accept="application/json"),d.hasOwnProperty("Content-Type")||(d["Content-Type"]="application/json");let m=l.ADMIN_BASE_URL+(null===t||void 0==t?l.DYNAMIC_METHOD_SUB_URL:t)+e;o=null!=o?o:"POST";let u={headers:d,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===o)return await r.A.post(m,c,u);if("GET"==o)return u.params=c,await r.A.get(m,u);return{data:{errorMessage:"Unsupported method type: ".concat(o),status:"method_not_supported"}}}catch(t){console.error("API call failed:",t);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(t&&"object"==typeof t&&"response"in t&&t.response){var c,d;let s=null==(c=t.response)?void 0:c.data;e.data={errorMessage:(null==s?void 0:s.errorMessage)||"An error occurred while processing your request.",status:null==(d=t.response)?void 0:d.status}}else if(t&&"object"==typeof t&&"request"in t){let s="Network error: No response received from server.";t.message&&t.message.includes("Network Error")&&(s="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+l.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:s,status:"network_error"}}else e.data={errorMessage:t&&"object"==typeof t&&"message"in t?t.message:"An unexpected error occurred",status:"request_error"};return e}},c=async()=>{try{return console.log("Using default currency rate (1430) - API endpoint not available"),1430}catch(e){return console.error("Error fetching currency rate:",e),1430}},d=(e,t)=>Math.round(e*t),m=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return null==e||isNaN(e)?"IQD"===t?"0 IQD":"$0.00":"IQD"===t?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},73339:(e,t,s)=>{"use strict";s.d(t,{S:()=>d,k:()=>c});var r=s(95155),a=s(56671),l=s(40646),i=s(27809),n=s(54416),o=s(97168);let c=e=>{let{productName:t,quantity:s,productImage:c,onViewCart:d}=e;return a.oR.custom(e=>(0,r.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-md w-full",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(l.A,{className:"w-5 h-5 text-green-600"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 mb-1",children:"Added to cart"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 line-clamp-2",children:[s," \xd7 ",t]})]}),c&&(0,r.jsx)("div",{className:"flex-shrink-0 ml-3",children:(0,r.jsx)("img",{src:(e=>{if(!e)return"/placeholder.svg";if(e.startsWith("http://")||e.startsWith("https://")||e.startsWith("/placeholder")||e.startsWith("/images/")||e.startsWith("/assets/"))return e;let t="https://admin.codemedicalapps.com/".replace(/\/$/,""),s=e.startsWith("/")?e:"/".concat(e);return s=s.replace(/\/+/g,"/"),"".concat(t).concat(s)})(c),alt:t,className:"w-12 h-12 rounded-md object-cover border border-gray-200",onError:e=>{let t=e.target;t.onerror=null,t.src="/placeholder.svg"}})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-3",children:[(0,r.jsxs)(o.$,{size:"sm",variant:"outline",onClick:()=>{a.oR.dismiss(e),null==d||d()},className:"h-8 text-xs",children:[(0,r.jsx)(i.A,{className:"w-3 h-3 mr-1"}),"View Cart"]}),(0,r.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>a.oR.dismiss(e),className:"h-8 text-xs text-gray-500 hover:text-gray-700",children:"Continue Shopping"})]})]}),(0,r.jsx)("button",{onClick:()=>a.oR.dismiss(e),className:"flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(n.A,{className:"w-4 h-4"})})]})}),{duration:5e3,position:"top-right"})},d=e=>{let{productName:t,quantity:s}=e;return a.oR.success((0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(l.A,{className:"w-4 h-4 text-green-600"}),(0,r.jsxs)("span",{children:[s," \xd7 ",t," added to cart"]})]}),{duration:3e3})}},84995:(e,t,s)=>{"use strict";s.d(t,{AB:()=>c,J5:()=>d,Qp:()=>o,tH:()=>x,tJ:()=>u,w1:()=>m});var r=s(95155),a=s(12115),l=s(99708),i=s(13052),n=(s(5623),s(53999));let o=a.forwardRef((e,t)=>{let{...s}=e;return(0,r.jsx)("nav",{ref:t,"aria-label":"breadcrumb",...s})});o.displayName="Breadcrumb";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("ol",{ref:t,className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",s),...a})});c.displayName="BreadcrumbList";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("li",{ref:t,className:(0,n.cn)("inline-flex items-center gap-1.5",s),...a})});d.displayName="BreadcrumbItem";let m=a.forwardRef((e,t)=>{let{asChild:s,className:a,...i}=e,o=s?l.DX:"a";return(0,r.jsx)(o,{ref:t,className:(0,n.cn)("transition-colors hover:text-foreground",a),...i})});m.displayName="BreadcrumbLink";let u=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,n.cn)("font-normal text-foreground",s),...a})});u.displayName="BreadcrumbPage";let x=e=>{let{children:t,className:s,...a}=e;return(0,r.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,n.cn)("[&>svg]:size-3.5",s),...a,children:null!=t?t:(0,r.jsx)(i.A,{})})};x.displayName="BreadcrumbSeparator"},88145:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var r=s(95155),a=s(74466),l=s(53999);let i=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,l.cn)(i({variant:s}),t),...a})}},88482:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>m});var r=s(95155),a=s(12115),l=s(53999);let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...a})});i.displayName="Card";let n=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...a})});n.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});o.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...a})});d.displayName="CardContent";let m=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...a})});m.displayName="CardFooter"}},e=>{e.O(0,[4277,3464,4706,6774,3942,5371,4942,9321,8441,5964,7358],()=>e(e.s=34489)),_N_E=e.O()}]);