(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4005],{45887:(e,s,t)=>{Promise.resolve().then(t.bind(t,50260))},50260:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>I});var r=t(95155),a=t(12115),l=t(35695),i=t(84995),n=t(88482),d=t(97168),c=t(76981),o=t(5196),m=t(53999);let u=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(c.bL,{ref:s,className:(0,m.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...a,children:(0,r.jsx)(c.C1,{className:(0,m.cn)("flex items-center justify-center text-current"),children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})})});u.displayName=c.bL.displayName;var x=t(6874),p=t.n(x),h=t(79891),f=t(98816),g=t(78067),j=t(92215),N=t(9776),b=t(70306),y=t(38564),v=t(55868),w=t(87712),C=t(84616),A=t(62525),k=t(27809),D=t(24752),P=t.n(D);function I(){let{t:e,primaryColor:s}=(0,h.t)(),{user:t,isLoggedIn:c}=(0,f.J)(),{items:o,removeFromCart:m,updateQuantity:x,totalItems:D,subtotal:I,subtotalIQD:T,total:S,totalIQD:R}=(0,g._)(),{validateCoupon:M,appliedCoupon:B,clearCoupon:E,isLoading:_}=(0,j.Y)(),{formatIQD:O,formatUSD:V}=(0,N.H)(),U=(0,l.useRouter)(),[L,Q]=(0,a.useState)(""),[q,$]=(0,a.useState)(!1),[J,Y]=(0,a.useState)(!1);(0,a.useEffect)(()=>{localStorage.setItem("usePoints",J.toString())},[J]),(0,a.useEffect)(()=>{c||U.push("/login?redirect=/cart")},[c,U]);let Z=(null==t?void 0:t.Pointno)||0,z=J?Z:0,F=Math.round(1500*z),H=Math.max(0,S-(B?B.discount:0)-z),W=Math.max(0,R-(B?Math.round(1500*B.discount):0)-F);return c?(0,r.jsxs)("div",{className:"container mx-auto py-4 sm:py-6 md:py-8 px-4",children:[(0,r.jsx)(i.Qp,{className:"mb-4 sm:mb-6",children:(0,r.jsxs)(i.AB,{children:[(0,r.jsx)(i.J5,{children:(0,r.jsx)(i.w1,{asChild:!0,children:(0,r.jsx)(p(),{href:"/",children:e("home")})})}),(0,r.jsx)(i.tH,{}),(0,r.jsx)(i.J5,{children:(0,r.jsx)(i.tJ,{children:e("cart")})})]})}),(0,r.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 sm:mb-6 gap-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl sm:text-3xl font-bold",children:e("cart")}),Z>0&&(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 text-yellow-500"}),(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:["You have ",Z," credit (",q?V(Z):O(Math.round(1500*Z)),")"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("p",{className:"text-muted-foreground",children:[D," ",1===D?"item":"items"]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.$,{variant:q?"outline":"default",size:"sm",onClick:()=>$(!1),className:"flex items-center gap-2",children:"IQD"}),(0,r.jsxs)(d.$,{variant:q?"default":"outline",size:"sm",onClick:()=>$(!0),className:"flex items-center gap-2",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),"USD"]})]})]})]}),o.length>0?(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2 space-y-4",children:o.map(e=>(0,r.jsx)(n.Zp,{className:"overflow-hidden hover:shadow-lg transition-shadow duration-300 border-l-4",style:{borderLeftColor:s},children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row",children:[(0,r.jsxs)("div",{className:"w-full sm:w-40 h-40 bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden",children:[(0,r.jsx)("img",{src:e.image||"/products/book".concat(e.id,".jpg"),alt:e.name,className:"w-full h-full object-cover hover:scale-105 transition-transform duration-300"}),(0,r.jsxs)("div",{className:"absolute top-2 right-2 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 text-xs font-medium",style:{color:s},children:["#",e.id]})]}),(0,r.jsxs)("div",{className:"flex-1 p-6 flex flex-col sm:flex-row justify-between bg-gradient-to-r from-white to-gray-50/50",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"font-semibold text-lg mb-3 text-gray-800 leading-tight",children:e.name}),(0,r.jsx)("div",{className:"mb-4 p-3 bg-white rounded-lg border border-gray-100 shadow-sm",children:q?(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",style:{color:s},children:V(e.adjustedPrice)}),(0,r.jsx)("div",{className:"bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium",children:"USD"})]}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,r.jsx)("span",{children:"≈"}),(0,r.jsx)("span",{children:O(e.adjustedIqdPrice||e.iqdPrice||0)})]})]}):(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"text-2xl font-bold",style:{color:s},children:O(e.adjustedIqdPrice||e.iqdPrice||0)}),(0,r.jsx)("div",{className:"bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium",children:"IQD"})]}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground flex items-center gap-1",children:[(0,r.jsx)("span",{children:"≈"}),(0,r.jsx)("span",{children:V(e.adjustedPrice)})]})]})}),e.discountPrice&&e.discountPrice<(e.originalPrice||e.price)&&(0,r.jsxs)("div",{className:"text-sm text-green-600 mb-2 flex items-center gap-2",children:[(0,r.jsx)("span",{className:"bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium",children:"SALE"}),(0,r.jsxs)("span",{children:["Original: ",q?V(e.originalPrice):O(Math.round(1500*e.originalPrice))]})]}),e.attributes&&e.attributes.length>0&&(0,r.jsxs)("div",{className:"mt-2 space-y-1 pt-2 border-t border-gray-100",children:[(0,r.jsx)("div",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wide mb-1",children:"Selected Options:"}),e.attributes.map((e,s)=>(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,r.jsxs)("span",{children:[(0,r.jsxs)("span",{className:"font-medium",children:[e.DisplayName||e.AttributeName,":"]})," ",e.AttributeValueText]}),e.PriceAdjustment&&(0,r.jsxs)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full",children:[1===e.PriceAdjustmentType?"+":"",q?"$".concat(e.PriceAdjustment):"".concat(Math.round(1500*e.PriceAdjustment).toLocaleString()," IQD"),2===e.PriceAdjustmentType?"%":""]})]},s))]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mt-4 sm:mt-0",children:[(0,r.jsxs)("div",{className:"flex items-center bg-gray-50 rounded-lg border border-gray-200 overflow-hidden",children:[(0,r.jsx)("button",{className:"p-3 hover:bg-gray-100 transition-colors text-gray-600 hover:text-gray-800",onClick:()=>x(e.id,e.quantity-1),children:(0,r.jsx)(w.A,{className:"h-4 w-4"})}),(0,r.jsx)("span",{className:"px-4 py-3 font-medium bg-white border-x border-gray-200 min-w-[3rem] text-center",children:e.quantity}),(0,r.jsx)("button",{className:"p-3 hover:bg-gray-100 transition-colors text-gray-600 hover:text-gray-800",onClick:()=>x(e.id,e.quantity+1),children:(0,r.jsx)(C.A,{className:"h-4 w-4"})})]}),(0,r.jsx)("button",{className:"p-3 text-red-500 hover:bg-red-50 rounded-lg transition-colors border border-red-200 hover:border-red-300",onClick:()=>m(e.id),title:"Remove from cart",children:(0,r.jsx)(A.A,{className:"h-5 w-5"})})]})]})]})},e.id))}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsx)(n.Zp,{className:"sticky top-4 shadow-lg border-0 bg-gradient-to-br from-white to-gray-50",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-bold text-gray-800 flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-6 rounded-full",style:{backgroundColor:s}}),"Order Summary"]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(d.$,{variant:q?"outline":"default",size:"sm",onClick:()=>$(!1),className:"text-xs border-2 hover:scale-105 transition-transform px-2",style:{borderColor:s,backgroundColor:q?"transparent":s,color:q?s:"white"},children:"IQD"}),(0,r.jsx)(d.$,{variant:q?"default":"outline",size:"sm",onClick:()=>$(!0),className:"text-xs border-2 hover:scale-105 transition-transform px-2",style:{borderColor:s,backgroundColor:q?s:"transparent",color:q?"white":s},children:"USD"})]})]}),(0,r.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Subtotal"}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"font-medium",children:q?V(I):O(T)}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:["≈ ",q?O(T):V(I)]})]})]}),Z>0&&(0,r.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(u,{id:"use-points",checked:J,onCheckedChange:e=>Y(e)}),(0,r.jsxs)("label",{htmlFor:"use-points",className:"flex items-center gap-2 cursor-pointer",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 text-yellow-600"}),(0,r.jsx)("span",{className:"font-medium text-yellow-700",children:"Use Credit"})]})]}),J&&(0,r.jsxs)("span",{className:"text-yellow-700 font-bold",children:["-",q?V(z):O(F)]})]}),(0,r.jsxs)("div",{className:"text-xs text-yellow-600",children:["Available: ",Z," credit = ",q?V(Z):O(Math.round(1500*Z))]}),J&&(0,r.jsxs)("div",{className:"text-xs text-yellow-600 mt-1",children:["✓ Using ",Z," credit as discount"]})]}),B&&(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3 space-y-2",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsx)("span",{className:"text-green-700 font-medium",children:B.title||B.code}),(0,r.jsxs)("span",{className:"text-xs text-green-600",children:["Code: ",B.code," •",1===B.discountTypeId&&"Applied on order total",2===B.discountTypeId&&"Applied on order subtotal",3===B.discountTypeId&&"Applied on products",4===B.discountTypeId&&"Applied on categories",5===B.discountTypeId&&"Applied on manufacturers",6===B.discountTypeId&&"Applied on cities",7===B.discountTypeId&&"Applied on shipping"]})]}),(0,r.jsxs)("span",{className:"text-green-700 font-bold",children:["-",q?V(B.discount):O(Math.round(1500*B.discount))]})]})}),(0,r.jsxs)("div",{className:"border-t pt-4 mt-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-lg font-bold",children:"Total"}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"text-xl font-bold",style:{color:s},children:q?V(H):O(W)}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["≈ ",q?O(W):V(H)]})]})]}),(B||J)&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-green-50 rounded-lg border border-green-200",children:(0,r.jsxs)("div",{className:"text-sm text-green-700",children:[(0,r.jsx)("div",{className:"font-medium mb-1",children:"\uD83D\uDCB0 You're saving:"}),B&&(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Coupon discount:"}),(0,r.jsxs)("span",{children:["-",q?V(B.discount):O(Math.round(1500*B.discount))]})]}),J&&(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{children:"Credit discount:"}),(0,r.jsxs)("span",{children:["-$",z.toFixed(2)]})]}),(0,r.jsxs)("div",{className:"border-t border-green-300 mt-2 pt-2 flex justify-between font-bold",children:[(0,r.jsx)("span",{children:"Total savings:"}),(0,r.jsxs)("span",{children:["-",q?V((B?B.discount:0)+z):O((B?Math.round(1500*B.discount):0)+F)]})]})]})})]}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("input",{type:"text",placeholder:"Enter coupon code",className:"flex-1 p-2 border rounded-md",value:L,onChange:e=>Q(e.target.value)}),(0,r.jsx)(d.$,{onClick:async()=>{if(L)try{let e=await M(L,I,o);e.valid?(P().fire({title:"Success!",text:e.message,icon:"success",timer:2e3,showConfirmButton:!1}),Q("")):P().fire({title:"Error",text:e.message,icon:"error",timer:2e3,showConfirmButton:!1})}catch(e){P().fire({title:"Error",text:"Failed to validate coupon. Please try again.",icon:"error",timer:2e3,showConfirmButton:!1})}},variant:"outline",disabled:_,children:_?"Validating...":"Apply"})]}),B&&(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2 text-sm",children:[(0,r.jsxs)("span",{className:"text-green-600",children:["Coupon ",B.code," applied"]}),(0,r.jsx)("button",{onClick:()=>{E(),P().fire({title:"Coupon Removed",text:"Coupon has been removed successfully",icon:"info",timer:2e3,showConfirmButton:!1})},className:"text-red-500 hover:underline",children:"Remove"})]})]})]}),(0,r.jsx)(d.$,{className:"w-full py-6",style:{backgroundColor:s},asChild:!0,children:(0,r.jsx)(p(),{href:"/checkout",children:"Proceed to Checkout"})}),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(p(),{href:"/",className:"text-sm text-center block hover:underline",children:"Continue Shopping"})})]})})})]}):(0,r.jsx)(n.Zp,{children:(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(k.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Your cart is empty"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Add items to your cart to proceed to checkout"}),(0,r.jsx)(d.$,{asChild:!0,children:(0,r.jsx)(p(),{href:"/",children:"Continue Shopping"})})]})})]})]}):(0,r.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,r.jsx)(n.Zp,{className:"max-w-md mx-auto",children:(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(b.A,{className:"h-8 w-8 text-primary"})}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Login Required"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please log in to view your cart and continue shopping"}),(0,r.jsx)(d.$,{asChild:!0,className:"w-full",children:(0,r.jsx)(p(),{href:"/login?redirect=/cart",children:"Login to Continue"})})]})})})}},84995:(e,s,t)=>{"use strict";t.d(s,{AB:()=>c,J5:()=>o,Qp:()=>d,tH:()=>x,tJ:()=>u,w1:()=>m});var r=t(95155),a=t(12115),l=t(99708),i=t(13052),n=(t(5623),t(53999));let d=a.forwardRef((e,s)=>{let{...t}=e;return(0,r.jsx)("nav",{ref:s,"aria-label":"breadcrumb",...t})});d.displayName="Breadcrumb";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("ol",{ref:s,className:(0,n.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",t),...a})});c.displayName="BreadcrumbList";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("li",{ref:s,className:(0,n.cn)("inline-flex items-center gap-1.5",t),...a})});o.displayName="BreadcrumbItem";let m=a.forwardRef((e,s)=>{let{asChild:t,className:a,...i}=e,d=t?l.DX:"a";return(0,r.jsx)(d,{ref:s,className:(0,n.cn)("transition-colors hover:text-foreground",a),...i})});m.displayName="BreadcrumbLink";let u=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("span",{ref:s,role:"link","aria-disabled":"true","aria-current":"page",className:(0,n.cn)("font-normal text-foreground",t),...a})});u.displayName="BreadcrumbPage";let x=e=>{let{children:s,className:t,...a}=e;return(0,r.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,n.cn)("[&>svg]:size-3.5",t),...a,children:null!=s?s:(0,r.jsx)(i.A,{})})};x.displayName="BreadcrumbSeparator"},88482:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>i,aR:()=>n,wL:()=>m});var r=t(95155),a=t(12115),l=t(53999);let i=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});i.displayName="Card";let n=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...a})});n.displayName="CardHeader";let d=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});d.displayName="CardTitle";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...a})});c.displayName="CardDescription";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",t),...a})});o.displayName="CardContent";let m=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",t),...a})});m.displayName="CardFooter"},92215:(e,s,t)=>{"use strict";t.d(s,{U:()=>n,Y:()=>d});var r=t(95155),a=t(12115),l=t(65409);let i=(0,a.createContext)(void 0);function n(e){let{children:s}=e,[t,n]=(0,a.useState)(null),[d,c]=(0,a.useState)(!1),o=async(e,s,t)=>{if(!e.trim())return{valid:!1,message:"Please enter a coupon code",discount:0};c(!0);try{let a=(null==t?void 0:t.map(e=>({ProductId:e.id,ProductName:e.name,Price:e.adjustedPrice||e.price,Quantity:e.quantity,IsDiscountAllowed:!0})))||[],i=JSON.stringify(a),d={requestParameters:{CouponCode:e.toUpperCase(),cartJsonData:i}},c=await (0,l.MakeApiCallAsync)(l.Config.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT,l.Config.DYNAMIC_METHOD_SUB_URL,d,{"Content-Type":"application/json",Accept:"application/json"},"POST");if(c&&c.data&&!c.data.errorMessage){let t;if((t="string"==typeof c.data.data?JSON.parse(c.data.data):c.data.data)&&t.DiscountValueAfterCouponAppliedWithQuantity>0){let s=t.DiscountValueAfterCouponAppliedWithQuantity,r=1===t.DiscountValueType?"percentage":"fixed",a={code:e.toUpperCase(),discount:s,type:r,discountTypeId:t.DiscountTypeId||1};return n(a),{valid:!0,message:"Coupon applied successfully!",discount:s}}if(t&&Array.isArray(t)&&t.length>0){let r=t[0];if(r&&r.DiscountValue>0&&r.IsActive){let t=0,a=r.DiscountTypeId||1;switch(a){case 1:case 2:1===r.DiscountValueType?t=r.DiscountValue*s/100:2===r.DiscountValueType&&(t=r.DiscountValue);break;case 3:case 4:case 5:case 6:case 7:t=1===r.DiscountValueType?r.DiscountValue*s/100:r.DiscountValue;break;default:t=0}if(t>0){let s={code:e.toUpperCase(),discount:t,type:1===r.DiscountValueType?"percentage":"fixed",discountTypeId:a,title:r.Title,discountId:r.DiscountId,maxQuantity:r.MaxQuantity,productId:r.ProductId,categoryId:r.CategoryID};return n(s),{valid:!0,message:'Coupon "'.concat(r.Title,'" applied successfully! Discount applied on ').concat({1:"order total",2:"order subtotal",3:"products",4:"categories",5:"manufacturers",6:"cities",7:"shipping"}[a]||"order","."),discount:t}}}}return{valid:!1,message:"Invalid coupon code or coupon not applicable to your cart",discount:0}}else{var r;return{valid:!1,message:(null==(r=c.data)?void 0:r.errorMessage)||"Failed to validate coupon",discount:0}}}catch(e){return console.error("Coupon validation error:",e),{valid:!1,message:"Error validating coupon. Please try again.",discount:0}}finally{c(!1)}};return(0,r.jsx)(i.Provider,{value:{appliedCoupon:t,validateCoupon:o,clearCoupon:()=>{n(null)},isLoading:d},children:s})}function d(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useCoupon must be used within a CouponProvider");return e}}},e=>{e.O(0,[8320,4277,3464,4706,3393,8816,9321,8441,5964,7358],()=>e(e.s=45887)),_N_E=e.O()}]);