{"/api/addresses/get-user-addresses/route": "app/api/addresses/get-user-addresses/route.js", "/api/addresses/insert-address/route": "app/api/addresses/insert-address/route.js", "/api/addresses/update-address/route": "app/api/addresses/update-address/route.js", "/api/auth/clear-cookies/route": "app/api/auth/clear-cookies/route.js", "/api/auth/get-token/route": "app/api/auth/get-token/route.js", "/api/auth/set-cookies/route": "app/api/auth/set-cookies/route.js", "/api/cities/route": "app/api/cities/route.js", "/api/categories/route": "app/api/categories/route.js", "/api/countries/route": "app/api/countries/route.js", "/api/auth/update-user-cookies/route": "app/api/auth/update-user-cookies/route.js", "/api/orders/post-order/route": "app/api/orders/post-order/route.js", "/api/orders/history/route": "app/api/orders/history/route.js", "/api/orders/details/route": "app/api/orders/details/route.js", "/api/product-detail/route": "app/api/product-detail/route.js", "/api/payment-methods/route": "app/api/payment-methods/route.js", "/api/products/get-products/route": "app/api/products/get-products/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/product-types/route": "app/api/product-types/route.js", "/api/reviews/check-user-review/route": "app/api/reviews/check-user-review/route.js", "/api/reviews/get-product-reviews/route": "app/api/reviews/get-product-reviews/route.js", "/api/reviews/insert/route": "app/api/reviews/insert/route.js", "/api/test-backend-connection/route": "app/api/test-backend-connection/route.js", "/api/verification/get/route": "app/api/verification/get/route.js", "/api/security/dashboard/route": "app/api/security/dashboard/route.js", "/api/sms/verify-code/route": "app/api/sms/verify-code/route.js", "/api/verification/delete/route": "app/api/verification/delete/route.js", "/api/verification/store/route": "app/api/verification/store/route.js", "/api/verification/verify/route": "app/api/verification/verify/route.js", "/api/sms/send-verification/route": "app/api/sms/send-verification/route.js", "/api/video-proxy/route": "app/api/video-proxy/route.js", "/account/page": "app/account/page.js", "/addresses/page": "app/addresses/page.js", "/about/page": "app/about/page.js", "/cart/page": "app/cart/page.js", "/category/[categoryId]/page": "app/category/[categoryId]/page.js", "/contact/page": "app/contact/page.js", "/follow-us/page": "app/follow-us/page.js", "/checkout/page": "app/checkout/page.js", "/debug-auth/page": "app/debug-auth/page.js", "/login/page": "app/login/page.js", "/orders/[orderId]/page": "app/orders/[orderId]/page.js", "/debug-verification/page": "app/debug-verification/page.js", "/forgot-password/page": "app/forgot-password/page.js", "/hot-deals/page": "app/hot-deals/page.js", "/payment-methods/page": "app/payment-methods/page.js", "/orders/page": "app/orders/page.js", "/page": "app/page.js", "/product/[id]/page": "app/product/[id]/page.js", "/terms/page": "app/terms/page.js", "/signup-twilio/page": "app/signup-twilio/page.js", "/test-coupon/page": "app/test-coupon/page.js", "/signup/page": "app/signup/page.js", "/test-auth-redirect/page": "app/test-auth-redirect/page.js", "/test-local-backend/page": "app/test-local-backend/page.js", "/test-firebase-connection/page": "app/test-firebase-connection/page.js", "/test-coupon-ui/page": "app/test-coupon-ui/page.js", "/test-login-api/page": "app/test-login-api/page.js", "/profile/page": "app/profile/page.js", "/test-modern-toast/page": "app/test-modern-toast/page.js", "/test-login-debug/page": "app/test-login-debug/page.js", "/test-order-coupon/page": "app/test-order-coupon/page.js", "/test-phone-api/page": "app/test-phone-api/page.js", "/test-profile-update/page": "app/test-profile-update/page.js", "/test-register/page": "app/test-register/page.js", "/test-review-api/page": "app/test-review-api/page.js", "/test-payment-details/page": "app/test-payment-details/page.js", "/test-reset-password/page": "app/test-reset-password/page.js", "/test-reviews/page": "app/test-reviews/page.js", "/test-token/page": "app/test-token/page.js", "/test-security/page": "app/test-security/page.js", "/test-user-data/page": "app/test-user-data/page.js", "/test-toast/page": "app/test-toast/page.js", "/test-twilio/page": "app/test-twilio/page.js", "/test-verification-debug/page": "app/test-verification-debug/page.js", "/test-user-debug/page": "app/test-user-debug/page.js", "/test-verification-flow/page": "app/test-verification-flow/page.js", "/wishlist/page": "app/wishlist/page.js", "/products/page": "app/products/page.js", "/products/category/[id]/page": "app/products/category/[id]/page.js"}