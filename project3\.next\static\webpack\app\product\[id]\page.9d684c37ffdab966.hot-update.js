"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./components/products/product-media-gallery.tsx":
/*!*******************************************************!*\
  !*** ./components/products/product-media-gallery.tsx ***!
  \*******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductMediaGallery: () => (/* binding */ ProductMediaGallery)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-out.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zoom-in.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,Image,Pause,Play,Search,Video,ZoomIn,ZoomOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ProductMediaGallery auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ProductMediaGallery(param) {\n    let { media, className } = param;\n    _s();\n    const [currentIndex, setCurrentIndex] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isVideoLoaded, setIsVideoLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isZoomed, setIsZoomed] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [zoomPosition, setZoomPosition] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [zoomLevel, setZoomLevel] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1.5);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [activeMediaType, setActiveMediaType] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('all');\n    // Filter media based on active type\n    const filteredMedia = media.filter((item)=>activeMediaType === 'all' || item.type === activeMediaType);\n    const currentItem = filteredMedia[currentIndex] || media[0];\n    const hasMultipleItems = filteredMedia.length > 1;\n    // Reset index when media changes\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ProductMediaGallery.useEffect\": ()=>{\n            setCurrentIndex(0);\n            setIsPlaying(false);\n        }\n    }[\"ProductMediaGallery.useEffect\"], [\n        activeMediaType,\n        media\n    ]);\n    const goToPrev = ()=>{\n        setCurrentIndex((prev)=>prev === 0 ? filteredMedia.length - 1 : prev - 1);\n        setIsPlaying(false);\n    };\n    const goToNext = ()=>{\n        setCurrentIndex((prev)=>prev === filteredMedia.length - 1 ? 0 : prev + 1);\n        setIsPlaying(false);\n    };\n    const togglePlayPause = ()=>{\n        if (currentItem.type === 'video') {\n            if (videoRef.current) {\n                if (isPlaying) {\n                    videoRef.current.pause();\n                } else {\n                    videoRef.current.play();\n                }\n                setIsPlaying(!isPlaying);\n            }\n        }\n    };\n    const handleVideoEnded = ()=>{\n        setIsPlaying(false);\n        if (hasMultipleItems) {\n            goToNext();\n        }\n    };\n    const handleThumbnailClick = (index)=>{\n        setCurrentIndex(index);\n        setIsPlaying(false);\n        setIsZoomed(false);\n    };\n    const handleImageMouseMove = (e)=>{\n        if (!isZoomed || !imageRef.current) return;\n        const rect = imageRef.current.getBoundingClientRect();\n        const x = (e.clientX - rect.left) / rect.width * 100;\n        const y = (e.clientY - rect.top) / rect.height * 100;\n        setZoomPosition({\n            x,\n            y\n        });\n    };\n    const handleImageClick = ()=>{\n        if (currentItem.type === 'image') {\n            setIsZoomed(!isZoomed);\n        }\n    };\n    const handleImageDoubleClick = ()=>{\n        if (currentItem.type === 'image') {\n            setIsFullscreen(true);\n        }\n    };\n    const increaseZoom = ()=>{\n        setZoomLevel((prev)=>Math.min(prev + 0.5, 4));\n        setIsZoomed(true);\n    };\n    const decreaseZoom = ()=>{\n        setZoomLevel((prev)=>{\n            const newLevel = Math.max(prev - 0.5, 1);\n            if (newLevel === 1) {\n                setIsZoomed(false);\n            }\n            return newLevel;\n        });\n    };\n    const resetZoom = ()=>{\n        setZoomLevel(1.5);\n        setIsZoomed(false);\n    };\n    // Group media by type for filter buttons\n    const mediaCounts = media.reduce((acc, item)=>{\n        acc[item.type] = (acc[item.type] || 0) + 1;\n        return acc;\n    }, {});\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex flex-col gap-4', className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative aspect-square w-full bg-gray-100 rounded-lg overflow-hidden\",\n                children: [\n                    currentItem.type === 'image' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-9ac6182445f1a16e\" + \" \" + \"relative w-full h-full overflow-hidden group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                id: \"9ac6182445f1a16e\",\n                                children: '.cursor-zoom-in.jsx-9ac6182445f1a16e{cursor:url(\\'data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><circle cx=\"11\" cy=\"11\" r=\"8\"/><path d=\"m21 21-4.35-4.35\"/><line x1=\"11\" y1=\"8\" x2=\"11\" y2=\"14\"/><line x1=\"8\" y1=\"11\" x2=\"14\" y2=\"11\"/></svg>\\')12 12,auto}.cursor-zoom-out.jsx-9ac6182445f1a16e{cursor:url(\\'data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><circle cx=\"11\" cy=\"11\" r=\"8\"/><path d=\"m21 21-4.35-4.35\"/><line x1=\"8\" y1=\"11\" x2=\"14\" y2=\"11\"/></svg>\\')12 12,auto}'\n                            }, void 0, false, void 0, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                ref: imageRef,\n                                src: currentItem.url,\n                                alt: currentItem.alt || 'Product image',\n                                style: isZoomed ? {\n                                    transform: \"scale(\".concat(zoomLevel, \")\"),\n                                    transformOrigin: \"\".concat(zoomPosition.x, \"% \").concat(zoomPosition.y, \"%\")\n                                } : {\n                                    transform: 'scale(1)'\n                                },\n                                onClick: handleImageClick,\n                                onDoubleClick: handleImageDoubleClick,\n                                onMouseMove: handleImageMouseMove,\n                                onMouseLeave: ()=>setIsZoomed(false),\n                                className: \"jsx-9ac6182445f1a16e\" + \" \" + ((0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-full h-full object-contain transition-transform duration-300\", isZoomed ? \"cursor-zoom-out\" : \"cursor-zoom-in\") || \"\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"absolute inset-0 flex items-center justify-center pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-white/95 rounded-full p-4 shadow-xl border-2 border-gray-200\",\n                                    children: isZoomed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-8 w-8 text-gray-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"absolute top-2 left-2 flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1\",\n                                        children: isZoomed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-9ac6182445f1a16e\",\n                                                    children: [\n                                                        \"Zoom: \",\n                                                        Math.round(zoomLevel * 100),\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"jsx-9ac6182445f1a16e\",\n                                                    children: \"Click to zoom\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-9ac6182445f1a16e\" + \" \" + \"flex flex-col gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: increaseZoom,\n                                                title: \"Zoom in\",\n                                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: decreaseZoom,\n                                                title: \"Zoom out\",\n                                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: resetZoom,\n                                                title: \"Reset zoom\",\n                                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"bg-white/90 hover:bg-white text-gray-700 rounded p-1 shadow-sm transition-all text-xs\",\n                                                children: \"1:1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsFullscreen(true),\n                                title: \"View fullscreen\",\n                                className: \"jsx-9ac6182445f1a16e\" + \" \" + \"absolute top-2 right-12 bg-white/90 hover:bg-white text-gray-700 rounded p-2 shadow-sm transition-all\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative w-full h-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                ref: videoRef,\n                                src: currentItem.url,\n                                className: \"w-full h-full object-contain\",\n                                controls: false,\n                                onEnded: handleVideoEnded,\n                                onPlay: ()=>setIsPlaying(true),\n                                onPause: ()=>setIsPlaying(false),\n                                onLoadedData: ()=>setIsVideoLoaded(true),\n                                playsInline: true\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this),\n                            !isVideoLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center bg-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-pulse\",\n                                    children: \"Loading video...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: togglePlayPause,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('absolute inset-0 flex items-center justify-center transition-opacity', isPlaying ? 'opacity-0 hover:opacity-100' : 'opacity-80', !isVideoLoaded && 'hidden'),\n                                \"aria-label\": isPlaying ? 'Pause' : 'Play',\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-black/50 text-white rounded-full p-3\",\n                                    children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 30\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 52\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded flex items-center gap-1\",\n                        children: [\n                            currentItem.type === 'image' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                size: 12\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 12\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: currentItem.type === 'image' ? 'Image' : 'Video'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 9\n                    }, this),\n                    hasMultipleItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: goToPrev,\n                                className: \"absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all\",\n                                \"aria-label\": \"Previous media\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: goToNext,\n                                className: \"absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-900 rounded-full p-2 shadow-md transition-all\",\n                                \"aria-label\": \"Next media\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveMediaType('all'),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('px-3 py-1 text-sm rounded-full border', activeMediaType === 'all' ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                        children: [\n                            \"All (\",\n                            media.length,\n                            \")\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this),\n                    mediaCounts.image > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveMediaType('image'),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('px-3 py-1 text-sm rounded-full border flex items-center gap-1', activeMediaType === 'image' ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                size: 14\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: mediaCounts.image\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 11\n                    }, this),\n                    mediaCounts.video > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setActiveMediaType('video'),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('px-3 py-1 text-sm rounded-full border flex items-center gap-1', activeMediaType === 'video' ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                size: 14\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: mediaCounts.video\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this),\n            hasMultipleItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-2 overflow-x-auto pb-2 -mx-2 px-2\",\n                children: filteredMedia.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>handleThumbnailClick(index),\n                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('relative flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 transition-all', index === currentIndex ? 'border-blue-600 ring-2 ring-blue-400' : 'border-gray-200 hover:border-gray-400'),\n                        \"aria-label\": \"View \".concat(item.type, \" \").concat(index + 1),\n                        children: item.type === 'image' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: item.thumbnail || item.url,\n                            alt: item.alt || '',\n                            className: \"w-full h-full object-cover\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full h-full bg-gray-200 overflow-hidden\",\n                            children: [\n                                item.thumbnail ? // Use provided thumbnail if available\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: item.thumbnail,\n                                    alt: item.alt || 'Video thumbnail',\n                                    className: \"w-full h-full object-cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 21\n                                }, this) : // Generate thumbnail from video\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                    src: item.url,\n                                    className: \"w-full h-full object-cover\",\n                                    muted: true,\n                                    playsInline: true,\n                                    preload: \"metadata\",\n                                    onLoadedMetadata: (e)=>{\n                                        const video = e.target;\n                                        video.currentTime = 1; // Seek to 1 second to get a frame\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 flex items-center justify-center bg-black/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/90 rounded-full p-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            size: 12,\n                                            className: \"text-gray-700 ml-0.5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 23\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 17\n                        }, this)\n                    }, index, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                        lineNumber: 339,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 337,\n                columnNumber: 9\n            }, this),\n            isFullscreen && currentItem.type === 'image' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-50 bg-black/95 flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative w-full h-full flex items-center justify-center p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: currentItem.url,\n                            alt: currentItem.alt || 'Product image',\n                            className: \"max-w-full max-h-full object-contain\",\n                            style: {\n                                maxWidth: '95vw',\n                                maxHeight: '95vh'\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsFullscreen(false),\n                            className: \"absolute top-4 right-4 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all\",\n                            title: \"Close fullscreen\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"h-6 w-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 13\n                        }, this),\n                        hasMultipleItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: goToPrev,\n                                    className: \"absolute left-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all\",\n                                    title: \"Previous image\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: goToNext,\n                                    className: \"absolute right-4 top-1/2 -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white rounded-full p-3 transition-all\",\n                                    title: \"Next image\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_Image_Pause_Play_Search_Video_ZoomIn_ZoomOut_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                                    lineNumber: 424,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-4 left-1/2 -translate-x-1/2 bg-black/70 text-white px-3 py-1 rounded-full text-sm\",\n                            children: [\n                                currentIndex + 1,\n                                \" of \",\n                                filteredMedia.length\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n                lineNumber: 394,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\components\\\\products\\\\product-media-gallery.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductMediaGallery, \"0G3bEXxMzDpxYPHFGofm4PcYc2I=\");\n_c = ProductMediaGallery;\nvar _c;\n$RefreshReg$(_c, \"ProductMediaGallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/products/product-media-gallery.tsx\n"));

/***/ })

});