(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5409,7790,8847],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>l,t:()=>n});var o=r(12115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(...e){return t=>{let r=!1,o=e.map(e=>{let o=s(e,t);return r||"function"!=typeof o||(r=!0),o});if(r)return()=>{for(let t=0;t<o.length;t++){let r=o[t];"function"==typeof r?r():s(e[t],null)}}}}function l(...e){return o.useCallback(n(...e),e)}},7703:(e,t,r)=>{Promise.resolve().then(r.bind(r,16224))},16224:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var o=r(95155),s=r(12115),n=r(97168),l=r(89852),a=r(99474),i=r(88482),c=r(38564),d=r(65409);function u(){let[e,t]=(0,s.useState)("101"),[r,u]=(0,s.useState)("Excellent Product"),[m,p]=(0,s.useState)("This product exceeded my expectations."),[f,g]=(0,s.useState)(5),[h,v]=(0,s.useState)("Yahya Yilmaz"),[y,x]=(0,s.useState)("<EMAIL>"),[b,N]=(0,s.useState)("1"),[_,w]=(0,s.useState)(null),[R,T]=(0,s.useState)(!1),E=async()=>{T(!0);try{let t={requestParameters:{ProductID:parseInt(e),Title:r,Body:m,Rating:f,ReviewerName:h,ReviewerEmail:y,UserID:parseInt(b)}};console.log("Sending review data:",t);let o=await (0,d.MakeApiCallAsync)(d.Config.END_POINT_NAMES.INSERT_PRODUCT_REVIEW,null,t,{Accept:"application/json","Content-Type":"application/json"},"POST",!0);console.log("Review API Response:",o),w(o)}catch(e){console.error("Review API error:",e),w({error:e instanceof Error?e.message:"An unknown error occurred"})}finally{T(!1)}};return(0,o.jsx)("div",{className:"container mx-auto p-8",children:(0,o.jsxs)(i.Zp,{className:"max-w-2xl mx-auto p-6",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Review API Test"}),(0,o.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Product ID:"}),(0,o.jsx)(l.p,{type:"number",value:e,onChange:e=>t(e.target.value),placeholder:"Product ID"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium mb-2",children:"User ID:"}),(0,o.jsx)(l.p,{type:"number",value:b,onChange:e=>N(e.target.value),placeholder:"User ID"})]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Review Title:"}),(0,o.jsx)(l.p,{type:"text",value:r,onChange:e=>u(e.target.value),placeholder:"Review title"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Review Body:"}),(0,o.jsx)(a.T,{value:m,onChange:e=>p(e.target.value),placeholder:"Review content",rows:3})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Rating:"}),(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[[1,2,3,4,5].map(e=>(0,o.jsx)("button",{type:"button",onClick:()=>g(e),className:"p-1",children:(0,o.jsx)(c.A,{className:"h-6 w-6 ".concat(e<=f?"fill-yellow-400 text-yellow-400":"text-gray-300")})},e)),(0,o.jsxs)("span",{className:"ml-2 text-sm",children:["(",f," stars)"]})]})]}),(0,o.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Reviewer Name:"}),(0,o.jsx)(l.p,{type:"text",value:h,onChange:e=>v(e.target.value),placeholder:"Reviewer name"})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Reviewer Email:"}),(0,o.jsx)(l.p,{type:"email",value:y,onChange:e=>x(e.target.value),placeholder:"Reviewer email"})]})]}),(0,o.jsx)(n.$,{onClick:E,disabled:R||!e||!r||!m||!h||!y||!b,className:"w-full",children:R?"Submitting Review...":"Test Review API"})]}),_&&(0,o.jsxs)("div",{className:"mt-6",children:[(0,o.jsx)("h3",{className:"text-lg font-semibold mb-3",children:"API Response:"}),(0,o.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96",children:JSON.stringify(_,null,2)})]}),(0,o.jsxs)("div",{className:"mt-4 text-sm text-gray-600",children:[(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"API Endpoint:"})," Insert-Product-Review"]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Method:"})," POST"]}),(0,o.jsxs)("p",{children:[(0,o.jsx)("strong",{children:"Note:"})," This tests the product review submission functionality"]})]})]})})}},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(12115);let s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,o.forwardRef)((e,t)=>{let{color:r="currentColor",size:l=24,strokeWidth:a=2,absoluteStrokeWidth:i,className:c="",children:d,iconNode:u,...m}=e;return(0,o.createElement)("svg",{ref:t,...n,width:l,height:l,stroke:r,strokeWidth:i?24*Number(a)/Number(l):a,className:s("lucide",c),...m},[...u.map(e=>{let[t,r]=e;return(0,o.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),a=(e,t)=>{let r=(0,o.forwardRef)((r,n)=>{let{className:a,...i}=r;return(0,o.createElement)(l,{ref:n,iconNode:t,className:s("lucide-".concat(e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),a),...i})});return r.displayName="".concat(e),r}},38564:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(19946).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var o=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,o.$)(t))}},61204:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});let o={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",COMMON_CONTROLLER_SUB_URL:"api/v1/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile"}}},65409:(e,t,r)=>{"use strict";r.d(t,{$g:()=>u,Config:()=>n,MakeApiCallAsync:()=>i,XX:()=>d,k6:()=>c});var o=r(23464),s=r(61204);o.A.defaults.timeout=3e4,"https:"===window.location.protocol&&s.T.ADMIN_BASE_URL.includes("localhost")&&(o.A.defaults.httpsAgent={rejectUnauthorized:!1});let n={ADMIN_BASE_URL:s.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...s.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",RESET_PASSWORD_FIREBASE:"reset-password-firebase",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile",INSERT_PRODUCT_REVIEW:"Insert-Product-Review",GET_PRODUCT_REVIEWS:"get-product-reviews"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},l=async()=>{try{{try{let e=await fetch("/api/auth/get-token",{method:"GET",credentials:"include"});if(e.ok){let t=await e.json();if(t.token)return console.log("\uD83D\uDD10 Retrieved JWT token from secure HttpOnly cookie"),t.token}}catch(e){console.log("API token retrieval failed, trying client-side cookies:",e)}for(let e of document.cookie.split(";")){let[t,r]=e.trim().split("=");if("auth_token"===t)return console.log("\uD83D\uDD10 Retrieved JWT token from client-side cookie"),decodeURIComponent(r)}let e=localStorage.getItem("token")||localStorage.getItem("authToken");if(e)return console.log("⚠️ Using token from localStorage (migrating to secure cookies)"),localStorage.removeItem("token"),localStorage.removeItem("authToken"),e}return null}catch(e){return console.error("Error getting token for header:",e),null}},a=async()=>{try{{for(let r of document.cookie.split(";")){let[o,s]=r.trim().split("=");if("auth_user"===o)try{var e,t;let r=JSON.parse(decodeURIComponent(s)),o=(null==(e=r.UserId)?void 0:e.toString())||(null==(t=r.UserID)?void 0:t.toString());if(o)return console.log("\uD83D\uDD10 Retrieved User ID from secure cookie"),o}catch(e){console.warn("Failed to parse user data from cookie:",e)}}let r=localStorage.getItem("userId")||localStorage.getItem("userID");if(r)return console.log("⚠️ Using User ID from localStorage (migrating to secure cookies)"),localStorage.removeItem("userId"),localStorage.removeItem("userID"),r}return null}catch(e){return console.error("Error getting user ID for header:",e),null}},i=async function(e,t,r,s,i){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let c=(e=>{if(!e)return e;let t=JSON.parse(JSON.stringify(e));return t.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from request body (will use JWT token instead)"),delete t.UserId),t.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from request body (will use JWT token instead)"),delete t.UserID),t.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from request body (will use JWT token instead)"),delete t.user_id),t.requestParameters&&(t.requestParameters.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from requestParameters (will use JWT token instead)"),delete t.requestParameters.UserId),t.requestParameters.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from requestParameters (will use JWT token instead)"),delete t.requestParameters.UserID),t.requestParameters.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from requestParameters (will use JWT token instead)"),delete t.requestParameters.user_id)),t})(r),d={...s};if(!d.hasOwnProperty("Authorization")){let e=await l();e&&(d.Authorization="Bearer "+e,console.log("\uD83D\uDD10 Added JWT token to Authorization header"))}if(!d.hasOwnProperty("Token")){let e=await l();d.Token=null!=e?e:"",e&&console.log("\uD83D\uDD10 Added JWT token to Token header (backward compatibility)")}if(!d.hasOwnProperty("UserID")){let e=await a();d.UserID=null!=e?e:""}d.hasOwnProperty("Accept")||(d.Accept="application/json"),d.hasOwnProperty("Content-Type")||(d["Content-Type"]="application/json");let u=n.ADMIN_BASE_URL+(null===t||void 0==t?n.DYNAMIC_METHOD_SUB_URL:t)+e;i=null!=i?i:"POST";let m={headers:d,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===i)return await o.A.post(u,c,m);if("GET"==i)return m.params=c,await o.A.get(u,m);return{data:{errorMessage:"Unsupported method type: ".concat(i),status:"method_not_supported"}}}catch(t){console.error("API call failed:",t);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(t&&"object"==typeof t&&"response"in t&&t.response){var c,d;let r=null==(c=t.response)?void 0:c.data;e.data={errorMessage:(null==r?void 0:r.errorMessage)||"An error occurred while processing your request.",status:null==(d=t.response)?void 0:d.status}}else if(t&&"object"==typeof t&&"request"in t){let r="Network error: No response received from server.";t.message&&t.message.includes("Network Error")&&(r="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+n.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:r,status:"network_error"}}else e.data={errorMessage:t&&"object"==typeof t&&"message"in t?t.message:"An unexpected error occurred",status:"request_error"};return e}},c=async()=>{try{return console.log("Using default currency rate (1430) - API endpoint not available"),1430}catch(e){return console.error("Error fetching currency rate:",e),1430}},d=(e,t)=>Math.round(e*t),u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return null==e||isNaN(e)?"IQD"===t?"0 IQD":"$0.00":"IQD"===t?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var o=r(52596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=o.$,l=(e,t)=>r=>{var o;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:a}=t,i=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],o=null==a?void 0:a[e];if(null===t)return null;let n=s(t)||s(o);return l[e][n]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,o]=t;return void 0===o||(e[r]=o),e},{});return n(e,i,null==t||null==(o=t.compoundVariants)?void 0:o.reduce((e,t)=>{let{class:r,className:o,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...c}[t]):({...a,...c})[t]===r})?[...e,r,o]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>i,Zp:()=>l,aR:()=>a,wL:()=>u});var o=r(95155),s=r(12115),n=r(53999);let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});l.displayName="Card";let a=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...s})});a.displayName="CardHeader";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});i.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...s})});d.displayName="CardContent";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...s})});u.displayName="CardFooter"},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var o=r(95155),s=r(12115),n=r(53999);let l=s.forwardRef((e,t)=>{let{className:r,type:s,...l}=e;return(0,o.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...l})});l.displayName="Input"},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>i});var o=r(95155),s=r(12115),n=r(99708),l=r(74466),a=r(53999);let i=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:r,variant:s,size:l,asChild:c=!1,...d}=e,u=c?n.DX:"button";return(0,o.jsx)(u,{className:(0,a.cn)(i({variant:s,size:l,className:r})),ref:t,...d})});c.displayName="Button"},99474:(e,t,r)=>{"use strict";r.d(t,{T:()=>l});var o=r(95155),s=r(12115),n=r(53999);let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,o.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...s})});l.displayName="Textarea"},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>a,Dc:()=>c,TL:()=>l});var o=r(12115),s=r(6101),n=r(95155);function l(e){let t=function(e){let t=o.forwardRef((e,t)=>{let{children:r,...n}=e;if(o.isValidElement(r)){var l;let e,a,i=(l=r,(a=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(a=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,t){let r={...t};for(let o in t){let s=e[o],n=t[o];/^on[A-Z]/.test(o)?s&&n?r[o]=(...e)=>{let t=n(...e);return s(...e),t}:s&&(r[o]=s):"style"===o?r[o]={...s,...n}:"className"===o&&(r[o]=[s,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==o.Fragment&&(c.ref=t?(0,s.t)(t,i):i),o.cloneElement(r,c)}return o.Children.count(r)>1?o.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=o.forwardRef((e,r)=>{let{children:s,...l}=e,a=o.Children.toArray(s),i=a.find(d);if(i){let e=i.props.children,s=a.map(t=>t!==i?t:o.Children.count(e)>1?o.Children.only(null):o.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...l,ref:r,children:o.isValidElement(e)?o.cloneElement(e,void 0,s):null})}return(0,n.jsx)(t,{...l,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var a=l("Slot"),i=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=i,t}function d(e){return o.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}}},e=>{e.O(0,[4277,3464,8441,5964,7358],()=>e(e.s=7703)),_N_E=e.O()}]);