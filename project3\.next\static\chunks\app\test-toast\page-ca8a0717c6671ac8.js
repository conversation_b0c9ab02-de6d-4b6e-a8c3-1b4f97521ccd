(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5309],{1779:(e,t,r)=>{Promise.resolve().then(r.bind(r,38196))},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>o});var n=r(12115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=s(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():s(e[t],null)}}}}function i(...e){return n.useCallback(o(...e),e)}},14503:(e,t,r)=>{"use strict";r.d(t,{dj:()=>u,oR:()=>d});var n=r(12115);let s=0,o=new Map,i=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},a=[],l={toasts:[]};function c(e){l=((e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}})(l,e),a.forEach(e=>{e(l)})}function d(e){let{duration:t=2e3,...r}=e,n=(s=(s+1)%100).toString(),o=()=>c({type:"DISMISS_TOAST",toastId:n});return c({type:"ADD_TOAST",toast:{...r,id:n,duration:t,open:!0,onOpenChange:e=>{e||o()}}}),setTimeout(()=>{o()},t),{id:n,dismiss:o,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:n}})}}function u(){let[e,t]=n.useState(l);return n.useEffect(()=>(a.push(t),()=>{let e=a.indexOf(t);e>-1&&a.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}d.success=(e,t)=>d({description:e,type:"success",duration:2e3,...t}),d.error=(e,t)=>d({description:e,type:"error",duration:2e3,...t}),d.warning=(e,t)=>d({description:e,type:"warning",duration:2e3,...t}),d.info=(e,t)=>d({description:e,type:"info",duration:2e3,...t})},38196:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(95155),s=r(97168),o=r(14503),i=r(56671);function a(){return(0,n.jsxs)("div",{className:"container mx-auto p-8",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Toast Test Page"}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("h2",{className:"text-2xl font-semibold mb-4",children:"Custom Toast System"}),(0,n.jsxs)("div",{className:"flex gap-4 flex-wrap",children:[(0,n.jsx)(s.$,{onClick:()=>o.oR.success("Success! Item added to cart"),className:"bg-green-600 hover:bg-green-700",children:"Test Success Toast"}),(0,n.jsx)(s.$,{onClick:()=>o.oR.error("Error! Failed to add item"),className:"bg-red-600 hover:bg-red-700",children:"Test Error Toast"}),(0,n.jsx)(s.$,{onClick:()=>o.oR.warning("Warning! Low stock"),className:"bg-yellow-600 hover:bg-yellow-700",children:"Test Warning Toast"}),(0,n.jsx)(s.$,{onClick:()=>o.oR.info("Info! Maximum quantity reached"),className:"bg-blue-600 hover:bg-blue-700",children:"Test Info Toast"})]}),(0,n.jsx)("h2",{className:"text-2xl font-semibold mb-4 mt-8",children:"Sonner Toast System"}),(0,n.jsxs)("div",{className:"flex gap-4 flex-wrap",children:[(0,n.jsx)(s.$,{onClick:()=>i.oR.success("Success! Item added to cart"),className:"bg-green-600 hover:bg-green-700",children:"Test Sonner Success"}),(0,n.jsx)(s.$,{onClick:()=>i.oR.error("Error! Failed to add item"),className:"bg-red-600 hover:bg-red-700",children:"Test Sonner Error"}),(0,n.jsx)(s.$,{onClick:()=>i.oR.warning("Warning! Low stock"),className:"bg-yellow-600 hover:bg-yellow-700",children:"Test Sonner Warning"}),(0,n.jsx)(s.$,{onClick:()=>i.oR.info("Info! Maximum quantity reached"),className:"bg-blue-600 hover:bg-blue-700",children:"Test Sonner Info"})]}),(0,n.jsxs)("div",{className:"mt-8 p-4 bg-gray-100 rounded-lg",children:[(0,n.jsx)("h3",{className:"font-semibold mb-2",children:"Expected Behavior:"}),(0,n.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,n.jsx)("li",{children:"Success toasts should have green background with white text"}),(0,n.jsx)("li",{children:"Error toasts should have red background with white text"}),(0,n.jsx)("li",{children:"Warning toasts should have yellow background with white text"}),(0,n.jsx)("li",{children:"Info toasts should have blue background with white text"}),(0,n.jsx)("li",{children:"All toasts should auto-dismiss after 2 seconds"})]})]})]})]})}},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var n=r(52596),s=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,n.$)(t))}},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>l});var n=r(95155),s=r(12115),o=r(99708),i=r(74466),a=r(53999);let l=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:r,variant:s,size:i,asChild:c=!1,...d}=e,u=c?o.DX:"button";return(0,n.jsx)(u,{className:(0,a.cn)(l({variant:s,size:i,className:r})),ref:t,...d})});c.displayName="Button"},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>a,Dc:()=>c,TL:()=>i});var n=r(12115),s=r(6101),o=r(95155);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var i;let e,a,l=(i=r,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let r={...t};for(let n in t){let s=e[n],o=t[n];/^on[A-Z]/.test(n)?s&&o?r[n]=(...e)=>{let t=o(...e);return s(...e),t}:s&&(r[n]=s):"style"===n?r[n]={...s,...o}:"className"===n&&(r[n]=[s,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,s.t)(t,l):l),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:s,...i}=e,a=n.Children.toArray(s),l=a.find(d);if(l){let e=l.props.children,s=a.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,o.jsx)(t,{...i,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var a=i("Slot"),l=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=l,t}function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}}},e=>{e.O(0,[4277,6774,8441,5964,7358],()=>e(e.s=1779)),_N_E=e.O()}]);