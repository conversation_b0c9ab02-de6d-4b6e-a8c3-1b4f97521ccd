(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9364],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>l});var s=r(12115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,s=e.map(e=>{let s=n(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():n(e[t],null)}}}}function i(...e){return s.useCallback(l(...e),e)}},40224:(e,t,r)=>{Promise.resolve().then(r.bind(r,62979))},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>l});var s=r(52596),n=r(39688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,s.$)(t))}},62979:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(95155),n=r(12115),l=r(97168),i=r(89852),a=r(88482);function o(){let[e,t]=(0,n.useState)(""),[r,o]=(0,n.useState)(""),[d,c]=(0,n.useState)(null),[u,f]=(0,n.useState)(!1),p=async()=>{f(!0),c(null);try{let t=await fetch("/api/sms/send-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:e})}),r=await t.json();c({status:t.status,data:r,type:"send"})}catch(e){c({status:"error",data:{error:e instanceof Error?e.message:"Unknown error"},type:"send"})}finally{f(!1)}},m=async()=>{f(!0),c(null);try{let t=await fetch("/api/sms/verify-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:e,code:r})}),s=await t.json();c({status:t.status,data:s,type:"verify"})}catch(e){c({status:"error",data:{error:e instanceof Error?e.message:"Unknown error"},type:"verify"})}finally{f(!1)}},h=async()=>{f(!0),c(null);try{let e=await fetch("/api/sms/send-verification",{method:"POST",headers:{"Content-Type":"application/json","User-Agent":"curl/7.68.0"},body:JSON.stringify({phoneNumber:"+1234567890"})}),t=await e.json();c({status:e.status,data:t,type:"bot-test"})}catch(e){c({status:"error",data:{error:e instanceof Error?e.message:"Unknown error"},type:"bot-test"})}finally{f(!1)}},x=async()=>{f(!0),c(null);try{let e=await fetch("/api/sms/send-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:"+1111111111"})}),t=await e.json();c({status:e.status,data:t,type:"suspicious-phone"})}catch(e){c({status:"error",data:{error:e instanceof Error?e.message:"Unknown error"},type:"suspicious-phone"})}finally{f(!1)}};return(0,s.jsxs)("div",{className:"container mx-auto p-8 max-w-4xl",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"SMS Security Test Page"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)(a.Zp,{className:"p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Normal SMS Test"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(i.p,{placeholder:"Phone number (e.g., +1234567890)",value:e,onChange:e=>t(e.target.value)}),(0,s.jsx)(l.$,{onClick:p,disabled:u||!e,className:"w-full",children:"Send Verification SMS"})]})]}),(0,s.jsxs)(a.Zp,{className:"p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Verification Test"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(i.p,{placeholder:"Verification code",value:r,onChange:e=>o(e.target.value)}),(0,s.jsx)(l.$,{onClick:m,disabled:u||!r||!e,className:"w-full",children:"Verify Code"})]})]}),(0,s.jsxs)(a.Zp,{className:"p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Security Tests"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(l.$,{onClick:h,disabled:u,className:"w-full",variant:"outline",children:"Test Bot Detection"}),(0,s.jsx)(l.$,{onClick:x,disabled:u,className:"w-full",variant:"outline",children:"Test Suspicious Phone"})]})]}),(0,s.jsxs)(a.Zp,{className:"p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Results"}),d?(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("strong",{children:"Status:"})," ",d.status]}),(0,s.jsxs)("div",{className:"text-sm",children:[(0,s.jsx)("strong",{children:"Type:"})," ",d.type]}),(0,s.jsx)("pre",{className:"bg-gray-100 p-3 rounded text-xs overflow-auto",children:JSON.stringify(d.data,null,2)})]}):(0,s.jsx)("p",{className:"text-gray-500",children:"No results yet. Run a test above."})]})]}),(0,s.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"Security Features Being Tested:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,s.jsx)("li",{children:"Rate limiting (3 attempts per 15 minutes per phone)"}),(0,s.jsx)("li",{children:"Bot detection (user agent analysis)"}),(0,s.jsx)("li",{children:"Phone number validation (format and suspicious patterns)"}),(0,s.jsx)("li",{children:"Origin validation (CORS and referer checks)"}),(0,s.jsx)("li",{children:"IP address monitoring"}),(0,s.jsx)("li",{children:"Verification code security (3 attempts max)"})]})]}),(0,s.jsxs)("div",{className:"mt-4 p-4 bg-yellow-50 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"Expected Behaviors:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,s.jsx)("li",{children:"Normal SMS should work with valid phone numbers"}),(0,s.jsx)("li",{children:"Bot detection test should return 403 (blocked)"}),(0,s.jsx)("li",{children:"Suspicious phone test should return 400 (invalid)"}),(0,s.jsx)("li",{children:"After 3 SMS attempts, you should get rate limited"}),(0,s.jsx)("li",{children:"After 3 wrong verification codes, code should be invalidated"})]})]})]})}},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var s=r(52596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=s.$,i=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return l(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:a}=t,o=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],s=null==a?void 0:a[e];if(null===t)return null;let l=n(t)||n(s);return i[e][l]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return l(e,o,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...d}[t]):({...a,...d})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>a,wL:()=>u});var s=r(95155),n=r(12115),l=r(53999);let i=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...n})});i.displayName="Card";let a=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...n})});a.displayName="CardHeader";let o=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...n})});o.displayName="CardTitle";let d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...n})});d.displayName="CardDescription";let c=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",r),...n})});c.displayName="CardContent";let u=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",r),...n})});u.displayName="CardFooter"},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(95155),n=r(12115),l=r(53999);let i=n.forwardRef((e,t)=>{let{className:r,type:n,...i}=e;return(0,s.jsx)("input",{type:n,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...i})});i.displayName="Input"},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>o});var s=r(95155),n=r(12115),l=r(99708),i=r(74466),a=r(53999);let o=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,t)=>{let{className:r,variant:n,size:i,asChild:d=!1,...c}=e,u=d?l.DX:"button";return(0,s.jsx)(u,{className:(0,a.cn)(o({variant:n,size:i,className:r})),ref:t,...c})});d.displayName="Button"},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>a,Dc:()=>d,TL:()=>i});var s=r(12115),n=r(6101),l=r(95155);function i(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...l}=e;if(s.isValidElement(r)){var i;let e,a,o=(i=r,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,t){let r={...t};for(let s in t){let n=e[s],l=t[s];/^on[A-Z]/.test(s)?n&&l?r[s]=(...e)=>{let t=l(...e);return n(...e),t}:n&&(r[s]=n):"style"===s?r[s]={...n,...l}:"className"===s&&(r[s]=[n,l].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props);return r.type!==s.Fragment&&(d.ref=t?(0,n.t)(t,o):o),s.cloneElement(r,d)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:n,...i}=e,a=s.Children.toArray(n),o=a.find(c);if(o){let e=o.props.children,n=a.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,l.jsx)(t,{...i,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var a=i("Slot"),o=Symbol("radix.slottable");function d(e){let t=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{e.O(0,[4277,8441,5964,7358],()=>e(e.s=40224)),_N_E=e.O()}]);