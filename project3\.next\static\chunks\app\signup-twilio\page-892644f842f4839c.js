(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2146],{19420:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>n});var s=r(12115),a=r(63655),i=r(95155),l=s.forwardRef((e,t)=>(0,i.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var n=l},43453:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},49026:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>d,TN:()=>c});var s=r(95155),a=r(12115),i=r(74466),l=r(53999);let n=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-red-500 dark:border-destructive [&>svg]:text-red-500"}},defaultVariants:{variant:"default"}}),d=a.forwardRef((e,t)=>{let{className:r,variant:a,...i}=e;return(0,s.jsx)("div",{ref:t,role:"alert",className:(0,l.cn)(n({variant:a}),r),...i})});d.displayName="Alert",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h5",{ref:t,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",r),...a})}).displayName="AlertTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",r),...a})});c.displayName="AlertDescription"},51154:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(52596),a=r(39688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},57340:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},63655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>d,sG:()=>n});var s=r(12115),a=r(47650),i=r(99708),l=r(95155),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),a=s.forwardRef((e,s)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a?r:t,{...i,ref:s})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function d(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},71007:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var s=r(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=s.$,l=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:n}=t,d=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],s=null==n?void 0:n[e];if(null===t)return null;let i=a(t)||a(s);return l[e][i]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return i(e,d,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...n,...c}[t]):({...n,...c})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},75441:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var s=r(95155),a=r(12115),i=r(88482),l=r(97168),n=r(89852),d=r(82714),c=r(49026),o=r(1978),u=r(6874),m=r.n(u),f=r(57340),h=r(71007),x=r(78749),p=r(92657),g=r(92138),v=r(43453),y=r(19420),b=r(75525),j=r(51154);async function N(e){try{let t=await fetch("/api/sms/send-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:e})}),r=await t.json();if(!t.ok)return{success:!1,message:r.error||"Failed to send verification code",error:r.error};return{success:!0,message:r.message,messageId:r.messageId}}catch(e){return console.error("Error sending SMS verification:",e),{success:!1,message:"Network error occurred",error:"Network error"}}}async function w(e,t){try{let r=await fetch("/api/sms/verify-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:e,code:t})}),s=await r.json();if(!r.ok)return{success:!1,message:s.error||"Verification failed",error:s.error};return{success:!0,message:s.message}}catch(e){return console.error("Error verifying SMS code:",e),{success:!1,message:"Network error occurred",error:"Network error"}}}function k(e){let{onVerificationSuccess:t,onError:r}=e,[d,o]=(0,a.useState)("phone"),[u,m]=(0,a.useState)(""),[f,h]=(0,a.useState)(""),[x,p]=(0,a.useState)(!1),[g,v]=(0,a.useState)(""),[k,C]=(0,a.useState)(""),A=async()=>{if(!u.trim())return void v("Please enter a phone number");let e=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"+1",r=e.replace(/\D/g,"");return r.startsWith(t.replace("+",""))?"+".concat(r):"".concat(t).concat(r)}(u);if(!/^\+[1-9]\d{1,14}$/.test(e))return void v("Please enter a valid phone number");p(!0),v(""),C("");try{let t=await N(e);t.success?(C("Verification code sent successfully!"),o("code"),m(e)):(v(t.message),null==r||r(t.message))}catch(t){let e="Failed to send verification code";v(e),null==r||r(e)}finally{p(!1)}},S=async()=>{if(!f.trim())return void v("Please enter the verification code");if(6!==f.length)return void v("Verification code must be 6 digits");p(!0),v("");try{let e=await w(u,f);e.success?(C("Phone number verified successfully!"),t(u)):(v(e.message),null==r||r(e.message))}catch(t){let e="Failed to verify code";v(e),null==r||r(e)}finally{p(!1)}},P=async()=>{h(""),await A()};return(0,s.jsxs)(i.Zp,{className:"w-full max-w-md mx-auto",children:[(0,s.jsxs)(i.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4",children:"phone"===d?(0,s.jsx)(y.A,{className:"w-6 h-6 text-primary"}):(0,s.jsx)(b.A,{className:"w-6 h-6 text-primary"})}),(0,s.jsx)(i.ZB,{children:"phone"===d?"Verify Phone Number":"Enter Verification Code"}),(0,s.jsx)(i.BT,{children:"phone"===d?"We'll send you a verification code via SMS":"Enter the 6-digit code sent to ".concat(u)})]}),(0,s.jsxs)(i.Wu,{className:"space-y-4",children:[g&&(0,s.jsx)(c.Fc,{variant:"destructive",children:(0,s.jsx)(c.TN,{children:g})}),k&&(0,s.jsx)(c.Fc,{children:(0,s.jsx)(c.TN,{children:k})}),"phone"===d?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium mb-2",children:"Phone Number"}),(0,s.jsx)(n.p,{id:"phone",type:"tel",placeholder:"+****************",value:u,onChange:e=>m(e.target.value),disabled:x,autoComplete:"tel",inputMode:"tel",className:"h-12 md:h-10 text-lg md:text-base"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Include country code (e.g., +1 for US)"})]}),(0,s.jsx)(l.$,{onClick:A,disabled:x,className:"w-full",children:x?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Sending Code..."]}):"Send Verification Code"})]}):(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"code",className:"block text-sm font-medium mb-2",children:"Verification Code"}),(0,s.jsx)(n.p,{id:"code",type:"text",placeholder:"123456",value:f,onChange:e=>h(e.target.value.replace(/\D/g,"").slice(0,6)),disabled:x,maxLength:6,autoComplete:"one-time-code",inputMode:"numeric",className:"text-center text-xl md:text-base h-16 md:h-10 tracking-widest font-mono px-4"})]}),(0,s.jsx)(l.$,{onClick:S,disabled:x||6!==f.length,className:"w-full",children:x?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(j.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Verifying..."]}):"Verify Code"}),(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)(l.$,{variant:"ghost",onClick:()=>{o("phone"),h(""),v(""),C("")},disabled:x,children:"Change Number"}),(0,s.jsx)(l.$,{variant:"ghost",onClick:P,disabled:x,children:"Resend Code"})]})]})]})]})}function C(){let[e,t]=(0,a.useState)("phone"),[r,u]=(0,a.useState)(""),[y,b]=(0,a.useState)(!1),[j,N]=(0,a.useState)(""),[w,C]=(0,a.useState)(!1),[A,S]=(0,a.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:""}),P=async e=>{if(e.preventDefault(),b(!0),N(""),!A.firstName.trim()){N("First name is required"),b(!1);return}if(!A.lastName.trim()){N("Last name is required"),b(!1);return}if(!A.email.trim()){N("Email is required"),b(!1);return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(A.email)){N("Please enter a valid email address"),b(!1);return}if(!A.password){N("Password is required"),b(!1);return}if(A.password.length<8){N("Password must be at least 8 characters long"),b(!1);return}if(A.password!==A.confirmPassword){N("Passwords do not match"),b(!1);return}try{let e={...A,phoneNumber:r,phoneVerified:!0};console.log("Registration data:",e),await new Promise(e=>setTimeout(e,2e3)),t("success")}catch(e){N("Registration failed. Please try again."),console.error("Registration error:",e)}finally{b(!1)}},F=(e,t)=>{S(r=>({...r,[e]:t}))};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsxs)(m(),{href:"/",className:"inline-flex items-center text-primary hover:text-primary/80 mb-4",children:[(0,s.jsx)(f.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Create Account"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Join CodeMedical today"})]}),(0,s.jsx)("div",{className:"flex justify-center mb-8",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ".concat("phone"===e?"bg-primary text-white":"details"===e||"success"===e?"bg-green-500 text-white":"bg-gray-200 text-gray-600"),children:"1"}),(0,s.jsx)("div",{className:"w-16 h-1 ".concat("details"===e||"success"===e?"bg-green-500":"bg-gray-200")}),(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ".concat("details"===e?"bg-primary text-white":"success"===e?"bg-green-500 text-white":"bg-gray-200 text-gray-600"),children:"2"}),(0,s.jsx)("div",{className:"w-16 h-1 ".concat("success"===e?"bg-green-500":"bg-gray-200")}),(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ".concat("success"===e?"bg-green-500 text-white":"bg-gray-200 text-gray-600"),children:"3"})]})}),(0,s.jsxs)(o.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:["phone"===e&&(0,s.jsx)(k,{onVerificationSuccess:e=>{u(e),t("details"),N("")},onError:e=>{N(e)}}),"details"===e&&(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(h.A,{className:"w-6 h-6 text-primary"})}),(0,s.jsx)(i.ZB,{children:"Personal Details"}),(0,s.jsx)(i.BT,{children:"Complete your profile information"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)("form",{onSubmit:P,className:"space-y-4",children:[j&&(0,s.jsx)(c.Fc,{variant:"destructive",children:(0,s.jsx)(c.TN,{children:j})}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"firstName",children:"First Name"}),(0,s.jsx)(n.p,{id:"firstName",type:"text",value:A.firstName,onChange:e=>F("firstName",e.target.value),disabled:y,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"lastName",children:"Last Name"}),(0,s.jsx)(n.p,{id:"lastName",type:"text",value:A.lastName,onChange:e=>F("lastName",e.target.value),disabled:y,required:!0})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"email",children:"Email Address"}),(0,s.jsx)(n.p,{id:"email",type:"email",value:A.email,onChange:e=>F("email",e.target.value),disabled:y,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"password",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.p,{id:"password",type:w?"text":"password",value:A.password,onChange:e=>F("password",e.target.value),disabled:y,required:!0}),(0,s.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>C(!w),children:w?(0,s.jsx)(x.A,{className:"h-4 w-4"}):(0,s.jsx)(p.A,{className:"h-4 w-4"})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(d.J,{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,s.jsx)(n.p,{id:"confirmPassword",type:"password",value:A.confirmPassword,onChange:e=>F("confirmPassword",e.target.value),disabled:y,required:!0})]}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded-md",children:[(0,s.jsx)("strong",{children:"Phone:"})," ",r," ✓ Verified"]}),(0,s.jsxs)(l.$,{type:"submit",disabled:y,className:"w-full",children:[y?"Creating Account...":"Create Account",(0,s.jsx)(g.A,{className:"w-4 h-4 ml-2"})]})]})})]}),"success"===e&&(0,s.jsx)(i.Zp,{children:(0,s.jsxs)(i.Wu,{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6",children:(0,s.jsx)(v.A,{className:"w-8 h-8 text-green-600"})}),(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Account Created!"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"Your account has been successfully created. You can now sign in with your credentials."}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(l.$,{asChild:!0,className:"w-full",children:(0,s.jsxs)(m(),{href:"/login",children:["Sign In Now",(0,s.jsx)(g.A,{className:"w-4 h-4 ml-2"})]})}),(0,s.jsx)(l.$,{variant:"outline",asChild:!0,className:"w-full",children:(0,s.jsxs)(m(),{href:"/",children:[(0,s.jsx)(f.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]})})]})]})})]},e),(0,s.jsx)("div",{className:"text-center mt-8",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,s.jsx)(m(),{href:"/login",className:"text-primary hover:text-primary/80 font-medium",children:"Sign in here"})]})})]})})}},75525:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},78749:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},82714:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var s=r(95155),a=r(12115),i=r(40968),l=r(74466),n=r(53999);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(i.b,{ref:t,className:(0,n.cn)(d(),r),...a})});c.displayName=i.b.displayName},83750:(e,t,r)=>{Promise.resolve().then(r.bind(r,75441))},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>l,aR:()=>n,wL:()=>u});var s=r(95155),a=r(12115),i=r(53999);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});l.displayName="Card";let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",r),...a})});n.displayName="CardHeader";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});d.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",r),...a})});o.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var s=r(95155),a=r(12115),i=r(53999);let l=a.forwardRef((e,t)=>{let{className:r,type:a,...l}=e;return(0,s.jsx)("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...l})});l.displayName="Input"},92138:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>d});var s=r(95155),a=r(12115),i=r(99708),l=r(74466),n=r(53999);let d=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:l,asChild:c=!1,...o}=e,u=c?i.DX:"button";return(0,s.jsx)(u,{className:(0,n.cn)(d({variant:a,size:l,className:r})),ref:t,...o})});c.displayName="Button"}},e=>{e.O(0,[4277,4706,4042,8441,5964,7358],()=>e(e.s=83750)),_N_E=e.O()}]);