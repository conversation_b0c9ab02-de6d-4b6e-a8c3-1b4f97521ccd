"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4042],{1978:(t,e,i)=>{let s;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}i.d(e,{P:()=>rn});let r=t=>Array.isArray(t);function o(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function a(t){return"string"==typeof t||Array.isArray(t)}function l(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function u(t,e,i,s){if("function"==typeof e){let[n,r]=l(s);e=e(void 0!==i?i:t.custom,n,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,r]=l(s);e=e(void 0!==i?i:t.custom,n,r)}return e}function h(t,e,i){let s=t.getProps();return u(s,e,void 0!==i?i:s.custom,t)}let d=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],c=["initial",...d];function p(t){let e;return()=>(void 0===e&&(e=t()),e)}let m=p(()=>void 0!==window.ScrollTimeline);class f{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>m()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class v extends f{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function g(t,e){return t?t[e]||t.default||t:void 0}function y(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function x(t){return"function"==typeof t}function w(t,e){t.timeline=e,t.onfinish=null}let P=t=>Array.isArray(t)&&"number"==typeof t[0],T={linearEasing:void 0},b=function(t,e){let i=p(t);return()=>{var t;return null!=(t=T[e])?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),S=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s},A=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=t(S(0,n-1,e))+", ";return`linear(${s.substring(0,s.length-2)})`},V=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,E={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:V([0,.65,.55,1]),circOut:V([.55,0,1,.45]),backIn:V([.31,.01,.66,-.59]),backOut:V([.33,1.53,.69,.99])},M={x:!1,y:!1};function D(t,e){let i=function(t,e,i){if(t instanceof Element)return[t];if("string"==typeof t){let e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function C(t){return e=>{"touch"===e.pointerType||M.x||M.y||t(e)}}let k=(t,e)=>!!e&&(t===e||k(t,e.parentElement)),R=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,L=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),j=new WeakSet;function F(t){return e=>{"Enter"===e.key&&t(e)}}function B(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function O(t){return R(t)&&!(M.x||M.y)}let I=t=>1e3*t,U=t=>t,N=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],$=new Set(N),W=new Set(["width","height","top","left","right","bottom",...N]),z=t=>r(t)?t[t.length-1]||0:t,Y={skipAnimations:!1,useManualTiming:!1},H=["read","resolveKeyframes","update","preRender","render","postRender"];function X(t,e){let i=!1,s=!0,n={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,o=H.reduce((t,e)=>(t[e]=function(t){let e=new Set,i=new Set,s=!1,n=!1,r=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1};function a(e){r.has(e)&&(l.schedule(e),t()),e(o)}let l={schedule:(t,n=!1,o=!1)=>{let a=o&&s?e:i;return n&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{if(o=t,s){n=!0;return}s=!0,[e,i]=[i,e],e.forEach(a),e.clear(),s=!1,n&&(n=!1,l.process(t))}};return l}(r),t),{}),{read:a,resolveKeyframes:l,update:u,preRender:h,render:d,postRender:c}=o,p=()=>{let r=Y.useManualTiming?n.timestamp:performance.now();i=!1,n.delta=s?1e3/60:Math.max(Math.min(r-n.timestamp,40),1),n.timestamp=r,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),h.process(n),d.process(n),c.process(n),n.isProcessing=!1,i&&e&&(s=!1,t(p))};return{schedule:H.reduce((e,r)=>{let a=o[r];return e[r]=(e,r=!1,o=!1)=>(!i&&(i=!0,s=!0,n.isProcessing||t(p)),a.schedule(e,r,o)),e},{}),cancel:t=>{for(let e=0;e<H.length;e++)o[H[e]].cancel(t)},state:n,steps:o}}let{schedule:K,cancel:q,state:G,steps:_}=X("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:U,!0);function Z(){s=void 0}let Q={now:()=>(void 0===s&&Q.set(G.isProcessing||Y.useManualTiming?G.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(Z)}};function J(t,e){-1===t.indexOf(e)&&t.push(e)}function tt(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class te{constructor(){this.subscriptions=[]}add(t){return J(this.subscriptions,t),()=>tt(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let ti={current:void 0};class ts{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=Q.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=Q.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new te);let i=this.events[t].add(e);return"change"===t?()=>{i(),K.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return ti.current&&ti.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=Q.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function tn(t,e){return new ts(t,e)}let tr=t=>!!(t&&t.getVelocity);function to(t,e){let i=t.getValue("willChange");if(tr(i)&&i.add)return i.add(e)}let ta=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tl="data-"+ta("framerAppearId"),tu={current:!1},th=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function td(t,e,i,s){return t===e&&i===s?U:n=>0===n||1===n?n:th(function(t,e,i,s,n){let r,o,a=0;do(r=th(o=e+(i-e)/2,s,n)-t)>0?i=o:e=o;while(Math.abs(r)>1e-7&&++a<12);return o}(n,0,1,t,i),e,s)}let tc=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tp=t=>e=>1-t(1-e),tm=td(.33,1.53,.69,.99),tf=tp(tm),tv=tc(tf),tg=t=>(t*=2)<1?.5*tf(t):.5*(2-Math.pow(2,-10*(t-1))),ty=t=>1-Math.sin(Math.acos(t)),tx=tp(ty),tw=tc(ty),tP=t=>/^0[^.\s]+$/u.test(t),tT=(t,e,i)=>i>e?e:i<t?t:i,tb={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tS={...tb,transform:t=>tT(0,1,t)},tA={...tb,default:1},tV=t=>Math.round(1e5*t)/1e5,tE=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tM=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tD=(t,e)=>i=>!!("string"==typeof i&&tM.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tC=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[n,r,o,a]=s.match(tE);return{[t]:parseFloat(n),[e]:parseFloat(r),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tk={...tb,transform:t=>Math.round(tT(0,255,t))},tR={test:tD("rgb","red"),parse:tC("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+tk.transform(t)+", "+tk.transform(e)+", "+tk.transform(i)+", "+tV(tS.transform(s))+")"},tL={test:tD("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:tR.transform},tj=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tF=tj("deg"),tB=tj("%"),tO=tj("px"),tI=tj("vh"),tU=tj("vw"),tN={...tB,parse:t=>tB.parse(t)/100,transform:t=>tB.transform(100*t)},t$={test:tD("hsl","hue"),parse:tC("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+tB.transform(tV(e))+", "+tB.transform(tV(i))+", "+tV(tS.transform(s))+")"},tW={test:t=>tR.test(t)||tL.test(t)||t$.test(t),parse:t=>tR.test(t)?tR.parse(t):t$.test(t)?t$.parse(t):tL.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tR.transform(t):t$.transform(t)},tz=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tY="number",tH="color",tX=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tK(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},n=[],r=0,o=e.replace(tX,t=>(tW.test(t)?(s.color.push(r),n.push(tH),i.push(tW.parse(t))):t.startsWith("var(")?(s.var.push(r),n.push("var"),i.push(t)):(s.number.push(r),n.push(tY),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:o,indexes:s,types:n}}function tq(t){return tK(t).values}function tG(t){let{split:e,types:i}=tK(t),s=e.length;return t=>{let n="";for(let r=0;r<s;r++)if(n+=e[r],void 0!==t[r]){let e=i[r];e===tY?n+=tV(t[r]):e===tH?n+=tW.transform(t[r]):n+=t[r]}return n}}let t_=t=>"number"==typeof t?0:t,tZ={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null==(e=t.match(tE))?void 0:e.length)||0)+((null==(i=t.match(tz))?void 0:i.length)||0)>0},parse:tq,createTransformer:tG,getAnimatableNone:function(t){let e=tq(t);return tG(t)(e.map(t_))}},tQ=new Set(["brightness","contrast","saturate","opacity"]);function tJ(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(tE)||[];if(!s)return t;let n=i.replace(s,""),r=+!!tQ.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let t0=/\b([a-z-]*)\(.*?\)/gu,t1={...tZ,getAnimatableNone:t=>{let e=t.match(t0);return e?e.map(tJ).join(" "):t}},t5={...tb,transform:Math.round},t2={borderWidth:tO,borderTopWidth:tO,borderRightWidth:tO,borderBottomWidth:tO,borderLeftWidth:tO,borderRadius:tO,radius:tO,borderTopLeftRadius:tO,borderTopRightRadius:tO,borderBottomRightRadius:tO,borderBottomLeftRadius:tO,width:tO,maxWidth:tO,height:tO,maxHeight:tO,top:tO,right:tO,bottom:tO,left:tO,padding:tO,paddingTop:tO,paddingRight:tO,paddingBottom:tO,paddingLeft:tO,margin:tO,marginTop:tO,marginRight:tO,marginBottom:tO,marginLeft:tO,backgroundPositionX:tO,backgroundPositionY:tO,rotate:tF,rotateX:tF,rotateY:tF,rotateZ:tF,scale:tA,scaleX:tA,scaleY:tA,scaleZ:tA,skew:tF,skewX:tF,skewY:tF,distance:tO,translateX:tO,translateY:tO,translateZ:tO,x:tO,y:tO,z:tO,perspective:tO,transformPerspective:tO,opacity:tS,originX:tN,originY:tN,originZ:tO,zIndex:t5,size:tO,fillOpacity:tS,strokeOpacity:tS,numOctaves:t5},t3={...t2,color:tW,backgroundColor:tW,outlineColor:tW,fill:tW,stroke:tW,borderColor:tW,borderTopColor:tW,borderRightColor:tW,borderBottomColor:tW,borderLeftColor:tW,filter:t1,WebkitFilter:t1},t9=t=>t3[t];function t8(t,e){let i=t9(t);return i!==t1&&(i=tZ),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let t6=new Set(["auto","none","0"]),t4=t=>t===tb||t===tO,t7=(t,e)=>parseFloat(t.split(", ")[e]),et=(t,e)=>(i,{transform:s})=>{if("none"===s||!s)return 0;let n=s.match(/^matrix3d\((.+)\)$/u);if(n)return t7(n[1],e);{let e=s.match(/^matrix\((.+)\)$/u);return e?t7(e[1],t):0}},ee=new Set(["x","y","z"]),ei=N.filter(t=>!ee.has(t)),es={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:et(4,13),y:et(5,14)};es.translateX=es.x,es.translateY=es.y;let en=new Set,er=!1,eo=!1;function ea(){if(eo){let t=Array.from(en).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ei.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var s;null==(s=t.getValue(e))||s.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eo=!1,er=!1,en.forEach(t=>t.complete()),en.clear()}function el(){en.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eo=!0)})}class eu{constructor(t,e,i,s,n,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(en.add(this),er||(er=!0,K.read(el),K.resolveKeyframes(ea))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;for(let n=0;n<t.length;n++)if(null===t[n])if(0===n){let n=null==s?void 0:s.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}else t[n]=t[n-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),en.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,en.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let eh=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ed=t=>e=>"string"==typeof e&&e.startsWith(t),ec=ed("--"),ep=ed("var(--"),em=t=>!!ep(t)&&ef.test(t.split("/*")[0].trim()),ef=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ev=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,eg=t=>e=>e.test(t),ey=[tb,tO,tB,tF,tU,tI,{test:t=>"auto"===t,parse:t=>t}],ex=t=>ey.find(eg(t));class ew extends eu{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&em(s=s.trim())){let n=function t(e,i,s=1){U(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=ev.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${null!=i?i:s}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return eh(t)?parseFloat(t):t}return em(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!W.has(i)||2!==t.length)return;let[s,n]=t,r=ex(s),o=ex(n);if(r!==o)if(t4(r)&&t4(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||tP(s))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!t6.has(e)&&tK(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=t8(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=es[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:s}=this;if(!e||!e.current)return;let n=e.getValue(i);n&&n.jump(this.measuredOrigin,!1);let r=s.length-1,o=s[r];s[r]=es[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null==(t=this.removedTransforms)?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let eP=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tZ.test(t)||"0"===t)&&!t.startsWith("url(")),eT=t=>null!==t;function eb(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(eT),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return r&&void 0!==s?s:n[r]}class eS{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Q.now(),this.options={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(el(),ea()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=Q.now(),this.hasAttemptedResolve=!0;let{name:i,type:s,velocity:n,delay:r,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],o=eP(n,e),a=eP(r,e);return U(o===a,`You are trying to animate ${e} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||x(i))&&s)}(t,i,s,n))if(tu.current||!r){a&&a(eb(t,this.options,e)),o&&o(),this.resolveFinishedPromise();return}else this.options.duration=0;let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}let eA=(t,e,i)=>t+(e-t)*i;function eV(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function eE(t,e){return i=>i>0?e:t}let eM=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},eD=[tL,tR,t$];function eC(t){let e=eD.find(e=>e.test(t));if(U(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===t$&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,r=0,o=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,a=2*i-s;n=eV(a,s,t+1/3),r=eV(a,s,t),o=eV(a,s,t-1/3)}else n=r=o=i;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*o),alpha:s}}(i)),i}let ek=(t,e)=>{let i=eC(t),s=eC(e);if(!i||!s)return eE(t,e);let n={...i};return t=>(n.red=eM(i.red,s.red,t),n.green=eM(i.green,s.green,t),n.blue=eM(i.blue,s.blue,t),n.alpha=eA(i.alpha,s.alpha,t),tR.transform(n))},eR=(t,e)=>i=>e(t(i)),eL=(...t)=>t.reduce(eR),ej=new Set(["none","hidden"]);function eF(t,e){return i=>eA(t,e,i)}function eB(t){return"number"==typeof t?eF:"string"==typeof t?em(t)?eE:tW.test(t)?ek:eU:Array.isArray(t)?eO:"object"==typeof t?tW.test(t)?ek:eI:eE}function eO(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>eB(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function eI(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=eB(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let eU=(t,e)=>{let i=tZ.createTransformer(e),s=tK(t),n=tK(e);return s.indexes.var.length===n.indexes.var.length&&s.indexes.color.length===n.indexes.color.length&&s.indexes.number.length>=n.indexes.number.length?ej.has(t)&&!n.values.length||ej.has(e)&&!s.values.length?function(t,e){return ej.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):eL(eO(function(t,e){var i;let s=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let o=e.types[r],a=t.indexes[o][n[o]],l=null!=(i=t.values[a])?i:0;s[r]=l,n[o]++}return s}(s,n),n.values),i):(U(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eE(t,e))};function eN(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?eA(t,e,i):eB(t)(t,e)}function e$(t,e,i){var s,n;let r=Math.max(e-5,0);return s=i-t(r),(n=e-r)?1e3/n*s:0}let eW={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function ez(t,e){return t*Math.sqrt(1-e*e)}let eY=["duration","bounce"],eH=["stiffness","damping","mass"];function eX(t,e){return e.some(e=>void 0!==t[e])}function eK(t=eW.visualDuration,e=eW.bounce){let i,s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,o=s.keyframes[0],a=s.keyframes[s.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:eW.velocity,stiffness:eW.stiffness,damping:eW.damping,mass:eW.mass,isResolvedFromDuration:!1,...t};if(!eX(t,eH)&&eX(t,eY))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*tT(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:eW.mass,stiffness:s,damping:n}}else{let i=function({duration:t=eW.duration,bounce:e=eW.bounce,velocity:i=eW.velocity,mass:s=eW.mass}){let n,r;U(t<=I(eW.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=tT(eW.minDamping,eW.maxDamping,o),t=tT(eW.minDuration,eW.maxDuration,t/1e3),o<1?(n=e=>{let s=e*o,n=s*t;return .001-(s-i)/ez(e,o)*Math.exp(-n)},r=e=>{let s=e*o*t,r=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-s),l=ez(Math.pow(e,2),o);return(s*i+i-r)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=I(t),isNaN(a))return{stiffness:eW.stiffness,damping:eW.damping,duration:t};{let e=Math.pow(a,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:eW.mass}).isResolvedFromDuration=!0}return e}({...s,velocity:-((s.velocity||0)/1e3)}),f=p||0,v=h/(2*Math.sqrt(u*d)),g=a-o,x=Math.sqrt(u/d)/1e3,w=5>Math.abs(g);if(n||(n=w?eW.restSpeed.granular:eW.restSpeed.default),r||(r=w?eW.restDelta.granular:eW.restDelta.default),v<1){let t=ez(x,v);i=e=>a-Math.exp(-v*x*e)*((f+v*x*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===v)i=t=>a-Math.exp(-x*t)*(g+(f+x*g)*t);else{let t=x*Math.sqrt(v*v-1);i=e=>{let i=Math.exp(-v*x*e),s=Math.min(t*e,300);return a-i*((f+v*x*g)*Math.sinh(s)+t*g*Math.cosh(s))/t}}let P={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let s=0;v<1&&(s=0===t?I(f):e$(i,t,e));let o=Math.abs(a-e)<=r;l.done=Math.abs(s)<=n&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(y(P),2e4),e=A(e=>P.next(t*e).value,t,30);return t+"ms "+e}};return P}function eq({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=i*e,v=p+f,g=void 0===o?v:o(v);g!==v&&(f=g-p);let y=t=>-f*Math.exp(-t/s),x=t=>g+y(t),w=t=>{let e=y(t),i=x(t);m.done=Math.abs(e)<=u,m.value=m.done?g:i},P=t=>{let e;if(e=m.value,void 0!==a&&e<a||void 0!==l&&e>l){var i;d=t,c=eK({keyframes:[m.value,(i=m.value,void 0===a?l:void 0===l||Math.abs(a-i)<Math.abs(l-i)?a:l)],velocity:e$(x,t,m.value),damping:n,stiffness:r,restDelta:u,restSpeed:h})}};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,w(t),P(t)),void 0!==d&&t>=d)?c.next(t-d):(e||w(t),m)}}}let eG=td(.42,0,1,1),e_=td(0,0,.58,1),eZ=td(.42,0,.58,1),eQ={linear:U,easeIn:eG,easeInOut:eZ,easeOut:e_,circIn:ty,circInOut:tw,circOut:tx,backIn:tf,backInOut:tv,backOut:tm,anticipate:tg},eJ=t=>{if(P(t)){U(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,n]=t;return td(e,i,s,n)}return"string"==typeof t?(U(void 0!==eQ[t],`Invalid easing type '${t}'`),eQ[t]):t};function e0({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var n;let r=Array.isArray(s)&&"number"!=typeof s[0]?s.map(eJ):eJ(s),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:s,mixer:n}={}){let r=t.length;if(U(r===e.length,"Both input and output ranges must be the same length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let s=[],n=i||eN,r=t.length-1;for(let i=0;i<r;i++){let r=n(t[i],t[i+1]);e&&(r=eL(Array.isArray(e)?e[i]||U:e,r)),s.push(r)}return s}(e,s,n),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=S(t[s],t[s+1],i);return a[s](n)};return i?e=>u(tT(t[0],t[r-1],e)):u}((n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let n=S(0,e,s);t.push(eA(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),e,{ease:Array.isArray(r)?r:e.map(()=>r||eZ).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let e1=t=>{let e=({timestamp:e})=>t(e);return{start:()=>K.update(e,!0),stop:()=>q(e),now:()=>G.isProcessing?G.timestamp:Q.now()}},e5={decay:eq,inertia:eq,tween:e0,keyframes:e0,spring:eK},e2=t=>t/100;class e3 extends eS{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:s,keyframes:n}=this.options,r=(null==s?void 0:s.KeyframeResolver)||eu,o=(t,e)=>this.onKeyframesResolved(t,e);this.resolver=new r(n,o,e,i,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i,{type:s="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:o,velocity:a=0}=this.options,l=x(s)?s:e5[s]||e0;l!==e0&&"number"!=typeof t[0]&&(e=eL(e2,eN(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===o&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=y(u));let{calculatedDuration:h}=u,d=h+r;return{generator:u,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:h,resolvedDuration:d,totalDuration:d*(n+1)-r}}onPostResolved(){let{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:s,generator:n,mirroredGenerator:r,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return n.next(0);let{delay:d,repeat:c,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let v=this.currentTime-d*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=n;if(c){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/h)):"mirror"===p&&(x=r)),y=tT(0,1,i)*h}let w=g?{done:!1,value:a[0]}:x.next(y);o&&(w.value=o(w.value));let{done:P}=w;g||null===l||(P=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return T&&void 0!==s&&(w.value=eb(a,this.options,s)),f&&f(w.value),T&&this.finish(),w}get duration(){let{resolved:t}=this;return t?t.calculatedDuration/1e3:0}get time(){return this.currentTime/1e3}set time(t){t=I(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=this.currentTime/1e3)}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=e1,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let s=this.driver.now();null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=s):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!=(t=this.currentTime)?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let e9=new Set(["opacity","clipPath","filter","transform"]),e8=p(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),e6={anticipate:tg,backInOut:tv,circInOut:tw};class e4 extends eS{constructor(t){super(t);let{name:e,motionValue:i,element:s,keyframes:n}=this.options;this.resolver=new ew(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:s=300,times:n,ease:r,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof r&&b()&&r in e6&&(r=e6[r]),x((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&b()||!e||"string"==typeof e&&(e in E||b())||P(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:a,element:l,...u}=this.options,h=function(t,e){let i=new e3({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),s={done:!1,value:t[0]},n=[],r=0;for(;!s.done&&r<2e4;)n.push((s=i.sample(r)).value),r+=10;return{times:void 0,keyframes:n,duration:r-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),s=h.duration,n=h.times,r=h.ease,o="keyframes"}let h=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=function t(e,i){if(e)return"function"==typeof e&&b()?A(e,i):P(e)?V(e):Array.isArray(e)?e.map(e=>t(e,i)||E.easeOut):E[e]}(a,n);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:s,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"})}(a.owner.current,l,t,{...this.options,duration:s,times:n,ease:r});return h.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(w(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:i}=this.options;a.set(eb(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:s,times:n,type:o,ease:r,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return e/1e3}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return(e.currentTime||0)/1e3}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=I(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return U;let{animation:i}=e;w(i,t)}else this.pendingTimeline=t;return U}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:s,type:n,ease:r,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,h=new e3({...u,keyframes:i,duration:s,type:n,ease:r,times:o,isGenerator:!0}),d=I(this.time);t.setWithVelocity(h.sample(d-10).value,h.sample(d).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return e8()&&i&&e9.has(i)&&!a&&!l&&!s&&"mirror"!==n&&0!==r&&"inertia"!==o}}let e7={type:"spring",stiffness:500,damping:25,restSpeed:10},it={type:"keyframes",duration:.8},ie={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ii=(t,e,i,s={},n,r)=>o=>{let a=g(s,t)||{},l=a.delay||s.delay||0,{elapsed:u=0}=s;u-=I(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&(h={...h,...((t,{keyframes:e})=>e.length>2?it:$.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:e7:ie)(t,h)}),h.duration&&(h.duration=I(h.duration)),h.repeatDelay&&(h.repeatDelay=I(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(d=!0)),(tu.current||Y.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),d&&!r&&void 0!==e.get()){let t=eb(h.keyframes,a);if(void 0!==t)return K.update(()=>{h.onUpdate(t),h.onComplete()}),new v([])}return!r&&e4.supports(h)?new e4(h):new e3(h)};function is(t,e,{delay:i=0,transitionOverride:s,type:n}={}){var r;let{transition:o=t.getDefaultTransition(),transitionEnd:a,...l}=e;s&&(o=s);let u=[],d=n&&t.animationState&&t.animationState.getState()[n];for(let e in l){let s=t.getValue(e,null!=(r=t.latestValues[e])?r:null),n=l[e];if(void 0===n||d&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(d,e))continue;let a={delay:i,...g(o||{},e)},h=!1;if(window.MotionHandoffAnimation){let i=t.props[tl];if(i){let t=window.MotionHandoffAnimation(i,e,K);null!==t&&(a.startTime=t,h=!0)}}to(t,e),s.start(ii(e,s,n,t.shouldReduceMotion&&W.has(e)?{type:!1}:a,t,h));let c=s.animation;c&&u.push(c)}return a&&Promise.all(u).then(()=>{K.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=h(t,e)||{};for(let e in n={...n,...i}){let i=z(n[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,tn(i))}}(t,a)})}),u}function ir(t,e,i={}){var s;let n=h(t,e,"exit"===i.type?null==(s=t.presenceContext)?void 0:s.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let o=n?()=>Promise.all(is(t,n,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,s=0,n=1,r){let o=[],a=(t.variantChildren.size-1)*s,l=1===n?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(io).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(ir(t,e,{...r,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,n+s,o,a,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([o(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[o,a]:[a,o];return t().then(()=>e())}}function io(t,e){return t.sortNodePosition(e)}let ia=c.length,il=[...d].reverse(),iu=d.length;function ih(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function id(){return{animate:ih(!0),whileInView:ih(),whileHover:ih(),whileTap:ih(),whileDrag:ih(),whileFocus:ih(),exit:ih()}}class ic{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ip extends ic{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>ir(t,e,i)));else if("string"==typeof e)s=ir(t,e,i);else{let n="function"==typeof e?h(t,e,i.custom):e;s=Promise.all(is(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=id(),s=!0,l=e=>(i,s)=>{var n;let r=h(t,s,"exit"===e?null==(n=t.presenceContext)?void 0:n.custom:void 0);if(r){let{transition:t,transitionEnd:e,...s}=r;i={...i,...s,...e}}return i};function u(u){let{props:h}=t,d=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ia;t++){let s=c[t],n=e.props[s];(a(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},p=[],m=new Set,f={},v=1/0;for(let e=0;e<iu;e++){var g,y;let c=il[e],x=i[c],w=void 0!==h[c]?h[c]:d[c],P=a(w),T=c===u?x.isActive:null;!1===T&&(v=e);let b=w===d[c]&&w!==h[c]&&P;if(b&&s&&t.manuallyAnimateOnMount&&(b=!1),x.protectedKeys={...f},!x.isActive&&null===T||!w&&!x.prevProp||n(w)||"boolean"==typeof w)continue;let S=(g=x.prevProp,"string"==typeof(y=w)?y!==g:!!Array.isArray(y)&&!o(y,g)),A=S||c===u&&x.isActive&&!b&&P||e>v&&P,V=!1,E=Array.isArray(w)?w:[w],M=E.reduce(l(c),{});!1===T&&(M={});let{prevResolvedValues:D={}}=x,C={...D,...M},k=e=>{A=!0,m.has(e)&&(V=!0,m.delete(e)),x.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in C){let e=M[t],i=D[t];if(!f.hasOwnProperty(t))(r(e)&&r(i)?o(e,i):e===i)?void 0!==e&&m.has(t)?k(t):x.protectedKeys[t]=!0:null!=e?k(t):m.add(t)}x.prevProp=w,x.prevResolvedValues=M,x.isActive&&(f={...f,...M}),s&&t.blockInitialAnimation&&(A=!1);let R=!(b&&S)||V;A&&R&&p.push(...E.map(t=>({animation:t,options:{type:c}})))}if(m.size){let e={};m.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=null!=s?s:null}),p.push({animation:e})}let x=!!p.length;return s&&(!1===h.initial||h.initial===h.animate)&&!t.manuallyAnimateOnMount&&(x=!1),s=!1,x?e(p):Promise.resolve()}return{animateChanges:u,setActive:function(e,s){var n;if(i[e].isActive===s)return Promise.resolve();null==(n=t.variantChildren)||n.forEach(t=>{var i;return null==(i=t.animationState)?void 0:i.setActive(e,s)}),i[e].isActive=s;let r=u(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=id(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null==(t=this.unmountControls)||t.call(this)}}let im=0;class iv extends ic{constructor(){super(...arguments),this.id=im++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}function ig(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}function iy(t){return{point:{x:t.pageX,y:t.pageY}}}function ix(t,e,i,s){return ig(t,e,t=>R(t)&&i(t,iy(t)),s)}let iw=(t,e)=>Math.abs(t-e);class iP{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iS(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iw(t.x,e.x)**2+iw(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=G;this.history.push({...s,timestamp:n});let{onStart:r,onMove:o}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iT(e,this.transformPagePoint),K.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=iS("pointercancel"===t.type?this.lastMoveEventInfo:iT(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!R(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=iT(iy(t),this.transformPagePoint),{point:o}=r,{timestamp:a}=G;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iS(r,this.history)),this.removeListeners=eL(ix(this.contextWindow,"pointermove",this.handlePointerMove),ix(this.contextWindow,"pointerup",this.handlePointerUp),ix(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),q(this.updatePoint)}}function iT(t,e){return e?{point:e(t.point)}:t}function ib(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iS({point:t},e){return{point:t,delta:ib(t,iA(e)),offset:ib(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=iA(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>I(.1)));)i--;if(!s)return{x:0,y:0};let r=(n.timestamp-s.timestamp)/1e3;if(0===r)return{x:0,y:0};let o={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iA(t){return t[t.length-1]}function iV(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function iE(t){return t.max-t.min}function iM(t,e,i,s=.5){t.origin=s,t.originPoint=eA(e.min,e.max,t.origin),t.scale=iE(i)/iE(e),t.translate=eA(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iD(t,e,i,s){iM(t.x,e.x,i.x,s?s.originX:void 0),iM(t.y,e.y,i.y,s?s.originY:void 0)}function iC(t,e,i){t.min=i.min+e.min,t.max=t.min+iE(e)}function ik(t,e,i){t.min=e.min-i.min,t.max=t.min+iE(e)}function iR(t,e,i){ik(t.x,e.x,i.x),ik(t.y,e.y,i.y)}function iL(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function ij(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function iF(t,e,i){return{min:iB(t,e),max:iB(t,i)}}function iB(t,e){return"number"==typeof t?t:t[e]||0}let iO=()=>({translate:0,scale:1,origin:0,originPoint:0}),iI=()=>({x:iO(),y:iO()}),iU=()=>({min:0,max:0}),iN=()=>({x:iU(),y:iU()});function i$(t){return[t("x"),t("y")]}function iW({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function iz(t){return void 0===t||1===t}function iY({scale:t,scaleX:e,scaleY:i}){return!iz(t)||!iz(e)||!iz(i)}function iH(t){return iY(t)||iX(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iX(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iK(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function iq(t,e=0,i=1,s,n){t.min=iK(t.min,e,i,s,n),t.max=iK(t.max,e,i,s,n)}function iG(t,{x:e,y:i}){iq(t.x,e.translate,e.scale,e.originPoint),iq(t.y,i.translate,i.scale,i.originPoint)}function i_(t,e){t.min=t.min+e,t.max=t.max+e}function iZ(t,e,i,s,n=.5){let r=eA(t.min,t.max,n);iq(t,e,i,r,s)}function iQ(t,e){iZ(t.x,e.x,e.scaleX,e.scale,e.originX),iZ(t.y,e.y,e.scaleY,e.scale,e.originY)}function iJ(t,e){return iW(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let i0=({current:t})=>t?t.ownerDocument.defaultView:null,i1=new WeakMap;class i5{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iN(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let s=t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iy(t).point)},n=(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:n}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(M[t])return null;else return M[t]=!0,()=>{M[t]=!1};return M.x||M.y?null:(M.x=M.y=!0,()=>{M.x=M.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),i$(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tB.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=iE(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&K.postRender(()=>n(t,e)),to(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},r=(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),r&&r(t,e)},o=(t,e)=>this.stop(t,e),a=()=>i$(t=>{var e;return"paused"===this.getAnimationState(t)&&(null==(e=this.getAxisMotionValue(t).animation)?void 0:e.play())}),{dragSnapToOrigin:l}=this.getProps();this.panSession=new iP(t,{onSessionStart:s,onStart:n,onMove:r,onSessionEnd:o,resumeAnimation:a},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:l,contextWindow:i0(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:n}=this.getProps();n&&K.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!i2(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?eA(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?eA(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(t=this.visualElement.projection)?void 0:t.layout,n=this.constraints;e&&iV(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:iL(t.x,i,n),y:iL(t.y,e,s)}}(s.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iF(t,"left","right"),y:iF(t,"top","bottom")}}(i),n!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&i$(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(s.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iV(e))return!1;let s=e.current;U(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=iJ(t,i),{scroll:n}=e;return n&&(i_(s.x,n.offset.x),i_(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:ij(t.x,r.x),y:ij(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iW(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(i$(o=>{if(!i2(o,e,this.currentDirection))return;let l=a&&a[o]||{};r&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return to(this.visualElement,t),i.start(ii(t,i,0,e,this.visualElement,!1))}stopAnimation(){i$(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){i$(t=>{var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.pause()})}getAnimationState(t){var e;return null==(e=this.getAxisMotionValue(t).animation)?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){i$(e=>{let{drag:i}=this.getProps();if(!i2(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-eA(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iV(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};i$(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=iE(t),n=iE(e);return n>s?i=S(e.min,e.max-s,t.min):s>n&&(i=S(t.min,t.max-n,e.min)),tT(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),i$(e=>{if(!i2(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set(eA(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;i1.set(this.visualElement,this);let t=ix(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iV(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),K.read(e);let n=ig(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(i$(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:o}}}function i2(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i3 extends ic{constructor(t){super(t),this.removeGroupControls=U,this.removeListeners=U,this.controls=new i5(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||U}unmount(){this.removeGroupControls(),this.removeListeners()}}let i9=t=>(e,i)=>{t&&K.postRender(()=>t(e,i))};class i8 extends ic{constructor(){super(...arguments),this.removePointerDownListener=U}onPointerDown(t){this.session=new iP(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:i0(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:i9(t),onStart:i9(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&K.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=ix(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i6,i4,i7=i(95155),st=i(12115),se=i(32082),si=i(90869);let ss=(0,st.createContext)({}),sn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function sr(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let so={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tO.test(t))return t;else t=parseFloat(t);let i=sr(t,e.target.x),s=sr(t,e.target.y);return`${i}% ${s}%`}},sa={},{schedule:sl,cancel:su}=X(queueMicrotask,!1);class sh extends st.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;Object.assign(sa,sc),n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),sn.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,r=i.projection;return r&&(r.isPresent=n,s||t.layoutDependency!==e||void 0===e?r.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?r.promote():r.relegate()||K.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),sl.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function sd(t){let[e,i]=(0,se.xQ)(),s=(0,st.useContext)(si.L);return(0,i7.jsx)(sh,{...t,layoutGroup:s,switchLayoutGroup:(0,st.useContext)(ss),isPresent:e,safeToRemove:i})}let sc={borderRadius:{...so,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:so,borderTopRightRadius:so,borderBottomLeftRadius:so,borderBottomRightRadius:so,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=tZ.parse(t);if(s.length>5)return t;let n=tZ.createTransformer(t),r=+("number"!=typeof s[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;s[0+r]/=o,s[1+r]/=a;let l=eA(o,a,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}},sp=(t,e)=>t.depth-e.depth;class sm{constructor(){this.children=[],this.isDirty=!1}add(t){J(this.children,t),this.isDirty=!0}remove(t){tt(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(sp),this.isDirty=!1,this.children.forEach(t)}}function sf(t){let e=tr(t)?t.get():t;return e&&"object"==typeof e&&e.mix&&e.toValue?e.toValue():e}let sv=["TopLeft","TopRight","BottomLeft","BottomRight"],sg=sv.length,sy=t=>"string"==typeof t?parseFloat(t):t,sx=t=>"number"==typeof t||tO.test(t);function sw(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let sP=sb(0,.5,tx),sT=sb(.5,.95,U);function sb(t,e,i){return s=>s<t?0:s>e?1:i(S(t,e,s))}function sS(t,e){t.min=e.min,t.max=e.max}function sA(t,e){sS(t.x,e.x),sS(t.y,e.y)}function sV(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sE(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function sM(t,e,[i,s,n],r,o){!function(t,e=0,i=1,s=.5,n,r=t,o=t){if(tB.test(e)&&(e=parseFloat(e),e=eA(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eA(r.min,r.max,s);t===r&&(a-=e),t.min=sE(t.min,e,i,a,n),t.max=sE(t.max,e,i,a,n)}(t,e[i],e[s],e[n],e.scale,r,o)}let sD=["x","scaleX","originX"],sC=["y","scaleY","originY"];function sk(t,e,i,s){sM(t.x,e,sD,i?i.x:void 0,s?s.x:void 0),sM(t.y,e,sC,i?i.y:void 0,s?s.y:void 0)}function sR(t){return 0===t.translate&&1===t.scale}function sL(t){return sR(t.x)&&sR(t.y)}function sj(t,e){return t.min===e.min&&t.max===e.max}function sF(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sB(t,e){return sF(t.x,e.x)&&sF(t.y,e.y)}function sO(t){return iE(t.x)/iE(t.y)}function sI(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sU{constructor(){this.members=[]}add(t){J(this.members,t),t.scheduleRender()}remove(t){if(tt(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sN={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},s$="undefined"!=typeof window&&void 0!==window.MotionDebug,sW=["","X","Y","Z"],sz={visibility:"hidden"},sY=0;function sH(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function sX({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=null==e?void 0:e()){this.id=sY++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,s$&&(sN.totalNodes=sN.resolvedTargetDeltas=sN.recalculatedProjection=0),this.nodes.forEach(sG),this.nodes.forEach(s5),this.nodes.forEach(s2),this.nodes.forEach(s_),s$&&window.MotionDebug.record(sN)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sm)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new te),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:s,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(n||s)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=Q.now(),s=({timestamp:e})=>{let n=e-i;n>=250&&(q(s),t(n-250))};return K.read(s,!0),()=>q(s)}(s,250),sn.hasAnimatedSinceResize&&(sn.hasAnimatedSinceResize=!1,this.nodes.forEach(s1))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&r&&(s||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||r.getDefaultTransition()||s7,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!sB(this.targetLayout,s)||i,u=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...g(n,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||s1(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,q(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(s3),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[tl];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",K,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(sQ);return}this.isUpdating||this.nodes.forEach(sJ),this.isUpdating=!1,this.nodes.forEach(s0),this.nodes.forEach(sK),this.nodes.forEach(sq),this.clearAllSnapshots();let t=Q.now();G.delta=tT(0,1e3/60,t-G.timestamp),G.timestamp=t,G.isProcessing=!0,_.update.process(G),_.preRender.process(G),_.render.process(G),G.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,sl.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sZ),this.sharedNodes.forEach(s9)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,K.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){K.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iN(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sL(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&(e||iH(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),ni((e=s).x),ni(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return iN();let i=e.measureViewportBox();if(!((null==(t=this.scroll)?void 0:t.wasRoot)||this.path.some(nn))){let{scroll:t}=this.root;t&&(i_(i.x,t.offset.x),i_(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=iN();if(sA(i,t),null==(e=this.scroll)?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let s=this.path[e],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&sA(i,t),i_(i.x,n.offset.x),i_(i.y,n.offset.y))}return i}applyTransform(t,e=!1){let i=iN();sA(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&iQ(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),iH(s.latestValues)&&iQ(i,s.latestValues)}return iH(this.latestValues)&&iQ(i,this.latestValues),i}removeTransform(t){let e=iN();sA(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iH(i.latestValues))continue;iY(i.latestValues)&&i.updateSnapshot();let s=iN();sA(s,i.measurePageBox()),sk(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return iH(this.latestValues)&&sk(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==G.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,s,n;let r=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=r.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=r.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=r.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==r;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=G.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iN(),this.relativeTargetOrigin=iN(),iR(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=iN(),this.targetWithTransforms=iN()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,s=this.relativeTarget,n=this.relativeParent.target,iC(i.x,s.x,n.x),iC(i.y,s.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sA(this.target,this.layout.layoutBox),iG(this.target,this.targetDelta)):sA(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iN(),this.relativeTargetOrigin=iN(),iR(this.relativeTargetOrigin,this.target,t.target),sA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}s$&&sN.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iY(this.parent.latestValues)||iX(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,s=!0;if((this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty))&&(s=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===G.timestamp&&(s=!1),s)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;sA(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,s=!1){let n,r,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iQ(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,iG(t,r)),s&&iH(n.latestValues)&&iQ(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=iN());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sV(this.prevProjectionDelta.x,this.projectionDelta.x),sV(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iD(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&sI(this.projectionDelta.x,this.prevProjectionDelta.x)&&sI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),s$&&sN.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null==(e=this.options.visualElement)||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iI(),this.projectionDelta=iI(),this.projectionDeltaWithTransform=iI()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},o=iI();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iN(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(s4));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(s8(o.x,t.x,s),s8(o.y,t.y,s),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,v;iR(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,v=s,s6(p.x,m.x,f.x,v),s6(p.y,m.y,f.y,v),i&&(u=this.relativeTarget,c=i,sj(u.x,c.x)&&sj(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iN()),sA(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=eA(0,void 0!==i.opacity?i.opacity:1,sP(s)),t.opacityExit=eA(void 0!==e.opacity?e.opacity:1,0,sT(s))):r&&(t.opacity=eA(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,s));for(let n=0;n<sg;n++){let r=`border${sv[n]}Radius`,o=sw(e,r),a=sw(i,r);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||sx(o)===sx(a)?(t[r]=Math.max(eA(sy(o),sy(a),s),0),(tB.test(a)||tB.test(o))&&(t[r]+="%")):t[r]=a)}(e.rotate||i.rotate)&&(t.rotate=eA(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(q(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=K.update(()=>{sn.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let s=tr(0)?0:tn(t);return s.start(ii("",s,1e3,i)),s.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&ns(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||iN();let e=iE(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=iE(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}sA(e,i),iQ(e,n),iD(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sU),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null==(t=this.getStack())?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null==(t=this.getStack())?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&sH("z",t,s,this.animationValues);for(let e=0;e<sW.length;e++)sH(`rotate${sW[e]}`,t,s,this.animationValues),sH(`skew${sW[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return sz;let s={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,s.opacity="",s.pointerEvents=sf(null==t?void 0:t.pointerEvents)||"",s.transform=n?n(this.latestValues,""):"none",s;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=sf(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!iH(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let o=r.animationValues||r.latestValues;this.applyTransformsToTarget(),s.transform=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((n||r||o)&&(s=`translate3d(${n}px, ${r}px, ${o}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:o,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),o&&(s+=`skewX(${o}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),n&&(s.transform=n(o,s.transform));let{x:a,y:l}=this.projectionDelta;for(let t in s.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,r.animationValues?s.opacity=r===this?null!=(i=null!=(e=o.opacity)?e:this.latestValues.opacity)?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:s.opacity=r===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,sa){if(void 0===o[t])continue;let{correct:e,applyTo:i}=sa[t],n="none"===s.transform?o[t]:e(o[t],r);if(i){let t=i.length;for(let e=0;e<t;e++)s[i[e]]=n}else s[t]=n}return this.options.layoutId&&(s.pointerEvents=r===this?sf(null==t?void 0:t.pointerEvents)||"":"none"),s}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null==(e=t.currentAnimation)?void 0:e.stop()}),this.root.nodes.forEach(sQ),this.root.sharedNodes.clear()}}}function sK(t){t.updateLayout()}function sq(t){var e;let i=(null==(e=t.resumeFrom)?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:s}=t.layout,{animationType:n}=t.options,r=i.source!==t.layout.source;"size"===n?i$(t=>{let s=r?i.measuredBox[t]:i.layoutBox[t],n=iE(s);s.min=e[t].min,s.max=s.min+n}):ns(n,i.layoutBox,e)&&i$(s=>{let n=r?i.measuredBox[s]:i.layoutBox[s],o=iE(e[s]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+o)});let o=iI();iD(o,e,i.layoutBox);let a=iI();r?iD(a,t.applyTransform(s,!0),i.measuredBox):iD(a,e,i.layoutBox);let l=!sL(o),u=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let o=iN();iR(o,i.layoutBox,n.layoutBox);let a=iN();iR(a,e,r.layoutBox),sB(o,a)||(u=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function sG(t){s$&&sN.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function s_(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function sZ(t){t.clearSnapshot()}function sQ(t){t.clearMeasurements()}function sJ(t){t.isLayoutDirty=!1}function s0(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function s1(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function s5(t){t.resolveTargetDelta()}function s2(t){t.calcProjection()}function s3(t){t.resetSkewAndRotation()}function s9(t){t.removeLeadSnapshot()}function s8(t,e,i){t.translate=eA(e.translate,0,i),t.scale=eA(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function s6(t,e,i,s){t.min=eA(e.min,i.min,s),t.max=eA(e.max,i.max,s)}function s4(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let s7={duration:.45,ease:[.4,0,.1,1]},nt=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),ne=nt("applewebkit/")&&!nt("chrome/")?Math.round:U;function ni(t){t.min=ne(t.min),t.max=ne(t.max)}function ns(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(sO(e)-sO(i)))}function nn(t){var e;return t!==t.root&&(null==(e=t.scroll)?void 0:e.wasRoot)}let nr=sX({attachResizeListener:(t,e)=>ig(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),no={current:void 0},na=sX({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!no.current){let t=new nr({});t.mount(window),t.setOptions({layoutScroll:!0}),no.current=t}return no.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function nl(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&K.postRender(()=>n(e,iy(e)))}class nu extends ic{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=D(t,i),o=C(t=>{let{target:i}=t,s=e(t);if("function"!=typeof s||!i)return;let r=C(t=>{s(t),i.removeEventListener("pointerleave",r)});i.addEventListener("pointerleave",r,n)});return s.forEach(t=>{t.addEventListener("pointerenter",o,n)}),r}(t,t=>(nl(this.node,t,"Start"),t=>nl(this.node,t,"End"))))}unmount(){}}class nh extends ic{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eL(ig(this.node.current,"focus",()=>this.onFocus()),ig(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function nd(t,e,i){let{props:s}=t;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&K.postRender(()=>n(e,iy(e)))}class nc extends ic{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=D(t,i),o=t=>{let s=t.currentTarget;if(!O(t)||j.has(s))return;j.add(s);let r=e(t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),O(t)&&j.has(s)&&(j.delete(s),"function"==typeof r&&r(t,{success:e}))},a=t=>{o(t,i.useGlobalTarget||k(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return s.forEach(t=>{L.has(t.tagName)||-1!==t.tabIndex||null!==t.getAttribute("tabindex")||(t.tabIndex=0),(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),t.addEventListener("focus",t=>((t,e)=>{let i=t.currentTarget;if(!i)return;let s=F(()=>{if(j.has(i))return;B(i,"down");let t=F(()=>{B(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>B(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)})(t,n),n)}),r}(t,t=>(nd(this.node,t,"Start"),(t,{success:e})=>nd(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let np=new WeakMap,nm=new WeakMap,nf=t=>{let e=np.get(t.target);e&&e(t)},nv=t=>{t.forEach(nf)},ng={some:0,all:1};class ny extends ic{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:ng[s]},o=t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)};var a=this.node.current;let l=function({root:t,...e}){let i=t||document;nm.has(i)||nm.set(i,{});let s=nm.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(nv,{root:t,...e})),s[n]}(r);return np.set(a,o),l.observe(a),()=>{np.delete(a),l.unobserve(a)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nx=(0,st.createContext)({strict:!1});var nw=i(51508);let nP=(0,st.createContext)({});function nT(t){return n(t.animate)||c.some(e=>a(t[e]))}function nb(t){return!!(nT(t)||t.variants)}function nS(t){return Array.isArray(t)?t.join(" "):t}var nA=i(68972);let nV={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nE={};for(let t in nV)nE[t]={isEnabled:e=>nV[t].some(t=>!!e[t])};let nM=Symbol.for("motionComponentSymbol");var nD=i(80845),nC=i(97494);let nk=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nR(t){if("string"!=typeof t||t.includes("-"));else if(nk.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var nL=i(82885);let nj=t=>(e,i)=>{let s=(0,st.useContext)(nP),r=(0,st.useContext)(nD.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},s,r,o){let a={latestValues:function(t,e,i,s){let r={},o=s(t,{});for(let t in o)r[t]=sf(o[t]);let{initial:a,animate:l}=t,h=nT(t),d=nb(t);e&&d&&!h&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===l&&(l=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===a)?l:a;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=u(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(s,r,o,t),renderState:e()};return i&&(a.onMount=t=>i({props:s,current:t,...a}),a.onUpdate=t=>i(t)),a})(t,e,s,r);return i?o():(0,nL.M)(o)},nF=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nB={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nO=N.length;function nI(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,o=!1,a=!1;for(let t in e){let i=e[t];if($.has(t)){o=!0;continue}if(ec(t)){n[t]=i;continue}{let e=nF(i,t2[t]);t.startsWith("origin")?(a=!0,r[t]=e):s[t]=e}}if(!e.transform&&(o||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<nO;r++){let o=N[r],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=nF(a,t2[o]);if(!l){n=!1;let e=nB[o]||o;s+=`${e}(${t}) `}i&&(e[o]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let nU={offset:"stroke-dashoffset",array:"stroke-dasharray"},nN={offset:"strokeDashoffset",array:"strokeDasharray"};function n$(t,e,i){return"string"==typeof t?t:tO.transform(e+i*t)}function nW(t,{attrX:e,attrY:i,attrScale:s,originX:n,originY:r,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,d){if(nI(t,u,d),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p,dimensions:m}=t;c.transform&&(m&&(p.transform=c.transform),delete c.transform),m&&(void 0!==n||void 0!==r||p.transform)&&(p.transformOrigin=function(t,e,i){let s=n$(e,t.x,t.width),n=n$(i,t.y,t.height);return`${s} ${n}`}(m,void 0!==n?n:.5,void 0!==r?r:.5)),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==s&&(c.scale=s),void 0!==o&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?nU:nN;t[r.offset]=tO.transform(-s);let o=tO.transform(e),a=tO.transform(i);t[r.array]=`${o} ${a}`}(c,o,a,l,!1)}let nz=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),nY=()=>({...nz(),attrs:{}}),nH=t=>"string"==typeof t&&"svg"===t.toLowerCase();function nX(t,{style:e,vars:i},s,n){for(let r in Object.assign(t.style,e,n&&n.getProjectionStyles(s)),i)t.style.setProperty(r,i[r])}let nK=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function nq(t,e,i,s){for(let i in nX(t,e,void 0,s),e.attrs)t.setAttribute(nK.has(i)?i:ta(i),e.attrs[i])}function nG(t,{layout:e,layoutId:i}){return $.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!sa[t]||"opacity"===t)}function n_(t,e,i){var s;let{style:n}=t,r={};for(let o in n)(tr(n[o])||e.style&&tr(e.style[o])||nG(o,t)||(null==(s=null==i?void 0:i.getValue(o))?void 0:s.liveStyle)!==void 0)&&(r[o]=n[o]);return r}function nZ(t,e,i){let s=n_(t,e,i);for(let i in t)(tr(t[i])||tr(e[i]))&&(s[-1!==N.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let nQ=["x","y","width","height","cx","cy","r"],nJ={useVisualState:nj({scrapeMotionValuesFromProps:nZ,createRenderState:nY,onUpdate:({props:t,prevProps:e,current:i,renderState:s,latestValues:n})=>{if(!i)return;let r=!!t.drag;if(!r){for(let t in n)if($.has(t)){r=!0;break}}if(!r)return;let o=!e;if(e)for(let i=0;i<nQ.length;i++){let s=nQ[i];t[s]!==e[s]&&(o=!0)}o&&K.read(()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}(i,s),K.render(()=>{nW(s,n,nH(i.tagName),t.transformTemplate),nq(i,s)})})}})},n0={useVisualState:nj({scrapeMotionValuesFromProps:n_,createRenderState:nz})};function n1(t,e,i){for(let s in e)tr(e[s])||nG(s,i)||(t[s]=e[s])}let n5=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function n2(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||n5.has(t)}let n3=t=>!n2(t);try{!function(t){t&&(n3=e=>e.startsWith("on")?!n2(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let n9={current:null},n8={current:!1},n6=[...ey,tW,tZ],n4=new WeakMap,n7=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rt{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eu,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=Q.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,K.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=r;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=nT(e),this.isVariantNode=nb(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&tr(e)&&e.set(a[t],!1)}}mount(t){this.current=t,n4.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),n8.current||function(){if(n8.current=!0,nA.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>n9.current=t.matches;t.addListener(e),e()}else n9.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||n9.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in n4.delete(this.current),this.projection&&this.projection.unmount(),q(this.notifyUpdate),q(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=$.has(t),n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&K.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nE){let e=nE[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iN()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<n7.length;e++){let i=n7[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if(tr(n))t.addValue(s,n);else if(tr(r))t.addValue(s,tn(n,{owner:t}));else if(r!==n)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,tn(void 0!==e?e:n,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=tn(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let s=void 0===this.latestValues[t]&&this.current?null!=(i=this.getBaseTargetFromProps(this.props,t))?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];if(null!=s){if("string"==typeof s&&(eh(s)||tP(s)))s=parseFloat(s);else{let i;i=s,!n6.find(eg(i))&&tZ.test(e)&&(s=t8(t,e))}this.setBaseTarget(t,tr(s)?s.get():s)}return tr(s)?s.get():s}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i,{initial:s}=this.props;if("string"==typeof s||"object"==typeof s){let n=u(this.props,s,null==(e=this.presenceContext)?void 0:e.custom);n&&(i=n[t])}if(s&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||tr(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new te),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class re extends rt{constructor(){super(...arguments),this.KeyframeResolver=ew}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tr(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class ri extends re{constructor(){super(...arguments),this.type="html",this.renderInstance=nX}readValueFromInstance(t,e){if($.has(e)){let t=t9(e);return t&&t.default||0}{let i=window.getComputedStyle(t),s=(ec(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iJ(t,e)}build(t,e,i){nI(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n_(t,e,i)}}class rs extends re{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iN}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if($.has(e)){let t=t9(e);return t&&t.default||0}return e=nK.has(e)?e:ta(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return nZ(t,e,i)}build(t,e,i){nW(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,s){nq(t,e,i,s)}mount(t){this.isSVGTag=nH(t.tagName),super.mount(t)}}let rn=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((i6={animation:{Feature:ip},exit:{Feature:iv},inView:{Feature:ny},tap:{Feature:nc},focus:{Feature:nh},hover:{Feature:nu},pan:{Feature:i8},drag:{Feature:i3,ProjectionNode:na,MeasureLayout:sd},layout:{ProjectionNode:na,MeasureLayout:sd}},i4=(t,e)=>nR(t)?new rs(e):new ri(e,{allowProjection:t!==st.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:s,createVisualElement:n,useRender:r,useVisualState:o,Component:l}=t;function u(t,e){var i;let s,u={...(0,st.useContext)(nw.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,st.useContext)(si.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:h}=u,d=function(t){let{initial:e,animate:i}=function(t,e){if(nT(t)){let{initial:e,animate:i}=t;return{initial:!1===e||a(e)?e:void 0,animate:a(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,st.useContext)(nP));return(0,st.useMemo)(()=>({initial:e,animate:i}),[nS(e),nS(i)])}(t),c=o(t,h);if(!h&&nA.B){(0,st.useContext)(nx).strict;let t=function(t){let{drag:e,layout:i}=nE;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(u);s=t.MeasureLayout,d.visualElement=function(t,e,i,s,n){var r,o;let{visualElement:a}=(0,st.useContext)(nP),l=(0,st.useContext)(nx),u=(0,st.useContext)(nD.t),h=(0,st.useContext)(nw.Q).reducedMotion,d=(0,st.useRef)(null);s=s||l.renderer,!d.current&&s&&(d.current=s(t,{visualState:e,parent:a,props:i,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:h}));let c=d.current,p=(0,st.useContext)(ss);c&&!c.projection&&n&&("html"===c.type||"svg"===c.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!o||a&&iV(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,layoutScroll:l,layoutRoot:u})}(d.current,i,n,p);let m=(0,st.useRef)(!1);(0,st.useInsertionEffect)(()=>{c&&m.current&&c.update(i,u)});let f=i[tl],v=(0,st.useRef)(!!f&&!(null==(r=window.MotionHandoffIsComplete)?void 0:r.call(window,f))&&(null==(o=window.MotionHasOptimisedAnimation)?void 0:o.call(window,f)));return(0,nC.E)(()=>{c&&(m.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),sl.render(c.render),v.current&&c.animationState&&c.animationState.animateChanges())}),(0,st.useEffect)(()=>{c&&(!v.current&&c.animationState&&c.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var t;null==(t=window.MotionHandoffMarkAsComplete)||t.call(window,f)}),v.current=!1))}),c}(l,c,u,n,t.ProjectionNode)}return(0,i7.jsxs)(nP.Provider,{value:d,children:[s&&d.visualElement?(0,i7.jsx)(s,{visualElement:d.visualElement,...u}):null,r(l,t,(i=d.visualElement,(0,st.useCallback)(t=>{t&&c.onMount&&c.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):iV(e)&&(e.current=t))},[i])),c,h,d.visualElement)]})}s&&function(t){for(let e in t)nE[e]={...nE[e],...t[e]}}(s),u.displayName="motion.".concat("string"==typeof l?l:"create(".concat(null!=(i=null!=(e=l.displayName)?e:l.name)?i:"",")"));let h=(0,st.forwardRef)(u);return h[nM]=l,h}({...nR(t)?nJ:n0,preloadedFeatures:i6,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let o=(nR(e)?function(t,e,i,s){let n=(0,st.useMemo)(()=>{let i=nY();return nW(i,e,nH(s),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};n1(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return n1(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,st.useMemo)(()=>{let i=nz();return nI(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),a=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(n3(n)||!0===i&&n2(n)||!e&&!n2(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),l=e!==st.Fragment?{...a,...o,ref:s}:{},{children:u}=i,h=(0,st.useMemo)(()=>tr(u)?u.get():u,[u]);return(0,st.createElement)(e,{...l,children:h})}}(e),createVisualElement:i4,Component:t})}))},32082:(t,e,i)=>{i.d(e,{xQ:()=>r});var s=i(12115),n=i(80845);function r(t=!0){let e=(0,s.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,s.useId)();(0,s.useEffect)(()=>{t&&a(l)},[t]);let u=(0,s.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,u]:[!0]}},51508:(t,e,i)=>{i.d(e,{Q:()=>s});let s=(0,i(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},68972:(t,e,i)=>{i.d(e,{B:()=>s});let s="undefined"!=typeof window},80845:(t,e,i)=>{i.d(e,{t:()=>s});let s=(0,i(12115).createContext)(null)},82885:(t,e,i)=>{i.d(e,{M:()=>n});var s=i(12115);function n(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}},90869:(t,e,i)=>{i.d(e,{L:()=>s});let s=(0,i(12115).createContext)({})},92657:(t,e,i)=>{i.d(e,{A:()=>s});let s=(0,i(19946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},97494:(t,e,i)=>{i.d(e,{E:()=>n});var s=i(12115);let n=i(68972).B?s.useLayoutEffect:s.useEffect}}]);