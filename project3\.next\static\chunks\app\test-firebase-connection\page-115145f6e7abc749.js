(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6834],{3979:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var n=t(95155),i=t(12115),a=t(98915),s=t(88482),l=t(97168),o=t(49509);function c(){let[e,r]=(0,i.useState)("Testing..."),[c,d]=(0,i.useState)({});(0,i.useEffect)(()=>{u()},[]);let u=async()=>{try{a.auth?(r("✅ Firebase Auth initialized successfully"),d({currentUser:a.auth.currentUser,config:{apiKey:o.env.NEXT_PUBLIC_FIREBASE_API_KEY?"Present":"Missing",authDomain:o.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN?"Present":"Missing",projectId:o.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID?"Present":"Missing"},domain:window.location.hostname})):(r("⚠️ Firebase has been replaced with Twilio SMS verification"),d({message:"Firebase authentication is no longer used in this application",replacement:"Twilio SMS verification is now used for phone number verification",config:{twilioConfigured:o.env.TWILIO_ACCOUNT_SID?"Present":"Missing"}}))}catch(e){r("❌ Firebase connection failed"),d({error:e.message})}};return(0,n.jsx)("div",{className:"min-h-screen bg-background p-8",children:(0,n.jsxs)("div",{className:"max-w-2xl mx-auto",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Firebase Connection Test"}),(0,n.jsxs)(s.Zp,{className:"p-6 mb-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Connection Status"}),(0,n.jsx)("pre",{className:"whitespace-pre-wrap text-sm bg-muted p-4 rounded",children:e})]}),(0,n.jsxs)(s.Zp,{className:"p-6 mb-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Configuration Details"}),(0,n.jsx)("pre",{className:"text-sm bg-muted p-4 rounded overflow-auto",children:JSON.stringify(c,null,2)})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)(l.$,{onClick:u,className:"w-full",children:"Retest Firebase Connection"}),(0,n.jsx)(l.$,{onClick:()=>{try{window.recaptchaVerifier&&(window.recaptchaVerifier.clear(),window.recaptchaVerifier=null);let{setupRecaptcha:e}=t(98915);e("test-recaptcha")&&r(e=>e+"\n✅ reCAPTCHA setup successful")}catch(e){r(r=>r+"\n❌ reCAPTCHA setup failed: "+e.message)}},variant:"outline",className:"w-full",children:"Test reCAPTCHA Setup"}),(0,n.jsx)("div",{id:"test-recaptcha"})]})]})})}},6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>s,t:()=>a});var n=t(12115);function i(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,n=e.map(e=>{let n=i(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():i(e[r],null)}}}}function s(...e){return n.useCallback(a(...e),e)}},8868:(e,r,t)=>{Promise.resolve().then(t.bind(t,3979))},53999:(e,r,t)=>{"use strict";t.d(r,{cn:()=>a});var n=t(52596),i=t(39688);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,i.QP)((0,n.$)(r))}},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>s});var n=t(52596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,s=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:l}=r,o=Object.keys(s).map(e=>{let r=null==t?void 0:t[e],n=null==l?void 0:l[e];if(null===r)return null;let a=i(r)||i(n);return s[e][a]}),c=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return a(e,o,null==r||null==(n=r.compoundVariants)?void 0:n.reduce((e,r)=>{let{class:t,className:n,...i}=r;return Object.entries(i).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...l,...c}[r]):({...l,...c})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},88482:(e,r,t)=>{"use strict";t.d(r,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>s,aR:()=>l,wL:()=>u});var n=t(95155),i=t(12115),a=t(53999);let s=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...i})});s.displayName="Card";let l=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",t),...i})});l.displayName="CardHeader";let o=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,n.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",t),...i})});o.displayName="CardTitle";let c=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,n.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",t),...i})});c.displayName="CardDescription";let d=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",t),...i})});d.displayName="CardContent";let u=i.forwardRef((e,r)=>{let{className:t,...i}=e;return(0,n.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",t),...i})});u.displayName="CardFooter"},97168:(e,r,t)=>{"use strict";t.d(r,{$:()=>c,r:()=>o});var n=t(95155),i=t(12115),a=t(99708),s=t(74466),l=t(53999);let o=(0,s.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=i.forwardRef((e,r)=>{let{className:t,variant:i,size:s,asChild:c=!1,...d}=e,u=c?a.DX:"button";return(0,n.jsx)(u,{className:(0,l.cn)(o({variant:i,size:s,className:t})),ref:r,...d})});c.displayName="Button"},98915:(e,r,t)=>{"use strict";t.r(r),t.d(r,{auth:()=>n,checkDomainAuthorization:()=>a,setupRecaptcha:()=>i}),console.warn("Firebase authentication has been replaced with Twilio SMS verification");let n=null,i=()=>null,a=()=>!0},99708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>l,Dc:()=>c,TL:()=>s});var n=t(12115),i=t(6101),a=t(95155);function s(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...a}=e;if(n.isValidElement(t)){var s;let e,l,o=(s=t,(l=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(l=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),c=function(e,r){let t={...r};for(let n in r){let i=e[n],a=r[n];/^on[A-Z]/.test(n)?i&&a?t[n]=(...e)=>{let r=a(...e);return i(...e),r}:i&&(t[n]=i):"style"===n?t[n]={...i,...a}:"className"===n&&(t[n]=[i,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==n.Fragment&&(c.ref=r?(0,i.t)(r,o):o),n.cloneElement(t,c)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:i,...s}=e,l=n.Children.toArray(i),o=l.find(d);if(o){let e=o.props.children,i=l.map(r=>r!==o?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...s,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,a.jsx)(r,{...s,ref:t,children:i})});return t.displayName=`${e}.Slot`,t}var l=s("Slot"),o=Symbol("radix.slottable");function c(e){let r=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=o,r}function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{e.O(0,[4277,8441,5964,7358],()=>e(e.s=8868)),_N_E=e.O()}]);