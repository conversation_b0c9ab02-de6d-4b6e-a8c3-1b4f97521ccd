(()=>{var a={};a.id=2143,a.ids=[2143],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6502:(a,b,c)=>{"use strict";function d(a){return/^\+[1-9]\d{9,14}$/.test(a)}function e(a){let b=a.replace(/[^\d+]/g,"");return b.startsWith("+")?b:`+1${b}`}c.d(b,{g0:()=>d,n4:()=>e})},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27343:(a,b,c)=>{"use strict";c.d(b,{KM:()=>e,MH:()=>f});let d=new Map;async function e(a,b){try{console.log("\uD83D\uDD27 Verification store - Starting request:",{phoneNumber:a,codeLength:b.length});let c=`${function(){let a=process.env.ADMIN_BASE_URL||"https://admin.codemedicalapps.com/";return a.endsWith("/")?a.slice(0,-1):a}()}/api/v1/verification/store`;console.log("\uD83D\uDD27 Using API URL:",c);let d=await fetch(c,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({PhoneNumber:a,VerificationCode:b,ExpirationMinutes:10})});if(console.log("\uD83D\uDCE5 Verification store response status:",d.status),console.log("\uD83D\uDCE5 Verification store response headers:",Object.fromEntries(d.headers.entries())),!d.ok){let a=await d.text();return console.error("❌ Verification store HTTP error:",d.status,d.statusText),console.error("❌ Verification store error response body:",a),!1}let e=await d.json();if(console.log("✅ Verification store response:",e),e.success)return console.log("✅ Verification code stored successfully"),!0;return console.error("❌ Verification store failed:",e.error||"Unknown error"),!1}catch(a){return console.error("❌ Error storing verification code:",a),console.error("❌ Error details:",a instanceof Error?a.message:"Unknown error"),!1}}async function f(a){let b=Date.now(),c=`va:${a}`,e=d.get(c)||{count:0,resetTime:b+9e5};return b>=e.resetTime?e={count:1,resetTime:b+9e5}:e.count+=1,d.set(c,e),e}setInterval(()=>{let a=Date.now();d.forEach((b,c)=>{b.resetTime<a&&d.delete(c)})},3e5)},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},35431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>E,patchFetch:()=>D,routeModule:()=>z,serverHooks:()=>C,workAsyncStorage:()=>A,workUnitAsyncStorage:()=>B});var d={};c.r(d),c.d(d,{POST:()=>y});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(40746),w=c(6502),x=c(27343);async function y(a){try{let b=a.headers,c=b.get("x-forwarded-for")||b.get("x-real-ip")||"unknown",d=(0,v.fs)(b);if(d.isBot)return console.warn(`Bot detected in verification: ${d.reason} from IP: ${c}`),u.NextResponse.json({error:"Request blocked for security reasons"},{status:403});let e=(0,v.TB)(b);if(!e.valid)return console.warn(`Invalid origin in verification: ${e.reason} from IP: ${c}`),u.NextResponse.json({error:"Request blocked for security reasons"},{status:403});let f=(0,v.bV)(c);if(f.suspicious)return console.warn(`Suspicious IP in verification: ${f.reason} - ${c}`),u.NextResponse.json({error:"Request blocked for security reasons"},{status:403});let{phoneNumber:g,code:h}=await a.json();if(!g||!h)return u.NextResponse.json({error:"Phone number and verification code are required"},{status:400});let i=(0,w.n4)(g);if(!(0,w.g0)(i))return u.NextResponse.json({error:"Invalid phone number format"},{status:400});let j=await (0,x.MH)(i);if(j.count>5)return u.NextResponse.json({error:"Too many verification attempts. Please try again later.",resetTime:j.resetTime},{status:429});let k=process.env.ADMIN_BASE_URL||"https://admin.codemedicalapps.com/",l=k.endsWith("/")?k.slice(0,-1):k,m=JSON.stringify({PhoneNumber:i,VerificationCode:h}),n=await fetch(`${l}/api/v1/verification/verify`,{method:"POST",headers:{"Content-Type":"application/json","Content-Length":m.length.toString(),"User-Agent":"NextJS-Frontend/1.0",Accept:"application/json"},body:m});if(!n.ok)throw Error("Failed to verify code with backend");let o=await n.json();if(o.success&&o.isValid)return console.log(`Phone number verified successfully: ${i.slice(0,5)}*****`),u.NextResponse.json({success:!0,message:"Phone number verified successfully"});return console.warn(`Verification failed for: ${i.slice(0,5)}***** - ${o.message}`),u.NextResponse.json({error:o.message||"Invalid verification code",remainingAttempts:o.attemptCount?3-o.attemptCount:0},{status:400})}catch(a){return console.error("Code verification error:",a),u.NextResponse.json({error:"Internal server error"},{status:500})}}let z=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/sms/verify-code/route",pathname:"/api/sms/verify-code",filename:"route",bundlePath:"app/api/sms/verify-code/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\api\\sms\\verify-code\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:A,workUnitAsyncStorage:B,serverHooks:C}=z;function D(){return(0,g.patchFetch)({workAsyncStorage:A,workUnitAsyncStorage:B})}async function E(a,b,c){var d;let e="/api/sms/verify-code/route";"/index"===e&&(e="/");let g=await z.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(y.dynamicRoutes[E]||y.routes[D]);if(F&&!x){let a=!!y.routes[D],b=y.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||z.isDev||x||(G="/index"===(G=D)?"/":G);let H=!0===z.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>z.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>z.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await z.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await z.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await z.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},40746:(a,b,c)=>{"use strict";c.d(b,{IK:()=>g,Q7:()=>f,TB:()=>i,bV:()=>j,fs:()=>h});var d=c(55511),e=c.n(d);function f(a){let b=a.replace(/[^\d+]/g,"");if(!/^\+[1-9]\d{9,14}$/.test(b))return{valid:!1,reason:"Invalid phone number format"};let c=b.replace(/^\+/,"");return/^(\d)\1{9,}$/.test(c)||c.split("").every((a,b)=>0===b||parseInt(a)===parseInt(c[b-1])+1)?{valid:!1,reason:"Suspicious phone number pattern"}:{valid:!0}}function g(a=6){let b="0123456789",c="";for(let d=0;d<a;d++){let a=e().randomInt(0,b.length);c+=b[a]}return c}function h(a){let b=a.get("user-agent")?.toLowerCase()||"",c=a.get("referer");for(let a of["bot","crawler","spider","scraper","curl","wget","python","java","postman","insomnia","httpie","axios","fetch"])if(b.includes(a))return{isBot:!0,reason:`Bot detected: ${a}`};return c||b.includes("mobile")?b.length<10||!b.includes("mozilla")?{isBot:!0,reason:"Suspicious user agent"}:{isBot:!1}:{isBot:!0,reason:"Missing referer header"}}function i(a){let b=a.get("origin"),c=a.get("referer"),d=["http://localhost:3000","https://codemedicalapps.com","https://www.codemedicalapps.com"];if(b&&!d.includes(b))return{valid:!1,reason:"Invalid origin"};if(c){let a=new URL(c),b=`${a.protocol}//${a.host}`;if(!d.includes(b))return{valid:!1,reason:"Invalid referer"}}return{valid:!0}}function j(a){return"127.0.0.1"===a||"::1"===a||a.startsWith("192.168.")||a.startsWith("10.")?{suspicious:!0,reason:"Private IP in production"}:{suspicious:!1}}},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:a=>{"use strict";a.exports=require("crypto")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},96487:()=>{}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,6055],()=>b(b.s=35431));module.exports=c})();