{"version": 1, "files": ["../../webpack-runtime.js", "../../chunks/4985.js", "../../chunks/1697.js", "../../chunks/9500.js", "../../chunks/9822.js", "../../chunks/6085.js", "page_client-reference-manifest.js", "../../../../package.json", "../../../../components/ui/button.tsx", "../../../../components/ui/skeleton.tsx", "../../../../components/ui/breadcrumb.tsx", "../../../../components/ui/card.tsx", "../../../../contexts/settings-context.tsx", "../../../../lib/utils.ts", "../../../../components/ui/tabs.tsx", "../../../../lib/api-helper.ts", "../../../../contexts/user-context.tsx", "../../../../lib/encryption.ts", "../../../../lib/translations.ts", "../../../../lib/color-utils.ts"]}