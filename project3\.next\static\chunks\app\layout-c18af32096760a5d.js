(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{4115:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(95155);s(12115);let r=e=>{let{className:t,...s}=e;return(0,a.jsxs)("svg",{viewBox:"0 0 308 308",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",className:t,...s,children:[(0,a.jsx)("path",{d:"M227.904 176.981c-.6-.288-23.054-11.345-27.044-12.781-1.629-.585-3.374-1.156-5.23-1.156-3.032 0-5.579 1.511-7.563 4.479-2.243 3.334-9.033 11.271-11.131 13.642-.274.313-.648.687-.872.687-.201 0-3.676-1.431-4.728-1.888-24.087-10.463-42.37-35.624-44.877-39.867-.358-.61-.373-.887-.376-.887.088-.323.898-1.135 1.316-1.554 1.223-1.21 2.548-2.805 3.83-4.348.607-.731 1.215-1.463 1.812-2.153 1.86-2.164 2.688-3.844 3.648-5.79l.503-1.011c2.344-4.657.342-8.587-.305-9.856-.531-1.062-10.012-23.944-11.02-26.348-2.424-5.801-5.627-8.502-10.078-8.502-.413 0 0 0-1.732.073-2.109.089-13.594 1.601-18.672 4.802-5.385 3.395-14.495 14.217-14.495 33.249 0 17.129 10.87 33.302 15.537 39.453.116.155.329.47.638.922 17.873 26.102 40.154 45.446 62.741 54.469 21.745 8.686 32.042 9.69 37.896 9.69h.001c2.46 0 4.429-.193 6.166-.364l1.102-.105c7.512-.666 24.02-9.22 27.775-19.655 2.958-8.219 3.738-17.199 1.77-20.458-1.418-2.472-3.741-3.587-6.682-4.999z"}),(0,a.jsx)("path",{d:"M156.734 0C73.318 0 5.454 67.354 5.454 150.143c0 26.777 7.166 52.988 20.741 75.928L.212 302.716a3.999 3.999 0 0 0 .933 4.085A3.99 3.99 0 0 0 4 308c.405 0 .813-.061 1.211-.188l79.92-25.396c21.87 11.685 46.588 17.853 71.604 17.853 83.409 0 151.266-67.347 151.266-150.127C308 67.354 240.143 0 156.734 0zM156.734 268.994c-23.539 0-46.338-6.797-65.936-19.657a4.005 4.005 0 0 0-3.406-.655 4.018 4.018 0 0 0-1.212.188l-40.035 12.726 12.924-38.129a4.001 4.001 0 0 0-.561-3.647C43.584 199.428 35.695 175.335 35.695 150.143c0-65.543 53.754-118.867 119.826-118.867 66.064 0 119.812 53.324 119.812 118.867 0 65.535-53.747 118.851-119.599 118.851z"})]})}},14503:(e,t,s)=>{"use strict";s.d(t,{dj:()=>m,oR:()=>d});var a=s(12115);let r=0,o=new Map,n=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},i=[],l={toasts:[]};function c(e){l=((e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?n(s):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}})(l,e),i.forEach(e=>{e(l)})}function d(e){let{duration:t=2e3,...s}=e,a=(r=(r+1)%100).toString(),o=()=>c({type:"DISMISS_TOAST",toastId:a});return c({type:"ADD_TOAST",toast:{...s,id:a,duration:t,open:!0,onOpenChange:e=>{e||o()}}}),setTimeout(()=>{o()},t),{id:a,dismiss:o,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function m(){let[e,t]=a.useState(l);return a.useEffect(()=>(i.push(t),()=>{let e=i.indexOf(t);e>-1&&i.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}d.success=(e,t)=>d({description:e,type:"success",duration:2e3,...t}),d.error=(e,t)=>d({description:e,type:"error",duration:2e3,...t}),d.warning=(e,t)=>d({description:e,type:"warning",duration:2e3,...t}),d.info=(e,t)=>d({description:e,type:"info",duration:2e3,...t})},15796:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(95155);s(12115);let r=e=>{let{className:t,...s}=e;return(0,a.jsxs)("svg",{viewBox:"0 0 455 455",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",className:t,...s,children:[(0,a.jsx)("path",{d:"M0 0v455h455V0H0zm384.814 100.68l-53.458 257.136a8.001 8.001 0 0 1-13.401 5.172l-72.975-52.981a11.996 11.996 0 0 0-11.942-.744l-40.46 32.981c-4.695 3.84-11.771 1.7-13.569-4.083l-28.094-90.351-72.583-27.089c-7.373-2.762-7.436-13.171-.084-16.003L373.36 90.959c6.315-2.442 12.83 3.09 11.454 9.721z"}),(0,a.jsx)("path",{d:"M313.567 147.179l-141.854 87.367c-5.437 3.355-7.996 9.921-6.242 16.068l15.337 53.891a4.002 4.002 0 0 0 7.162-.517l3.986-29.553a20.016 20.016 0 0 1 7.522-14.522l117.069-108.822c2.729-2.53.105-5.977-2.653-4.912z",fill:"#ffffff"})]})}},19324:()=>{},23472:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});var a=s(95155);s(12115);let r=e=>{let{className:t,...s}=e;return(0,a.jsxs)("svg",{viewBox:"-1 0 226 226",xmlns:"http://www.w3.org/2000/svg",fill:"currentColor",className:t,...s,children:[(0,a.jsx)("path",{d:"M41.255 185.52v40.2l37.589-21.37c10.478 3.02 21.616 4.65 33.156 4.65 61.86 0 112-46.79 112-104.5 0-57.714-50.14-104.5-112-104.5-61.856 0-112 46.786-112 104.5 0 32.68 16.078 61.86 41.255 81.02z"}),(0,a.jsx)("path",{d:"m100.04 75.878-60.401 63.952 54.97-30.16 28.721 30.16 60.06-63.952-54.36 29.632-28.99-29.632z",fill:"#ffffff"})]})}},59268:(e,t,s)=>{"use strict";s.d(t,{Z:()=>n,n:()=>i});var a=s(95155),r=s(12115);let o=(0,r.createContext)(void 0);function n(e){let{children:t}=e,[s,n]=(0,r.useState)([]),[i,l]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=localStorage.getItem("wishlist");if(e)try{let t=JSON.parse(e);if(Array.isArray(t)&&t.length>0)if("number"==typeof t[0]){let e=t.map(e=>({productId:e,productName:"Product ".concat(e),productUrl:"/product/".concat(e),addedAt:new Date().toISOString()}));n(e),localStorage.setItem("wishlist",JSON.stringify(e))}else n(t)}catch(e){console.error("Failed to parse wishlist from localStorage:",e)}l(!0)},[]),(0,r.useEffect)(()=>{localStorage.setItem("wishlist",JSON.stringify(s))},[s]),(0,a.jsx)(o.Provider,{value:{wishlistItems:s,addToWishlist:(e,t,a,r,o)=>{s.some(t=>t.productId===e)||n([...s,{productId:e,productName:t,productUrl:a,imageUrl:r,price:o,addedAt:new Date().toISOString()}])},removeFromWishlist:e=>{n(s.filter(t=>t.productId!==e))},isInWishlist:e=>s.some(t=>t.productId===e),getWishlistItem:e=>s.find(t=>t.productId===e),totalItems:s.length,isHydrated:i},children:t})}function i(){let e=(0,r.useContext)(o);if(void 0===e)throw Error("useWishlist must be used within a WishlistProvider");return e}},71826:(e,t,s)=>{Promise.resolve().then(s.bind(s,87594))},87594:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>ek});var a=s(95155);s(19324);var r=s(12115),o=s(29840),n=s.n(o),i=s(19420),l=s(28883),c=s(34869),d=s(71366),m=s(71007),h=s(66474),u=s(37108),p=s(4516),x=s(51976),f=s(34835),g=s(12318),y=s(47924),j=s(27809),b=s(54416),v=s(74783),N=s(35695),w=s(6874),C=s.n(w),A=s(78067),k=s(59268),S=s(56671),I=s(65409),T=s(46248),P=s(74466),D=s(53999);let E=r.forwardRef((e,t)=>{let{className:s,children:r,...o}=e;return(0,a.jsxs)(T.bL,{ref:t,className:(0,D.cn)("relative z-10 flex max-w-max flex-1 items-center justify-center",s),...o,children:[r,(0,a.jsx)(U,{})]})});E.displayName=T.bL.displayName;let _=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(T.B8,{ref:t,className:(0,D.cn)("group flex flex-1 list-none items-center justify-center space-x-1 bg-white",s),...r})});_.displayName=T.B8.displayName;let M=T.q7,z=(0,P.F)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-white px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50");r.forwardRef((e,t)=>{let{className:s,children:r,...o}=e;return(0,a.jsxs)(T.l9,{ref:t,className:(0,D.cn)(z(),"group",s),...o,children:[r," ",(0,a.jsx)(h.A,{className:"relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180","aria-hidden":"true"})]})}).displayName=T.l9.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(T.UC,{ref:t,className:(0,D.cn)("left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto ",s),...r})}).displayName=T.UC.displayName;let O=T.N_,U=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{className:(0,D.cn)("absolute left-0 top-full flex justify-center z-50"),children:(0,a.jsx)(T.LM,{className:(0,D.cn)("origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-white text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)] !bg-white",s),ref:t,...r})})});U.displayName=T.LM.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(T.C1,{ref:t,className:(0,D.cn)("top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in",s),...r,children:(0,a.jsx)("div",{className:"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md"})})}).displayName=T.C1.displayName;var L=s(20547);let R=L.bL,F=L.l9,$=r.forwardRef((e,t)=>{let{className:s,align:r="center",sideOffset:o=4,...n}=e;return(0,a.jsx)(L.ZL,{children:(0,a.jsx)(L.UC,{ref:t,align:r,sideOffset:o,className:(0,D.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...n})})});$.displayName=L.UC.displayName;var W=s(97168),V=s(79891),B=s(98816),J=s(94213);let q=["#0074b2","#194234","#2a9d8f","#81d4fa","#f295ce","#fce4ec","#b39ddb","#bcaaa4","#ffccbc","#b2dfdb","#6c9bcf","#ffd552","#39b1df","#7986cb","#003554"];function H(e){let{onColorSelect:t,onClose:s}=e,[o,n]=(0,r.useState)("#0074b2");return(0,r.useEffect)(()=>{n("#0074b2"),document.documentElement.style.setProperty("--primary","#0074b2"),document.documentElement.style.setProperty("--primary-foreground",(0,J.N)("#0074b2"))},[]),(0,a.jsx)("div",{className:"fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"bg-card border rounded-lg shadow-lg p-6 w-[320px] space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold",children:"Choose a Color"}),(0,a.jsx)(W.$,{variant:"ghost",size:"icon",onClick:s,children:(0,a.jsx)(b.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-2",children:q.map(e=>(0,a.jsx)("button",{className:"w-full aspect-square rounded-lg transition-all duration-200 hover:scale-105 hover:shadow-lg ".concat(o===e?"ring-2 ring-primary":""),style:{backgroundColor:e,color:(0,J.N)(e)},onClick:()=>(e=>{n(e);let s=(0,J.N)(e);document.documentElement.style.setProperty("--primary",e),document.documentElement.style.setProperty("--primary-foreground",s),t(e)})(e),children:o===e&&"✓"},e))})]})})}function G(){let e=(0,N.useRouter)(),[t,s]=(0,r.useState)([]),[o,n]=(0,r.useState)(!0),[w,T]=(0,r.useState)(!1),[P,z]=(0,r.useState)(null),[U,L]=(0,r.useState)(null),[J,q]=(0,r.useState)(null),[G,Q]=(0,r.useState)(!1),[Z,K]=(0,r.useState)(!1),[Y,X]=(0,r.useState)(""),[ee,et]=(0,r.useState)(0),[es,ea]=(0,r.useState)(0),er=(0,A._)(),eo=(0,k.n)(),{user:en,isLoggedIn:ei,logout:el}=(0,B.J)(),{theme:ec,language:ed,primaryColor:em,primaryTextColor:eh,toggleTheme:eu,setLanguage:ep,setPrimaryColor:ex,t:ef}=(0,V.t)(),eg=()=>{let t=new URLSearchParams;Y&&t.append("search",Y),U&&t.append("category",U.toString()),e.push("/products?".concat(t.toString()))};return(0,r.useEffect)(()=>{(async()=>{try{var e,t;let a={"Content-Type":"application/json",Accept:"application/json",Authorization:"Bearer "+localStorage.getItem("token")},r=await (0,I.MakeApiCallAsync)(I.Config.END_POINT_NAMES.GET_CATEGORIES_LIST,null,{PageNumber:1,PageSize:100,SortColumn:"Name",SortOrder:"ASC"},a,"POST",!0);if(null==r||null==(e=r.data)?void 0:e.data)try{let e=JSON.parse(r.data.data);if(Array.isArray(e)){let t=e.filter(e=>!e.ParentCategoryID),a=e.filter(e=>e.ParentCategoryID),r=t.map(e=>({id:e.CategoryID,name:e.Name,subcategories:a.filter(t=>t.ParentCategoryID===e.CategoryID).map(e=>e.Name)}));s(r)}else console.error("Categories data is not an array:",e),s([])}catch(e){console.error("Error parsing categories data:",e),s([])}else(null==r||null==(t=r.data)?void 0:t.errorMessage)?console.error("API Error:",r.data.errorMessage):console.error("Invalid or empty response from API"),s([])}catch(e){console.error("Error fetching categories:",e),s([])}finally{n(!1)}})()},[]),(0,r.useEffect)(()=>{er&&er.items&&et(er.totalItems)},[er,null==er?void 0:er.items]),(0,r.useEffect)(()=>{eo&&ea(eo.totalItems)},[eo,null==eo?void 0:eo.wishlistItems]),(0,a.jsxs)("header",{className:"w-full",children:[(0,a.jsx)(W.$,{variant:"ghost",size:"sm",className:"fixed bottom-36 right-4 md:bottom-24 md:right-6 md:left-auto z-50 bg-gradient-to-r from-purple-500 via-pink-500 to-red-500 shadow-2xl rounded-2xl border-2 border-white/50 flex hover:scale-110 hover:-rotate-6 transition-all duration-300 hover:shadow-purple-500/50 group items-center justify-center w-14 h-14 md:w-16 md:h-16 hover:rounded-full",onClick:()=>T(!0),children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"h-8 w-8 md:h-10 md:w-10 rounded-xl group-hover:rounded-full ring-2 ring-white/80 group-hover:ring-4 transition-all duration-300 shadow-inner",style:{backgroundColor:em}}),(0,a.jsx)("div",{className:"absolute -top-2 -right-2 w-5 h-5 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full shadow-lg animate-spin group-hover:animate-pulse"}),(0,a.jsx)("div",{className:"absolute -bottom-1 -left-1 w-3 h-3 bg-gradient-to-br from-green-400 to-emerald-500 rounded-full animate-bounce"})]})}),(0,a.jsx)("div",{className:"hidden md:block py-2.5",style:{backgroundColor:em,color:eh},children:(0,a.jsxs)("div",{className:"container mx-auto flex flex-col md:flex-row justify-between items-center text-sm px-4",children:[(0,a.jsxs)("div",{className:"flex md:flex-row items-start justify-start gap-4 md:gap-8",children:[(0,a.jsxs)(C(),{href:"tel:00*************",className:"flex items-center gap-2 hover:text-white/80",children:[(0,a.jsx)(i.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-xs md:text-sm",children:ef("phone")})]}),(0,a.jsxs)(C(),{href:"mailto:<EMAIL>",className:"flex items-center gap-2 hover:text-white/80",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-xs md:text-sm",children:ef("email")})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 md:gap-4",children:[(0,a.jsxs)(W.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",onClick:()=>ep("en"===ed?"ar":"en"),children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:"en"===ed?"العربية":"English"})]}),(0,a.jsxs)(W.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 flex items-center gap-2",onClick:()=>window.open("https://wa.me/*************?text=".concat(encodeURIComponent("Hello! I would like to chat with you regarding your services.")),"_blank"),children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:ef("liveChat")})]}),ei?(0,a.jsxs)(R,{children:[(0,a.jsx)(F,{asChild:!0,children:(0,a.jsxs)(W.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{className:"text-sm",children:["Welcome, ",(null==en?void 0:en.FirstName)||(null==en?void 0:en.UserName)]}),(0,a.jsx)(h.A,{className:"h-3 w-3"})]})}),(0,a.jsx)($,{className:"w-48 p-2 bg-white border border-gray-200 shadow-lg",align:"end",children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(C(),{href:"/account",children:(0,a.jsxs)(W.$,{variant:"ghost",size:"sm",className:"w-full justify-start hover:bg-gray-50",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"My Account"]})}),(0,a.jsx)(C(),{href:"/orders",children:(0,a.jsxs)(W.$,{variant:"ghost",size:"sm",className:"w-full justify-start hover:bg-gray-50",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"My Orders"]})}),(0,a.jsx)(C(),{href:"/addresses",children:(0,a.jsxs)(W.$,{variant:"ghost",size:"sm",className:"w-full justify-start hover:bg-gray-50",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"My Addresses"]})}),(0,a.jsx)(C(),{href:"/wishlist",children:(0,a.jsxs)(W.$,{variant:"ghost",size:"sm",className:"w-full justify-start hover:bg-gray-50",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Wishlist"]})}),(0,a.jsx)("div",{className:"border-t border-gray-100 my-1"}),(0,a.jsxs)(W.$,{variant:"ghost",size:"sm",className:"w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50",onClick:()=>{el(),e.push("/"),S.oR.success("Logged out successfully")},children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Logout"]})]})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(C(),{href:"/login",children:(0,a.jsxs)(W.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:ef("login")})]})}),(0,a.jsx)(C(),{href:"/signup",children:(0,a.jsxs)(W.$,{variant:"ghost",size:"sm",className:"text-white hover:text-white/80 p-1 md:p-2 flex items-center gap-2",children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:ef("signUp")})]})})]})]})]})}),(0,a.jsxs)("div",{className:"container mx-auto py-4 px-4",children:[(0,a.jsxs)("div",{className:"md:hidden",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("div",{className:"w-20"}),(0,a.jsx)(C(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)("div",{className:"text-[#1B3764] flex items-center gap-2",children:(0,a.jsx)("img",{src:"".concat("https://admin.codemedicalapps.com/","content/commonImages/otherImages/18b_logo2x.png"),alt:"Logo",className:"h-12 w-auto"})})}),(0,a.jsxs)(W.$,{variant:"ghost",size:"sm",className:"flex items-center gap-2 px-3 py-2 rounded-full border border-gray-200 hover:bg-gray-50",onClick:()=>ep("en"===ed?"ar":"en"),children:[(0,a.jsx)("span",{className:"text-lg",children:"en"===ed?"\uD83C\uDDFA\uD83C\uDDF8":"\uD83C\uDDEE\uD83C\uDDF6"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"en"===ed?"EN":"AR"})]})]}),(0,a.jsx)("div",{className:"w-full",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 border rounded-full px-4 py-3 bg-background shadow-sm",children:[(0,a.jsx)("input",{type:"text",placeholder:ef("products")||"البحث عن المنتجات...",className:"bg-transparent border-none focus:outline-none text-sm flex-1 placeholder:text-gray-400",value:Y,onChange:e=>X(e.target.value),onKeyDown:e=>"Enter"===e.key&&eg()}),(0,a.jsx)(W.$,{variant:"ghost",className:"h-8 w-8 p-0 hover:bg-accent/80 transition-colors",style:{color:em},onClick:eg,children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})})]}),(0,a.jsxs)("div",{className:"hidden md:flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 flex-1",children:[(0,a.jsx)(C(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)("div",{className:"text-[#1B3764] flex items-center gap-2",children:(0,a.jsx)("img",{src:"".concat("https://admin.codemedicalapps.com/","content/commonImages/otherImages/18b_logo2x.png"),alt:"Logo",className:"h-16 w-auto"})})}),(0,a.jsxs)("div",{className:"flex items-center gap-2 border rounded-full px-3 py-1.5 flex-1 max-w-md ml-4",children:[(0,a.jsxs)(R,{children:[(0,a.jsx)(F,{asChild:!0,children:(0,a.jsxs)(W.$,{variant:"ghost",className:"h-8 flex items-center gap-1 px-2",children:[(0,a.jsx)("span",{className:"text-muted-foreground text-sm",children:P||J||ef("category")}),(0,a.jsx)(h.A,{className:"h-4 w-4 text-muted-foreground"})]})}),(0,a.jsx)($,{className:"w-64 p-0 bg-white border border-gray-200 shadow-lg",align:"start",children:(0,a.jsx)("div",{className:"max-h-[300px] overflow-auto",children:o?(0,a.jsx)("div",{className:"p-4 text-center text-muted-foreground",children:ef("loadingCategories")}):(0,a.jsx)("div",{className:"grid",children:t.map(e=>(0,a.jsxs)("div",{className:"group",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 text-left hover:bg-gray-50",onClick:()=>{z(e.name),L(e.id),q(null)},children:e.name}),(0,a.jsx)("div",{className:"hidden group-hover:block absolute left-full top-0 w-48 bg-white shadow-lg rounded-md border border-gray-200",children:e.subcategories.map((t,s)=>(0,a.jsx)("button",{className:"w-full px-4 py-2 text-left hover:bg-gray-50",onClick:()=>{q(t),z(null),L(e.id)},children:t},s))})]},e.id))})})})]}),(0,a.jsx)("div",{className:"h-5 w-px bg-border mx-2"}),(0,a.jsx)("input",{type:"text",placeholder:ef("products"),className:"bg-transparent border-none focus:outline-none text-sm flex-1",value:Y,onChange:e=>X(e.target.value),onKeyDown:e=>"Enter"===e.key&&eg()}),(0,a.jsx)(W.$,{variant:"ghost",className:"h-8 w-8 p-0",onClick:eg,children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})]})]}),(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsx)(E,{children:(0,a.jsxs)(_,{children:[(0,a.jsx)(M,{children:(0,a.jsx)(O,{asChild:!0,children:(0,a.jsx)(C(),{href:"/",className:(0,D.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:ef("home")})})}),(0,a.jsx)(M,{children:(0,a.jsx)(O,{asChild:!0,children:(0,a.jsx)(C(),{href:"/hot-deals",className:(0,D.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:ef("hotDeals")})})}),(0,a.jsx)(M,{children:(0,a.jsx)(O,{asChild:!0,children:(0,a.jsx)(C(),{href:"/products",className:(0,D.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:ef("products")||"Products"})})}),(0,a.jsx)(M,{children:(0,a.jsx)(O,{asChild:!0,children:(0,a.jsx)(C(),{href:"/payment-methods",className:(0,D.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:ef("paymentMethods")})})}),(0,a.jsx)(M,{children:(0,a.jsx)(O,{asChild:!0,children:(0,a.jsx)(C(),{href:"/follow-us",className:(0,D.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:ef("followUs")})})}),(0,a.jsx)(M,{children:(0,a.jsx)(O,{asChild:!0,children:(0,a.jsx)(C(),{href:"/about",className:(0,D.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:ef("aboutUs")})})}),(0,a.jsx)(M,{children:(0,a.jsx)(O,{asChild:!0,children:(0,a.jsx)(C(),{href:"/contact",className:(0,D.cn)("group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50"),children:ef("contactUs")})})})]})})}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"hidden md:flex items-center gap-4",children:[(0,a.jsx)(C(),{href:"/wishlist",children:(0,a.jsxs)(W.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,a.jsx)(x.A,{className:"h-5 w-5",style:{color:em}}),(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center",children:es})]})}),(0,a.jsx)(C(),{href:"/cart",children:(0,a.jsxs)(W.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,a.jsx)(j.A,{className:"h-5 w-5",style:{color:em}}),(0,a.jsx)("span",{className:"absolute -top-1 -right-1 bg-primary text-white text-xs rounded-full h-4 w-4 flex items-center justify-center",children:ee})]})})]}),(0,a.jsx)(W.$,{variant:"ghost",size:"icon",className:"md:hidden",onClick:()=>Q(!G),"aria-label":"Toggle menu",children:G?(0,a.jsx)(b.A,{className:"h-6 w-6"}):(0,a.jsx)(v.A,{className:"h-6 w-6"})})]})]})]}),G&&(0,a.jsx)("div",{className:"md:hidden bg-white border-t border-gray-200 shadow-lg",children:(0,a.jsxs)("nav",{className:"px-4 py-4 space-y-2",children:[(0,a.jsx)(C(),{href:"/",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>Q(!1),children:ef("home")}),(0,a.jsx)(C(),{href:"/hot-deals",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>Q(!1),children:ef("hotDeals")}),(0,a.jsx)(C(),{href:"/products",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>Q(!1),children:ef("products")||"Products"}),(0,a.jsx)(C(),{href:"/payment-methods",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>Q(!1),children:ef("paymentMethods")}),(0,a.jsx)(C(),{href:"/follow-us",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>Q(!1),children:ef("followUs")}),(0,a.jsx)(C(),{href:"/about",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>Q(!1),children:ef("aboutUs")}),(0,a.jsx)(C(),{href:"/contact",className:"block px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>Q(!1),children:ef("contactUs")}),(0,a.jsxs)("div",{className:"border-t border-gray-200 pt-4 mt-4",children:[(0,a.jsxs)(C(),{href:"/wishlist",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>Q(!1),children:[(0,a.jsx)(x.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Wishlist"}),(0,a.jsx)("span",{className:"ml-auto bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:es})]}),(0,a.jsxs)(C(),{href:"/cart",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>Q(!1),children:[(0,a.jsx)(j.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Cart"}),(0,a.jsx)("span",{className:"ml-auto bg-primary text-white text-xs rounded-full h-5 w-5 flex items-center justify-center",children:ee})]})]}),(0,a.jsx)("div",{className:"border-t border-gray-200 pt-4 mt-4",children:ei?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"px-4 py-2 text-sm text-gray-500",children:["Welcome, ",(null==en?void 0:en.FirstName)||(null==en?void 0:en.UserName)]}),(0,a.jsxs)(C(),{href:"/account",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>Q(!1),children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"My Account"})]}),(0,a.jsxs)(C(),{href:"/orders",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>Q(!1),children:[(0,a.jsx)(u.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"My Orders"})]}),(0,a.jsxs)(C(),{href:"/addresses",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>Q(!1),children:[(0,a.jsx)(p.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"My Addresses"})]}),(0,a.jsxs)("button",{className:"flex items-center gap-3 w-full px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors",onClick:()=>{el(),Q(!1),e.push("/"),S.oR.success("Logged out successfully")},children:[(0,a.jsx)(f.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Logout"})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(C(),{href:"/login",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>Q(!1),children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:ef("login")})]}),(0,a.jsxs)(C(),{href:"/signup",className:"flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary rounded-lg transition-colors",onClick:()=>Q(!1),children:[(0,a.jsx)(g.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:ef("signUp")})]})]})})]})}),w&&(0,a.jsx)(H,{onColorSelect:e=>{ex(e),T(!1)},onClose:()=>T(!1)})]})}var Q=s(10488),Z=s(4115),K=s(23472),Y=s(15796),X=s(30242);function ee(){let{primaryColor:e,primaryTextColor:t,t:s}=(0,V.t)(),[o,n]=(0,r.useState)(""),[i,l]=(0,r.useState)(!1),[c,d]=(0,r.useState)({type:"",text:""}),{executeRecaptcha:m}=(0,X._Y)(),h=async e=>{if(e.preventDefault(),!o||!o.trim())return void d({type:"error",text:"Please enter a valid email address."});if(!m){d({type:"error",text:"reCAPTCHA not available. Please refresh the page and try again."}),console.error("Execute recaptcha not yet available");return}try{l(!0),d({type:"",text:""});try{if(!await m("subscribe"))throw Error("reCAPTCHA verification failed")}catch(e){console.error("reCAPTCHA error:",e),d({type:"error",text:"reCAPTCHA verification failed. Please try again."});return}let e="".concat("https://admin.codemedicalapps.com/","api/v1/dynamic/dataoperation/insert-subscriber");console.log("Making request to:",e);let t=await fetch(e,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({requestParameters:{SubscriberEmail:o.trim()}})});if(console.log("Response status:",t.status),!t.ok)throw Error("HTTP error! status: ".concat(t.status));let s=await t.json();console.log("Response data:",s),200===s.statusCode||t.ok?(d({type:"success",text:"Successfully subscribed to our newsletter!"}),n("")):d({type:"error",text:s.message||s.errorMessage||"Failed to subscribe. Please try again."})}catch(e){console.error("Subscription error:",e),e instanceof TypeError&&e.message.includes("fetch")?d({type:"error",text:"Network error. Please check your connection and try again."}):d({type:"error",text:"Subscription failed: ".concat(e instanceof Error?e.message:"Unknown error")})}finally{l(!1)}};return(0,a.jsx)("footer",{className:"w-full",children:(0,a.jsxs)("div",{className:"py-8 sm:py-12",style:{backgroundColor:e,color:t},children:[(0,a.jsxs)("div",{className:"container mx-auto px-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 sm:gap-8",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)("img",{src:"".concat("https://admin.codemedicalapps.com/","content/commonImages/otherImages/18b_logo2x.png").replace(/\//g,"/"),alt:"Logo",className:"h-12 sm:h-16 w-auto bg-white p-2 rounded-md"})}),(0,a.jsx)("p",{className:"text-sm sm:text-base opacity-90",children:"We are professional team specialized in providing well known valuable medical courses, Ebooks, Printed books and popular medical accounts for all medical field staff all around the world with low cost and in short time."}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(C(),{href:"https://www.facebook.com/codemedicalapps/",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-70 transition-opacity",children:(0,a.jsx)(Q.A,{className:"h-5 w-5"})}),(0,a.jsx)(C(),{href:"https://t.me/codemedicalapps",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-70 transition-opacity",children:(0,a.jsx)(Y.A,{className:"h-5 w-5"})}),(0,a.jsx)(C(),{href:"https://wa.me/*************",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-70 transition-opacity",children:(0,a.jsx)(Z.A,{className:"h-5 w-5"})}),(0,a.jsx)(C(),{href:"https://m.me/***************",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-70 transition-opacity",children:(0,a.jsx)(K.A,{className:"h-5 w-5",style:{color:"#00B2FF"}})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-6 sm:gap-8 col-span-2 sm:col-span-1 md:col-span-2",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:s("quickLinks")}),(0,a.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,a.jsx)("li",{children:(0,a.jsx)(C(),{href:"/about",className:"hover:opacity-70 transition-opacity",children:s("about")})}),(0,a.jsx)("li",{children:(0,a.jsx)(C(),{href:"/contact",className:"hover:opacity-70 transition-opacity",children:s("contact")})}),(0,a.jsx)("li",{children:(0,a.jsx)(C(),{href:"/hot-deals",className:"hover:opacity-70 transition-opacity",children:s("hotDeals")})}),(0,a.jsx)("li",{children:(0,a.jsx)(C(),{href:"/login",className:"hover:opacity-70 transition-opacity",children:s("login")})}),(0,a.jsx)("li",{children:(0,a.jsx)(C(),{href:"/signup",className:"hover:opacity-70 transition-opacity",children:s("signup")})})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:s("customerArea")}),(0,a.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,a.jsx)("li",{children:(0,a.jsx)(C(),{href:"/account",className:"hover:opacity-70 transition-opacity",children:s("myAccount")})}),(0,a.jsx)("li",{children:(0,a.jsx)(C(),{href:"/orders",className:"hover:opacity-70 transition-opacity",children:s("orders")})}),(0,a.jsx)("li",{children:(0,a.jsx)(C(),{href:"/cart",className:"hover:opacity-70 transition-opacity",children:s("cart")})}),(0,a.jsx)("li",{children:(0,a.jsx)(C(),{href:"/wishlist",className:"hover:opacity-70 transition-opacity",children:s("wishlist")})}),(0,a.jsx)("li",{children:(0,a.jsx)(C(),{href:"/payment-methods",className:"hover:opacity-70 transition-opacity",children:s("paymentMethods")})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold",children:s("contact")}),(0,a.jsxs)("ul",{className:"space-y-2 sm:space-y-3 text-sm sm:text-base",children:[(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"opacity-75",children:[s("location"),":"]}),(0,a.jsx)("span",{children:"Iraq"})]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"opacity-75",children:[s("callUs"),":"]}),(0,a.jsx)("a",{href:"tel:+*************",className:"hover:opacity-70 transition-opacity",children:"+964 ************"})]}),(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:"opacity-75",children:[s("emailUs"),":"]}),(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"hover:opacity-70 transition-opacity",children:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h3",{className:"text-base sm:text-lg font-semibold mb-4",children:s("newsletter")}),(0,a.jsxs)("form",{onSubmit:h,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,a.jsx)("input",{type:"email",value:o,onChange:e=>n(e.target.value),placeholder:s("enterEmail"),required:!0,className:"w-full px-4 py-2 text-gray-900 rounded-md focus:outline-none focus:ring-2 focus:ring-opacity-50",style:{outlineColor:e}}),(0,a.jsx)("button",{type:"submit",disabled:i,className:" px-2 py-2 border border-white rounded-md",style:{backgroundColor:e,color:t,borderColor:t},children:i?(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}):(0,a.jsx)(a.Fragment,{children:(0,a.jsx)("span",{children:s("subscribe")})})})]}),c.text&&(0,a.jsx)("p",{className:"text-sm text-center ".concat("success"===c.type?"text-green-400":"text-red-400"),children:c.text}),(0,a.jsx)("p",{className:"text-xs opacity-75 text-center",children:s("newsletterDisclaimer")})]})]})]})]}),(0,a.jsx)("div",{className:"mt-6 pt-6 border-t text-center opacity-75 text-sm sm:text-base px-4",style:{borderColor:t},children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-4",children:[(0,a.jsx)("p",{children:"\xa9 2024 Code Medical. All rights reserved."}),(0,a.jsx)("span",{className:"hidden sm:inline",children:"•"}),(0,a.jsxs)("p",{children:["Powered by"," ",(0,a.jsx)("a",{href:"https://perfectjobline.com/",target:"_blank",rel:"noopener noreferrer",className:"hover:opacity-70 transition-opacity underline",children:"perfectjobline"})]})]})})]})})}function et(e){let{className:t=""}=e;return(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:t,fill:"currentColor",children:(0,a.jsx)("path",{d:"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893a11.821 11.821 0 00-3.48-8.413Z"})})}function es(){return(0,a.jsx)("button",{className:"fixed bottom-20 right-4 md:bottom-6 md:right-6 z-50 flex h-12 w-12 md:h-14 md:w-14 items-center justify-center rounded-full bg-[#25D366] text-white shadow-lg transition-all duration-300 hover:bg-[#128C7E] hover:scale-110 active:scale-95",onClick:()=>{window.open("https://wa.me/+*************","_blank")},"aria-label":"Chat on WhatsApp",children:(0,a.jsx)(et,{className:"h-6 w-6 md:h-7 md:w-7"})})}var ea=s(57340),er=s(54653),eo=s(47863);function en(){let[e,t]=(0,r.useState)(!1),[s,o]=(0,r.useState)(!1),[n,i]=(0,r.useState)([]),[l,c]=(0,r.useState)([]),[d,p]=(0,r.useState)({}),[f,g]=(0,r.useState)(""),[b,v]=(0,r.useState)(!1),w=(0,N.useRouter)(),S=(0,N.usePathname)(),{totalItems:T}=(0,A._)(),{totalItems:P}=(0,k.n)(),{user:D,isLoggedIn:E}=(0,B.J)(),{primaryColor:_,t:M}=(0,V.t)();(0,r.useEffect)(()=>{t(!0)},[]);let z=async()=>{if(l.length>0)return void o(!0);v(!0);try{var e;let t={"Content-Type":"application/json",Accept:"application/json",Authorization:"Bearer "+localStorage.getItem("token")},s=await (0,I.MakeApiCallAsync)(I.Config.END_POINT_NAMES.GET_CATEGORIES_LIST,null,{PageNumber:1,PageSize:100,SortColumn:"Name",SortOrder:"ASC"},t,"POST",!0);if(null==s||null==(e=s.data)?void 0:e.data)try{let e=JSON.parse(s.data.data);if(Array.isArray(e)){let t=e.filter(e=>!e.ParentCategoryID).sort((e,t)=>e.Name.localeCompare(t.Name));c(t),i(e),o(!0)}}catch(e){console.error("Error parsing categories data:",e)}}catch(e){console.error("Error fetching categories:",e)}finally{v(!1)}};if(!e)return null;let O=[{href:"/",icon:ea.A,label:M("home")||"Home",isActive:"/"===S,onClick:null},{href:"#",icon:er.A,label:M("categories")||"Categories",isActive:!1,onClick:z},{href:"/cart",icon:j.A,label:M("cart")||"Cart",isActive:"/cart"===S,badge:T||0,onClick:null},{href:"/wishlist",icon:x.A,label:M("wishlist")||"Wishlist",isActive:"/wishlist"===S,badge:P||0,onClick:null},{href:E?"/account":"/login",icon:m.A,label:E?(null==D?void 0:D.FirstName)||M("myAccount")||"My Account":M("login")||"Login",isActive:"/login"===S||"/signup"===S||"/profile"===S,onClick:null}];return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50 safe-area-pb",children:(0,a.jsx)("div",{className:"flex items-center justify-end py-2 px-4",children:O.map(e=>{let t=e.icon,s=!!e.onClick,r="flex flex-col items-center justify-center min-w-0 py-2 px-3 ml-2"+(s?" bg-transparent border-none":""),o=(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(t,{className:"h-6 w-6 mb-1",style:{color:e.isActive?_:"#6B7280"}}),void 0!==e.badge&&e.badge>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center font-medium shadow-md",style:{backgroundColor:_},children:e.badge>99?"99+":e.badge})]}),(0,a.jsx)("span",{className:"text-xs font-medium text-center leading-tight mt-1",style:{color:e.isActive?_:"#6B7280"},children:e.label})]});return s?(0,a.jsx)("button",{onClick:e.onClick||void 0,className:r,type:"button",children:o},e.href):(0,a.jsx)(C(),{href:e.href,className:r,children:o},e.href)})})}),s&&(0,a.jsx)("div",{className:"md:hidden fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-end animate-in fade-in duration-300",children:(0,a.jsxs)("div",{className:"bg-white w-full max-h-[75vh] rounded-t-2xl overflow-hidden shadow-2xl animate-in slide-in-from-bottom duration-500",children:[(0,a.jsxs)("div",{className:"relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-10",style:{background:"linear-gradient(135deg, ".concat(_,", ").concat(_,"80)")}}),(0,a.jsxs)("div",{className:"relative flex items-center justify-between p-5 border-b border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-xl flex items-center justify-center animate-pulse",style:{backgroundColor:"".concat(_,"20")},children:(0,a.jsx)(er.A,{className:"h-5 w-5",style:{color:_}})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-xl font-bold",style:{color:_},children:M("categories")||"Categories"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Choose the right category for you"})]})]}),(0,a.jsx)("button",{onClick:()=>o(!1),className:"w-10 h-10 rounded-xl bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-all duration-300 hover:scale-110 hover:rotate-90 group",children:(0,a.jsx)("span",{className:"text-gray-500 group-hover:text-gray-700 font-bold text-lg",children:"✕"})})]}),(0,a.jsx)("div",{className:"h-1 w-full",style:{background:"linear-gradient(90deg, transparent, ".concat(_,", transparent)")}})]}),(0,a.jsx)("div",{className:"overflow-y-auto max-h-[calc(75vh-120px)] scrollbar-hide",children:(()=>{if(b)return(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-4 border-t-transparent mx-auto mb-4",style:{borderColor:"".concat(_,"30"),borderTopColor:_}}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-3 h-3 rounded-full animate-pulse",style:{backgroundColor:_}})})]}),(0,a.jsx)("p",{className:"text-gray-600 font-medium animate-pulse",children:"Loading..."})]});let e=""===f.trim()?l.sort((e,t)=>e.Name.localeCompare(t.Name)):l.filter(e=>e.Name.toLowerCase().includes(f.toLowerCase())||n.some(t=>t.ParentCategoryID===e.CategoryID&&t.Name.toLowerCase().includes(f.toLowerCase()))).sort((e,t)=>e.Name.localeCompare(t.Name));return(0,a.jsxs)("div",{className:"h-full flex flex-col bg-gradient-to-b from-gray-50 to-white",children:[(0,a.jsx)("div",{className:"p-4 bg-white shadow-sm",children:(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("input",{type:"text",placeholder:"Search for category...",className:"w-full p-3 pr-12 border-2 rounded-xl focus:outline-none focus:ring-0 transition-all duration-300 bg-gray-50 focus:bg-white focus:shadow-lg",style:{borderColor:f?_:"#e5e7eb",paddingRight:"3rem"},value:f,onChange:e=>g(e.target.value)}),(0,a.jsx)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:(0,a.jsx)(y.A,{className:"h-5 w-5 transition-colors duration-300",style:{color:f?_:"#9ca3af"}})}),f&&(0,a.jsx)("button",{onClick:()=>g(""),className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors",children:"✕"})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto px-2",children:0===e.length?(0,a.jsxs)("div",{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center",children:(0,a.jsx)(y.A,{className:"h-8 w-8 text-gray-400"})}),(0,a.jsx)("p",{className:"text-gray-500 font-medium",children:"No matching categories found"}),(0,a.jsx)("p",{className:"text-gray-400 text-sm mt-1",children:"Try searching with different keywords"})]}):(0,a.jsx)("div",{className:"space-y-2 py-2",children:e.map((e,t)=>{let s=n.some(t=>t.ParentCategoryID===e.CategoryID),r=d[e.CategoryID],i=s?n.filter(t=>t.ParentCategoryID===e.CategoryID).sort((e,t)=>e.Name.localeCompare(t.Name)):[],l=""===f.trim()?i:i.filter(e=>e.Name.toLowerCase().includes(f.toLowerCase())).sort((e,t)=>e.Name.localeCompare(t.Name));return(0,a.jsxs)("div",{className:"group animate-in slide-in-from-right duration-300",style:{animationDelay:"".concat(100*t,"ms")},children:[(0,a.jsx)("div",{className:"relative overflow-hidden rounded-xl bg-white shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-300 hover:scale-[1.02] hover:border-gray-200",children:(0,a.jsxs)("button",{onClick:()=>{n.filter(t=>t.ParentCategoryID===e.CategoryID).length>0?p(t=>({...t,[e.CategoryID]:!t[e.CategoryID]})):(w.push("/products?category=".concat(e.CategoryID)),o(!1))},className:"w-full text-right p-4 flex justify-between items-center relative z-10",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300",style:{background:"linear-gradient(135deg, ".concat(_,", ").concat(_,"80)")}}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-lg flex items-center justify-center transition-all duration-300 group-hover:scale-110",style:{backgroundColor:"".concat(_,"15")},children:(0,a.jsx)(u.A,{className:"h-5 w-5 transition-colors duration-300",style:{color:_}})}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("span",{className:"font-semibold text-gray-800 group-hover:text-gray-900 transition-colors duration-300",children:e.Name}),s&&(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[i.length," subcategories"]})]})]}),s&&(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 group-hover:scale-110",style:{backgroundColor:"".concat(_,"10")},children:r?(0,a.jsx)(eo.A,{className:"h-4 w-4 transition-all duration-300",style:{color:_}}):(0,a.jsx)(h.A,{className:"h-4 w-4 transition-all duration-300",style:{color:_}})})})]})}),s&&(0,a.jsx)("div",{className:"overflow-hidden transition-all duration-500 ease-in-out ".concat(r?"max-h-80 opacity-100 mt-2":"max-h-0 opacity-0"),children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-gray-50 to-white rounded-xl p-3 border border-gray-100",children:[(0,a.jsx)("div",{className:"max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100",children:(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2 mb-3 pr-2",children:l.map((e,t)=>(0,a.jsx)("button",{onClick:t=>{t.stopPropagation(),w.push("/products?category=".concat(e.CategoryID)),o(!1)},className:"group/child p-2 text-xs text-right rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-md border border-transparent hover:border-gray-200 bg-white hover:bg-gradient-to-r",style:{animationDelay:"".concat(100*t,"ms"),"--tw-gradient-from":"".concat(_,"05"),"--tw-gradient-to":"".concat(_,"10")},children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"font-medium transition-colors duration-300 group-hover/child:font-semibold text-xs leading-tight",style:{color:_},children:e.Name}),(0,a.jsx)("div",{className:"w-1.5 h-1.5 rounded-full transition-all duration-300 group-hover/child:scale-150 flex-shrink-0",style:{backgroundColor:"".concat(_,"40")}})]})},e.CategoryID))})}),(0,a.jsxs)("button",{onClick:()=>{w.push("/products?category=".concat(e.CategoryID)),o(!1)},className:"w-full py-3 text-center text-sm font-semibold rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg relative overflow-hidden group/all",style:{backgroundColor:_,color:"white"},children:[(0,a.jsx)("div",{className:"absolute inset-0 -translate-x-full group-hover/all:translate-x-full transition-transform duration-1000 bg-gradient-to-r from-transparent via-white/20 to-transparent"}),(0,a.jsxs)("span",{className:"relative z-10 flex items-center justify-center gap-2",children:["View All in ",e.Name,(0,a.jsx)(h.A,{className:"h-4 w-4 rotate-[-90deg] transition-transform duration-300 group-hover/all:translate-x-1"})]})]})]})})]},e.CategoryID)})})})]})})()})]})})]})}var ei=s(14503);let el=r.forwardRef((e,t)=>{let{className:s,title:r,description:o,action:n,type:i,...l}=e;return(0,a.jsxs)("div",{ref:t,className:(0,D.cn)("group relative flex items-center justify-between p-4 pr-8 space-x-4 overflow-hidden rounded-md border shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none",(()=>{switch(i){case"success":return"bg-green-500 text-white border-green-600";case"error":return"bg-red-500 text-white border-red-600";case"warning":return"bg-yellow-500 text-white border-yellow-600";case"info":return"bg-blue-500 text-white border-blue-600";default:return"bg-background text-foreground border-border"}})(),s),...l,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[r&&(0,a.jsx)("div",{className:"text-sm font-semibold",children:r}),o&&(0,a.jsx)("div",{className:"text-sm opacity-90",children:o})]}),n,(0,a.jsx)("button",{className:"absolute right-2 top-2 rounded-md p-1 text-white/70 opacity-0 transition-opacity hover:text-white focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100",children:(0,a.jsx)(b.A,{className:"h-4 w-4"})})]})});el.displayName="Toast";let ec=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,D.cn)("text-sm font-semibold",s),...r})});ec.displayName="ToastTitle";let ed=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,D.cn)("text-sm opacity-90",s),...r})});ed.displayName="ToastDescription";let em=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("button",{ref:t,className:(0,D.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100",s),...r,children:(0,a.jsx)(b.A,{className:"h-4 w-4"})})});em.displayName="ToastClose";let eh=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,D.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",s),...r})});function eu(e){let{children:t,...s}=e;return(0,a.jsx)(a.Fragment,{children:t})}function ep(){let{toasts:e}=(0,ei.dj)();return(0,a.jsxs)(eu,{children:[e.map(function(e){let{id:t,title:s,description:r,action:o,type:n,...i}=e;return(0,a.jsxs)(el,{type:n,...i,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[s&&(0,a.jsx)(ec,{children:s}),r&&(0,a.jsx)(ed,{children:r})]}),o,(0,a.jsx)(em,{})]},t)}),(0,a.jsx)(eh,{})]})}eh.displayName="ToastViewport",eu.displayName="ToastProvider";let ex={whatsappNumber:"+**********",phoneNumber:"+**********",whatsappLink:"https://wa.me/**********"},ef=(0,r.createContext)(ex);function eg(e){let{children:t}=e;return(0,a.jsx)(ef.Provider,{value:ex,children:t})}var ey=s(92215),ej=s(9776);let eb=[{primary:"#0074b2",primaryForeground:"#ffffff",name:"Default Blue"},{primary:"#dc2626",primaryForeground:"#ffffff",name:"Red"},{primary:"#16a34a",primaryForeground:"#ffffff",name:"Green"},{primary:"#ca8a04",primaryForeground:"#ffffff",name:"Yellow"},{primary:"#9333ea",primaryForeground:"#ffffff",name:"Purple"},{primary:"#ea580c",primaryForeground:"#ffffff",name:"Orange"},{primary:"#0891b2",primaryForeground:"#ffffff",name:"Cyan"},{primary:"#be185d",primaryForeground:"#ffffff",name:"Pink"}],ev="color_theme",eN=(0,r.createContext)(void 0);function ew(e){let{children:t}=e,s=function(){let[e,t]=(0,r.useState)(eb[0]),[s,a]=(0,r.useState)(!0),o=(0,r.useCallback)(e=>{if("undefined"==typeof document)return null;let t=e+"=";for(let e of document.cookie.split(";")){let s=e.trim();if(0===s.indexOf(t))return decodeURIComponent(s.substring(t.length))}return null},[]),n=(0,r.useCallback)(function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:365;if("undefined"==typeof document)return;let a=new Date;a.setTime(a.getTime()+24*s*36e5);let r="".concat(e,"=").concat(encodeURIComponent(t),"; expires=").concat(a.toUTCString(),"; path=/; SameSite=strict");r+="; Secure",document.cookie=r},[]),i=(0,r.useCallback)(e=>{if("undefined"==typeof document)return;let t=document.documentElement,s=e=>{let t=parseInt(e.slice(1,3),16)/255,s=parseInt(e.slice(3,5),16)/255,a=parseInt(e.slice(5,7),16)/255,r=Math.max(t,s,a),o=Math.min(t,s,a),n=0,i=0,l=(r+o)/2;if(r!==o){let e=r-o;switch(i=l>.5?e/(2-r-o):e/(r+o),r){case t:n=(s-a)/e+6*(s<a);break;case s:n=(a-t)/e+2;break;case a:n=(t-s)/e+4}n/=6}return"".concat(Math.round(360*n)," ").concat(Math.round(100*i),"% ").concat(Math.round(100*l),"%")},a=s(e.primary),r=s(e.primaryForeground);t.style.setProperty("--primary",a),t.style.setProperty("--primary-foreground",r);let o=document.querySelector('meta[name="theme-color"]');o&&o.setAttribute("content",e.primary)},[]);(0,r.useEffect)(()=>{let e=o(ev);if(e)try{let s=JSON.parse(e);t(s),i(s)}catch(e){console.warn("Failed to parse saved color theme:",e),i(eb[0])}else i(eb[0]);a(!1)},[o,i]);let l=(0,r.useCallback)(e=>{t(e),i(e),n(ev,JSON.stringify(e))},[i,n]),c=(0,r.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Custom",s={primary:e,primaryForeground:"#ffffff",name:t};return l(s),s},[l]);return{currentTheme:e,availableThemes:eb,changeTheme:l,createCustomTheme:c,isLoading:s}}();return(0,a.jsx)(eN.Provider,{value:s,children:t})}function eC(e){let{children:t}=e;return(0,a.jsx)(V.Z,{children:(0,a.jsx)(ew,{children:(0,a.jsx)(B.v,{children:(0,a.jsx)(ej.B,{children:(0,a.jsx)(A.e,{children:(0,a.jsx)(eg,{children:(0,a.jsx)(ey.U,{children:(0,a.jsx)(k.Z,{children:t})})})})})})})})}function eA(e){let{data:t}=e;return(0,a.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(t,null,2)}})}function ek(e){let{children:t}=e;return(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[(0,a.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),(0,a.jsx)("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),(0,a.jsx)("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),(0,a.jsx)("link",{rel:"manifest",href:"/site.webmanifest"}),(0,a.jsx)("link",{rel:"shortcut icon",href:"/favicon.ico"}),(0,a.jsx)("meta",{name:"theme-color",content:"#0074b2"}),(0,a.jsx)("meta",{name:"msapplication-TileColor",content:"#0074b2"}),(0,a.jsx)("title",{children:"Code Medical Website - Medical Courses & Resources"}),(0,a.jsx)("meta",{name:"description",content:"Professional medical courses, ebooks, printed books and medical accounts for medical field staff worldwide."}),(0,a.jsx)(eA,{data:{"@context":"https://schema.org","@type":"Organization",name:"Code Medical",url:"https://codemedicalapps.com",logo:"https://admin.codemedicalapps.com/content/commonImages/otherImages/18b_logo2x.png",description:"Professional team specialized in providing well known valuable medical courses, Ebooks, Printed books and popular medical accounts for all medical field staff all around the world with low cost and in short time.",contactPoint:{"@type":"ContactPoint",telephone:"+964-************",contactType:"customer service",email:"<EMAIL>"},sameAs:["https://www.facebook.com/codemedicalapps/","https://t.me/codemedicalapps","https://wa.me/*************"]}}),(0,a.jsx)(eA,{data:{"@context":"https://schema.org","@type":"WebSite",name:"Code Medical",url:"https://codemedicalapps.com",description:"Medical courses, ebooks, printed books and medical accounts for medical professionals worldwide.",potentialAction:{"@type":"SearchAction",target:{"@type":"EntryPoint",urlTemplate:"https://codemedicalapps.com/products?search={search_term_string}"},"query-input":"required name=search_term_string"}}})]}),(0,a.jsx)("body",{className:n().className,suppressHydrationWarning:!0,children:(0,a.jsx)(X.G3,{reCaptchaKey:"6LfS7D0rAAAAAAybL-FSr2N1pWStAiKxN_EapgHJ",scriptProps:{async:!0,defer:!0,appendTo:"body",nonce:void 0},container:{parameters:{badge:"inline",theme:"light"}},children:(0,a.jsxs)(eC,{children:[(0,a.jsx)(G,{}),(0,a.jsx)("main",{className:"min-h-screen pb-16 md:pb-0",children:t}),(0,a.jsx)(ee,{}),(0,a.jsx)(es,{}),(0,a.jsx)(en,{}),(0,a.jsx)(ep,{}),(0,a.jsx)(S.l$,{position:"top-right"})]})})})]})}},92215:(e,t,s)=>{"use strict";s.d(t,{U:()=>i,Y:()=>l});var a=s(95155),r=s(12115),o=s(65409);let n=(0,r.createContext)(void 0);function i(e){let{children:t}=e,[s,i]=(0,r.useState)(null),[l,c]=(0,r.useState)(!1),d=async(e,t,s)=>{if(!e.trim())return{valid:!1,message:"Please enter a coupon code",discount:0};c(!0);try{let r=(null==s?void 0:s.map(e=>({ProductId:e.id,ProductName:e.name,Price:e.adjustedPrice||e.price,Quantity:e.quantity,IsDiscountAllowed:!0})))||[],n=JSON.stringify(r),l={requestParameters:{CouponCode:e.toUpperCase(),cartJsonData:n}},c=await (0,o.MakeApiCallAsync)(o.Config.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT,o.Config.DYNAMIC_METHOD_SUB_URL,l,{"Content-Type":"application/json",Accept:"application/json"},"POST");if(c&&c.data&&!c.data.errorMessage){let s;if((s="string"==typeof c.data.data?JSON.parse(c.data.data):c.data.data)&&s.DiscountValueAfterCouponAppliedWithQuantity>0){let t=s.DiscountValueAfterCouponAppliedWithQuantity,a=1===s.DiscountValueType?"percentage":"fixed",r={code:e.toUpperCase(),discount:t,type:a,discountTypeId:s.DiscountTypeId||1};return i(r),{valid:!0,message:"Coupon applied successfully!",discount:t}}if(s&&Array.isArray(s)&&s.length>0){let a=s[0];if(a&&a.DiscountValue>0&&a.IsActive){let s=0,r=a.DiscountTypeId||1;switch(r){case 1:case 2:1===a.DiscountValueType?s=a.DiscountValue*t/100:2===a.DiscountValueType&&(s=a.DiscountValue);break;case 3:case 4:case 5:case 6:case 7:s=1===a.DiscountValueType?a.DiscountValue*t/100:a.DiscountValue;break;default:s=0}if(s>0){let t={code:e.toUpperCase(),discount:s,type:1===a.DiscountValueType?"percentage":"fixed",discountTypeId:r,title:a.Title,discountId:a.DiscountId,maxQuantity:a.MaxQuantity,productId:a.ProductId,categoryId:a.CategoryID};return i(t),{valid:!0,message:'Coupon "'.concat(a.Title,'" applied successfully! Discount applied on ').concat({1:"order total",2:"order subtotal",3:"products",4:"categories",5:"manufacturers",6:"cities",7:"shipping"}[r]||"order","."),discount:s}}}}return{valid:!1,message:"Invalid coupon code or coupon not applicable to your cart",discount:0}}else{var a;return{valid:!1,message:(null==(a=c.data)?void 0:a.errorMessage)||"Failed to validate coupon",discount:0}}}catch(e){return console.error("Coupon validation error:",e),{valid:!1,message:"Error validating coupon. Please try again.",discount:0}}finally{c(!1)}};return(0,a.jsx)(n.Provider,{value:{appliedCoupon:s,validateCoupon:d,clearCoupon:()=>{i(null)},isLoading:l},children:t})}function l(){let e=(0,r.useContext)(n);if(void 0===e)throw Error("useCoupon must be used within a CouponProvider");return e}}},e=>{e.O(0,[8804,2533,2749,4277,3464,4706,6774,3942,5725,5145,2856,8816,9321,8441,5964,7358],()=>e(e.s=71826)),_N_E=e.O()}]);