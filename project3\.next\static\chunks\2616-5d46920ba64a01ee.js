"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2616],{53580:(e,t,r)=>{r.d(t,{dj:()=>u});var s=r(12115);let o=0,a=new Map,n=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},i=[],l={toasts:[]};function c(e){l=((e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?n(r):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}})(l,e),i.forEach(e=>{e(l)})}function d(e){let{duration:t=2e3,...r}=e,s=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:s});return c({type:"ADD_TOAST",toast:{...r,id:s,duration:t,open:!0,onOpenChange:e=>{e||a()}}}),setTimeout(()=>{a()},t),{id:s,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function u(){let[e,t]=s.useState(l);return s.useEffect(()=>(i.push(t),()=>{let e=i.indexOf(t);e>-1&&i.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}d.success=(e,t)=>d({description:e,type:"success",duration:2e3,...t}),d.error=(e,t)=>d({description:e,type:"error",duration:2e3,...t}),d.warning=(e,t)=>d({description:e,type:"warning",duration:2e3,...t}),d.info=(e,t)=>d({description:e,type:"info",duration:2e3,...t})},53999:(e,t,r)=>{r.d(t,{cn:()=>a});var s=r(52596),o=r(39688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,s.$)(t))}},79891:(e,t,r)=>{r.d(t,{Z:()=>l,t:()=>c});var s=r(95155),o=r(12115);let a={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us",searchProducts:"Search products..."},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",searchProducts:"ابحث عن المنتجات...",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}};var n=r(94213);let i=(0,o.createContext)(void 0);function l(e){let{children:t}=e,[r,l]=(0,o.useState)("light"),[c,d]=(0,o.useState)("en"),[u,f]=(0,o.useState)("#0074b2"),[m,g]=(0,o.useState)("#ffffff");return(0,o.useEffect)(()=>{let e=(0,n.N)(u);g(e),document.documentElement.style.setProperty("--primary",u),document.documentElement.style.setProperty("--primary-foreground",e)},[u]),(0,s.jsx)(i.Provider,{value:{theme:r,language:c,primaryColor:u,primaryTextColor:m,toggleTheme:()=>{l("light"===r?"dark":"light")},setLanguage:e=>{d(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{f(e);let t=(0,n.N)(e);g(t),document.documentElement.style.setProperty("--primary",e),document.documentElement.style.setProperty("--primary-foreground",t)},t:e=>(function(e,t){let r=a[t];return e in r?r[e]:"en"!==t&&e in a.en?a.en[e]:e})(e,c)},children:t})}function c(){let e=(0,o.useContext)(i);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},88482:(e,t,r)=>{r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>u});var s=r(95155),o=r(12115),a=r(53999);let n=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...o})});n.displayName="Card";let i=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",r),...o})});i.displayName="CardHeader";let l=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",r),...o})});l.displayName="CardTitle";let c=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",r),...o})});c.displayName="CardDescription";let d=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",r),...o})});d.displayName="CardContent";let u=o.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,s.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",r),...o})});u.displayName="CardFooter"},94213:(e,t,r)=>{function s(e,t){let r=e=>{let t=e.replace("#",""),r=parseInt(t.slice(0,2),16)/255,s=[r,parseInt(t.slice(2,4),16)/255,parseInt(t.slice(4,6),16)/255].map(e=>e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4));return .2126*s[0]+.7152*s[1]+.0722*s[2]},s=r(e),o=r(t);return(Math.max(s,o)+.05)/(Math.min(s,o)+.05)}function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"AA",r=s(e,"#ffffff"),o=s(e,"#000000"),a="AAA"===t?7:4.5;return r>=a&&o>=a?r>o?"#ffffff":"#000000":r>=a?"#ffffff":o>=a?"#000000":r>o?"#ffffff":"#000000"}r.d(t,{N:()=>o})},97168:(e,t,r)=>{r.d(t,{$:()=>c,r:()=>l});var s=r(95155),o=r(12115),a=r(99708),n=r(74466),i=r(53999);let l=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=o.forwardRef((e,t)=>{let{className:r,variant:o,size:n,asChild:c=!1,...d}=e,u=c?a.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(l({variant:o,size:n,className:r})),ref:t,...d})});c.displayName="Button"}}]);