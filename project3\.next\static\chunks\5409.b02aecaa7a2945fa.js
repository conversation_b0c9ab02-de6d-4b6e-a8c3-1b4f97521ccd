"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5409,7790],{61204:(e,r,t)=>{t.d(r,{T:()=>o});let o={ADMIN_BASE_URL:"https://admin.codemedicalapps.com/",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",COMMON_CONTROLLER_SUB_URL:"api/v1/common/",END_POINT_NAMES:{DOWNLOAD_DIGITAL_FILE:"downloadDigitalFile",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile"}}},65409:(e,r,t)=>{t.d(r,{$g:()=>d,Config:()=>n,MakeApiCallAsync:()=>i,XX:()=>u,k6:()=>c});var o=t(23464),s=t(61204);o.A.defaults.timeout=3e4,"https:"===window.location.protocol&&s.T.ADMIN_BASE_URL.includes("localhost")&&(o.A.defaults.httpsAgent={rejectUnauthorized:!1});let n={ADMIN_BASE_URL:s.T.ADMIN_BASE_URL,API_VERSION:"v1",DYNAMIC_METHOD_SUB_URL:"api/v1/dynamic/dataoperation/",END_POINT_NAMES:{...s.T.END_POINT_NAMES,GET_CATEGORIES_LIST:"get-categories-list",SIGNUP_USER:"signup-user",GET_USER_LOGIN:"get-user-login",GET_USER_BY_PHONE:"get-user-by-phone",RESET_PASSWORD_BY_PHONE:"reset-password-by-phone",RESET_PASSWORD_FIREBASE:"reset-password-firebase",GET_HOME_SCREEN_BANNER:"get-home-screen-banner",GET_RECENT_PRODUCTS:"get-recents-products-list",GET_POPULAR_PRODUCTS:"get-popular-products-list",GET_HOT_DEAL_PRODUCTS:"get-hot-deal-products",GET_CAMPAIGNS_LIST:"get-web-campaign-list",GET_PRODUCTS_LIST:"get-products-list",GET_ALL_PRODUCTS:"api/v1/products/get-all-products",GET_MANUFACTURERS_LIST:"get-manufacturers-list",GET_TAGS_LIST:"get-tags-list",GET_CURRENCY_RATE:"get-currency-rate",GET_COUPON_CODE_DISCOUNT:"get-coupon-code-data",UPDATE_PROFILE:"update-profile",INSERT_PRODUCT_REVIEW:"Insert-Product-Review",GET_PRODUCT_REVIEWS:"get-product-reviews"},COMMON_CONTROLLER_SUB_URL:"api/v1/common/"},a=async()=>{try{{try{let e=await fetch("/api/auth/get-token",{method:"GET",credentials:"include"});if(e.ok){let r=await e.json();if(r.token)return console.log("\uD83D\uDD10 Retrieved JWT token from secure HttpOnly cookie"),r.token}}catch(e){console.log("API token retrieval failed, trying client-side cookies:",e)}for(let e of document.cookie.split(";")){let[r,t]=e.trim().split("=");if("auth_token"===r)return console.log("\uD83D\uDD10 Retrieved JWT token from client-side cookie"),decodeURIComponent(t)}let e=localStorage.getItem("token")||localStorage.getItem("authToken");if(e)return console.log("⚠️ Using token from localStorage (migrating to secure cookies)"),localStorage.removeItem("token"),localStorage.removeItem("authToken"),e}return null}catch(e){return console.error("Error getting token for header:",e),null}},l=async()=>{try{{for(let t of document.cookie.split(";")){let[o,s]=t.trim().split("=");if("auth_user"===o)try{var e,r;let t=JSON.parse(decodeURIComponent(s)),o=(null==(e=t.UserId)?void 0:e.toString())||(null==(r=t.UserID)?void 0:r.toString());if(o)return console.log("\uD83D\uDD10 Retrieved User ID from secure cookie"),o}catch(e){console.warn("Failed to parse user data from cookie:",e)}}let t=localStorage.getItem("userId")||localStorage.getItem("userID");if(t)return console.log("⚠️ Using User ID from localStorage (migrating to secure cookies)"),localStorage.removeItem("userId"),localStorage.removeItem("userID"),t}return null}catch(e){return console.error("Error getting user ID for header:",e),null}},i=async function(e,r,t,s,i){arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{let c=(e=>{if(!e)return e;let r=JSON.parse(JSON.stringify(e));return r.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from request body (will use JWT token instead)"),delete r.UserId),r.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from request body (will use JWT token instead)"),delete r.UserID),r.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from request body (will use JWT token instead)"),delete r.user_id),r.requestParameters&&(r.requestParameters.hasOwnProperty("UserId")&&(console.log("\uD83D\uDD27 Removing UserId from requestParameters (will use JWT token instead)"),delete r.requestParameters.UserId),r.requestParameters.hasOwnProperty("UserID")&&(console.log("\uD83D\uDD27 Removing UserID from requestParameters (will use JWT token instead)"),delete r.requestParameters.UserID),r.requestParameters.hasOwnProperty("user_id")&&(console.log("\uD83D\uDD27 Removing user_id from requestParameters (will use JWT token instead)"),delete r.requestParameters.user_id)),r})(t),u={...s};if(!u.hasOwnProperty("Authorization")){let e=await a();e&&(u.Authorization="Bearer "+e,console.log("\uD83D\uDD10 Added JWT token to Authorization header"))}if(!u.hasOwnProperty("Token")){let e=await a();u.Token=null!=e?e:"",e&&console.log("\uD83D\uDD10 Added JWT token to Token header (backward compatibility)")}if(!u.hasOwnProperty("UserID")){let e=await l();u.UserID=null!=e?e:""}u.hasOwnProperty("Accept")||(u.Accept="application/json"),u.hasOwnProperty("Content-Type")||(u["Content-Type"]="application/json");let d=n.ADMIN_BASE_URL+(null===r||void 0==r?n.DYNAMIC_METHOD_SUB_URL:r)+e;i=null!=i?i:"POST";let _={headers:u,responseType:"json",timeout:3e4,withCredentials:!1};if("POST"===i)return await o.A.post(d,c,_);if("GET"==i)return _.params=c,await o.A.get(d,_);return{data:{errorMessage:"Unsupported method type: ".concat(i),status:"method_not_supported"}}}catch(r){console.error("API call failed:",r);let e={data:{errorMessage:"An unexpected error occurred",status:"unknown_error"}};if(r&&"object"==typeof r&&"response"in r&&r.response){var c,u;let t=null==(c=r.response)?void 0:c.data;e.data={errorMessage:(null==t?void 0:t.errorMessage)||"An error occurred while processing your request.",status:null==(u=r.response)?void 0:u.status}}else if(r&&"object"==typeof r&&"request"in r){let t="Network error: No response received from server.";r.message&&r.message.includes("Network Error")&&(t="Network Error: This may be due to CORS policy restrictions, server unavailability, or an invalid SSL certificate. Please check that:\n1. The server is running and accessible\n2. The URL is correct: "+n.ADMIN_BASE_URL+"\n3. CORS is properly configured on the server\n4. If using HTTPS, the SSL certificate is valid"),e.data={errorMessage:t,status:"network_error"}}else e.data={errorMessage:r&&"object"==typeof r&&"message"in r?r.message:"An unexpected error occurred",status:"request_error"};return e}},c=async()=>{try{return console.log("Using default currency rate (1430) - API endpoint not available"),1430}catch(e){return console.error("Error fetching currency rate:",e),1430}},u=(e,r)=>Math.round(e*r),d=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return null==e||isNaN(e)?"IQD"===r?"0 IQD":"$0.00":"IQD"===r?"".concat(e.toLocaleString()," IQD"):"$".concat(e.toFixed(2))}}}]);