(()=>{var a={};a.id=1298,a.ids=[1298],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11487:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,25417)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\account\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\account\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/account/page",pathname:"/account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/account/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},12412:a=>{"use strict";a.exports=require("assert")},12597:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14952:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},24030:(a,b,c)=>{Promise.resolve().then(c.bind(c,67295))},25417:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\account\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\account\\page.tsx","default")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},39390:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(96241);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},60822:(a,b,c)=>{Promise.resolve().then(c.bind(c,25417))},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},67295:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>H});var d=c(60687),e=c(43210),f=c(16189),g=c(34993),h=c(55192),i=c(24934),j=c(68988),k=c(39390),l=c(99294),m=c(85814),n=c.n(m),o=c(77080),p=c(832),q=c(40529),r=c(71702),s=c(58869),t=c(62688);let u=(0,t.A)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);var v=c(64398),w=c(19080),x=c(97992),y=c(85778),z=c(67760),A=c(40083),B=c(48340),C=c(41550),D=c(6943);let E=(0,t.A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]);var F=c(12597),G=c(13861);function H(){let{t:a,primaryColor:b,primaryTextColor:c}=(0,o.t)(),{user:m,isLoggedIn:t,isLoading:H,logout:I,updateProfile:J}=(0,p.J)(),{toast:K}=(0,r.dj)(),L=(0,f.useRouter)(),[M,N]=(0,e.useState)(!1),[O,P]=(0,e.useState)(!1),[Q,R]=(0,e.useState)(!1),[S,T]=(0,e.useState)([]),[U,V]=(0,e.useState)(!0),[W,X]=(0,e.useState)(""),[Y,Z]=(0,e.useState)(!1),[$,_]=(0,e.useState)([]),[aa,ab]=(0,e.useState)({firstName:"",lastName:"",email:"",phone:"",gender:"",category:"",newPassword:"",confirmPassword:"",currentPassword:""}),[ac,ad]=(0,e.useState)(""),ae=a=>{let{name:b,value:c}=a.target;ab(a=>({...a,[b]:c})),ad(""),"gender"===b&&(console.log("\uD83D\uDD0D Account page: Gender changed to:",c),setTimeout(()=>{let a=document.getElementById("gender");a&&(a.value=c,console.log("\uD83D\uDD0D Account page: Gender select updated to:",c))},10))};if(H)return console.log("\uD83D\uDD04 Account page: Showing loading state"),(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-lg text-muted-foreground",children:"Loading..."})]})});if(!H&&!t)return console.log("\uD83D\uDEA8 Account page: Not logged in, should redirect"),(0,d.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-lg text-muted-foreground",children:"Redirecting to login..."})]})});let af=async a=>{if(a.preventDefault(),P(!0),ad(""),aa.newPassword||aa.confirmPassword||aa.currentPassword){if(aa.newPassword!==aa.confirmPassword){ad("New password and confirm password do not match"),P(!1);return}if(aa.newPassword&&aa.newPassword.length<8){ad("Password must be at least 8 characters long"),P(!1);return}ad("Password update functionality is not yet implemented"),P(!1);return}try{let a={requestParameters:{FirstName:aa.firstName,LastName:aa.lastName,Gender:aa.gender||"Male",CategoryId:aa.category||"1024"}};console.log("Sending update profile request:",a);let b=await (0,q.MakeApiCallAsync)(q.Config.END_POINT_NAMES.UPDATE_PROFILE,null,a,{Accept:"application/json","Content-Type":"application/json"},"POST",!0);if(console.log("Profile update response:",b),b?.data&&!b.data.errorMessage){let a;if(a="string"==typeof b.data.data?JSON.parse(b.data.data):b.data.data,Array.isArray(a)&&a.length>0&&"Saved Successfully"===a[0].ResponseMsg){R(!0);let a={FirstName:aa.firstName,LastName:aa.lastName,UserName:`${aa.firstName} ${aa.lastName}`.trim(),Gender:aa.gender,CategoryID:aa.category,CategoryId:aa.category,SpecialistId:aa.category};await J(a);try{(await fetch("/api/auth/update-user-cookies",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({FirstName:aa.firstName,LastName:aa.lastName,UserName:`${aa.firstName} ${aa.lastName}`.trim(),Gender:aa.gender,CategoryID:aa.category,CategoryId:aa.category,SpecialistId:aa.category})})).ok&&(console.log("User cookies updated successfully with all profile data"),setTimeout(()=>{let a=document.getElementById("gender");if(a&&(a.value=aa.gender),aa.category&&S.length>0){let a=S.find(a=>a.CategoryID===parseInt(aa.category.toString()));a&&X(a.Name||a.CategoryName||"")}},100))}catch(a){console.warn("Failed to update user cookies:",a)}K({title:"Success!",description:"Profile updated successfully!"}),setTimeout(()=>R(!1),3e3)}else throw Error(a?.[0]?.ResponseMsg||"Failed to update profile")}else throw Error(b?.data?.errorMessage||"Failed to update profile")}catch(b){console.error("Profile update error:",b);let a=b instanceof Error?b.message:"Failed to update profile. Please try again.";ad(a),K({title:"Error",description:a,variant:"destructive"})}finally{P(!1)}};return(0,d.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,d.jsx)(g.Qp,{className:"mb-6",children:(0,d.jsxs)(g.AB,{children:[(0,d.jsx)(g.J5,{children:(0,d.jsx)(g.w1,{asChild:!0,children:(0,d.jsx)(n(),{href:"/",children:a("home")})})}),(0,d.jsx)(g.tH,{}),(0,d.jsx)(g.J5,{children:(0,d.jsx)(g.tJ,{children:a("myAccount")})})]})}),(0,d.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,d.jsx)("h1",{className:"text-3xl font-bold mb-6",children:a("myAccount")}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-[250px_1fr] gap-6",children:[(0,d.jsx)("div",{className:"space-y-4",children:(0,d.jsx)(h.Zp,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"flex flex-col items-center text-center mb-6",children:[(0,d.jsxs)("div",{className:"w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center mb-4 relative",children:[(0,d.jsx)(s.A,{className:"h-10 w-10 text-primary"}),m?.IsVerified&&(0,d.jsx)("div",{className:"absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center",children:(0,d.jsx)(u,{className:"h-3 w-3 text-white"})})]}),(0,d.jsxs)("h3",{className:"font-medium",children:[m?.FirstName," ",m?.LastName]}),(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:m?.Email||m?.EmailAddress}),m?.Pointno!==void 0&&(0,d.jsxs)("div",{className:"flex items-center gap-1 mt-2 px-2 py-1 bg-yellow-100 rounded-full",children:[(0,d.jsx)(v.A,{className:"h-3 w-3 text-yellow-600"}),(0,d.jsxs)("span",{className:"text-xs font-medium text-yellow-700",children:[m.Pointno," Credit"]})]})]}),(0,d.jsxs)("div",{className:"space-y-1",children:[(0,d.jsx)(i.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,d.jsxs)(n(),{href:"/account",children:[(0,d.jsx)(s.A,{className:"mr-2 h-4 w-4"}),"Profile"]})}),(0,d.jsx)(i.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,d.jsxs)(n(),{href:"/orders",children:[(0,d.jsx)(w.A,{className:"mr-2 h-4 w-4"}),"Orders"]})}),(0,d.jsx)(i.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,d.jsxs)(n(),{href:"/addresses",children:[(0,d.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Addresses"]})}),(0,d.jsx)(i.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,d.jsxs)(n(),{href:"/payment-methods",children:[(0,d.jsx)(y.A,{className:"mr-2 h-4 w-4"}),"Payment Methods"]})}),(0,d.jsx)(i.$,{variant:"ghost",className:"w-full justify-start",asChild:!0,children:(0,d.jsxs)(n(),{href:"/wishlist",children:[(0,d.jsx)(z.A,{className:"mr-2 h-4 w-4"}),"Wishlist"]})}),(0,d.jsxs)(i.$,{variant:"ghost",className:"w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50",onClick:()=>{I(),K({title:"Logged Out",description:"You have been successfully logged out."}),L.push("/")},children:[(0,d.jsx)(A.A,{className:"mr-2 h-4 w-4"}),"Logout"]})]})]})})}),(0,d.jsx)("div",{children:(0,d.jsxs)(l.tU,{defaultValue:"profile",children:[(0,d.jsxs)(l.j7,{className:"grid w-full grid-cols-3 mb-6 gap-1 sm:gap-2 bg-transparent p-0 h-auto",children:[(0,d.jsx)(l.Xi,{value:"profile",className:"rounded-lg px-1 py-1 sm:px-4 sm:py-2 text-xs sm:text-sm font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"var(--state-active, rgb(209 213 219))",color:"var(--state-active-text, rgb(55 65 81))",transform:"var(--state-active-scale, scale(1))",boxShadow:"var(--state-active-shadow, none)",borderColor:"var(--state-active-border, transparent)"},"data-active-bg":b,"data-active-text":c,children:"Personal Information"}),(0,d.jsx)(l.Xi,{value:"password",className:"rounded-lg px-1 py-1 sm:px-4 sm:py-2 text-xs sm:text-sm font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"var(--state-active, rgb(209 213 219))",color:"var(--state-active-text, rgb(55 65 81))",transform:"var(--state-active-scale, scale(1))",boxShadow:"var(--state-active-shadow, none)",borderColor:"var(--state-active-border, transparent)"},"data-active-bg":b,"data-active-text":c,children:"Security"}),(0,d.jsx)(l.Xi,{value:"overview",className:"rounded-lg px-1 py-1 sm:px-4 sm:py-2 text-xs sm:text-sm font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102",style:{backgroundColor:"var(--state-active, rgb(209 213 219))",color:"var(--state-active-text, rgb(55 65 81))",transform:"var(--state-active-scale, scale(1))",boxShadow:"var(--state-active-shadow, none)",borderColor:"var(--state-active-border, transparent)"},"data-active-bg":b,"data-active-text":c,children:"Account Details"})]}),(0,d.jsx)(l.av,{value:"overview",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,d.jsxs)("div",{className:"p-8",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold mb-6",children:"Account Details"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsx)(h.Zp,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center gap-2",children:[(0,d.jsx)(s.A,{className:"h-5 w-5 text-primary"}),"Account Information"]}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,d.jsx)("span",{className:"text-sm text-muted-foreground",children:"Phone Number"}),(0,d.jsx)("span",{className:"font-medium",children:m?.PhoneNo||m?.MobileNo||m?.PhoneNumber||"Not specified"})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,d.jsx)("span",{className:"text-sm text-muted-foreground",children:"Gender"}),(0,d.jsx)("span",{className:"font-medium",children:m?.Gender||"Not specified"})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,d.jsx)("span",{className:"text-sm text-muted-foreground",children:"Specialist"}),(0,d.jsx)("span",{className:"font-medium",children:S.find(a=>a.CategoryID===parseInt(aa.category?.toString()||"0"))?.Name||S.find(a=>a.CategoryID===(m?.CategoryID||m?.CategoryId||m?.categoryId))?.Name||"Not specified"})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,d.jsx)("span",{className:"text-sm text-muted-foreground",children:"Account Status"}),(0,d.jsx)("div",{className:"flex items-center gap-2",children:m?.IsActive===!0||m?.IsActive===1||m?.IsActive==="true"||m?.IsActive==="1"||"string"==typeof m?.IsActive&&m?.IsActive.toLowerCase()==="true"?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,d.jsx)("span",{className:"text-green-600 font-medium",children:"Active"})]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,d.jsxs)("span",{className:"text-red-600 font-medium",children:["Inactive (Debug: IsActive=",JSON.stringify(m?.IsActive),", type=",typeof m?.IsActive,")"]})]})})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,d.jsx)("span",{className:"text-sm text-muted-foreground",children:"Member Since"}),(0,d.jsx)("span",{className:"font-medium",children:m?.CreatedOn?new Date(m.CreatedOn).toLocaleDateString():`N/A (Debug: CreatedOn=${JSON.stringify(m?.CreatedOn)}, type=${typeof m?.CreatedOn})`})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center py-2",children:[(0,d.jsx)("span",{className:"text-sm text-muted-foreground",children:"Credit Balance"}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(v.A,{className:"h-4 w-4 text-yellow-500"}),(0,d.jsx)("span",{className:"font-medium",children:m?.Pointno||0})]})]})]})]})}),(0,d.jsx)(h.Zp,{children:(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("h3",{className:"text-lg font-semibold mb-4 flex items-center gap-2",children:[(0,d.jsx)(B.A,{className:"h-5 w-5 text-primary"}),"Contact Information"]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)(C.A,{className:"h-5 w-5 text-gray-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Email Address"}),(0,d.jsx)("p",{className:"font-medium",children:m?.EmailAddress||m?.Email})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)(B.A,{className:"h-5 w-5 text-gray-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Phone Number"}),(0,d.jsx)("p",{className:"font-medium",children:m?.PhoneNo||m?.MobileNo||m?.PhoneNumber||"Not provided (Debug: PhoneNo="+JSON.stringify(m?.PhoneNo)+", MobileNo="+JSON.stringify(m?.MobileNo)+", PhoneNumber="+JSON.stringify(m?.PhoneNumber)+")"})]})]}),m?.MobileNo&&m?.PhoneNo&&m?.MobileNo!==m?.PhoneNo&&(0,d.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-gray-50 rounded-lg",children:[(0,d.jsx)(B.A,{className:"h-5 w-5 text-gray-500"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-gray-500",children:"Mobile Number"}),(0,d.jsx)("p",{className:"font-medium",children:m?.MobileNo})]})]})]})]})})]})]})}),(0,d.jsx)(l.av,{value:"profile",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,d.jsxs)("div",{className:"p-8",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold mb-6",children:"Personal Information"}),(0,d.jsx)(h.Zp,{children:(0,d.jsxs)("form",{onSubmit:af,className:"p-6",children:[Q&&(0,d.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6",children:"Profile updated successfully!"}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(k.J,{htmlFor:"firstName",children:"First Name"}),(0,d.jsx)(j.p,{id:"firstName",name:"firstName",value:aa.firstName,onChange:ae,placeholder:"Enter your first name"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(k.J,{htmlFor:"lastName",children:"Last Name"}),(0,d.jsx)(j.p,{id:"lastName",name:"lastName",value:aa.lastName,onChange:ae,placeholder:"Enter your last name"})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(k.J,{htmlFor:"gender",children:"Gender"}),(0,d.jsxs)("select",{id:"gender",name:"gender",value:aa.gender||"",onChange:ae,className:"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm text-black ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",children:[(0,d.jsx)("option",{value:"",children:"Select Gender"}),(0,d.jsx)("option",{value:"Male",children:"Male"}),(0,d.jsx)("option",{value:"Female",children:"Female"})]},`gender-select-${aa.gender}`)]}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(k.J,{htmlFor:"category",children:"Specialist"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(j.p,{id:"category",name:"category",value:W,onChange:a=>{X(a.target.value),Z(!0)},onFocus:()=>Z(!0),placeholder:"Search and select specialist...",disabled:U,className:"pr-10"},`category-input-${aa.category}`),W&&(0,d.jsx)("button",{type:"button",onClick:()=>{X(""),ab(a=>({...a,category:""})),Z(!1)},className:"absolute right-8 top-3 h-4 w-4 text-muted-foreground hover:text-red-500",children:"\xd7"}),(0,d.jsx)(D.A,{className:"absolute right-3 top-3 h-4 w-4 text-muted-foreground"}),Y&&!U&&(0,d.jsxs)("div",{className:"absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto",children:[S.filter(a=>a.Name.toLowerCase().includes(W.toLowerCase())).map(a=>(0,d.jsxs)("div",{className:"px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm",onClick:()=>{ab(b=>({...b,category:a.CategoryID})),X(a.Name),Z(!1)},children:[(0,d.jsx)("div",{className:"font-medium",children:a.Name}),a.ParentCategoryID&&(0,d.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Subcategory of:"," ",S.find(b=>b.CategoryID===a.ParentCategoryID)?.Name]})]},a.CategoryID)),0===S.filter(a=>a.Name.toLowerCase().includes(W.toLowerCase())).length&&(0,d.jsx)("div",{className:"px-3 py-2 text-sm text-muted-foreground",children:"No specialists found"})]})]}),U&&(0,d.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Loading specialists..."}),Y&&(0,d.jsx)("div",{className:"fixed inset-0 z-5",onClick:()=>Z(!1)})]})]}),(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(k.J,{htmlFor:"email",children:"Email Address"}),(0,d.jsx)(j.p,{id:"email",name:"email",type:"email",value:aa.email,onChange:ae,disabled:!0,className:"bg-gray-50",placeholder:"Email address"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Email address cannot be changed"})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(k.J,{htmlFor:"phone",children:"Phone Number"}),(0,d.jsx)(j.p,{id:"phone",name:"phone",value:aa.phone,onChange:ae,disabled:!0,className:"bg-gray-50",placeholder:"Phone number"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Phone number cannot be changed"})]})]}),(0,d.jsx)(i.$,{type:"submit",disabled:O,children:O?(0,d.jsxs)("span",{className:"flex items-center gap-2",children:[(0,d.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Updating..."]}):(0,d.jsxs)("span",{className:"flex items-center gap-2",children:[(0,d.jsx)(E,{className:"h-4 w-4"}),"Save Changes"]})})]})})]})}),(0,d.jsx)(l.av,{value:"password",className:"mt-4 bg-white rounded-lg shadow-sm",children:(0,d.jsxs)("div",{className:"p-8",children:[(0,d.jsx)("h3",{className:"text-xl font-semibold mb-6",children:"Security Settings"}),(0,d.jsx)(h.Zp,{children:(0,d.jsxs)("form",{onSubmit:af,className:"p-6",children:[Q&&(0,d.jsx)("div",{className:"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6",children:"Password updated successfully!"}),(0,d.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(k.J,{htmlFor:"currentPassword",children:"Current Password"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(j.p,{id:"currentPassword",name:"currentPassword",type:M?"text":"password",value:aa.currentPassword,onChange:ae}),(0,d.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>N(!M),children:M?(0,d.jsx)(F.A,{className:"h-4 w-4"}):(0,d.jsx)(G.A,{className:"h-4 w-4"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(k.J,{htmlFor:"newPassword",children:"New Password"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(j.p,{id:"newPassword",name:"newPassword",type:M?"text":"password",value:aa.newPassword,onChange:ae}),(0,d.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>N(!M),children:M?(0,d.jsx)(F.A,{className:"h-4 w-4"}):(0,d.jsx)(G.A,{className:"h-4 w-4"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(k.J,{htmlFor:"confirmPassword",children:"Confirm New Password"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(j.p,{id:"confirmPassword",name:"confirmPassword",type:M?"text":"password",value:aa.confirmPassword,onChange:ae}),(0,d.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3",onClick:()=>N(!M),children:M?(0,d.jsx)(F.A,{className:"h-4 w-4"}):(0,d.jsx)(G.A,{className:"h-4 w-4"})})]})]})]}),(0,d.jsx)(i.$,{type:"submit",disabled:O||!aa.currentPassword||!aa.newPassword||!aa.confirmPassword||aa.newPassword!==aa.confirmPassword,children:O?(0,d.jsxs)("span",{className:"flex items-center gap-2",children:[(0,d.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,d.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,d.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Updating..."]}):(0,d.jsxs)("span",{className:"flex items-center gap-2",children:[(0,d.jsx)(E,{className:"h-4 w-4"}),"Update Password"]})}),ac&&(0,d.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6",children:ac})]})})]})})]})})]})]})]})}},68988:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"},71702:(a,b,c)=>{"use strict";c.d(b,{dj:()=>l});var d=c(43210);let e=0,f=new Map,g=a=>{if(f.has(a))return;let b=setTimeout(()=>{f.delete(a),j({type:"REMOVE_TOAST",toastId:a})},1e6);f.set(a,b)},h=[],i={toasts:[]};function j(a){i=((a,b)=>{switch(b.type){case"ADD_TOAST":return{...a,toasts:[b.toast,...a.toasts].slice(0,1)};case"UPDATE_TOAST":return{...a,toasts:a.toasts.map(a=>a.id===b.toast.id?{...a,...b.toast}:a)};case"DISMISS_TOAST":{let{toastId:c}=b;return c?g(c):a.toasts.forEach(a=>{g(a.id)}),{...a,toasts:a.toasts.map(a=>a.id===c||void 0===c?{...a,open:!1}:a)}}case"REMOVE_TOAST":if(void 0===b.toastId)return{...a,toasts:[]};return{...a,toasts:a.toasts.filter(a=>a.id!==b.toastId)}}})(i,a),h.forEach(a=>{a(i)})}function k({duration:a=2e3,...b}){let c=(e=(e+1)%Number.MAX_SAFE_INTEGER).toString(),d=()=>j({type:"DISMISS_TOAST",toastId:c});return j({type:"ADD_TOAST",toast:{...b,id:c,duration:a,open:!0,onOpenChange:a=>{a||d()}}}),setTimeout(()=>{d()},a),{id:c,dismiss:d,update:a=>j({type:"UPDATE_TOAST",toast:{...a,id:c}})}}function l(){let[a,b]=d.useState(i);return d.useEffect(()=>(h.push(b),()=>{let a=h.indexOf(b);a>-1&&h.splice(a,1)}),[a]),{...a,toast:k,dismiss:a=>j({type:"DISMISS_TOAST",toastId:a})}}k.success=(a,b)=>k({description:a,type:"success",duration:2e3,...b}),k.error=(a,b)=>k({description:a,type:"error",duration:2e3,...b}),k.warning=(a,b)=>k({description:a,type:"warning",duration:2e3,...b}),k.info=(a,b)=>k({description:a,type:"info",duration:2e3,...b})},74075:a=>{"use strict";a.exports=require("zlib")},78148:(a,b,c)=>{"use strict";c.d(b,{b:()=>h});var d=c(43210),e=c(14163),f=c(60687),g=d.forwardRef((a,b)=>(0,f.jsx)(e.sG.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));g.displayName="Label";var h=g},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},85778:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93661:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:a=>{"use strict";a.exports=require("events")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,1697,9822,6085],()=>b(b.s=11487));module.exports=c})();