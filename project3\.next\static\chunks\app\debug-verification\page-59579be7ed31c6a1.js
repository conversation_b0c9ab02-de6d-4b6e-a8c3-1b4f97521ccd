(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4150],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>l,t:()=>o});var n=r(12115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=s(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():s(e[t],null)}}}}function l(...e){return n.useCallback(o(...e),e)}},35547:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var n=r(95155),s=r(12115),o=r(88482),l=r(97168),i=r(89852),a=r(82714);function c(){let[e,t]=(0,s.useState)("+9647858021300"),[r,c]=(0,s.useState)("123456"),[d,u]=(0,s.useState)(""),[f,p]=(0,s.useState)(!1),m=async()=>{p(!0),u("Testing direct backend...");try{let t={PhoneNumber:e,VerificationCode:r,ExpirationMinutes:10,IPAddress:"127.0.0.1",UserAgent:"Debug-Test"},n="https://admin.codemedicalapps.com/api/v1/verification/store";console.log("\uD83D\uDCE4 Testing direct backend:",n),console.log("\uD83D\uDCE4 Request data:",t);let s=await fetch(n,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});console.log("\uD83D\uDCE5 Response status:",s.status),console.log("\uD83D\uDCE5 Response headers:",Object.fromEntries(s.headers.entries()));let o=await s.text();if(console.log("\uD83D\uDCE5 Response body:",o),s.ok)try{let e=JSON.parse(o);u("✅ DIRECT BACKEND SUCCESS:\n".concat(JSON.stringify(e,null,2)))}catch(e){u("✅ DIRECT BACKEND SUCCESS (non-JSON):\n".concat(o))}else u("❌ DIRECT BACKEND FAILED:\nStatus: ".concat(s.status,"\nResponse: ").concat(o))}catch(t){console.error("❌ Error:",t);let e=t instanceof Error?t.message:"Unknown error occurred";u("❌ DIRECT BACKEND ERROR: ".concat(e))}finally{p(!1)}},h=async()=>{p(!0),u("Testing frontend API...");try{let t={phoneNumber:e,verificationCode:r,expirationMinutes:10};console.log("\uD83D\uDCE4 Testing frontend API: /api/verification/store"),console.log("\uD83D\uDCE4 Request data:",t);let n=await fetch("/api/verification/store",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});console.log("\uD83D\uDCE5 Response status:",n.status),console.log("\uD83D\uDCE5 Response headers:",Object.fromEntries(n.headers.entries()));let s=await n.text();if(console.log("\uD83D\uDCE5 Response body:",s),n.ok)try{let e=JSON.parse(s);u("✅ FRONTEND API SUCCESS:\n".concat(JSON.stringify(e,null,2)))}catch(e){u("✅ FRONTEND API SUCCESS (non-JSON):\n".concat(s))}else u("❌ FRONTEND API FAILED:\nStatus: ".concat(n.status,"\nResponse: ").concat(s))}catch(t){console.error("❌ Error:",t);let e=t instanceof Error?t.message:"Unknown error occurred";u("❌ FRONTEND API ERROR: ".concat(e))}finally{p(!1)}},g=async()=>{p(!0),u("Testing signup flow...");try{let t={phoneNumber:e,useWhatsApp:!0};console.log("\uD83D\uDCE4 Testing signup flow: /api/sms/send-verification"),console.log("\uD83D\uDCE4 Request data:",t);let r=await fetch("/api/sms/send-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});console.log("\uD83D\uDCE5 Response status:",r.status),console.log("\uD83D\uDCE5 Response headers:",Object.fromEntries(r.headers.entries()));let n=await r.text();if(console.log("\uD83D\uDCE5 Response body:",n),r.ok)try{let e=JSON.parse(n);u("✅ SIGNUP FLOW SUCCESS:\n".concat(JSON.stringify(e,null,2)))}catch(e){u("✅ SIGNUP FLOW SUCCESS (non-JSON):\n".concat(n))}else u("❌ SIGNUP FLOW FAILED:\nStatus: ".concat(r.status,"\nResponse: ").concat(n))}catch(t){console.error("❌ Error:",t);let e=t instanceof Error?t.message:"Unknown error occurred";u("❌ SIGNUP FLOW ERROR: ".concat(e))}finally{p(!1)}};return(0,n.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-8",children:"Verification Debug Tool"}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)(o.Zp,{className:"p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Configuration"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(a.J,{htmlFor:"phone",children:"Phone Number"}),(0,n.jsx)(i.p,{id:"phone",value:e,onChange:e=>t(e.target.value),placeholder:"+9647858021300"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(a.J,{htmlFor:"code",children:"Verification Code"}),(0,n.jsx)(i.p,{id:"code",value:r,onChange:e=>c(e.target.value),placeholder:"123456"})]})]})]}),(0,n.jsxs)(o.Zp,{className:"p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Tests"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,n.jsx)(l.$,{onClick:m,disabled:f,className:"w-full",children:f?"Testing...":"Test Direct Backend"}),(0,n.jsx)(l.$,{onClick:h,disabled:f,className:"w-full",variant:"outline",children:f?"Testing...":"Test Frontend API"}),(0,n.jsx)(l.$,{onClick:g,disabled:f,className:"w-full",variant:"secondary",children:f?"Testing...":"Test Signup Flow"})]})]}),d&&(0,n.jsxs)(o.Zp,{className:"p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Test Results"}),(0,n.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-sm overflow-auto whitespace-pre-wrap",children:d})]}),(0,n.jsxs)(o.Zp,{className:"p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Environment Information"}),(0,n.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"NEXT_PUBLIC_ADMIN_BASE_URL:"})," ","https://admin.codemedicalapps.com/"]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Current URL:"})," ",window.location.origin]})]})]}),(0,n.jsxs)(o.Zp,{className:"p-6",children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Debug Instructions"}),(0,n.jsxs)("ol",{className:"list-decimal list-inside space-y-2 text-sm",children:[(0,n.jsx)("li",{children:"Open browser console (F12) to see detailed logs"}),(0,n.jsx)("li",{children:'Test "Direct Backend" first to verify backend API works'}),(0,n.jsx)("li",{children:'Test "Frontend API" to check if the issue is in the API route'}),(0,n.jsx)("li",{children:'Test "Signup Flow" to check the complete signup process'}),(0,n.jsx)("li",{children:"Check the console logs for detailed error information"})]})]})]})]})}},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var n=r(12115),s=r(63655),o=r(95155),l=n.forwardRef((e,t)=>(0,o.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var n=r(52596),s=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,n.$)(t))}},55020:(e,t,r)=>{Promise.resolve().then(r.bind(r,35547))},63655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>a,sG:()=>i});var n=r(12115),s=r(47650),o=r(99708),l=r(95155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,o.TL)(`Primitive.${t}`),s=n.forwardRef((e,n)=>{let{asChild:s,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(s?r:t,{...o,ref:n})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function a(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var n=r(52596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.$,l=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:i}=t,a=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],n=null==i?void 0:i[e];if(null===t)return null;let o=s(t)||s(n);return l[e][o]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,a,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...c}[t]):({...i,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},82714:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var n=r(95155),s=r(12115),o=r(40968),l=r(74466),i=r(53999);let a=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(o.b,{ref:t,className:(0,i.cn)(a(),r),...s})});c.displayName=o.b.displayName},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>a,Zp:()=>l,aR:()=>i,wL:()=>u});var n=r(95155),s=r(12115),o=r(53999);let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});l.displayName="Card";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",r),...s})});i.displayName="CardHeader";let a=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("h3",{ref:t,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});a.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("p",{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("p-6 pt-0",r),...s})});d.displayName="CardContent";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center p-6 pt-0",r),...s})});u.displayName="CardFooter"},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>l});var n=r(95155),s=r(12115),o=r(53999);let l=s.forwardRef((e,t)=>{let{className:r,type:s,...l}=e;return(0,n.jsx)("input",{type:s,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...l})});l.displayName="Input"},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>a});var n=r(95155),s=r(12115),o=r(99708),l=r(74466),i=r(53999);let a=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:r,variant:s,size:l,asChild:c=!1,...d}=e,u=c?o.DX:"button";return(0,n.jsx)(u,{className:(0,i.cn)(a({variant:s,size:l,className:r})),ref:t,...d})});c.displayName="Button"},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i,Dc:()=>c,TL:()=>l});var n=r(12115),s=r(6101),o=r(95155);function l(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var l;let e,i,a=(l=r,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,t){let r={...t};for(let n in t){let s=e[n],o=t[n];/^on[A-Z]/.test(n)?s&&o?r[n]=(...e)=>{let t=o(...e);return s(...e),t}:s&&(r[n]=s):"style"===n?r[n]={...s,...o}:"className"===n&&(r[n]=[s,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,s.t)(t,a):a),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:s,...l}=e,i=n.Children.toArray(s),a=i.find(d);if(a){let e=a.props.children,s=i.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,o.jsx)(t,{...l,ref:r,children:s})});return r.displayName=`${e}.Slot`,r}var i=l("Slot"),a=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=a,t}function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}}},e=>{e.O(0,[4277,8441,5964,7358],()=>e(e.s=55020)),_N_E=e.O()}]);