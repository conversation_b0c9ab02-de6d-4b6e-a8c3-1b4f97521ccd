"use strict";exports.id=4773,exports.ids=[4773],exports.modules={7044:(a,b,c)=>{c.d(b,{B:()=>d});let d="undefined"!=typeof window},12157:(a,b,c)=>{c.d(b,{L:()=>d});let d=(0,c(43210).createContext)({})},13861:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15124:(a,b,c)=>{c.d(b,{E:()=>e});var d=c(43210);let e=c(7044).B?d.useLayoutEffect:d.useEffect},21279:(a,b,c)=>{c.d(b,{t:()=>d});let d=(0,c(43210).createContext)(null)},32582:(a,b,c)=>{c.d(b,{Q:()=>d});let d=(0,c(43210).createContext)({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"})},72789:(a,b,c)=>{c.d(b,{M:()=>e});var d=c(43210);function e(a){let b=(0,d.useRef)(null);return null===b.current&&(b.current=a()),b.current}},86044:(a,b,c)=>{c.d(b,{xQ:()=>f});var d=c(43210),e=c(21279);function f(a=!0){let b=(0,d.useContext)(e.t);if(null===b)return[!0,null];let{isPresent:c,onExitComplete:g,register:h}=b,i=(0,d.useId)();(0,d.useEffect)(()=>{a&&h(i)},[a]);let j=(0,d.useCallback)(()=>a&&g&&g(i),[i,g,a]);return!c&&g?[!1,j]:[!0]}},97905:(a,b,c)=>{let d;function e(a){return null!==a&&"object"==typeof a&&"function"==typeof a.start}c.d(b,{P:()=>fd});let f=a=>Array.isArray(a);function g(a,b){if(!Array.isArray(b))return!1;let c=b.length;if(c!==a.length)return!1;for(let d=0;d<c;d++)if(b[d]!==a[d])return!1;return!0}function h(a){return"string"==typeof a||Array.isArray(a)}function i(a){let b=[{},{}];return null==a||a.values.forEach((a,c)=>{b[0][c]=a.get(),b[1][c]=a.getVelocity()}),b}function j(a,b,c,d){if("function"==typeof b){let[e,f]=i(d);b=b(void 0!==c?c:a.custom,e,f)}if("string"==typeof b&&(b=a.variants&&a.variants[b]),"function"==typeof b){let[e,f]=i(d);b=b(void 0!==c?c:a.custom,e,f)}return b}function k(a,b,c){let d=a.getProps();return j(d,b,void 0!==c?c:d.custom,a)}let l=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],m=["initial",...l];function n(a){let b;return()=>(void 0===b&&(b=a()),b)}let o=n(()=>void 0!==window.ScrollTimeline);class p{constructor(a){this.stop=()=>this.runAll("stop"),this.animations=a.filter(Boolean)}get finished(){return Promise.all(this.animations.map(a=>"finished"in a?a.finished:a))}getAll(a){return this.animations[0][a]}setAll(a,b){for(let c=0;c<this.animations.length;c++)this.animations[c][a]=b}attachTimeline(a,b){let c=this.animations.map(c=>o()&&c.attachTimeline?c.attachTimeline(a):"function"==typeof b?b(c):void 0);return()=>{c.forEach((a,b)=>{a&&a(),this.animations[b].stop()})}}get time(){return this.getAll("time")}set time(a){this.setAll("time",a)}get speed(){return this.getAll("speed")}set speed(a){this.setAll("speed",a)}get startTime(){return this.getAll("startTime")}get duration(){let a=0;for(let b=0;b<this.animations.length;b++)a=Math.max(a,this.animations[b].duration);return a}runAll(a){this.animations.forEach(b=>b[a]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class q extends p{then(a,b){return Promise.all(this.animations).then(a).catch(b)}}function r(a,b){return a?a[b]||a.default||a:void 0}function s(a){let b=0,c=a.next(b);for(;!c.done&&b<2e4;)b+=50,c=a.next(b);return b>=2e4?1/0:b}function t(a){return"function"==typeof a}function u(a,b){a.timeline=b,a.onfinish=null}let v=a=>Array.isArray(a)&&"number"==typeof a[0],w={linearEasing:void 0},x=function(a,b){let c=n(a);return()=>{var a;return null!=(a=w[b])?a:c()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(a){return!1}return!0},"linearEasing"),y=(a,b,c)=>{let d=b-a;return 0===d?1:(c-a)/d},z=(a,b,c=10)=>{let d="",e=Math.max(Math.round(b/c),2);for(let b=0;b<e;b++)d+=a(y(0,e-1,b))+", ";return`linear(${d.substring(0,d.length-2)})`},A=([a,b,c,d])=>`cubic-bezier(${a}, ${b}, ${c}, ${d})`,B={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:A([0,.65,.55,1]),circOut:A([.55,0,1,.45]),backIn:A([.31,.01,.66,-.59]),backOut:A([.33,1.53,.69,.99])},C={x:!1,y:!1};function D(a,b){let c=function(a,b,c){if(a instanceof Element)return[a];if("string"==typeof a){let b=document.querySelectorAll(a);return b?Array.from(b):[]}return Array.from(a)}(a),d=new AbortController;return[c,{passive:!0,...b,signal:d.signal},()=>d.abort()]}function E(a){return b=>{"touch"===b.pointerType||C.x||C.y||a(b)}}let F=(a,b)=>!!b&&(a===b||F(a,b.parentElement)),G=a=>"mouse"===a.pointerType?"number"!=typeof a.button||a.button<=0:!1!==a.isPrimary,H=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),I=new WeakSet;function J(a){return b=>{"Enter"===b.key&&a(b)}}function K(a,b){a.dispatchEvent(new PointerEvent("pointer"+b,{isPrimary:!0,bubbles:!0}))}function L(a){return G(a)&&!(C.x||C.y)}let M=a=>1e3*a,N=a=>a,O=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],P=new Set(O),Q=new Set(["width","height","top","left","right","bottom",...O]),R=a=>f(a)?a[a.length-1]||0:a,S={skipAnimations:!1,useManualTiming:!1},T=["read","resolveKeyframes","update","preRender","render","postRender"];function U(a,b){let c=!1,d=!0,e={delta:0,timestamp:0,isProcessing:!1},f=()=>c=!0,g=T.reduce((a,b)=>(a[b]=function(a){let b=new Set,c=new Set,d=!1,e=!1,f=new WeakSet,g={delta:0,timestamp:0,isProcessing:!1};function h(b){f.has(b)&&(i.schedule(b),a()),b(g)}let i={schedule:(a,e=!1,g=!1)=>{let h=g&&d?b:c;return e&&f.add(a),h.has(a)||h.add(a),a},cancel:a=>{c.delete(a),f.delete(a)},process:a=>{if(g=a,d){e=!0;return}d=!0,[b,c]=[c,b],b.forEach(h),b.clear(),d=!1,e&&(e=!1,i.process(a))}};return i}(f),a),{}),{read:h,resolveKeyframes:i,update:j,preRender:k,render:l,postRender:m}=g,n=()=>{let f=S.useManualTiming?e.timestamp:performance.now();c=!1,e.delta=d?1e3/60:Math.max(Math.min(f-e.timestamp,40),1),e.timestamp=f,e.isProcessing=!0,h.process(e),i.process(e),j.process(e),k.process(e),l.process(e),m.process(e),e.isProcessing=!1,c&&b&&(d=!1,a(n))};return{schedule:T.reduce((b,f)=>{let h=g[f];return b[f]=(b,f=!1,g=!1)=>(!c&&(c=!0,d=!0,e.isProcessing||a(n)),h.schedule(b,f,g)),b},{}),cancel:a=>{for(let b=0;b<T.length;b++)g[T[b]].cancel(a)},state:e,steps:g}}let{schedule:V,cancel:W,state:X,steps:Y}=U("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:N,!0);function Z(){d=void 0}let $={now:()=>(void 0===d&&$.set(X.isProcessing||S.useManualTiming?X.timestamp:performance.now()),d),set:a=>{d=a,queueMicrotask(Z)}};function _(a,b){-1===a.indexOf(b)&&a.push(b)}function aa(a,b){let c=a.indexOf(b);c>-1&&a.splice(c,1)}class ab{constructor(){this.subscriptions=[]}add(a){return _(this.subscriptions,a),()=>aa(this.subscriptions,a)}notify(a,b,c){let d=this.subscriptions.length;if(d)if(1===d)this.subscriptions[0](a,b,c);else for(let e=0;e<d;e++){let d=this.subscriptions[e];d&&d(a,b,c)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let ac={current:void 0};class ad{constructor(a,b={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(a,b=!0)=>{let c=$.now();this.updatedAt!==c&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(a),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),b&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(a),this.owner=b.owner}setCurrent(a){this.current=a,this.updatedAt=$.now(),null===this.canTrackVelocity&&void 0!==a&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,b){this.events[a]||(this.events[a]=new ab);let c=this.events[a].add(b);return"change"===a?()=>{c(),V.read(()=>{this.events.change.getSize()||this.stop()})}:c}clearListeners(){for(let a in this.events)this.events[a].clear()}attach(a,b){this.passiveEffect=a,this.stopPassiveEffect=b}set(a,b=!0){b&&this.passiveEffect?this.passiveEffect(a,this.updateAndNotify):this.updateAndNotify(a,b)}setWithVelocity(a,b,c){this.set(b),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-c}jump(a,b=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,b&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return ac.current&&ac.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var a;let b=$.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||b-this.updatedAt>30)return 0;let c=Math.min(this.updatedAt-this.prevUpdatedAt,30);return a=parseFloat(this.current)-parseFloat(this.prevFrameValue),c?1e3/c*a:0}start(a){return this.stop(),new Promise(b=>{this.hasAnimated=!0,this.animation=a(b),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ae(a,b){return new ad(a,b)}let af=a=>!!(a&&a.getVelocity);function ag(a,b){let c=a.getValue("willChange");if(af(c)&&c.add)return c.add(b)}let ah=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),ai="data-"+ah("framerAppearId"),aj={current:!1},ak=(a,b,c)=>(((1-3*c+3*b)*a+(3*c-6*b))*a+3*b)*a;function al(a,b,c,d){return a===b&&c===d?N:e=>0===e||1===e?e:ak(function(a,b,c,d,e){let f,g,h=0;do(f=ak(g=b+(c-b)/2,d,e)-a)>0?c=g:b=g;while(Math.abs(f)>1e-7&&++h<12);return g}(e,0,1,a,c),b,d)}let am=a=>b=>b<=.5?a(2*b)/2:(2-a(2*(1-b)))/2,an=a=>b=>1-a(1-b),ao=al(.33,1.53,.69,.99),ap=an(ao),aq=am(ap),ar=a=>(a*=2)<1?.5*ap(a):.5*(2-Math.pow(2,-10*(a-1))),as=a=>1-Math.sin(Math.acos(a)),at=an(as),au=am(as),av=a=>/^0[^.\s]+$/u.test(a),aw=(a,b,c)=>c>b?b:c<a?a:c,ax={test:a=>"number"==typeof a,parse:parseFloat,transform:a=>a},ay={...ax,transform:a=>aw(0,1,a)},az={...ax,default:1},aA=a=>Math.round(1e5*a)/1e5,aB=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,aC=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,aD=(a,b)=>c=>!!("string"==typeof c&&aC.test(c)&&c.startsWith(a)||b&&null!=c&&Object.prototype.hasOwnProperty.call(c,b)),aE=(a,b,c)=>d=>{if("string"!=typeof d)return d;let[e,f,g,h]=d.match(aB);return{[a]:parseFloat(e),[b]:parseFloat(f),[c]:parseFloat(g),alpha:void 0!==h?parseFloat(h):1}},aF={...ax,transform:a=>Math.round(aw(0,255,a))},aG={test:aD("rgb","red"),parse:aE("red","green","blue"),transform:({red:a,green:b,blue:c,alpha:d=1})=>"rgba("+aF.transform(a)+", "+aF.transform(b)+", "+aF.transform(c)+", "+aA(ay.transform(d))+")"},aH={test:aD("#"),parse:function(a){let b="",c="",d="",e="";return a.length>5?(b=a.substring(1,3),c=a.substring(3,5),d=a.substring(5,7),e=a.substring(7,9)):(b=a.substring(1,2),c=a.substring(2,3),d=a.substring(3,4),e=a.substring(4,5),b+=b,c+=c,d+=d,e+=e),{red:parseInt(b,16),green:parseInt(c,16),blue:parseInt(d,16),alpha:e?parseInt(e,16)/255:1}},transform:aG.transform},aI=a=>({test:b=>"string"==typeof b&&b.endsWith(a)&&1===b.split(" ").length,parse:parseFloat,transform:b=>`${b}${a}`}),aJ=aI("deg"),aK=aI("%"),aL=aI("px"),aM=aI("vh"),aN=aI("vw"),aO={...aK,parse:a=>aK.parse(a)/100,transform:a=>aK.transform(100*a)},aP={test:aD("hsl","hue"),parse:aE("hue","saturation","lightness"),transform:({hue:a,saturation:b,lightness:c,alpha:d=1})=>"hsla("+Math.round(a)+", "+aK.transform(aA(b))+", "+aK.transform(aA(c))+", "+aA(ay.transform(d))+")"},aQ={test:a=>aG.test(a)||aH.test(a)||aP.test(a),parse:a=>aG.test(a)?aG.parse(a):aP.test(a)?aP.parse(a):aH.parse(a),transform:a=>"string"==typeof a?a:a.hasOwnProperty("red")?aG.transform(a):aP.transform(a)},aR=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,aS="number",aT="color",aU=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function aV(a){let b=a.toString(),c=[],d={color:[],number:[],var:[]},e=[],f=0,g=b.replace(aU,a=>(aQ.test(a)?(d.color.push(f),e.push(aT),c.push(aQ.parse(a))):a.startsWith("var(")?(d.var.push(f),e.push("var"),c.push(a)):(d.number.push(f),e.push(aS),c.push(parseFloat(a))),++f,"${}")).split("${}");return{values:c,split:g,indexes:d,types:e}}function aW(a){return aV(a).values}function aX(a){let{split:b,types:c}=aV(a),d=b.length;return a=>{let e="";for(let f=0;f<d;f++)if(e+=b[f],void 0!==a[f]){let b=c[f];b===aS?e+=aA(a[f]):b===aT?e+=aQ.transform(a[f]):e+=a[f]}return e}}let aY=a=>"number"==typeof a?0:a,aZ={test:function(a){var b,c;return isNaN(a)&&"string"==typeof a&&((null==(b=a.match(aB))?void 0:b.length)||0)+((null==(c=a.match(aR))?void 0:c.length)||0)>0},parse:aW,createTransformer:aX,getAnimatableNone:function(a){let b=aW(a);return aX(a)(b.map(aY))}},a$=new Set(["brightness","contrast","saturate","opacity"]);function a_(a){let[b,c]=a.slice(0,-1).split("(");if("drop-shadow"===b)return a;let[d]=c.match(aB)||[];if(!d)return a;let e=c.replace(d,""),f=+!!a$.has(b);return d!==c&&(f*=100),b+"("+f+e+")"}let a0=/\b([a-z-]*)\(.*?\)/gu,a1={...aZ,getAnimatableNone:a=>{let b=a.match(a0);return b?b.map(a_).join(" "):a}},a2={...ax,transform:Math.round},a3={borderWidth:aL,borderTopWidth:aL,borderRightWidth:aL,borderBottomWidth:aL,borderLeftWidth:aL,borderRadius:aL,radius:aL,borderTopLeftRadius:aL,borderTopRightRadius:aL,borderBottomRightRadius:aL,borderBottomLeftRadius:aL,width:aL,maxWidth:aL,height:aL,maxHeight:aL,top:aL,right:aL,bottom:aL,left:aL,padding:aL,paddingTop:aL,paddingRight:aL,paddingBottom:aL,paddingLeft:aL,margin:aL,marginTop:aL,marginRight:aL,marginBottom:aL,marginLeft:aL,backgroundPositionX:aL,backgroundPositionY:aL,rotate:aJ,rotateX:aJ,rotateY:aJ,rotateZ:aJ,scale:az,scaleX:az,scaleY:az,scaleZ:az,skew:aJ,skewX:aJ,skewY:aJ,distance:aL,translateX:aL,translateY:aL,translateZ:aL,x:aL,y:aL,z:aL,perspective:aL,transformPerspective:aL,opacity:ay,originX:aO,originY:aO,originZ:aL,zIndex:a2,size:aL,fillOpacity:ay,strokeOpacity:ay,numOctaves:a2},a4={...a3,color:aQ,backgroundColor:aQ,outlineColor:aQ,fill:aQ,stroke:aQ,borderColor:aQ,borderTopColor:aQ,borderRightColor:aQ,borderBottomColor:aQ,borderLeftColor:aQ,filter:a1,WebkitFilter:a1},a5=a=>a4[a];function a6(a,b){let c=a5(a);return c!==a1&&(c=aZ),c.getAnimatableNone?c.getAnimatableNone(b):void 0}let a7=new Set(["auto","none","0"]),a8=a=>a===ax||a===aL,a9=(a,b)=>parseFloat(a.split(", ")[b]),ba=(a,b)=>(c,{transform:d})=>{if("none"===d||!d)return 0;let e=d.match(/^matrix3d\((.+)\)$/u);if(e)return a9(e[1],b);{let b=d.match(/^matrix\((.+)\)$/u);return b?a9(b[1],a):0}},bb=new Set(["x","y","z"]),bc=O.filter(a=>!bb.has(a)),bd={width:({x:a},{paddingLeft:b="0",paddingRight:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),height:({y:a},{paddingTop:b="0",paddingBottom:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),top:(a,{top:b})=>parseFloat(b),left:(a,{left:b})=>parseFloat(b),bottom:({y:a},{top:b})=>parseFloat(b)+(a.max-a.min),right:({x:a},{left:b})=>parseFloat(b)+(a.max-a.min),x:ba(4,13),y:ba(5,14)};bd.translateX=bd.x,bd.translateY=bd.y;let be=new Set,bf=!1,bg=!1;function bh(){if(bg){let a=Array.from(be).filter(a=>a.needsMeasurement),b=new Set(a.map(a=>a.element)),c=new Map;b.forEach(a=>{let b=function(a){let b=[];return bc.forEach(c=>{let d=a.getValue(c);void 0!==d&&(b.push([c,d.get()]),d.set(+!!c.startsWith("scale")))}),b}(a);b.length&&(c.set(a,b),a.render())}),a.forEach(a=>a.measureInitialState()),b.forEach(a=>{a.render();let b=c.get(a);b&&b.forEach(([b,c])=>{var d;null==(d=a.getValue(b))||d.set(c)})}),a.forEach(a=>a.measureEndState()),a.forEach(a=>{void 0!==a.suspendedScrollY&&window.scrollTo(0,a.suspendedScrollY)})}bg=!1,bf=!1,be.forEach(a=>a.complete()),be.clear()}function bi(){be.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(bg=!0)})}class bj{constructor(a,b,c,d,e,f=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...a],this.onComplete=b,this.name=c,this.motionValue=d,this.element=e,this.isAsync=f}scheduleResolve(){this.isScheduled=!0,this.isAsync?(be.add(this),bf||(bf=!0,V.read(bi),V.resolveKeyframes(bh))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:a,name:b,element:c,motionValue:d}=this;for(let e=0;e<a.length;e++)if(null===a[e])if(0===e){let e=null==d?void 0:d.get(),f=a[a.length-1];if(void 0!==e)a[0]=e;else if(c&&b){let d=c.readValue(b,f);null!=d&&(a[0]=d)}void 0===a[0]&&(a[0]=f),d&&void 0===e&&d.set(a[0])}else a[e]=a[e-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),be.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,be.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let bk=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a),bl=a=>b=>"string"==typeof b&&b.startsWith(a),bm=bl("--"),bn=bl("var(--"),bo=a=>!!bn(a)&&bp.test(a.split("/*")[0].trim()),bp=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,bq=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,br=a=>b=>b.test(a),bs=[ax,aL,aK,aJ,aN,aM,{test:a=>"auto"===a,parse:a=>a}],bt=a=>bs.find(br(a));class bu extends bj{constructor(a,b,c,d,e){super(a,b,c,d,e,!0)}readKeyframes(){let{unresolvedKeyframes:a,element:b,name:c}=this;if(!b||!b.current)return;super.readKeyframes();for(let c=0;c<a.length;c++){let d=a[c];if("string"==typeof d&&bo(d=d.trim())){let e=function a(b,c,d=1){N(d<=4,`Max CSS variable fallback depth detected in property "${b}". This may indicate a circular fallback dependency.`);let[e,f]=function(a){let b=bq.exec(a);if(!b)return[,];let[,c,d,e]=b;return[`--${null!=c?c:d}`,e]}(b);if(!e)return;let g=window.getComputedStyle(c).getPropertyValue(e);if(g){let a=g.trim();return bk(a)?parseFloat(a):a}return bo(f)?a(f,c,d+1):f}(d,b.current);void 0!==e&&(a[c]=e),c===a.length-1&&(this.finalKeyframe=d)}}if(this.resolveNoneKeyframes(),!Q.has(c)||2!==a.length)return;let[d,e]=a,f=bt(d),g=bt(e);if(f!==g)if(a8(f)&&a8(g))for(let b=0;b<a.length;b++){let c=a[b];"string"==typeof c&&(a[b]=parseFloat(c))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:a,name:b}=this,c=[];for(let b=0;b<a.length;b++){var d;("number"==typeof(d=a[b])?0===d:null===d||"none"===d||"0"===d||av(d))&&c.push(b)}c.length&&function(a,b,c){let d,e=0;for(;e<a.length&&!d;){let b=a[e];"string"==typeof b&&!a7.has(b)&&aV(b).values.length&&(d=a[e]),e++}if(d&&c)for(let e of b)a[e]=a6(c,d)}(a,c,b)}measureInitialState(){let{element:a,unresolvedKeyframes:b,name:c}=this;if(!a||!a.current)return;"height"===c&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=bd[c](a.measureViewportBox(),window.getComputedStyle(a.current)),b[0]=this.measuredOrigin;let d=b[b.length-1];void 0!==d&&a.getValue(c,d).jump(d,!1)}measureEndState(){var a;let{element:b,name:c,unresolvedKeyframes:d}=this;if(!b||!b.current)return;let e=b.getValue(c);e&&e.jump(this.measuredOrigin,!1);let f=d.length-1,g=d[f];d[f]=bd[c](b.measureViewportBox(),window.getComputedStyle(b.current)),null!==g&&void 0===this.finalKeyframe&&(this.finalKeyframe=g),(null==(a=this.removedTransforms)?void 0:a.length)&&this.removedTransforms.forEach(([a,c])=>{b.getValue(a).set(c)}),this.resolveNoneKeyframes()}}let bv=(a,b)=>"zIndex"!==b&&!!("number"==typeof a||Array.isArray(a)||"string"==typeof a&&(aZ.test(a)||"0"===a)&&!a.startsWith("url(")),bw=a=>null!==a;function bx(a,{repeat:b,repeatType:c="loop"},d){let e=a.filter(bw),f=b&&"loop"!==c&&b%2==1?0:e.length-1;return f&&void 0!==d?d:e[f]}class by{constructor({autoplay:a=!0,delay:b=0,type:c="keyframes",repeat:d=0,repeatDelay:e=0,repeatType:f="loop",...g}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=$.now(),this.options={autoplay:a,delay:b,type:c,repeat:d,repeatDelay:e,repeatType:f,...g},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(bi(),bh()),this._resolved}onKeyframesResolved(a,b){this.resolvedAt=$.now(),this.hasAttemptedResolve=!0;let{name:c,type:d,velocity:e,delay:f,onComplete:g,onUpdate:h,isGenerator:i}=this.options;if(!i&&!function(a,b,c,d){let e=a[0];if(null===e)return!1;if("display"===b||"visibility"===b)return!0;let f=a[a.length-1],g=bv(e,b),h=bv(f,b);return N(g===h,`You are trying to animate ${b} from "${e}" to "${f}". ${e} is not an animatable value - to enable this animation set ${e} to a value animatable to ${f} via the \`style\` property.`),!!g&&!!h&&(function(a){let b=a[0];if(1===a.length)return!0;for(let c=0;c<a.length;c++)if(a[c]!==b)return!0}(a)||("spring"===c||t(c))&&d)}(a,c,d,e))if(aj.current||!f){h&&h(bx(a,this.options,b)),g&&g(),this.resolveFinishedPromise();return}else this.options.duration=0;let j=this.initPlayback(a,b);!1!==j&&(this._resolved={keyframes:a,finalKeyframe:b,...j},this.onPostResolved())}onPostResolved(){}then(a,b){return this.currentFinishedPromise.then(a,b)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(a=>{this.resolveFinishedPromise=a})}}let bz=(a,b,c)=>a+(b-a)*c;function bA(a,b,c){return(c<0&&(c+=1),c>1&&(c-=1),c<1/6)?a+(b-a)*6*c:c<.5?b:c<2/3?a+(b-a)*(2/3-c)*6:a}function bB(a,b){return c=>c>0?b:a}let bC=(a,b,c)=>{let d=a*a,e=c*(b*b-d)+d;return e<0?0:Math.sqrt(e)},bD=[aH,aG,aP];function bE(a){let b=bD.find(b=>b.test(a));if(N(!!b,`'${a}' is not an animatable color. Use the equivalent color code instead.`),!b)return!1;let c=b.parse(a);return b===aP&&(c=function({hue:a,saturation:b,lightness:c,alpha:d}){a/=360,c/=100;let e=0,f=0,g=0;if(b/=100){let d=c<.5?c*(1+b):c+b-c*b,h=2*c-d;e=bA(h,d,a+1/3),f=bA(h,d,a),g=bA(h,d,a-1/3)}else e=f=g=c;return{red:Math.round(255*e),green:Math.round(255*f),blue:Math.round(255*g),alpha:d}}(c)),c}let bF=(a,b)=>{let c=bE(a),d=bE(b);if(!c||!d)return bB(a,b);let e={...c};return a=>(e.red=bC(c.red,d.red,a),e.green=bC(c.green,d.green,a),e.blue=bC(c.blue,d.blue,a),e.alpha=bz(c.alpha,d.alpha,a),aG.transform(e))},bG=(a,b)=>c=>b(a(c)),bH=(...a)=>a.reduce(bG),bI=new Set(["none","hidden"]);function bJ(a,b){return c=>bz(a,b,c)}function bK(a){return"number"==typeof a?bJ:"string"==typeof a?bo(a)?bB:aQ.test(a)?bF:bN:Array.isArray(a)?bL:"object"==typeof a?aQ.test(a)?bF:bM:bB}function bL(a,b){let c=[...a],d=c.length,e=a.map((a,c)=>bK(a)(a,b[c]));return a=>{for(let b=0;b<d;b++)c[b]=e[b](a);return c}}function bM(a,b){let c={...a,...b},d={};for(let e in c)void 0!==a[e]&&void 0!==b[e]&&(d[e]=bK(a[e])(a[e],b[e]));return a=>{for(let b in d)c[b]=d[b](a);return c}}let bN=(a,b)=>{let c=aZ.createTransformer(b),d=aV(a),e=aV(b);return d.indexes.var.length===e.indexes.var.length&&d.indexes.color.length===e.indexes.color.length&&d.indexes.number.length>=e.indexes.number.length?bI.has(a)&&!e.values.length||bI.has(b)&&!d.values.length?function(a,b){return bI.has(a)?c=>c<=0?a:b:c=>c>=1?b:a}(a,b):bH(bL(function(a,b){var c;let d=[],e={color:0,var:0,number:0};for(let f=0;f<b.values.length;f++){let g=b.types[f],h=a.indexes[g][e[g]],i=null!=(c=a.values[h])?c:0;d[f]=i,e[g]++}return d}(d,e),e.values),c):(N(!0,`Complex values '${a}' and '${b}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),bB(a,b))};function bO(a,b,c){return"number"==typeof a&&"number"==typeof b&&"number"==typeof c?bz(a,b,c):bK(a)(a,b)}function bP(a,b,c){var d,e;let f=Math.max(b-5,0);return d=c-a(f),(e=b-f)?1e3/e*d:0}let bQ={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function bR(a,b){return a*Math.sqrt(1-b*b)}let bS=["duration","bounce"],bT=["stiffness","damping","mass"];function bU(a,b){return b.some(b=>void 0!==a[b])}function bV(a=bQ.visualDuration,b=bQ.bounce){let c,d="object"!=typeof a?{visualDuration:a,keyframes:[0,1],bounce:b}:a,{restSpeed:e,restDelta:f}=d,g=d.keyframes[0],h=d.keyframes[d.keyframes.length-1],i={done:!1,value:g},{stiffness:j,damping:k,mass:l,duration:m,velocity:n,isResolvedFromDuration:o}=function(a){let b={velocity:bQ.velocity,stiffness:bQ.stiffness,damping:bQ.damping,mass:bQ.mass,isResolvedFromDuration:!1,...a};if(!bU(a,bT)&&bU(a,bS))if(a.visualDuration){let c=2*Math.PI/(1.2*a.visualDuration),d=c*c,e=2*aw(.05,1,1-(a.bounce||0))*Math.sqrt(d);b={...b,mass:bQ.mass,stiffness:d,damping:e}}else{let c=function({duration:a=bQ.duration,bounce:b=bQ.bounce,velocity:c=bQ.velocity,mass:d=bQ.mass}){let e,f;N(a<=M(bQ.maxDuration),"Spring duration must be 10 seconds or less");let g=1-b;g=aw(bQ.minDamping,bQ.maxDamping,g),a=aw(bQ.minDuration,bQ.maxDuration,a/1e3),g<1?(e=b=>{let d=b*g,e=d*a;return .001-(d-c)/bR(b,g)*Math.exp(-e)},f=b=>{let d=b*g*a,f=Math.pow(g,2)*Math.pow(b,2)*a,h=Math.exp(-d),i=bR(Math.pow(b,2),g);return(d*c+c-f)*h*(-e(b)+.001>0?-1:1)/i}):(e=b=>-.001+Math.exp(-b*a)*((b-c)*a+1),f=b=>a*a*(c-b)*Math.exp(-b*a));let h=function(a,b,c){let d=c;for(let c=1;c<12;c++)d-=a(d)/b(d);return d}(e,f,5/a);if(a=M(a),isNaN(h))return{stiffness:bQ.stiffness,damping:bQ.damping,duration:a};{let b=Math.pow(h,2)*d;return{stiffness:b,damping:2*g*Math.sqrt(d*b),duration:a}}}(a);(b={...b,...c,mass:bQ.mass}).isResolvedFromDuration=!0}return b}({...d,velocity:-((d.velocity||0)/1e3)}),p=n||0,q=k/(2*Math.sqrt(j*l)),r=h-g,t=Math.sqrt(j/l)/1e3,u=5>Math.abs(r);if(e||(e=u?bQ.restSpeed.granular:bQ.restSpeed.default),f||(f=u?bQ.restDelta.granular:bQ.restDelta.default),q<1){let a=bR(t,q);c=b=>h-Math.exp(-q*t*b)*((p+q*t*r)/a*Math.sin(a*b)+r*Math.cos(a*b))}else if(1===q)c=a=>h-Math.exp(-t*a)*(r+(p+t*r)*a);else{let a=t*Math.sqrt(q*q-1);c=b=>{let c=Math.exp(-q*t*b),d=Math.min(a*b,300);return h-c*((p+q*t*r)*Math.sinh(d)+a*r*Math.cosh(d))/a}}let v={calculatedDuration:o&&m||null,next:a=>{let b=c(a);if(o)i.done=a>=m;else{let d=0;q<1&&(d=0===a?M(p):bP(c,a,b));let g=Math.abs(h-b)<=f;i.done=Math.abs(d)<=e&&g}return i.value=i.done?h:b,i},toString:()=>{let a=Math.min(s(v),2e4),b=z(b=>v.next(a*b).value,a,30);return a+"ms "+b}};return v}function bW({keyframes:a,velocity:b=0,power:c=.8,timeConstant:d=325,bounceDamping:e=10,bounceStiffness:f=500,modifyTarget:g,min:h,max:i,restDelta:j=.5,restSpeed:k}){let l,m,n=a[0],o={done:!1,value:n},p=c*b,q=n+p,r=void 0===g?q:g(q);r!==q&&(p=r-n);let s=a=>-p*Math.exp(-a/d),t=a=>r+s(a),u=a=>{let b=s(a),c=t(a);o.done=Math.abs(b)<=j,o.value=o.done?r:c},v=a=>{let b;if(b=o.value,void 0!==h&&b<h||void 0!==i&&b>i){var c;l=a,m=bV({keyframes:[o.value,(c=o.value,void 0===h?i:void 0===i||Math.abs(h-c)<Math.abs(i-c)?h:i)],velocity:bP(t,a,o.value),damping:e,stiffness:f,restDelta:j,restSpeed:k})}};return v(0),{calculatedDuration:null,next:a=>{let b=!1;return(m||void 0!==l||(b=!0,u(a),v(a)),void 0!==l&&a>=l)?m.next(a-l):(b||u(a),o)}}}let bX=al(.42,0,1,1),bY=al(0,0,.58,1),bZ=al(.42,0,.58,1),b$={linear:N,easeIn:bX,easeInOut:bZ,easeOut:bY,circIn:as,circInOut:au,circOut:at,backIn:ap,backInOut:aq,backOut:ao,anticipate:ar},b_=a=>{if(v(a)){N(4===a.length,"Cubic bezier arrays must contain four numerical values.");let[b,c,d,e]=a;return al(b,c,d,e)}return"string"==typeof a?(N(void 0!==b$[a],`Invalid easing type '${a}'`),b$[a]):a};function b0({duration:a=300,keyframes:b,times:c,ease:d="easeInOut"}){var e;let f=Array.isArray(d)&&"number"!=typeof d[0]?d.map(b_):b_(d),g={done:!1,value:b[0]},h=function(a,b,{clamp:c=!0,ease:d,mixer:e}={}){let f=a.length;if(N(f===b.length,"Both input and output ranges must be the same length"),1===f)return()=>b[0];if(2===f&&b[0]===b[1])return()=>b[1];let g=a[0]===a[1];a[0]>a[f-1]&&(a=[...a].reverse(),b=[...b].reverse());let h=function(a,b,c){let d=[],e=c||bO,f=a.length-1;for(let c=0;c<f;c++){let f=e(a[c],a[c+1]);b&&(f=bH(Array.isArray(b)?b[c]||N:b,f)),d.push(f)}return d}(b,d,e),i=h.length,j=c=>{if(g&&c<a[0])return b[0];let d=0;if(i>1)for(;d<a.length-2&&!(c<a[d+1]);d++);let e=y(a[d],a[d+1],c);return h[d](e)};return c?b=>j(aw(a[0],a[f-1],b)):j}((e=c&&c.length===b.length?c:function(a){let b=[0];return!function(a,b){let c=a[a.length-1];for(let d=1;d<=b;d++){let e=y(0,b,d);a.push(bz(c,1,e))}}(b,a.length-1),b}(b),e.map(b=>b*a)),b,{ease:Array.isArray(f)?f:b.map(()=>f||bZ).splice(0,b.length-1)});return{calculatedDuration:a,next:b=>(g.value=h(b),g.done=b>=a,g)}}let b1=a=>{let b=({timestamp:b})=>a(b);return{start:()=>V.update(b,!0),stop:()=>W(b),now:()=>X.isProcessing?X.timestamp:$.now()}},b2={decay:bW,inertia:bW,tween:b0,keyframes:b0,spring:bV},b3=a=>a/100;class b4 extends by{constructor(a){super(a),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:a}=this.options;a&&a()};let{name:b,motionValue:c,element:d,keyframes:e}=this.options,f=(null==d?void 0:d.KeyframeResolver)||bj,g=(a,b)=>this.onKeyframesResolved(a,b);this.resolver=new f(e,g,b,c,d),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(a){let b,c,{type:d="keyframes",repeat:e=0,repeatDelay:f=0,repeatType:g,velocity:h=0}=this.options,i=t(d)?d:b2[d]||b0;i!==b0&&"number"!=typeof a[0]&&(b=bH(b3,bO(a[0],a[1])),a=[0,100]);let j=i({...this.options,keyframes:a});"mirror"===g&&(c=i({...this.options,keyframes:[...a].reverse(),velocity:-h})),null===j.calculatedDuration&&(j.calculatedDuration=s(j));let{calculatedDuration:k}=j,l=k+f;return{generator:j,mirroredGenerator:c,mapPercentToKeyframes:b,calculatedDuration:k,resolvedDuration:l,totalDuration:l*(e+1)-f}}onPostResolved(){let{autoplay:a=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&a?this.state=this.pendingPlayState:this.pause()}tick(a,b=!1){let{resolved:c}=this;if(!c){let{keyframes:a}=this.options;return{done:!0,value:a[a.length-1]}}let{finalKeyframe:d,generator:e,mirroredGenerator:f,mapPercentToKeyframes:g,keyframes:h,calculatedDuration:i,totalDuration:j,resolvedDuration:k}=c;if(null===this.startTime)return e.next(0);let{delay:l,repeat:m,repeatType:n,repeatDelay:o,onUpdate:p}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-j/this.speed,this.startTime)),b?this.currentTime=a:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(a-this.startTime)*this.speed;let q=this.currentTime-l*(this.speed>=0?1:-1),r=this.speed>=0?q<0:q>j;this.currentTime=Math.max(q,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=j);let s=this.currentTime,t=e;if(m){let a=Math.min(this.currentTime,j)/k,b=Math.floor(a),c=a%1;!c&&a>=1&&(c=1),1===c&&b--,(b=Math.min(b,m+1))%2&&("reverse"===n?(c=1-c,o&&(c-=o/k)):"mirror"===n&&(t=f)),s=aw(0,1,c)*k}let u=r?{done:!1,value:h[0]}:t.next(s);g&&(u.value=g(u.value));let{done:v}=u;r||null===i||(v=this.speed>=0?this.currentTime>=j:this.currentTime<=0);let w=null===this.holdTime&&("finished"===this.state||"running"===this.state&&v);return w&&void 0!==d&&(u.value=bx(h,this.options,d)),p&&p(u.value),w&&this.finish(),u}get duration(){let{resolved:a}=this;return a?a.calculatedDuration/1e3:0}get time(){return this.currentTime/1e3}set time(a){a=M(a),this.currentTime=a,null!==this.holdTime||0===this.speed?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.speed)}get speed(){return this.playbackSpeed}set speed(a){let b=this.playbackSpeed!==a;this.playbackSpeed=a,b&&(this.time=this.currentTime/1e3)}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:a=b1,onPlay:b,startTime:c}=this.options;this.driver||(this.driver=a(a=>this.tick(a))),b&&b();let d=this.driver.now();null!==this.holdTime?this.startTime=d-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=d):this.startTime=null!=c?c:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var a;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!=(a=this.currentTime)?a:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:a}=this.options;a&&a()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}}let b5=new Set(["opacity","clipPath","filter","transform"]),b6=n(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),b7={anticipate:ar,backInOut:aq,circInOut:au};class b8 extends by{constructor(a){super(a);let{name:b,motionValue:c,element:d,keyframes:e}=this.options;this.resolver=new bu(e,(a,b)=>this.onKeyframesResolved(a,b),b,c,d),this.resolver.scheduleResolve()}initPlayback(a,b){var c;let{duration:d=300,times:e,ease:f,type:g,motionValue:h,name:i,startTime:j}=this.options;if(!h.owner||!h.owner.current)return!1;if("string"==typeof f&&x()&&f in b7&&(f=b7[f]),t((c=this.options).type)||"spring"===c.type||!function a(b){return!!("function"==typeof b&&x()||!b||"string"==typeof b&&(b in B||x())||v(b)||Array.isArray(b)&&b.every(a))}(c.ease)){let{onComplete:b,onUpdate:c,motionValue:h,element:i,...j}=this.options,k=function(a,b){let c=new b4({...b,keyframes:a,repeat:0,delay:0,isGenerator:!0}),d={done:!1,value:a[0]},e=[],f=0;for(;!d.done&&f<2e4;)e.push((d=c.sample(f)).value),f+=10;return{times:void 0,keyframes:e,duration:f-10,ease:"linear"}}(a,j);1===(a=k.keyframes).length&&(a[1]=a[0]),d=k.duration,e=k.times,f=k.ease,g="keyframes"}let k=function(a,b,c,{delay:d=0,duration:e=300,repeat:f=0,repeatType:g="loop",ease:h="easeInOut",times:i}={}){let j={[b]:c};i&&(j.offset=i);let k=function a(b,c){if(b)return"function"==typeof b&&x()?z(b,c):v(b)?A(b):Array.isArray(b)?b.map(b=>a(b,c)||B.easeOut):B[b]}(h,e);return Array.isArray(k)&&(j.easing=k),a.animate(j,{delay:d,duration:e,easing:Array.isArray(k)?"linear":k,fill:"both",iterations:f+1,direction:"reverse"===g?"alternate":"normal"})}(h.owner.current,i,a,{...this.options,duration:d,times:e,ease:f});return k.startTime=null!=j?j:this.calcStartTime(),this.pendingTimeline?(u(k,this.pendingTimeline),this.pendingTimeline=void 0):k.onfinish=()=>{let{onComplete:c}=this.options;h.set(bx(a,this.options,b)),c&&c(),this.cancel(),this.resolveFinishedPromise()},{animation:k,duration:d,times:e,type:g,ease:f,keyframes:a}}get duration(){let{resolved:a}=this;if(!a)return 0;let{duration:b}=a;return b/1e3}get time(){let{resolved:a}=this;if(!a)return 0;let{animation:b}=a;return(b.currentTime||0)/1e3}set time(a){let{resolved:b}=this;if(!b)return;let{animation:c}=b;c.currentTime=M(a)}get speed(){let{resolved:a}=this;if(!a)return 1;let{animation:b}=a;return b.playbackRate}set speed(a){let{resolved:b}=this;if(!b)return;let{animation:c}=b;c.playbackRate=a}get state(){let{resolved:a}=this;if(!a)return"idle";let{animation:b}=a;return b.playState}get startTime(){let{resolved:a}=this;if(!a)return null;let{animation:b}=a;return b.startTime}attachTimeline(a){if(this._resolved){let{resolved:b}=this;if(!b)return N;let{animation:c}=b;u(c,a)}else this.pendingTimeline=a;return N}play(){if(this.isStopped)return;let{resolved:a}=this;if(!a)return;let{animation:b}=a;"finished"===b.playState&&this.updateFinishedPromise(),b.play()}pause(){let{resolved:a}=this;if(!a)return;let{animation:b}=a;b.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:a}=this;if(!a)return;let{animation:b,keyframes:c,duration:d,type:e,ease:f,times:g}=a;if("idle"===b.playState||"finished"===b.playState)return;if(this.time){let{motionValue:a,onUpdate:b,onComplete:h,element:i,...j}=this.options,k=new b4({...j,keyframes:c,duration:d,type:e,ease:f,times:g,isGenerator:!0}),l=M(this.time);a.setWithVelocity(k.sample(l-10).value,k.sample(l).value,10)}let{onStop:h}=this.options;h&&h(),this.cancel()}complete(){let{resolved:a}=this;a&&a.animation.finish()}cancel(){let{resolved:a}=this;a&&a.animation.cancel()}static supports(a){let{motionValue:b,name:c,repeatDelay:d,repeatType:e,damping:f,type:g}=a;if(!b||!b.owner||!(b.owner.current instanceof HTMLElement))return!1;let{onUpdate:h,transformTemplate:i}=b.owner.getProps();return b6()&&c&&b5.has(c)&&!h&&!i&&!d&&"mirror"!==e&&0!==f&&"inertia"!==g}}let b9={type:"spring",stiffness:500,damping:25,restSpeed:10},ca={type:"keyframes",duration:.8},cb={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},cc=(a,b,c,d={},e,f)=>g=>{let h=r(d,a)||{},i=h.delay||d.delay||0,{elapsed:j=0}=d;j-=M(i);let k={keyframes:Array.isArray(c)?c:[null,c],ease:"easeOut",velocity:b.getVelocity(),...h,delay:-j,onUpdate:a=>{b.set(a),h.onUpdate&&h.onUpdate(a)},onComplete:()=>{g(),h.onComplete&&h.onComplete()},name:a,motionValue:b,element:f?void 0:e};!function({when:a,delay:b,delayChildren:c,staggerChildren:d,staggerDirection:e,repeat:f,repeatType:g,repeatDelay:h,from:i,elapsed:j,...k}){return!!Object.keys(k).length}(h)&&(k={...k,...((a,{keyframes:b})=>b.length>2?ca:P.has(a)?a.startsWith("scale")?{type:"spring",stiffness:550,damping:0===b[1]?2*Math.sqrt(550):30,restSpeed:10}:b9:cb)(a,k)}),k.duration&&(k.duration=M(k.duration)),k.repeatDelay&&(k.repeatDelay=M(k.repeatDelay)),void 0!==k.from&&(k.keyframes[0]=k.from);let l=!1;if(!1!==k.type&&(0!==k.duration||k.repeatDelay)||(k.duration=0,0===k.delay&&(l=!0)),(aj.current||S.skipAnimations)&&(l=!0,k.duration=0,k.delay=0),l&&!f&&void 0!==b.get()){let a=bx(k.keyframes,h);if(void 0!==a)return V.update(()=>{k.onUpdate(a),k.onComplete()}),new q([])}return!f&&b8.supports(k)?new b8(k):new b4(k)};function cd(a,b,{delay:c=0,transitionOverride:d,type:e}={}){var f;let{transition:g=a.getDefaultTransition(),transitionEnd:h,...i}=b;d&&(g=d);let j=[],l=e&&a.animationState&&a.animationState.getState()[e];for(let b in i){let d=a.getValue(b,null!=(f=a.latestValues[b])?f:null),e=i[b];if(void 0===e||l&&function({protectedKeys:a,needsAnimating:b},c){let d=a.hasOwnProperty(c)&&!0!==b[c];return b[c]=!1,d}(l,b))continue;let h={delay:c,...r(g||{},b)},k=!1;if(window.MotionHandoffAnimation){let c=a.props[ai];if(c){let a=window.MotionHandoffAnimation(c,b,V);null!==a&&(h.startTime=a,k=!0)}}ag(a,b),d.start(cc(b,d,e,a.shouldReduceMotion&&Q.has(b)?{type:!1}:h,a,k));let m=d.animation;m&&j.push(m)}return h&&Promise.all(j).then(()=>{V.update(()=>{h&&function(a,b){let{transitionEnd:c={},transition:d={},...e}=k(a,b)||{};for(let b in e={...e,...c}){let c=R(e[b]);a.hasValue(b)?a.getValue(b).set(c):a.addValue(b,ae(c))}}(a,h)})}),j}function ce(a,b,c={}){var d;let e=k(a,b,"exit"===c.type?null==(d=a.presenceContext)?void 0:d.custom:void 0),{transition:f=a.getDefaultTransition()||{}}=e||{};c.transitionOverride&&(f=c.transitionOverride);let g=e?()=>Promise.all(cd(a,e,c)):()=>Promise.resolve(),h=a.variantChildren&&a.variantChildren.size?(d=0)=>{let{delayChildren:e=0,staggerChildren:g,staggerDirection:h}=f;return function(a,b,c=0,d=0,e=1,f){let g=[],h=(a.variantChildren.size-1)*d,i=1===e?(a=0)=>a*d:(a=0)=>h-a*d;return Array.from(a.variantChildren).sort(cf).forEach((a,d)=>{a.notify("AnimationStart",b),g.push(ce(a,b,{...f,delay:c+i(d)}).then(()=>a.notify("AnimationComplete",b)))}),Promise.all(g)}(a,b,e+d,g,h,c)}:()=>Promise.resolve(),{when:i}=f;if(!i)return Promise.all([g(),h(c.delay)]);{let[a,b]="beforeChildren"===i?[g,h]:[h,g];return a().then(()=>b())}}function cf(a,b){return a.sortNodePosition(b)}let cg=m.length,ch=[...l].reverse(),ci=l.length;function cj(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ck(){return{animate:cj(!0),whileInView:cj(),whileHover:cj(),whileTap:cj(),whileDrag:cj(),whileFocus:cj(),exit:cj()}}class cl{constructor(a){this.isMounted=!1,this.node=a}update(){}}class cm extends cl{constructor(a){super(a),a.animationState||(a.animationState=function(a){let b=b=>Promise.all(b.map(({animation:b,options:c})=>(function(a,b,c={}){let d;if(a.notify("AnimationStart",b),Array.isArray(b))d=Promise.all(b.map(b=>ce(a,b,c)));else if("string"==typeof b)d=ce(a,b,c);else{let e="function"==typeof b?k(a,b,c.custom):b;d=Promise.all(cd(a,e,c))}return d.then(()=>{a.notify("AnimationComplete",b)})})(a,b,c))),c=ck(),d=!0,i=b=>(c,d)=>{var e;let f=k(a,d,"exit"===b?null==(e=a.presenceContext)?void 0:e.custom:void 0);if(f){let{transition:a,transitionEnd:b,...d}=f;c={...c,...d,...b}}return c};function j(j){let{props:k}=a,l=function a(b){if(!b)return;if(!b.isControllingVariants){let c=b.parent&&a(b.parent)||{};return void 0!==b.props.initial&&(c.initial=b.props.initial),c}let c={};for(let a=0;a<cg;a++){let d=m[a],e=b.props[d];(h(e)||!1===e)&&(c[d]=e)}return c}(a.parent)||{},n=[],o=new Set,p={},q=1/0;for(let b=0;b<ci;b++){var r,s;let m=ch[b],t=c[m],u=void 0!==k[m]?k[m]:l[m],v=h(u),w=m===j?t.isActive:null;!1===w&&(q=b);let x=u===l[m]&&u!==k[m]&&v;if(x&&d&&a.manuallyAnimateOnMount&&(x=!1),t.protectedKeys={...p},!t.isActive&&null===w||!u&&!t.prevProp||e(u)||"boolean"==typeof u)continue;let y=(r=t.prevProp,"string"==typeof(s=u)?s!==r:!!Array.isArray(s)&&!g(s,r)),z=y||m===j&&t.isActive&&!x&&v||b>q&&v,A=!1,B=Array.isArray(u)?u:[u],C=B.reduce(i(m),{});!1===w&&(C={});let{prevResolvedValues:D={}}=t,E={...D,...C},F=b=>{z=!0,o.has(b)&&(A=!0,o.delete(b)),t.needsAnimating[b]=!0;let c=a.getValue(b);c&&(c.liveStyle=!1)};for(let a in E){let b=C[a],c=D[a];if(!p.hasOwnProperty(a))(f(b)&&f(c)?g(b,c):b===c)?void 0!==b&&o.has(a)?F(a):t.protectedKeys[a]=!0:null!=b?F(a):o.add(a)}t.prevProp=u,t.prevResolvedValues=C,t.isActive&&(p={...p,...C}),d&&a.blockInitialAnimation&&(z=!1);let G=!(x&&y)||A;z&&G&&n.push(...B.map(a=>({animation:a,options:{type:m}})))}if(o.size){let b={};o.forEach(c=>{let d=a.getBaseTarget(c),e=a.getValue(c);e&&(e.liveStyle=!0),b[c]=null!=d?d:null}),n.push({animation:b})}let t=!!n.length;return d&&(!1===k.initial||k.initial===k.animate)&&!a.manuallyAnimateOnMount&&(t=!1),d=!1,t?b(n):Promise.resolve()}return{animateChanges:j,setActive:function(b,d){var e;if(c[b].isActive===d)return Promise.resolve();null==(e=a.variantChildren)||e.forEach(a=>{var c;return null==(c=a.animationState)?void 0:c.setActive(b,d)}),c[b].isActive=d;let f=j(b);for(let a in c)c[a].protectedKeys={};return f},setAnimateFunction:function(c){b=c(a)},getState:()=>c,reset:()=>{c=ck(),d=!0}}}(a))}updateAnimationControlsSubscription(){let{animate:a}=this.node.getProps();e(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:a}=this.node.getProps(),{animate:b}=this.node.prevProps||{};a!==b&&this.updateAnimationControlsSubscription()}unmount(){var a;this.node.animationState.reset(),null==(a=this.unmountControls)||a.call(this)}}let cn=0;class co extends cl{constructor(){super(...arguments),this.id=cn++}update(){if(!this.node.presenceContext)return;let{isPresent:a,onExitComplete:b}=this.node.presenceContext,{isPresent:c}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===c)return;let d=this.node.animationState.setActive("exit",!a);b&&!a&&d.then(()=>b(this.id))}mount(){let{register:a}=this.node.presenceContext||{};a&&(this.unmount=a(this.id))}unmount(){}}function cp(a,b,c,d={passive:!0}){return a.addEventListener(b,c,d),()=>a.removeEventListener(b,c)}function cq(a){return{point:{x:a.pageX,y:a.pageY}}}function cr(a,b,c,d){return cp(a,b,a=>G(a)&&c(a,cq(a)),d)}let cs=(a,b)=>Math.abs(a-b);class ct{constructor(a,b,{transformPagePoint:c,contextWindow:d,dragSnapToOrigin:e=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=cw(this.lastMoveEventInfo,this.history),b=null!==this.startEvent,c=function(a,b){return Math.sqrt(cs(a.x,b.x)**2+cs(a.y,b.y)**2)}(a.offset,{x:0,y:0})>=3;if(!b&&!c)return;let{point:d}=a,{timestamp:e}=X;this.history.push({...d,timestamp:e});let{onStart:f,onMove:g}=this.handlers;b||(f&&f(this.lastMoveEvent,a),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,a)},this.handlePointerMove=(a,b)=>{this.lastMoveEvent=a,this.lastMoveEventInfo=cu(b,this.transformPagePoint),V.update(this.updatePoint,!0)},this.handlePointerUp=(a,b)=>{this.end();let{onEnd:c,onSessionEnd:d,resumeAnimation:e}=this.handlers;if(this.dragSnapToOrigin&&e&&e(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let f=cw("pointercancel"===a.type?this.lastMoveEventInfo:cu(b,this.transformPagePoint),this.history);this.startEvent&&c&&c(a,f),d&&d(a,f)},!G(a))return;this.dragSnapToOrigin=e,this.handlers=b,this.transformPagePoint=c,this.contextWindow=d||window;let f=cu(cq(a),this.transformPagePoint),{point:g}=f,{timestamp:h}=X;this.history=[{...g,timestamp:h}];let{onSessionStart:i}=b;i&&i(a,cw(f,this.history)),this.removeListeners=bH(cr(this.contextWindow,"pointermove",this.handlePointerMove),cr(this.contextWindow,"pointerup",this.handlePointerUp),cr(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),W(this.updatePoint)}}function cu(a,b){return b?{point:b(a.point)}:a}function cv(a,b){return{x:a.x-b.x,y:a.y-b.y}}function cw({point:a},b){return{point:a,delta:cv(a,cx(b)),offset:cv(a,b[0]),velocity:function(a,b){if(a.length<2)return{x:0,y:0};let c=a.length-1,d=null,e=cx(a);for(;c>=0&&(d=a[c],!(e.timestamp-d.timestamp>M(.1)));)c--;if(!d)return{x:0,y:0};let f=(e.timestamp-d.timestamp)/1e3;if(0===f)return{x:0,y:0};let g={x:(e.x-d.x)/f,y:(e.y-d.y)/f};return g.x===1/0&&(g.x=0),g.y===1/0&&(g.y=0),g}(b,.1)}}function cx(a){return a[a.length-1]}function cy(a){return a&&"object"==typeof a&&Object.prototype.hasOwnProperty.call(a,"current")}function cz(a){return a.max-a.min}function cA(a,b,c,d=.5){a.origin=d,a.originPoint=bz(b.min,b.max,a.origin),a.scale=cz(c)/cz(b),a.translate=bz(c.min,c.max,a.origin)-a.originPoint,(a.scale>=.9999&&a.scale<=1.0001||isNaN(a.scale))&&(a.scale=1),(a.translate>=-.01&&a.translate<=.01||isNaN(a.translate))&&(a.translate=0)}function cB(a,b,c,d){cA(a.x,b.x,c.x,d?d.originX:void 0),cA(a.y,b.y,c.y,d?d.originY:void 0)}function cC(a,b,c){a.min=c.min+b.min,a.max=a.min+cz(b)}function cD(a,b,c){a.min=b.min-c.min,a.max=a.min+cz(b)}function cE(a,b,c){cD(a.x,b.x,c.x),cD(a.y,b.y,c.y)}function cF(a,b,c){return{min:void 0!==b?a.min+b:void 0,max:void 0!==c?a.max+c-(a.max-a.min):void 0}}function cG(a,b){let c=b.min-a.min,d=b.max-a.max;return b.max-b.min<a.max-a.min&&([c,d]=[d,c]),{min:c,max:d}}function cH(a,b,c){return{min:cI(a,b),max:cI(a,c)}}function cI(a,b){return"number"==typeof a?a:a[b]||0}let cJ=()=>({translate:0,scale:1,origin:0,originPoint:0}),cK=()=>({x:cJ(),y:cJ()}),cL=()=>({min:0,max:0}),cM=()=>({x:cL(),y:cL()});function cN(a){return[a("x"),a("y")]}function cO({top:a,left:b,right:c,bottom:d}){return{x:{min:b,max:c},y:{min:a,max:d}}}function cP(a){return void 0===a||1===a}function cQ({scale:a,scaleX:b,scaleY:c}){return!cP(a)||!cP(b)||!cP(c)}function cR(a){return cQ(a)||cS(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function cS(a){var b,c;return(b=a.x)&&"0%"!==b||(c=a.y)&&"0%"!==c}function cT(a,b,c,d,e){return void 0!==e&&(a=d+e*(a-d)),d+c*(a-d)+b}function cU(a,b=0,c=1,d,e){a.min=cT(a.min,b,c,d,e),a.max=cT(a.max,b,c,d,e)}function cV(a,{x:b,y:c}){cU(a.x,b.translate,b.scale,b.originPoint),cU(a.y,c.translate,c.scale,c.originPoint)}function cW(a,b){a.min=a.min+b,a.max=a.max+b}function cX(a,b,c,d,e=.5){let f=bz(a.min,a.max,e);cU(a,b,c,f,d)}function cY(a,b){cX(a.x,b.x,b.scaleX,b.scale,b.originX),cX(a.y,b.y,b.scaleY,b.scale,b.originY)}function cZ(a,b){return cO(function(a,b){if(!b)return a;let c=b({x:a.left,y:a.top}),d=b({x:a.right,y:a.bottom});return{top:c.y,left:c.x,bottom:d.y,right:d.x}}(a.getBoundingClientRect(),b))}let c$=({current:a})=>a?a.ownerDocument.defaultView:null,c_=new WeakMap;class c0{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=cM(),this.visualElement=a}start(a,{snapToCursor:b=!1}={}){let{presenceContext:c}=this.visualElement;if(c&&!1===c.isPresent)return;let d=a=>{let{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),b&&this.snapToCursor(cq(a).point)},e=(a,b)=>{let{drag:c,dragPropagation:d,onDragStart:e}=this.getProps();if(c&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(a){if("x"===a||"y"===a)if(C[a])return null;else return C[a]=!0,()=>{C[a]=!1};return C.x||C.y?null:(C.x=C.y=!0,()=>{C.x=C.y=!1})}(c),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),cN(a=>{let b=this.getAxisMotionValue(a).get()||0;if(aK.test(b)){let{projection:c}=this.visualElement;if(c&&c.layout){let d=c.layout.layoutBox[a];d&&(b=cz(d)*(parseFloat(b)/100))}}this.originPoint[a]=b}),e&&V.postRender(()=>e(a,b)),ag(this.visualElement,"transform");let{animationState:f}=this.visualElement;f&&f.setActive("whileDrag",!0)},f=(a,b)=>{let{dragPropagation:c,dragDirectionLock:d,onDirectionLock:e,onDrag:f}=this.getProps();if(!c&&!this.openDragLock)return;let{offset:g}=b;if(d&&null===this.currentDirection){this.currentDirection=function(a,b=10){let c=null;return Math.abs(a.y)>b?c="y":Math.abs(a.x)>b&&(c="x"),c}(g),null!==this.currentDirection&&e&&e(this.currentDirection);return}this.updateAxis("x",b.point,g),this.updateAxis("y",b.point,g),this.visualElement.render(),f&&f(a,b)},g=(a,b)=>this.stop(a,b),h=()=>cN(a=>{var b;return"paused"===this.getAnimationState(a)&&(null==(b=this.getAxisMotionValue(a).animation)?void 0:b.play())}),{dragSnapToOrigin:i}=this.getProps();this.panSession=new ct(a,{onSessionStart:d,onStart:e,onMove:f,onSessionEnd:g,resumeAnimation:h},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:c$(this.visualElement)})}stop(a,b){let c=this.isDragging;if(this.cancel(),!c)return;let{velocity:d}=b;this.startAnimation(d);let{onDragEnd:e}=this.getProps();e&&V.postRender(()=>e(a,b))}cancel(){this.isDragging=!1;let{projection:a,animationState:b}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:c}=this.getProps();!c&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),b&&b.setActive("whileDrag",!1)}updateAxis(a,b,c){let{drag:d}=this.getProps();if(!c||!c1(a,d,this.currentDirection))return;let e=this.getAxisMotionValue(a),f=this.originPoint[a]+c[a];this.constraints&&this.constraints[a]&&(f=function(a,{min:b,max:c},d){return void 0!==b&&a<b?a=d?bz(b,a,d.min):Math.max(a,b):void 0!==c&&a>c&&(a=d?bz(c,a,d.max):Math.min(a,c)),a}(f,this.constraints[a],this.elastic[a])),e.set(f)}resolveConstraints(){var a;let{dragConstraints:b,dragElastic:c}=this.getProps(),d=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(a=this.visualElement.projection)?void 0:a.layout,e=this.constraints;b&&cy(b)?this.constraints||(this.constraints=this.resolveRefConstraints()):b&&d?this.constraints=function(a,{top:b,left:c,bottom:d,right:e}){return{x:cF(a.x,c,e),y:cF(a.y,b,d)}}(d.layoutBox,b):this.constraints=!1,this.elastic=function(a=.35){return!1===a?a=0:!0===a&&(a=.35),{x:cH(a,"left","right"),y:cH(a,"top","bottom")}}(c),e!==this.constraints&&d&&this.constraints&&!this.hasMutatedConstraints&&cN(a=>{!1!==this.constraints&&this.getAxisMotionValue(a)&&(this.constraints[a]=function(a,b){let c={};return void 0!==b.min&&(c.min=b.min-a.min),void 0!==b.max&&(c.max=b.max-a.min),c}(d.layoutBox[a],this.constraints[a]))})}resolveRefConstraints(){var a;let{dragConstraints:b,onMeasureDragConstraints:c}=this.getProps();if(!b||!cy(b))return!1;let d=b.current;N(null!==d,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:e}=this.visualElement;if(!e||!e.layout)return!1;let f=function(a,b,c){let d=cZ(a,c),{scroll:e}=b;return e&&(cW(d.x,e.offset.x),cW(d.y,e.offset.y)),d}(d,e.root,this.visualElement.getTransformPagePoint()),g=(a=e.layout.layoutBox,{x:cG(a.x,f.x),y:cG(a.y,f.y)});if(c){let a=c(function({x:a,y:b}){return{top:b.min,right:a.max,bottom:b.max,left:a.min}}(g));this.hasMutatedConstraints=!!a,a&&(g=cO(a))}return g}startAnimation(a){let{drag:b,dragMomentum:c,dragElastic:d,dragTransition:e,dragSnapToOrigin:f,onDragTransitionEnd:g}=this.getProps(),h=this.constraints||{};return Promise.all(cN(g=>{if(!c1(g,b,this.currentDirection))return;let i=h&&h[g]||{};f&&(i={min:0,max:0});let j={type:"inertia",velocity:c?a[g]:0,bounceStiffness:d?200:1e6,bounceDamping:d?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...e,...i};return this.startAxisValueAnimation(g,j)})).then(g)}startAxisValueAnimation(a,b){let c=this.getAxisMotionValue(a);return ag(this.visualElement,a),c.start(cc(a,c,0,b,this.visualElement,!1))}stopAnimation(){cN(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){cN(a=>{var b;return null==(b=this.getAxisMotionValue(a).animation)?void 0:b.pause()})}getAnimationState(a){var b;return null==(b=this.getAxisMotionValue(a).animation)?void 0:b.state}getAxisMotionValue(a){let b=`_drag${a.toUpperCase()}`,c=this.visualElement.getProps();return c[b]||this.visualElement.getValue(a,(c.initial?c.initial[a]:void 0)||0)}snapToCursor(a){cN(b=>{let{drag:c}=this.getProps();if(!c1(b,c,this.currentDirection))return;let{projection:d}=this.visualElement,e=this.getAxisMotionValue(b);if(d&&d.layout){let{min:c,max:f}=d.layout.layoutBox[b];e.set(a[b]-bz(c,f,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:a,dragConstraints:b}=this.getProps(),{projection:c}=this.visualElement;if(!cy(b)||!c||!this.constraints)return;this.stopAnimation();let d={x:0,y:0};cN(a=>{let b=this.getAxisMotionValue(a);if(b&&!1!==this.constraints){let c=b.get();d[a]=function(a,b){let c=.5,d=cz(a),e=cz(b);return e>d?c=y(b.min,b.max-d,a.min):d>e&&(c=y(a.min,a.max-e,b.min)),aw(0,1,c)}({min:c,max:c},this.constraints[a])}});let{transformTemplate:e}=this.visualElement.getProps();this.visualElement.current.style.transform=e?e({},""):"none",c.root&&c.root.updateScroll(),c.updateLayout(),this.resolveConstraints(),cN(b=>{if(!c1(b,a,null))return;let c=this.getAxisMotionValue(b),{min:e,max:f}=this.constraints[b];c.set(bz(e,f,d[b]))})}addListeners(){if(!this.visualElement.current)return;c_.set(this.visualElement,this);let a=cr(this.visualElement.current,"pointerdown",a=>{let{drag:b,dragListener:c=!0}=this.getProps();b&&c&&this.start(a)}),b=()=>{let{dragConstraints:a}=this.getProps();cy(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",b);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),V.read(b);let e=cp(window,"resize",()=>this.scalePositionWithinConstraints()),f=c.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b})=>{this.isDragging&&b&&(cN(b=>{let c=this.getAxisMotionValue(b);c&&(this.originPoint[b]+=a[b].translate,c.set(c.get()+a[b].translate))}),this.visualElement.render())});return()=>{e(),a(),d(),f&&f()}}getProps(){let a=this.visualElement.getProps(),{drag:b=!1,dragDirectionLock:c=!1,dragPropagation:d=!1,dragConstraints:e=!1,dragElastic:f=.35,dragMomentum:g=!0}=a;return{...a,drag:b,dragDirectionLock:c,dragPropagation:d,dragConstraints:e,dragElastic:f,dragMomentum:g}}}function c1(a,b,c){return(!0===b||b===a)&&(null===c||c===a)}class c2 extends cl{constructor(a){super(a),this.removeGroupControls=N,this.removeListeners=N,this.controls=new c0(a)}mount(){let{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||N}unmount(){this.removeGroupControls(),this.removeListeners()}}let c3=a=>(b,c)=>{a&&V.postRender(()=>a(b,c))};class c4 extends cl{constructor(){super(...arguments),this.removePointerDownListener=N}onPointerDown(a){this.session=new ct(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:c$(this.node)})}createPanHandlers(){let{onPanSessionStart:a,onPanStart:b,onPan:c,onPanEnd:d}=this.node.getProps();return{onSessionStart:c3(a),onStart:c3(b),onMove:c,onEnd:(a,b)=>{delete this.session,d&&V.postRender(()=>d(a,b))}}}mount(){this.removePointerDownListener=cr(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var c5,c6,c7=c(60687),c8=c(43210),c9=c(86044),da=c(12157);let db=(0,c8.createContext)({}),dc={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function dd(a,b){return b.max===b.min?0:a/(b.max-b.min)*100}let de={correct:(a,b)=>{if(!b.target)return a;if("string"==typeof a)if(!aL.test(a))return a;else a=parseFloat(a);let c=dd(a,b.target.x),d=dd(a,b.target.y);return`${c}% ${d}%`}},df={},{schedule:dg,cancel:dh}=U(queueMicrotask,!1);class di extends c8.Component{componentDidMount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c,layoutId:d}=this.props,{projection:e}=a;Object.assign(df,dk),e&&(b.group&&b.group.add(e),c&&c.register&&d&&c.register(e),e.root.didUpdate(),e.addEventListener("animationComplete",()=>{this.safeToRemove()}),e.setOptions({...e.options,onExitComplete:()=>this.safeToRemove()})),dc.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){let{layoutDependency:b,visualElement:c,drag:d,isPresent:e}=this.props,f=c.projection;return f&&(f.isPresent=e,d||a.layoutDependency!==b||void 0===b?f.willUpdate():this.safeToRemove(),a.isPresent!==e&&(e?f.promote():f.relegate()||V.postRender(()=>{let a=f.getStack();a&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),dg.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c}=this.props,{projection:d}=a;d&&(d.scheduleCheckAfterUnmount(),b&&b.group&&b.group.remove(d),c&&c.deregister&&c.deregister(d))}safeToRemove(){let{safeToRemove:a}=this.props;a&&a()}render(){return null}}function dj(a){let[b,c]=(0,c9.xQ)(),d=(0,c8.useContext)(da.L);return(0,c7.jsx)(di,{...a,layoutGroup:d,switchLayoutGroup:(0,c8.useContext)(db),isPresent:b,safeToRemove:c})}let dk={borderRadius:{...de,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:de,borderTopRightRadius:de,borderBottomLeftRadius:de,borderBottomRightRadius:de,boxShadow:{correct:(a,{treeScale:b,projectionDelta:c})=>{let d=aZ.parse(a);if(d.length>5)return a;let e=aZ.createTransformer(a),f=+("number"!=typeof d[0]),g=c.x.scale*b.x,h=c.y.scale*b.y;d[0+f]/=g,d[1+f]/=h;let i=bz(g,h,.5);return"number"==typeof d[2+f]&&(d[2+f]/=i),"number"==typeof d[3+f]&&(d[3+f]/=i),e(d)}}},dl=(a,b)=>a.depth-b.depth;class dm{constructor(){this.children=[],this.isDirty=!1}add(a){_(this.children,a),this.isDirty=!0}remove(a){aa(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort(dl),this.isDirty=!1,this.children.forEach(a)}}function dn(a){let b=af(a)?a.get():a;return b&&"object"==typeof b&&b.mix&&b.toValue?b.toValue():b}let dp=["TopLeft","TopRight","BottomLeft","BottomRight"],dq=dp.length,dr=a=>"string"==typeof a?parseFloat(a):a,ds=a=>"number"==typeof a||aL.test(a);function dt(a,b){return void 0!==a[b]?a[b]:a.borderRadius}let du=dw(0,.5,at),dv=dw(.5,.95,N);function dw(a,b,c){return d=>d<a?0:d>b?1:c(y(a,b,d))}function dx(a,b){a.min=b.min,a.max=b.max}function dy(a,b){dx(a.x,b.x),dx(a.y,b.y)}function dz(a,b){a.translate=b.translate,a.scale=b.scale,a.originPoint=b.originPoint,a.origin=b.origin}function dA(a,b,c,d,e){return a-=b,a=d+1/c*(a-d),void 0!==e&&(a=d+1/e*(a-d)),a}function dB(a,b,[c,d,e],f,g){!function(a,b=0,c=1,d=.5,e,f=a,g=a){if(aK.test(b)&&(b=parseFloat(b),b=bz(g.min,g.max,b/100)-g.min),"number"!=typeof b)return;let h=bz(f.min,f.max,d);a===f&&(h-=b),a.min=dA(a.min,b,c,h,e),a.max=dA(a.max,b,c,h,e)}(a,b[c],b[d],b[e],b.scale,f,g)}let dC=["x","scaleX","originX"],dD=["y","scaleY","originY"];function dE(a,b,c,d){dB(a.x,b,dC,c?c.x:void 0,d?d.x:void 0),dB(a.y,b,dD,c?c.y:void 0,d?d.y:void 0)}function dF(a){return 0===a.translate&&1===a.scale}function dG(a){return dF(a.x)&&dF(a.y)}function dH(a,b){return a.min===b.min&&a.max===b.max}function dI(a,b){return Math.round(a.min)===Math.round(b.min)&&Math.round(a.max)===Math.round(b.max)}function dJ(a,b){return dI(a.x,b.x)&&dI(a.y,b.y)}function dK(a){return cz(a.x)/cz(a.y)}function dL(a,b){return a.translate===b.translate&&a.scale===b.scale&&a.originPoint===b.originPoint}class dM{constructor(){this.members=[]}add(a){_(this.members,a),a.scheduleRender()}remove(a){if(aa(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){let a=this.members[this.members.length-1];a&&this.promote(a)}}relegate(a){let b,c=this.members.findIndex(b=>a===b);if(0===c)return!1;for(let a=c;a>=0;a--){let c=this.members[a];if(!1!==c.isPresent){b=c;break}}return!!b&&(this.promote(b),!0)}promote(a,b){let c=this.lead;if(a!==c&&(this.prevLead=c,this.lead=a,a.show(),c)){c.instance&&c.scheduleRender(),a.scheduleRender(),a.resumeFrom=c,b&&(a.resumeFrom.preserveOpacity=!0),c.snapshot&&(a.snapshot=c.snapshot,a.snapshot.latestValues=c.animationValues||c.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);let{crossfade:d}=a.options;!1===d&&c.hide()}}exitAnimationComplete(){this.members.forEach(a=>{let{options:b,resumingFrom:c}=a;b.onExitComplete&&b.onExitComplete(),c&&c.options.onExitComplete&&c.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let dN={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},dO="undefined"!=typeof window&&void 0!==window.MotionDebug,dP=["","X","Y","Z"],dQ={visibility:"hidden"},dR=0;function dS(a,b,c,d){let{latestValues:e}=b;e[a]&&(c[a]=e[a],b.setStaticValue(a,0),d&&(d[a]=0))}function dT({attachResizeListener:a,defaultParent:b,measureScroll:c,checkIsScrollRoot:d,resetTransform:e}){return class{constructor(a={},c=null==b?void 0:b()){this.id=dR++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,dO&&(dN.totalNodes=dN.resolvedTargetDeltas=dN.recalculatedProjection=0),this.nodes.forEach(dW),this.nodes.forEach(d1),this.nodes.forEach(d2),this.nodes.forEach(dX),dO&&window.MotionDebug.record(dN)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=a,this.root=c?c.root||c:this,this.path=c?[...c.path,c]:[],this.parent=c,this.depth=c?c.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new dm)}addEventListener(a,b){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new ab),this.eventHandlers.get(a).add(b)}notifyListeners(a,...b){let c=this.eventHandlers.get(a);c&&c.notify(...b)}hasListeners(a){return this.eventHandlers.has(a)}mount(b,c=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=b instanceof SVGElement&&"svg"!==b.tagName,this.instance=b;let{layoutId:d,layout:e,visualElement:f}=this.options;if(f&&!f.current&&f.mount(b),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),c&&(e||d)&&(this.isLayoutDirty=!0),a){let c,d=()=>this.root.updateBlockedByResize=!1;a(b,()=>{this.root.updateBlockedByResize=!0,c&&c(),c=function(a,b){let c=$.now(),d=({timestamp:b})=>{let e=b-c;e>=250&&(W(d),a(e-250))};return V.read(d,!0),()=>W(d)}(d,250),dc.hasAnimatedSinceResize&&(dc.hasAnimatedSinceResize=!1,this.nodes.forEach(d0))})}d&&this.root.registerSharedNode(d,this),!1!==this.options.animate&&f&&(d||e)&&this.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b,hasRelativeTargetChanged:c,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let e=this.options.transition||f.getDefaultTransition()||d8,{onLayoutAnimationStart:g,onLayoutAnimationComplete:h}=f.getProps(),i=!this.targetLayout||!dJ(this.targetLayout,d)||c,j=!b&&c;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||j||b&&(i||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(a,j);let b={...r(e,"layout"),onPlay:g,onComplete:h};(f.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b)}else b||d0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,W(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(d3),this.animationId++)}getTransformTemplate(){let{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function a(b){if(b.hasCheckedOptimisedAppear=!0,b.root===b)return;let{visualElement:c}=b.options;if(!c)return;let d=c.props[ai];if(window.MotionHasOptimisedAnimation(d,"transform")){let{layout:a,layoutId:c}=b.options;window.MotionCancelOptimisedAnimation(d,"transform",V,!(a||c))}let{parent:e}=b;e&&!e.hasCheckedOptimisedAppear&&a(e)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let a=0;a<this.path.length;a++){let b=this.path[a];b.shouldResetTransform=!0,b.updateScroll("snapshot"),b.options.layoutRoot&&b.willUpdate(!1)}let{layoutId:b,layout:c}=this.options;if(void 0===b&&!c)return;let d=this.getTransformTemplate();this.prevTransformTemplateValue=d?d(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(dZ);return}this.isUpdating||this.nodes.forEach(d$),this.isUpdating=!1,this.nodes.forEach(d_),this.nodes.forEach(dU),this.nodes.forEach(dV),this.clearAllSnapshots();let a=$.now();X.delta=aw(0,1e3/60,a-X.timestamp),X.timestamp=a,X.isProcessing=!0,Y.update.process(X),Y.preRender.process(X),Y.render.process(X),X.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,dg.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(dY),this.sharedNodes.forEach(d4)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,V.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){V.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();let a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=cM(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:b}=this.options;b&&b.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let b=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(b=!1),b){let b=d(this.instance);this.scroll={animationId:this.root.animationId,phase:a,isRoot:b,offset:c(this.instance),wasRoot:this.scroll?this.scroll.isRoot:b}}}resetTransform(){if(!e)return;let a=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,b=this.projectionDelta&&!dG(this.projectionDelta),c=this.getTransformTemplate(),d=c?c(this.latestValues,""):void 0,f=d!==this.prevTransformTemplateValue;a&&(b||cR(this.latestValues)||f)&&(e(this.instance,d),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){var b;let c=this.measurePageBox(),d=this.removeElementScroll(c);return a&&(d=this.removeTransform(d)),eb((b=d).x),eb(b.y),{animationId:this.root.animationId,measuredBox:c,layoutBox:d,latestValues:{},source:this.id}}measurePageBox(){var a;let{visualElement:b}=this.options;if(!b)return cM();let c=b.measureViewportBox();if(!((null==(a=this.scroll)?void 0:a.wasRoot)||this.path.some(ed))){let{scroll:a}=this.root;a&&(cW(c.x,a.offset.x),cW(c.y,a.offset.y))}return c}removeElementScroll(a){var b;let c=cM();if(dy(c,a),null==(b=this.scroll)?void 0:b.wasRoot)return c;for(let b=0;b<this.path.length;b++){let d=this.path[b],{scroll:e,options:f}=d;d!==this.root&&e&&f.layoutScroll&&(e.wasRoot&&dy(c,a),cW(c.x,e.offset.x),cW(c.y,e.offset.y))}return c}applyTransform(a,b=!1){let c=cM();dy(c,a);for(let a=0;a<this.path.length;a++){let d=this.path[a];!b&&d.options.layoutScroll&&d.scroll&&d!==d.root&&cY(c,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),cR(d.latestValues)&&cY(c,d.latestValues)}return cR(this.latestValues)&&cY(c,this.latestValues),c}removeTransform(a){let b=cM();dy(b,a);for(let a=0;a<this.path.length;a++){let c=this.path[a];if(!c.instance||!cR(c.latestValues))continue;cQ(c.latestValues)&&c.updateSnapshot();let d=cM();dy(d,c.measurePageBox()),dE(b,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,d)}return cR(this.latestValues)&&dE(b,this.latestValues),b}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:void 0===a.crossfade||a.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==X.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(a=!1){var b,c,d,e;let f=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=f.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=f.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=f.isSharedProjectionDirty);let g=!!this.resumingFrom||this!==f;if(!(a||g&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(b=this.parent)?void 0:b.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:h,layoutId:i}=this.options;if(this.layout&&(h||i)){if(this.resolvedRelativeTargetAt=X.timestamp,!this.targetDelta&&!this.relativeTarget){let a=this.getClosestProjectingParent();a&&a.layout&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=cM(),this.relativeTargetOrigin=cM(),cE(this.relativeTargetOrigin,this.layout.layoutBox,a.layout.layoutBox),dy(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=cM(),this.targetWithTransforms=cM()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),c=this.target,d=this.relativeTarget,e=this.relativeParent.target,cC(c.x,d.x,e.x),cC(c.y,d.y,e.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):dy(this.target,this.layout.layoutBox),cV(this.target,this.targetDelta)):dy(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let a=this.getClosestProjectingParent();a&&!!a.resumingFrom==!!this.resumingFrom&&!a.options.layoutScroll&&a.target&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=cM(),this.relativeTargetOrigin=cM(),cE(this.relativeTargetOrigin,this.target,a.target),dy(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}dO&&dN.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||cQ(this.parent.latestValues)||cS(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var a;let b=this.getLead(),c=!!this.resumingFrom||this!==b,d=!0;if((this.isProjectionDirty||(null==(a=this.parent)?void 0:a.isProjectionDirty))&&(d=!1),c&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(d=!1),this.resolvedRelativeTargetAt===X.timestamp&&(d=!1),d)return;let{layout:e,layoutId:f}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(e||f))return;dy(this.layoutCorrected,this.layout.layoutBox);let g=this.treeScale.x,h=this.treeScale.y;!function(a,b,c,d=!1){let e,f,g=c.length;if(g){b.x=b.y=1;for(let h=0;h<g;h++){f=(e=c[h]).projectionDelta;let{visualElement:g}=e.options;(!g||!g.props.style||"contents"!==g.props.style.display)&&(d&&e.options.layoutScroll&&e.scroll&&e!==e.root&&cY(a,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),f&&(b.x*=f.x.scale,b.y*=f.y.scale,cV(a,f)),d&&cR(e.latestValues)&&cY(a,e.latestValues))}b.x<1.0000000000001&&b.x>.999999999999&&(b.x=1),b.y<1.0000000000001&&b.y>.999999999999&&(b.y=1)}}(this.layoutCorrected,this.treeScale,this.path,c),b.layout&&!b.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(b.target=b.layout.layoutBox,b.targetWithTransforms=cM());let{target:i}=b;if(!i){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(dz(this.prevProjectionDelta.x,this.projectionDelta.x),dz(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),cB(this.projectionDelta,this.layoutCorrected,i,this.latestValues),this.treeScale.x===g&&this.treeScale.y===h&&dL(this.projectionDelta.x,this.prevProjectionDelta.x)&&dL(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",i)),dO&&dN.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){var b;if(null==(b=this.options.visualElement)||b.scheduleRender(),a){let a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=cK(),this.projectionDelta=cK(),this.projectionDeltaWithTransform=cK()}setAnimationOrigin(a,b=!1){let c,d=this.snapshot,e=d?d.latestValues:{},f={...this.latestValues},g=cK();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!b;let h=cM(),i=(d?d.source:void 0)!==(this.layout?this.layout.source:void 0),j=this.getStack(),k=!j||j.members.length<=1,l=!!(i&&!k&&!0===this.options.crossfade&&!this.path.some(d7));this.animationProgress=0,this.mixTargetDelta=b=>{let d=b/1e3;if(d5(g.x,a.x,d),d5(g.y,a.y,d),this.setTargetDelta(g),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var j,m,n,o,p,q;cE(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),n=this.relativeTarget,o=this.relativeTargetOrigin,p=h,q=d,d6(n.x,o.x,p.x,q),d6(n.y,o.y,p.y,q),c&&(j=this.relativeTarget,m=c,dH(j.x,m.x)&&dH(j.y,m.y))&&(this.isProjectionDirty=!1),c||(c=cM()),dy(c,this.relativeTarget)}i&&(this.animationValues=f,function(a,b,c,d,e,f){e?(a.opacity=bz(0,void 0!==c.opacity?c.opacity:1,du(d)),a.opacityExit=bz(void 0!==b.opacity?b.opacity:1,0,dv(d))):f&&(a.opacity=bz(void 0!==b.opacity?b.opacity:1,void 0!==c.opacity?c.opacity:1,d));for(let e=0;e<dq;e++){let f=`border${dp[e]}Radius`,g=dt(b,f),h=dt(c,f);(void 0!==g||void 0!==h)&&(g||(g=0),h||(h=0),0===g||0===h||ds(g)===ds(h)?(a[f]=Math.max(bz(dr(g),dr(h),d),0),(aK.test(h)||aK.test(g))&&(a[f]+="%")):a[f]=h)}(b.rotate||c.rotate)&&(a.rotate=bz(b.rotate||0,c.rotate||0,d))}(f,e,this.latestValues,d,l,k)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=d},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(a){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(W(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=V.update(()=>{dc.hasAnimatedSinceResize=!0,this.currentAnimation=function(a,b,c){let d=af(0)?0:ae(a);return d.start(cc("",d,1e3,c)),d.animation}(0,0,{...a,onUpdate:b=>{this.mixTargetDelta(b),a.onUpdate&&a.onUpdate(b)},onComplete:()=>{a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let a=this.getLead(),{targetWithTransforms:b,target:c,layout:d,latestValues:e}=a;if(b&&c&&d){if(this!==a&&this.layout&&d&&ec(this.options.animationType,this.layout.layoutBox,d.layoutBox)){c=this.target||cM();let b=cz(this.layout.layoutBox.x);c.x.min=a.target.x.min,c.x.max=c.x.min+b;let d=cz(this.layout.layoutBox.y);c.y.min=a.target.y.min,c.y.max=c.y.min+d}dy(b,c),cY(b,e),cB(this.projectionDeltaWithTransform,this.layoutCorrected,b,e)}}registerSharedNode(a,b){this.sharedNodes.has(a)||this.sharedNodes.set(a,new dM),this.sharedNodes.get(a).add(b);let c=b.options.initialPromotionConfig;b.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(b):void 0})}isLead(){let a=this.getStack();return!a||a.lead===this}getLead(){var a;let{layoutId:b}=this.options;return b&&(null==(a=this.getStack())?void 0:a.lead)||this}getPrevLead(){var a;let{layoutId:b}=this.options;return b?null==(a=this.getStack())?void 0:a.prevLead:void 0}getStack(){let{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:b,preserveFollowOpacity:c}={}){let d=this.getStack();d&&d.promote(this,c),a&&(this.projectionDelta=void 0,this.needsReset=!0),b&&this.setOptions({transition:b})}relegate(){let a=this.getStack();return!!a&&a.relegate(this)}resetSkewAndRotation(){let{visualElement:a}=this.options;if(!a)return;let b=!1,{latestValues:c}=a;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(b=!0),!b)return;let d={};c.z&&dS("z",a,d,this.animationValues);for(let b=0;b<dP.length;b++)dS(`rotate${dP[b]}`,a,d,this.animationValues),dS(`skew${dP[b]}`,a,d,this.animationValues);for(let b in a.render(),d)a.setStaticValue(b,d[b]),this.animationValues&&(this.animationValues[b]=d[b]);a.scheduleRender()}getProjectionStyles(a){var b,c;if(!this.instance||this.isSVG)return;if(!this.isVisible)return dQ;let d={visibility:""},e=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,d.opacity="",d.pointerEvents=dn(null==a?void 0:a.pointerEvents)||"",d.transform=e?e(this.latestValues,""):"none",d;let f=this.getLead();if(!this.projectionDelta||!this.layout||!f.target){let b={};return this.options.layoutId&&(b.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,b.pointerEvents=dn(null==a?void 0:a.pointerEvents)||""),this.hasProjected&&!cR(this.latestValues)&&(b.transform=e?e({},""):"none",this.hasProjected=!1),b}let g=f.animationValues||f.latestValues;this.applyTransformsToTarget(),d.transform=function(a,b,c){let d="",e=a.x.translate/b.x,f=a.y.translate/b.y,g=(null==c?void 0:c.z)||0;if((e||f||g)&&(d=`translate3d(${e}px, ${f}px, ${g}px) `),(1!==b.x||1!==b.y)&&(d+=`scale(${1/b.x}, ${1/b.y}) `),c){let{transformPerspective:a,rotate:b,rotateX:e,rotateY:f,skewX:g,skewY:h}=c;a&&(d=`perspective(${a}px) ${d}`),b&&(d+=`rotate(${b}deg) `),e&&(d+=`rotateX(${e}deg) `),f&&(d+=`rotateY(${f}deg) `),g&&(d+=`skewX(${g}deg) `),h&&(d+=`skewY(${h}deg) `)}let h=a.x.scale*b.x,i=a.y.scale*b.y;return(1!==h||1!==i)&&(d+=`scale(${h}, ${i})`),d||"none"}(this.projectionDeltaWithTransform,this.treeScale,g),e&&(d.transform=e(g,d.transform));let{x:h,y:i}=this.projectionDelta;for(let a in d.transformOrigin=`${100*h.origin}% ${100*i.origin}% 0`,f.animationValues?d.opacity=f===this?null!=(c=null!=(b=g.opacity)?b:this.latestValues.opacity)?c:1:this.preserveOpacity?this.latestValues.opacity:g.opacityExit:d.opacity=f===this?void 0!==g.opacity?g.opacity:"":void 0!==g.opacityExit?g.opacityExit:0,df){if(void 0===g[a])continue;let{correct:b,applyTo:c}=df[a],e="none"===d.transform?g[a]:b(g[a],f);if(c){let a=c.length;for(let b=0;b<a;b++)d[c[b]]=e}else d[a]=e}return this.options.layoutId&&(d.pointerEvents=f===this?dn(null==a?void 0:a.pointerEvents)||"":"none"),d}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>{var b;return null==(b=a.currentAnimation)?void 0:b.stop()}),this.root.nodes.forEach(dZ),this.root.sharedNodes.clear()}}}function dU(a){a.updateLayout()}function dV(a){var b;let c=(null==(b=a.resumeFrom)?void 0:b.snapshot)||a.snapshot;if(a.isLead()&&a.layout&&c&&a.hasListeners("didUpdate")){let{layoutBox:b,measuredBox:d}=a.layout,{animationType:e}=a.options,f=c.source!==a.layout.source;"size"===e?cN(a=>{let d=f?c.measuredBox[a]:c.layoutBox[a],e=cz(d);d.min=b[a].min,d.max=d.min+e}):ec(e,c.layoutBox,b)&&cN(d=>{let e=f?c.measuredBox[d]:c.layoutBox[d],g=cz(b[d]);e.max=e.min+g,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[d].max=a.relativeTarget[d].min+g)});let g=cK();cB(g,b,c.layoutBox);let h=cK();f?cB(h,a.applyTransform(d,!0),c.measuredBox):cB(h,b,c.layoutBox);let i=!dG(g),j=!1;if(!a.resumeFrom){let d=a.getClosestProjectingParent();if(d&&!d.resumeFrom){let{snapshot:e,layout:f}=d;if(e&&f){let g=cM();cE(g,c.layoutBox,e.layoutBox);let h=cM();cE(h,b,f.layoutBox),dJ(g,h)||(j=!0),d.options.layoutRoot&&(a.relativeTarget=h,a.relativeTargetOrigin=g,a.relativeParent=d)}}}a.notifyListeners("didUpdate",{layout:b,snapshot:c,delta:h,layoutDelta:g,hasLayoutChanged:i,hasRelativeTargetChanged:j})}else if(a.isLead()){let{onExitComplete:b}=a.options;b&&b()}a.options.transition=void 0}function dW(a){dO&&dN.totalNodes++,a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function dX(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function dY(a){a.clearSnapshot()}function dZ(a){a.clearMeasurements()}function d$(a){a.isLayoutDirty=!1}function d_(a){let{visualElement:b}=a.options;b&&b.getProps().onBeforeLayoutMeasure&&b.notify("BeforeLayoutMeasure"),a.resetTransform()}function d0(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function d1(a){a.resolveTargetDelta()}function d2(a){a.calcProjection()}function d3(a){a.resetSkewAndRotation()}function d4(a){a.removeLeadSnapshot()}function d5(a,b,c){a.translate=bz(b.translate,0,c),a.scale=bz(b.scale,1,c),a.origin=b.origin,a.originPoint=b.originPoint}function d6(a,b,c,d){a.min=bz(b.min,c.min,d),a.max=bz(b.max,c.max,d)}function d7(a){return a.animationValues&&void 0!==a.animationValues.opacityExit}let d8={duration:.45,ease:[.4,0,.1,1]},d9=a=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),ea=d9("applewebkit/")&&!d9("chrome/")?Math.round:N;function eb(a){a.min=ea(a.min),a.max=ea(a.max)}function ec(a,b,c){return"position"===a||"preserve-aspect"===a&&!(.2>=Math.abs(dK(b)-dK(c)))}function ed(a){var b;return a!==a.root&&(null==(b=a.scroll)?void 0:b.wasRoot)}let ee=dT({attachResizeListener:(a,b)=>cp(a,"resize",b),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ef={current:void 0},eg=dT({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!ef.current){let a=new ee({});a.mount(window),a.setOptions({layoutScroll:!0}),ef.current=a}return ef.current},resetTransform:(a,b)=>{a.style.transform=void 0!==b?b:"none"},checkIsScrollRoot:a=>"fixed"===window.getComputedStyle(a).position});function eh(a,b,c){let{props:d}=a;a.animationState&&d.whileHover&&a.animationState.setActive("whileHover","Start"===c);let e=d["onHover"+c];e&&V.postRender(()=>e(b,cq(b)))}class ei extends cl{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=D(a,c),g=E(a=>{let{target:c}=a,d=b(a);if("function"!=typeof d||!c)return;let f=E(a=>{d(a),c.removeEventListener("pointerleave",f)});c.addEventListener("pointerleave",f,e)});return d.forEach(a=>{a.addEventListener("pointerenter",g,e)}),f}(a,a=>(eh(this.node,a,"Start"),a=>eh(this.node,a,"End"))))}unmount(){}}class ej extends cl{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch(b){a=!0}a&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=bH(cp(this.node.current,"focus",()=>this.onFocus()),cp(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function ek(a,b,c){let{props:d}=a;a.animationState&&d.whileTap&&a.animationState.setActive("whileTap","Start"===c);let e=d["onTap"+("End"===c?"":c)];e&&V.postRender(()=>e(b,cq(b)))}class el extends cl{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=D(a,c),g=a=>{let d=a.currentTarget;if(!L(a)||I.has(d))return;I.add(d);let f=b(a),g=(a,b)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",i),L(a)&&I.has(d)&&(I.delete(d),"function"==typeof f&&f(a,{success:b}))},h=a=>{g(a,c.useGlobalTarget||F(d,a.target))},i=a=>{g(a,!1)};window.addEventListener("pointerup",h,e),window.addEventListener("pointercancel",i,e)};return d.forEach(a=>{H.has(a.tagName)||-1!==a.tabIndex||null!==a.getAttribute("tabindex")||(a.tabIndex=0),(c.useGlobalTarget?window:a).addEventListener("pointerdown",g,e),a.addEventListener("focus",a=>((a,b)=>{let c=a.currentTarget;if(!c)return;let d=J(()=>{if(I.has(c))return;K(c,"down");let a=J(()=>{K(c,"up")});c.addEventListener("keyup",a,b),c.addEventListener("blur",()=>K(c,"cancel"),b)});c.addEventListener("keydown",d,b),c.addEventListener("blur",()=>c.removeEventListener("keydown",d),b)})(a,e),e)}),f}(a,a=>(ek(this.node,a,"Start"),(a,{success:b})=>ek(this.node,a,b?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let em=new WeakMap,en=new WeakMap,eo=a=>{let b=em.get(a.target);b&&b(a)},ep=a=>{a.forEach(eo)},eq={some:0,all:1};class er extends cl{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:a={}}=this.node.getProps(),{root:b,margin:c,amount:d="some",once:e}=a,f={root:b?b.current:void 0,rootMargin:c,threshold:"number"==typeof d?d:eq[d]},g=a=>{let{isIntersecting:b}=a;if(this.isInView===b||(this.isInView=b,e&&!b&&this.hasEnteredView))return;b&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",b);let{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=b?c:d;f&&f(a)};var h=this.node.current;let i=function({root:a,...b}){let c=a||document;en.has(c)||en.set(c,{});let d=en.get(c),e=JSON.stringify(b);return d[e]||(d[e]=new IntersectionObserver(ep,{root:a,...b})),d[e]}(f);return em.set(h,g),i.observe(h),()=>{em.delete(h),i.unobserve(h)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:a,prevProps:b}=this.node;["amount","margin","root"].some(function({viewport:a={}},{viewport:b={}}={}){return c=>a[c]!==b[c]}(a,b))&&this.startObserver()}unmount(){}}let es=(0,c8.createContext)({strict:!1});var et=c(32582);let eu=(0,c8.createContext)({});function ev(a){return e(a.animate)||m.some(b=>h(a[b]))}function ew(a){return!!(ev(a)||a.variants)}function ex(a){return Array.isArray(a)?a.join(" "):a}var ey=c(7044);let ez={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},eA={};for(let a in ez)eA[a]={isEnabled:b=>ez[a].some(a=>!!b[a])};let eB=Symbol.for("motionComponentSymbol");var eC=c(21279),eD=c(15124);let eE=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function eF(a){if("string"!=typeof a||a.includes("-"));else if(eE.indexOf(a)>-1||/[A-Z]/u.test(a))return!0;return!1}var eG=c(72789);let eH=a=>(b,c)=>{let d=(0,c8.useContext)(eu),f=(0,c8.useContext)(eC.t),g=()=>(function({scrapeMotionValuesFromProps:a,createRenderState:b,onUpdate:c},d,f,g){let h={latestValues:function(a,b,c,d){let f={},g=d(a,{});for(let a in g)f[a]=dn(g[a]);let{initial:h,animate:i}=a,k=ev(a),l=ew(a);b&&l&&!k&&!1!==a.inherit&&(void 0===h&&(h=b.initial),void 0===i&&(i=b.animate));let m=!!c&&!1===c.initial,n=(m=m||!1===h)?i:h;if(n&&"boolean"!=typeof n&&!e(n)){let b=Array.isArray(n)?n:[n];for(let c=0;c<b.length;c++){let d=j(a,b[c]);if(d){let{transitionEnd:a,transition:b,...c}=d;for(let a in c){let b=c[a];if(Array.isArray(b)){let a=m?b.length-1:0;b=b[a]}null!==b&&(f[a]=b)}for(let b in a)f[b]=a[b]}}}return f}(d,f,g,a),renderState:b()};return c&&(h.onMount=a=>c({props:d,current:a,...h}),h.onUpdate=a=>c(a)),h})(a,b,d,f);return c?g():(0,eG.M)(g)},eI=(a,b)=>b&&"number"==typeof a?b.transform(a):a,eJ={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},eK=O.length;function eL(a,b,c){let{style:d,vars:e,transformOrigin:f}=a,g=!1,h=!1;for(let a in b){let c=b[a];if(P.has(a)){g=!0;continue}if(bm(a)){e[a]=c;continue}{let b=eI(c,a3[a]);a.startsWith("origin")?(h=!0,f[a]=b):d[a]=b}}if(!b.transform&&(g||c?d.transform=function(a,b,c){let d="",e=!0;for(let f=0;f<eK;f++){let g=O[f],h=a[g];if(void 0===h)continue;let i=!0;if(!(i="number"==typeof h?h===+!!g.startsWith("scale"):0===parseFloat(h))||c){let a=eI(h,a3[g]);if(!i){e=!1;let b=eJ[g]||g;d+=`${b}(${a}) `}c&&(b[g]=a)}}return d=d.trim(),c?d=c(b,e?"":d):e&&(d="none"),d}(b,a.transform,c):d.transform&&(d.transform="none")),h){let{originX:a="50%",originY:b="50%",originZ:c=0}=f;d.transformOrigin=`${a} ${b} ${c}`}}let eM={offset:"stroke-dashoffset",array:"stroke-dasharray"},eN={offset:"strokeDashoffset",array:"strokeDasharray"};function eO(a,b,c){return"string"==typeof a?a:aL.transform(b+c*a)}function eP(a,{attrX:b,attrY:c,attrScale:d,originX:e,originY:f,pathLength:g,pathSpacing:h=1,pathOffset:i=0,...j},k,l){if(eL(a,j,l),k){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};let{attrs:m,style:n,dimensions:o}=a;m.transform&&(o&&(n.transform=m.transform),delete m.transform),o&&(void 0!==e||void 0!==f||n.transform)&&(n.transformOrigin=function(a,b,c){let d=eO(b,a.x,a.width),e=eO(c,a.y,a.height);return`${d} ${e}`}(o,void 0!==e?e:.5,void 0!==f?f:.5)),void 0!==b&&(m.x=b),void 0!==c&&(m.y=c),void 0!==d&&(m.scale=d),void 0!==g&&function(a,b,c=1,d=0,e=!0){a.pathLength=1;let f=e?eM:eN;a[f.offset]=aL.transform(-d);let g=aL.transform(b),h=aL.transform(c);a[f.array]=`${g} ${h}`}(m,g,h,i,!1)}let eQ=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),eR=()=>({...eQ(),attrs:{}}),eS=a=>"string"==typeof a&&"svg"===a.toLowerCase();function eT(a,{style:b,vars:c},d,e){for(let f in Object.assign(a.style,b,e&&e.getProjectionStyles(d)),c)a.style.setProperty(f,c[f])}let eU=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function eV(a,b,c,d){for(let c in eT(a,b,void 0,d),b.attrs)a.setAttribute(eU.has(c)?c:ah(c),b.attrs[c])}function eW(a,{layout:b,layoutId:c}){return P.has(a)||a.startsWith("origin")||(b||void 0!==c)&&(!!df[a]||"opacity"===a)}function eX(a,b,c){var d;let{style:e}=a,f={};for(let g in e)(af(e[g])||b.style&&af(b.style[g])||eW(g,a)||(null==(d=null==c?void 0:c.getValue(g))?void 0:d.liveStyle)!==void 0)&&(f[g]=e[g]);return f}function eY(a,b,c){let d=eX(a,b,c);for(let c in a)(af(a[c])||af(b[c]))&&(d[-1!==O.indexOf(c)?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c]=a[c]);return d}let eZ=["x","y","width","height","cx","cy","r"],e$={useVisualState:eH({scrapeMotionValuesFromProps:eY,createRenderState:eR,onUpdate:({props:a,prevProps:b,current:c,renderState:d,latestValues:e})=>{if(!c)return;let f=!!a.drag;if(!f){for(let a in e)if(P.has(a)){f=!0;break}}if(!f)return;let g=!b;if(b)for(let c=0;c<eZ.length;c++){let d=eZ[c];a[d]!==b[d]&&(g=!0)}g&&V.read(()=>{!function(a,b){try{b.dimensions="function"==typeof a.getBBox?a.getBBox():a.getBoundingClientRect()}catch(a){b.dimensions={x:0,y:0,width:0,height:0}}}(c,d),V.render(()=>{eP(d,e,eS(c.tagName),a.transformTemplate),eV(c,d)})})}})},e_={useVisualState:eH({scrapeMotionValuesFromProps:eX,createRenderState:eQ})};function e0(a,b,c){for(let d in b)af(b[d])||eW(d,c)||(a[d]=b[d])}let e1=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function e2(a){return a.startsWith("while")||a.startsWith("drag")&&"draggable"!==a||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||e1.has(a)}let e3=a=>!e2(a);try{!function(a){a&&(e3=b=>b.startsWith("on")?!e2(b):a(b))}(require("@emotion/is-prop-valid").default)}catch(a){}let e4={current:null},e5={current:!1},e6=[...bs,aQ,aZ],e7=new WeakMap,e8=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class e9{scrapeMotionValuesFromProps(a,b,c){return{}}constructor({parent:a,props:b,presenceContext:c,reducedMotionConfig:d,blockInitialAnimation:e,visualState:f},g={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=bj,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let a=$.now();this.renderScheduledAt<a&&(this.renderScheduledAt=a,V.render(this.render,!1,!0))};let{latestValues:h,renderState:i,onUpdate:j}=f;this.onUpdate=j,this.latestValues=h,this.baseTarget={...h},this.initialValues=b.initial?{...h}:{},this.renderState=i,this.parent=a,this.props=b,this.presenceContext=c,this.depth=a?a.depth+1:0,this.reducedMotionConfig=d,this.options=g,this.blockInitialAnimation=!!e,this.isControllingVariants=ev(b),this.isVariantNode=ew(b),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);let{willChange:k,...l}=this.scrapeMotionValuesFromProps(b,{},this);for(let a in l){let b=l[a];void 0!==h[a]&&af(b)&&b.set(h[a],!1)}}mount(a){this.current=a,e7.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((a,b)=>this.bindToMotionValue(b,a)),e5.current||function(){if(e5.current=!0,ey.B)if(window.matchMedia){let a=window.matchMedia("(prefers-reduced-motion)"),b=()=>e4.current=a.matches;a.addListener(b),b()}else e4.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||e4.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let a in e7.delete(this.current),this.projection&&this.projection.unmount(),W(this.notifyUpdate),W(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[a].clear();for(let a in this.features){let b=this.features[a];b&&(b.unmount(),b.isMounted=!1)}this.current=null}bindToMotionValue(a,b){let c;this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();let d=P.has(a),e=b.on("change",b=>{this.latestValues[a]=b,this.props.onUpdate&&V.preRender(this.notifyUpdate),d&&this.projection&&(this.projection.isTransformDirty=!0)}),f=b.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(c=window.MotionCheckAppearSync(this,a,b)),this.valueSubscriptions.set(a,()=>{e(),f(),c&&c(),b.owner&&b.stop()})}sortNodePosition(a){return this.current&&this.sortInstanceNodePosition&&this.type===a.type?this.sortInstanceNodePosition(this.current,a.current):0}updateFeatures(){let a="animation";for(a in eA){let b=eA[a];if(!b)continue;let{isEnabled:c,Feature:d}=b;if(!this.features[a]&&d&&c(this.props)&&(this.features[a]=new d(this)),this.features[a]){let b=this.features[a];b.isMounted?b.update():(b.mount(),b.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):cM()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,b){this.latestValues[a]=b}update(a,b){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=b;for(let b=0;b<e8.length;b++){let c=e8[b];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);let d=a["on"+c];d&&(this.propEventSubscriptions[c]=this.on(c,d))}this.prevMotionValues=function(a,b,c){for(let d in b){let e=b[d],f=c[d];if(af(e))a.addValue(d,e);else if(af(f))a.addValue(d,ae(e,{owner:a}));else if(f!==e)if(a.hasValue(d)){let b=a.getValue(d);!0===b.liveStyle?b.jump(e):b.hasAnimated||b.set(e)}else{let b=a.getStaticValue(d);a.addValue(d,ae(void 0!==b?b:e,{owner:a}))}}for(let d in c)void 0===b[d]&&a.removeValue(d);return b}(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){let b=this.getClosestVariantNode();if(b)return b.variantChildren&&b.variantChildren.add(a),()=>b.variantChildren.delete(a)}addValue(a,b){let c=this.values.get(a);b!==c&&(c&&this.removeValue(a),this.bindToMotionValue(a,b),this.values.set(a,b),this.latestValues[a]=b.get())}removeValue(a){this.values.delete(a);let b=this.valueSubscriptions.get(a);b&&(b(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,b){if(this.props.values&&this.props.values[a])return this.props.values[a];let c=this.values.get(a);return void 0===c&&void 0!==b&&(c=ae(null===b?void 0:b,{owner:this}),this.addValue(a,c)),c}readValue(a,b){var c;let d=void 0===this.latestValues[a]&&this.current?null!=(c=this.getBaseTargetFromProps(this.props,a))?c:this.readValueFromInstance(this.current,a,this.options):this.latestValues[a];if(null!=d){if("string"==typeof d&&(bk(d)||av(d)))d=parseFloat(d);else{let c;c=d,!e6.find(br(c))&&aZ.test(b)&&(d=a6(a,b))}this.setBaseTarget(a,af(d)?d.get():d)}return af(d)?d.get():d}setBaseTarget(a,b){this.baseTarget[a]=b}getBaseTarget(a){var b;let c,{initial:d}=this.props;if("string"==typeof d||"object"==typeof d){let e=j(this.props,d,null==(b=this.presenceContext)?void 0:b.custom);e&&(c=e[a])}if(d&&void 0!==c)return c;let e=this.getBaseTargetFromProps(this.props,a);return void 0===e||af(e)?void 0!==this.initialValues[a]&&void 0===c?void 0:this.baseTarget[a]:e}on(a,b){return this.events[a]||(this.events[a]=new ab),this.events[a].add(b)}notify(a,...b){this.events[a]&&this.events[a].notify(...b)}}class fa extends e9{constructor(){super(...arguments),this.KeyframeResolver=bu}sortInstanceNodePosition(a,b){return 2&a.compareDocumentPosition(b)?1:-1}getBaseTargetFromProps(a,b){return a.style?a.style[b]:void 0}removeValueFromRenderState(a,{vars:b,style:c}){delete b[a],delete c[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:a}=this.props;af(a)&&(this.childSubscription=a.on("change",a=>{this.current&&(this.current.textContent=`${a}`)}))}}class fb extends fa{constructor(){super(...arguments),this.type="html",this.renderInstance=eT}readValueFromInstance(a,b){if(P.has(b)){let a=a5(b);return a&&a.default||0}{let c=window.getComputedStyle(a),d=(bm(b)?c.getPropertyValue(b):c[b])||0;return"string"==typeof d?d.trim():d}}measureInstanceViewportBox(a,{transformPagePoint:b}){return cZ(a,b)}build(a,b,c){eL(a,b,c.transformTemplate)}scrapeMotionValuesFromProps(a,b,c){return eX(a,b,c)}}class fc extends fa{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=cM}getBaseTargetFromProps(a,b){return a[b]}readValueFromInstance(a,b){if(P.has(b)){let a=a5(b);return a&&a.default||0}return b=eU.has(b)?b:ah(b),a.getAttribute(b)}scrapeMotionValuesFromProps(a,b,c){return eY(a,b,c)}build(a,b,c){eP(a,b,this.isSVGTag,c.transformTemplate)}renderInstance(a,b,c,d){eV(a,b,c,d)}mount(a){this.isSVGTag=eS(a.tagName),super.mount(a)}}let fd=function(a){if("undefined"==typeof Proxy)return a;let b=new Map;return new Proxy((...b)=>a(...b),{get:(c,d)=>"create"===d?a:(b.has(d)||b.set(d,a(d)),b.get(d))})}((c5={animation:{Feature:cm},exit:{Feature:co},inView:{Feature:er},tap:{Feature:el},focus:{Feature:ej},hover:{Feature:ei},pan:{Feature:c4},drag:{Feature:c2,ProjectionNode:eg,MeasureLayout:dj},layout:{ProjectionNode:eg,MeasureLayout:dj}},c6=(a,b)=>eF(a)?new fc(b):new fb(b,{allowProjection:a!==c8.Fragment}),function(a,{forwardMotionProps:b}={forwardMotionProps:!1}){return function({preloadedFeatures:a,createVisualElement:b,useRender:c,useVisualState:d,Component:e}){var f,g;function i(a,f){var g;let i,j={...(0,c8.useContext)(et.Q),...a,layoutId:function({layoutId:a}){let b=(0,c8.useContext)(da.L).id;return b&&void 0!==a?b+"-"+a:a}(a)},{isStatic:k}=j,l=function(a){let{initial:b,animate:c}=function(a,b){if(ev(a)){let{initial:b,animate:c}=a;return{initial:!1===b||h(b)?b:void 0,animate:h(c)?c:void 0}}return!1!==a.inherit?b:{}}(a,(0,c8.useContext)(eu));return(0,c8.useMemo)(()=>({initial:b,animate:c}),[ex(b),ex(c)])}(a),m=d(a,k);if(!k&&ey.B){(0,c8.useContext)(es).strict;let a=function(a){let{drag:b,layout:c}=eA;if(!b&&!c)return{};let d={...b,...c};return{MeasureLayout:(null==b?void 0:b.isEnabled(a))||(null==c?void 0:c.isEnabled(a))?d.MeasureLayout:void 0,ProjectionNode:d.ProjectionNode}}(j);i=a.MeasureLayout,l.visualElement=function(a,b,c,d,e){var f,g;let{visualElement:h}=(0,c8.useContext)(eu),i=(0,c8.useContext)(es),j=(0,c8.useContext)(eC.t),k=(0,c8.useContext)(et.Q).reducedMotion,l=(0,c8.useRef)(null);d=d||i.renderer,!l.current&&d&&(l.current=d(a,{visualState:b,parent:h,props:c,presenceContext:j,blockInitialAnimation:!!j&&!1===j.initial,reducedMotionConfig:k}));let m=l.current,n=(0,c8.useContext)(db);m&&!m.projection&&e&&("html"===m.type||"svg"===m.type)&&function(a,b,c,d){let{layoutId:e,layout:f,drag:g,dragConstraints:h,layoutScroll:i,layoutRoot:j}=b;a.projection=new c(a.latestValues,b["data-framer-portal-id"]?void 0:function a(b){if(b)return!1!==b.options.allowProjection?b.projection:a(b.parent)}(a.parent)),a.projection.setOptions({layoutId:e,layout:f,alwaysMeasureLayout:!!g||h&&cy(h),visualElement:a,animationType:"string"==typeof f?f:"both",initialPromotionConfig:d,layoutScroll:i,layoutRoot:j})}(l.current,c,e,n);let o=(0,c8.useRef)(!1);(0,c8.useInsertionEffect)(()=>{m&&o.current&&m.update(c,j)});let p=c[ai],q=(0,c8.useRef)(!!p&&!(null==(f=window.MotionHandoffIsComplete)?void 0:f.call(window,p))&&(null==(g=window.MotionHasOptimisedAnimation)?void 0:g.call(window,p)));return(0,eD.E)(()=>{m&&(o.current=!0,window.MotionIsMounted=!0,m.updateFeatures(),dg.render(m.render),q.current&&m.animationState&&m.animationState.animateChanges())}),(0,c8.useEffect)(()=>{m&&(!q.current&&m.animationState&&m.animationState.animateChanges(),q.current&&(queueMicrotask(()=>{var a;null==(a=window.MotionHandoffMarkAsComplete)||a.call(window,p)}),q.current=!1))}),m}(e,m,j,b,a.ProjectionNode)}return(0,c7.jsxs)(eu.Provider,{value:l,children:[i&&l.visualElement?(0,c7.jsx)(i,{visualElement:l.visualElement,...j}):null,c(e,a,(g=l.visualElement,(0,c8.useCallback)(a=>{a&&m.onMount&&m.onMount(a),g&&(a?g.mount(a):g.unmount()),f&&("function"==typeof f?f(a):cy(f)&&(f.current=a))},[g])),m,k,l.visualElement)]})}a&&function(a){for(let b in a)eA[b]={...eA[b],...a[b]}}(a),i.displayName=`motion.${"string"==typeof e?e:`create(${null!=(g=null!=(f=e.displayName)?f:e.name)?g:""})`}`;let j=(0,c8.forwardRef)(i);return j[eB]=e,j}({...eF(a)?e$:e_,preloadedFeatures:c5,useRender:function(a=!1){return(b,c,d,{latestValues:e},f)=>{let g=(eF(b)?function(a,b,c,d){let e=(0,c8.useMemo)(()=>{let c=eR();return eP(c,b,eS(d),a.transformTemplate),{...c.attrs,style:{...c.style}}},[b]);if(a.style){let b={};e0(b,a.style,a),e.style={...b,...e.style}}return e}:function(a,b){let c={},d=function(a,b){let c=a.style||{},d={};return e0(d,c,a),Object.assign(d,function({transformTemplate:a},b){return(0,c8.useMemo)(()=>{let c=eQ();return eL(c,b,a),Object.assign({},c.vars,c.style)},[b])}(a,b)),d}(a,b);return a.drag&&!1!==a.dragListener&&(c.draggable=!1,d.userSelect=d.WebkitUserSelect=d.WebkitTouchCallout="none",d.touchAction=!0===a.drag?"none":`pan-${"x"===a.drag?"y":"x"}`),void 0===a.tabIndex&&(a.onTap||a.onTapStart||a.whileTap)&&(c.tabIndex=0),c.style=d,c})(c,e,f,b),h=function(a,b,c){let d={};for(let e in a)("values"!==e||"object"!=typeof a.values)&&(e3(e)||!0===c&&e2(e)||!b&&!e2(e)||a.draggable&&e.startsWith("onDrag"))&&(d[e]=a[e]);return d}(c,"string"==typeof b,a),i=b!==c8.Fragment?{...h,...g,ref:d}:{},{children:j}=c,k=(0,c8.useMemo)(()=>af(j)?j.get():j,[j]);return(0,c8.createElement)(b,{...i,children:k})}}(b),createVisualElement:c6,Component:a})}))}};