(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8279],{5623:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},7597:(e,s,t)=>{Promise.resolve().then(t.bind(t,28754))},13052:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},28754:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>P});var r=t(95155),a=t(12115),n=t(35695),i=t(78067),l=t(98816),d=t(92215),o=t(9776),c=t(79891),m=t(97168),u=t(88482),p=t(82714),x=t(24792),h=t(84995),y=t(6874),f=t.n(y),g=t(70306),j=t(71007),v=t(81586),N=t(55868),b=t(38564),w=t(24752),C=t.n(w);function P(){var e,s;let{t,primaryColor:y}=(0,c.t)(),{user:w,isLoggedIn:P,token:D,isLoading:k}=(0,l.J)(),{items:A,total:I,subtotal:S,totalIQD:T,subtotalIQD:M,clearCart:O}=(0,i._)(),{appliedCoupon:B}=(0,d.Y)(),{formatIQD:L,formatUSD:E}=(0,o.H)(),z=(0,n.useRouter)(),[R,J]=(0,a.useState)(null),[F,U]=(0,a.useState)([]),[V,H]=(0,a.useState)(!0),[q,_]=(0,a.useState)(!1),[$,Q]=(0,a.useState)(!1),[W,Y]=(0,a.useState)({address:"",cityId:"",countryId:"107",zipCode:""}),[Z,G]=(0,a.useState)([]),[X,K]=(0,a.useState)([]),[ee,es]=(0,a.useState)(!1),[et,er]=(0,a.useState)(!1),[ea,en]=(0,a.useState)([]),[ei,el]=(0,a.useState)(null),[ed,eo]=(0,a.useState)(!1),[ec,em]=(0,a.useState)(!1);(0,a.useEffect)(()=>{k||P||z.push("/login")},[P,k,z]),(0,a.useEffect)(()=>{P&&D&&ef()},[P,D]);let eu=(null==w?void 0:w.Pointno)||0,ep=$?eu:0,ex=Math.round(1500*ep),eh=Math.max(0,I-(B?B.discount:0)-ep),ey=Math.max(0,T-(B?Math.round(1500*B.discount):0)-ex),ef=async()=>{eo(!0);try{let e=await fetch("/api/addresses/get-user-addresses",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(D)},body:JSON.stringify({requestParameters:{recordValueJson:"[]"}})}),s=await e.json();if(200===s.statusCode&&s.data){let e="string"==typeof s.data?JSON.parse(s.data):s.data;en(e||[])}}catch(e){console.error("Error fetching saved addresses:",e)}finally{eo(!1)}},eg=async()=>{try{H(!0);let e=await fetch("/api/payment-methods",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({requestParameters:{}})});if(e.ok){let s=await e.json();if(200===s.statusCode&&s.data){let e=JSON.parse(s.data);U(e),e.length>0&&J(e[0].PaymentMethodID)}}}catch(e){console.error("Error fetching payment methods:",e),U([])}finally{H(!1)}};(0,a.useEffect)(()=>{Q("true"===localStorage.getItem("usePoints")),eg()},[]);let ej=async e=>{if(e.preventDefault(),!P)return void C().fire({title:"Login Required",text:"Please login to complete your purchase",icon:"info",showCancelButton:!0,confirmButtonText:"Login",cancelButtonText:"Cancel"}).then(e=>{e.isConfirmed&&z.push("/login?redirect=checkout")});if(!R)return void C().fire({title:"Error",text:"Please select a payment method",icon:"error"});if(!ei)return void C().fire({title:"Address Required",text:"Please select a delivery address to continue",icon:"error"});if(!W.address)return void C().fire({title:"Error",text:"Please provide a shipping address",icon:"error"});if(!W.countryId)return void C().fire({title:"Error",text:"Please select a country",icon:"error"});if(!W.cityId)return void C().fire({title:"Error",text:"Please select a city",icon:"error"});try{C().fire({title:"Processing",text:"Please wait while we process your order...",allowOutsideClick:!1,didOpen:()=>{C().showLoading()}});let e=A.map(e=>({ProductId:e.id,Quantity:e.quantity,Price:parseFloat((e.discountPrice||e.price).toFixed(2)),ItemPriceTotal:parseFloat(((e.discountPrice||e.price)*e.quantity).toFixed(2)),ItemSubTotal:parseFloat(((e.discountPrice||e.price)*e.quantity).toFixed(2)),IsShippingFree:!0,ShippingChargesTotal:0,OrderItemAttributeChargesTotal:0,DiscountId:(null==B?void 0:B.discountId)||null,CouponCode:(null==B?void 0:B.code)||"",DiscountedPrice:e.discountPrice?parseFloat(e.discountPrice.toFixed(2)):null,OrderItemDiscountTotal:e.discountPrice?parseFloat(((e.price-e.discountPrice)*e.quantity).toFixed(2)):0,IsDiscountCalculated:!1,ProductAllSelectedAttributes:JSON.stringify(e.attributes||[])})),t=F.find(e=>e.PaymentMethodID===R),r=(null==t?void 0:t.PaymentMethodName)||"Unknown Payment Method";if(!w)throw Error("User not found. Please login again.");let a=Z.find(e=>e.CountryID.toString()===W.countryId),n=X.find(e=>e.CityID.toString()===W.cityId),i=(null==a?void 0:a.CountryName)||(null==a?void 0:a.Name)||"Unknown Country",l=(null==n?void 0:n.CityName)||(null==n?void 0:n.Name)||"Unknown City",d={OrderNote:"Order from WEB app".concat(B?" - Coupon: ".concat(B.code):"").concat($?" - Credit used: ".concat(ep):""," - Payment: ").concat(r," - Address: ").concat(W.address,", ").concat(i,", ").concat(l),cartJsonData:JSON.stringify(e),OrderTotal:parseFloat(eh.toFixed(2)),CouponCode:(null==B?void 0:B.code)||"",Description:"Order placed via WEB checkout - Payment Method: ".concat(r),StripeStatus:"",StripeResponseJson:"",StripeBalanceTransactionId:"",StripeChargeId:"",PayPalResponseJson:"",CurrencyCode:"USD",PaymentMethod:R,Point:$&&ep>0?ep:null,addressid:ei||1};console.log("Order data being sent:",d);let o={"Content-Type":"application/json",Accept:"application/json"};D&&(o.Authorization="Bearer ".concat(D),console.log("\uD83D\uDD10 Added JWT token to order placement request"));let c=await fetch("/api/orders/post-order",{method:"POST",headers:o,body:JSON.stringify(d)}),m=await c.json();if(console.log("Order response:",m),c.ok&&m.data){let e=m.data;if(e&&("Order Placed Successfully"===e.message||e.orderID))C().fire({title:"\uD83C\uDF89 Order Placed Successfully!",html:'\n              <div style="text-align: center; padding: 20px;">\n                <div style="font-size: 48px; margin-bottom: 20px;">✅</div>\n                <h3 style="color: #10B981; margin-bottom: 15px;">Thank you for your order!</h3>\n                '.concat(e.orderID?"<p><strong>Order ID:</strong> #".concat(e.orderID,"</p>"):"","\n                ").concat(e.orderNumber?"<p><strong>Order Number:</strong> ".concat(e.orderNumber,"</p>"):"",'\n                <p style="margin-top: 15px; color: #6B7280;">\n                  <strong>Total:</strong> $').concat(eh.toFixed(2),'\n                </p>\n                <p style="color: #6B7280;">\n                  We will contact you soon.\n                </p>\n              </div>\n            '),icon:"success",confirmButtonText:"View My Orders",confirmButtonColor:y,showCancelButton:!0,cancelButtonText:"Continue Shopping",allowOutsideClick:!1,showCloseButton:!1,customClass:{popup:"swal-wide",actions:"swal-actions-visible"},didOpen:()=>{let e=document.createElement("style");e.textContent="\n                .swal-wide {\n                  width: 600px !important;\n                  max-width: 90vw !important;\n                }\n                .swal2-html-container {\n                  font-family: inherit !important;\n                }\n                .swal-actions-visible .swal2-actions {\n                  display: flex !important;\n                  justify-content: center !important;\n                  gap: 10px !important;\n                  margin-top: 20px !important;\n                  visibility: visible !important;\n                  opacity: 1 !important;\n                }\n                .swal2-confirm, .swal2-cancel {\n                  display: inline-block !important;\n                  margin: 0 5px !important;\n                  visibility: visible !important;\n                  opacity: 1 !important;\n                  background-color: #6B7280 !important;\n                  color: white !important;\n                  border: none !important;\n                  padding: 10px 20px !important;\n                  border-radius: 6px !important;\n                  cursor: pointer !important;\n                }\n                .swal2-confirm {\n                  background-color: ".concat(y," !important;\n                }\n                .swal2-cancel:hover, .swal2-confirm:hover {\n                  opacity: 0.9 !important;\n                  transform: translateY(-1px) !important;\n                }\n              "),document.head.appendChild(e)}}).then(e=>{O(),localStorage.removeItem("usePoints"),e.isConfirmed?z.push("/orders"):z.push("/")});else throw Error((null==e?void 0:e.message)||"Order placement failed")}else{var s;throw console.error("Order placement failed. Response:",m),Error((null==m?void 0:m.error)||(null==m||null==(s=m.details)?void 0:s.ErrorMessage)||"Order placement failed")}}catch(s){console.error("Error placing order:",s);let e=s instanceof Error?s.message:"There was an error processing your order. Please try again.";C().fire({title:"Order Failed",text:e,icon:"error",confirmButtonText:"Try Again",footer:"If the problem persists, please contact support."})}};return k?(0,r.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):0===A.length?(0,r.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Your cart is empty"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Add items to your cart to proceed to checkout"}),(0,r.jsx)(m.$,{asChild:!0,children:(0,r.jsx)(f(),{href:"/",children:"Continue Shopping"})})]})}):P?(0,r.jsxs)("div",{className:"container mx-auto py-4 sm:py-6 lg:py-8 px-3 sm:px-4",children:[(0,r.jsx)(h.Qp,{className:"mb-3 sm:mb-4 lg:mb-6",children:(0,r.jsxs)(h.AB,{children:[(0,r.jsx)(h.J5,{children:(0,r.jsx)(h.w1,{asChild:!0,children:(0,r.jsx)(f(),{href:"/",children:"Home"})})}),(0,r.jsx)(h.tH,{}),(0,r.jsx)(h.J5,{children:(0,r.jsx)(h.w1,{asChild:!0,children:(0,r.jsx)(f(),{href:"/cart",children:"Cart"})})}),(0,r.jsx)(h.tH,{}),(0,r.jsx)(h.J5,{children:(0,r.jsx)(h.tJ,{children:"Checkout"})})]})}),(0,r.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-3 gap-3 lg:gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2 order-1 lg:order-none",children:(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)("form",{id:"checkout-form",onSubmit:ej,className:"p-3 sm:p-4 lg:p-6 space-y-3 sm:space-y-4 lg:space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2 sm:space-y-3",children:[(0,r.jsxs)("h2",{className:"text-base sm:text-lg font-bold flex items-center gap-2",children:[(0,r.jsx)(j.A,{className:"h-4 w-4"}),"Account Information"]}),(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-2 sm:p-3",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 sm:gap-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Name"}),(0,r.jsxs)("p",{className:"font-medium break-words",children:[null==w?void 0:w.FirstName," ",null==w?void 0:w.LastName]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Email"}),(0,r.jsx)("p",{className:"font-medium break-all",children:(null==w?void 0:w.Email)||(null==w?void 0:w.EmailAddress)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Phone"}),(0,r.jsx)("p",{className:"font-medium break-words",children:(null==w?void 0:w.PhoneNumber)||(null==w?void 0:w.PhoneNo)||(null==w?void 0:w.MobileNo)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"User ID"}),(0,r.jsxs)("p",{className:"font-medium",children:["#",(null==w?void 0:w.UserID)||(null==w?void 0:w.UserId)]})]})]})})]}),(0,r.jsxs)("div",{className:"space-y-2 sm:space-y-3",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0",children:[(0,r.jsx)("h2",{className:"text-base sm:text-lg font-bold",children:"Shipping Address"}),(0,r.jsx)(m.$,{type:"button",variant:"outline",size:"sm",onClick:()=>z.push("/addresses"),className:"text-xs sm:text-sm w-full sm:w-auto",children:"Manage Addresses"})]}),ed&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(p.J,{children:"Loading saved addresses..."}),(0,r.jsx)("div",{className:"space-y-2",children:[1,2,3].map(e=>(0,r.jsx)("div",{className:"p-3 border rounded-lg animate-pulse",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,r.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]}),(0,r.jsx)("div",{className:"w-4 h-4 bg-gray-200 rounded-full"})]})},e))})]}),!ed&&ea.length>0&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(p.J,{className:"text-sm sm:text-base font-semibold",children:"Select Delivery Address"}),(0,r.jsxs)("span",{className:"text-xs sm:text-sm text-gray-500",children:[ea.length," saved address",1!==ea.length?"es":""]})]}),(0,r.jsx)("div",{className:"space-y-2",children:ea.map(e=>(0,r.jsxs)("div",{className:"relative p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ".concat(ei===e.AddressID?"border-primary bg-primary/5 shadow-sm":"border-gray-200 hover:border-gray-300"),onClick:()=>(e=>{let s=ea.find(s=>s.AddressID===e);if(s){var t;el(e),Y({address:"".concat(s.AddressLineOne).concat(s.AddressLineTwo?", "+s.AddressLineTwo:""),cityId:(null==(t=s.CityID)?void 0:t.toString())||"",countryId:s.CountryID.toString(),zipCode:s.PostalCode||""})}})(e.AddressID),children:[ei===e.AddressID&&(0,r.jsx)("div",{className:"absolute top-3 right-3",children:(0,r.jsx)("div",{className:"w-6 h-6 bg-primary rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})}),(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:1===e.AddressTypeID?(0,r.jsx)("svg",{className:"w-4 h-4 text-primary",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"})}):2===e.AddressTypeID?(0,r.jsx)("svg",{className:"w-4 h-4 text-primary",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"})}):3===e.AddressTypeID?(0,r.jsx)("svg",{className:"w-4 h-4 text-primary",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"})}):4===e.AddressTypeID?(0,r.jsxs)("svg",{className:"w-4 h-4 text-primary",fill:"currentColor",viewBox:"0 0 20 20",children:[(0,r.jsx)("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),(0,r.jsx)("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]}):(0,r.jsx)("svg",{className:"w-4 h-4 text-primary",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{fillRule:"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z",clipRule:"evenodd"})})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary break-words",children:e.AddressTypeName||(1===e.AddressTypeID?"Home":2===e.AddressTypeID?"Billing":3===e.AddressTypeID?"Shipping":4===e.AddressTypeID?"Mailing":"Other")}),e.IsDefault&&(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Default"})]}),(0,r.jsx)("p",{className:"font-medium text-gray-900 mb-1 break-words",children:e.AddressLineOne}),e.AddressLineTwo&&(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-1 break-words",children:e.AddressLineTwo}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,r.jsx)("span",{children:e.CityName||"City"}),(0,r.jsx)("span",{children:"•"}),(0,r.jsx)("span",{children:e.CountryName}),e.PostalCode&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{children:"•"}),(0,r.jsx)("span",{children:e.PostalCode})]})]})]})]})]},e.AddressID))})]}),!ed&&0===ea.length&&(0,r.jsxs)("div",{className:"text-center py-8 border-2 border-dashed border-gray-200 rounded-lg",children:[(0,r.jsx)("div",{className:"w-12 h-12 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center",children:(0,r.jsxs)("svg",{className:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No saved addresses"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Please add a delivery address in your account settings to continue with checkout."}),(0,r.jsx)(m.$,{asChild:!0,variant:"outline",children:(0,r.jsx)(f(),{href:"/addresses",children:"Manage Addresses"})})]})]}),(0,r.jsxs)("div",{className:"space-y-2 sm:space-y-3",children:[(0,r.jsx)("h2",{className:"text-base sm:text-lg font-bold",children:"Payment Method"}),V?(0,r.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-3",children:[...Array(6)].map((e,s)=>(0,r.jsxs)("div",{className:"flex flex-col items-center p-2 sm:p-4 border rounded-md",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-200 animate-pulse mb-3"}),(0,r.jsx)("div",{className:"h-4 w-20 bg-gray-200 rounded animate-pulse mb-2"}),(0,r.jsx)("div",{className:"w-4 h-4 rounded-full border bg-gray-200 animate-pulse"})]},s))}):(0,r.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-3",children:F.map(e=>(0,r.jsxs)("div",{className:"flex flex-col items-center p-2 sm:p-4 border rounded-md cursor-pointer hover:bg-muted/50 transition-colors relative",onClick:()=>J(e.PaymentMethodID),style:{borderColor:R===e.PaymentMethodID?y:""},children:[(0,r.jsx)("div",{className:"absolute top-3 right-3 w-5 h-5 rounded-full border flex items-center justify-center",style:{borderColor:y},children:R===e.PaymentMethodID&&(0,r.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:y}})}),(0,r.jsxs)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center overflow-hidden mb-3",style:{backgroundColor:"".concat(y,"20")},children:[e.ImageUrl?(0,r.jsx)("img",{src:e.ImageUrl,alt:e.PaymentMethodName,className:"w-full h-full object-cover",onError:e=>{e.currentTarget.style.display="none";let s=e.currentTarget.nextElementSibling;s&&(s.style.display="block")}}):null,(0,r.jsx)(v.A,{className:"h-6 w-6",style:{color:y,display:e.ImageUrl?"none":"block"}})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"font-medium text-sm break-words text-center",children:e.PaymentMethodName}),e.Description&&(0,r.jsx)("p",{className:"text-xs text-muted-foreground mt-1 break-words text-center",children:e.Description})]})]},e.PaymentMethodID))}),R&&(()=>{let e=F.find(e=>e.PaymentMethodID===R);return e?(0,r.jsx)(x.Y,{paymentMethodName:e.PaymentMethodName,paymentMethodId:e.PaymentMethodID}):null})(),(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(f(),{href:"/payment-methods",className:"text-sm text-primary hover:underline",children:"View detailed payment instructions"})})]})]}),(0,r.jsx)("div",{className:"p-3 sm:p-4 lg:p-6 pt-0",children:(0,r.jsx)(m.$,{type:"submit",className:"w-full hidden lg:block",style:{backgroundColor:y,color:"white"},disabled:!R||!ei&&!W.address,children:"Place Order"})})]})}),(0,r.jsx)("div",{className:"lg:col-span-1 order-2 lg:order-none",children:(0,r.jsx)(u.Zp,{className:"lg:sticky lg:top-4",children:(0,r.jsxs)("div",{className:"p-3 sm:p-4 lg:p-6",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 mb-3 sm:mb-4",children:[(0,r.jsx)("h2",{className:"text-base sm:text-lg font-bold",children:"Order Summary"}),(0,r.jsxs)("div",{className:"flex items-center gap-1 w-full sm:w-auto",children:[(0,r.jsx)("a",{href:"#",onClick:e=>{e.preventDefault(),e.stopPropagation();let s="Order Summary:\nSubtotal: ".concat(q?E(S):L(M)).concat(B?"\nCoupon (".concat(B.code,"): -").concat(q?E(B.discount):L(Math.round(1500*B.discount))):"").concat($&&ep>0?"\nCredit Discount: -$".concat(ep.toFixed(2)):"","\nTotal: ").concat(q?E(eh):L(ey));navigator.clipboard.writeText(s);let t=e.target,r=t.textContent;t.textContent="Copied!",setTimeout(()=>{t.textContent=r},2e3)},className:"inline-block px-3 py-1.5 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 border border-gray-200 rounded-md text-xs font-medium transition-colors duration-200 mr-2 no-underline cursor-pointer",children:"\uD83D\uDCCB Copy Summary"}),(0,r.jsx)(m.$,{variant:q?"outline":"default",size:"sm",onClick:()=>_(!1),className:"text-xs px-2 flex-1 sm:flex-none",children:"IQD"}),(0,r.jsxs)(m.$,{variant:q?"default":"outline",size:"sm",onClick:()=>_(!0),className:"text-xs px-2 flex-1 sm:flex-none",children:[(0,r.jsx)(N.A,{className:"h-3 w-3 mr-1"}),"USD"]})]})]}),(0,r.jsx)("div",{className:"space-y-2 sm:space-y-3 mb-4 sm:mb-6",children:A.map(e=>(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex items-start gap-2 sm:gap-3",children:[(0,r.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-muted rounded-md overflow-hidden flex-shrink-0",children:(0,r.jsx)("img",{src:e.image||"/products/book".concat(e.id,".jpg"),alt:e.name,className:"w-full h-full object-cover"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"font-medium text-xs sm:text-sm break-words",children:e.name}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground break-words",children:["Qty: ",e.quantity]}),e.attributes&&e.attributes.length>0&&(0,r.jsxs)("div",{className:"mt-1",children:[(0,r.jsx)("p",{className:"text-xs font-medium text-gray-600 mb-1",children:"Selected Options:"}),(0,r.jsx)("div",{className:"space-y-1",children:e.attributes.map((e,s)=>(0,r.jsxs)("div",{className:"text-xs text-gray-500 break-words",children:[(0,r.jsxs)("span",{className:"font-medium",children:[e.DisplayName||e.AttributeName,":"]})," ",(0,r.jsx)("span",{children:e.AttributeValueText}),e.PriceAdjustment&&0!==e.PriceAdjustment&&(0,r.jsxs)("span",{className:"text-green-600 ml-1",children:["(",1===e.PriceAdjustmentType?"+":"",1===e.PriceAdjustmentType?E(e.PriceAdjustment):"+".concat(e.PriceAdjustment,"%"),")"]})]},s))})]})]})]}),(0,r.jsx)("p",{className:"font-medium text-sm",children:q?E(e.adjustedPrice||e.discountPrice||e.price):L(e.adjustedIqdPrice||e.iqdPrice||Math.round(1500*(e.discountPrice||e.price)))})]},e.id))}),(ei||W.address)&&(0,r.jsxs)("div",{className:"border-t pt-4 mb-4",children:[(0,r.jsxs)("h3",{className:"font-medium mb-2 flex items-center gap-2",children:[(0,r.jsxs)("svg",{className:"w-4 h-4 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),"Delivery Address"]}),(0,r.jsx)("div",{className:"bg-gray-50 rounded-lg p-3",children:ei?(()=>{let e=ea.find(e=>e.AddressID===ei);return e?(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("div",{className:"flex items-center gap-2 mb-1",children:(0,r.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary break-words",children:e.AddressTypeName||(1===e.AddressTypeID?"Home":2===e.AddressTypeID?"Billing":3===e.AddressTypeID?"Shipping":4===e.AddressTypeID?"Mailing":"Other")})}),(0,r.jsx)("p",{className:"font-medium text-gray-900 break-words",children:e.AddressLineOne}),e.AddressLineTwo&&(0,r.jsx)("p",{className:"text-gray-600 break-words",children:e.AddressLineTwo}),(0,r.jsxs)("p",{className:"text-gray-600 break-words",children:[e.CityName||"City"," •"," ",e.CountryName,e.PostalCode&&" • ".concat(e.PostalCode)]})]}):null})():(0,r.jsxs)("div",{className:"text-sm",children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:W.address}),(0,r.jsxs)("p",{className:"text-gray-600",children:[(null==(e=X.find(e=>e.CityID.toString()===W.cityId))?void 0:e.CityName)||"City"," ","•"," ",(null==(s=Z.find(e=>e.CountryID.toString()===W.countryId))?void 0:s.CountryName)||"Country",W.zipCode&&" • ".concat(W.zipCode)]})]})})]}),(0,r.jsxs)("div",{className:"space-y-3 border-t pt-4",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("span",{className:"text-muted-foreground",children:"Subtotal"}),(0,r.jsx)("span",{className:"font-medium",children:q?E(S):L(M)})]}),B&&(0,r.jsxs)("div",{className:"flex justify-between text-green-600",children:[(0,r.jsxs)("span",{children:["Coupon (",B.code,")"]}),(0,r.jsxs)("span",{children:["-",q?E(B.discount):L(Math.round(1500*B.discount))]})]}),$&&ep>0&&(0,r.jsxs)("div",{className:"flex justify-between text-yellow-600",children:[(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(b.A,{className:"h-3 w-3"}),"Credit Discount"]}),(0,r.jsxs)("span",{children:["-$",ep.toFixed(2)]})]}),(0,r.jsx)("div",{className:"border-t pt-3 mt-3",children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-lg font-bold",children:"Total"}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"text-xl font-bold",style:{color:y},children:q?E(eh):L(ey)}),(0,r.jsx)("a",{href:"#",onClick:e=>{e.preventDefault(),e.stopPropagation();let s=q?E(eh):L(ey);navigator.clipboard.writeText(s);let t=e.target,r=t.textContent;t.textContent="Copied!",setTimeout(()=>{t.textContent=r},2e3)},className:"inline-block px-3 py-1.5 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 border border-gray-200 rounded-md text-xs font-medium transition-colors duration-200 no-underline cursor-pointer",children:"\uD83D\uDCCB Copy"})]}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground",children:["≈"," ",q?L(ey):E(eh)]})]})]})}),(B||$&&ep>0)&&(0,r.jsx)("div",{className:"bg-green-50 rounded-lg p-3 border border-green-200",children:(0,r.jsxs)("div",{className:"text-sm text-green-700",children:[(0,r.jsx)("div",{className:"font-medium mb-1",children:"\uD83D\uDCB0 Total Savings:"}),(0,r.jsx)("div",{className:"font-bold",children:q?E((B?B.discount:0)+ep):L((B?Math.round(1500*B.discount):0)+ex)})]})})]})]})})}),(0,r.jsx)("div",{className:"lg:hidden order-3",children:(0,r.jsx)(u.Zp,{children:(0,r.jsx)("div",{className:"p-3",children:(0,r.jsx)(m.$,{type:"submit",form:"checkout-form",className:"w-full",style:{backgroundColor:y,color:"white"},disabled:!R||!ei&&!W.address,children:"Place Order"})})})})]})]}):(0,r.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,r.jsx)(u.Zp,{className:"max-w-md mx-auto",children:(0,r.jsxs)("div",{className:"p-8 text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(g.A,{className:"h-8 w-8 text-primary"})}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Login Required"}),(0,r.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please log in to continue with checkout"}),(0,r.jsx)(m.$,{asChild:!0,className:"w-full",children:(0,r.jsx)(f(),{href:"/login",children:"Login to Continue"})})]})})})}},35695:(e,s,t)=>{"use strict";var r=t(18999);t.o(r,"useParams")&&t.d(s,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(s,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(s,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(s,{useSearchParams:function(){return r.useSearchParams}})},38564:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},40968:(e,s,t)=>{"use strict";t.d(s,{b:()=>l});var r=t(12115),a=t(63655),n=t(95155),i=r.forwardRef((e,s)=>(0,n.jsx)(a.sG.label,{...e,ref:s,onMouseDown:s=>{var t;s.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));i.displayName="Label";var l=i},55868:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},63655:(e,s,t)=>{"use strict";t.d(s,{hO:()=>d,sG:()=>l});var r=t(12115),a=t(47650),n=t(99708),i=t(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let t=(0,n.TL)(`Primitive.${s}`),a=r.forwardRef((e,r)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?t:s,{...n,ref:r})});return a.displayName=`Primitive.${s}`,{...e,[s]:a}},{});function d(e,s){e&&a.flushSync(()=>e.dispatchEvent(s))}},70306:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]])},71007:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},82714:(e,s,t)=>{"use strict";t.d(s,{J:()=>o});var r=t(95155),a=t(12115),n=t(40968),i=t(74466),l=t(53999);let d=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)(n.b,{ref:s,className:(0,l.cn)(d(),t),...a})});o.displayName=n.b.displayName},84995:(e,s,t)=>{"use strict";t.d(s,{AB:()=>o,J5:()=>c,Qp:()=>d,tH:()=>p,tJ:()=>u,w1:()=>m});var r=t(95155),a=t(12115),n=t(99708),i=t(13052),l=(t(5623),t(53999));let d=a.forwardRef((e,s)=>{let{...t}=e;return(0,r.jsx)("nav",{ref:s,"aria-label":"breadcrumb",...t})});d.displayName="Breadcrumb";let o=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("ol",{ref:s,className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",t),...a})});o.displayName="BreadcrumbList";let c=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("li",{ref:s,className:(0,l.cn)("inline-flex items-center gap-1.5",t),...a})});c.displayName="BreadcrumbItem";let m=a.forwardRef((e,s)=>{let{asChild:t,className:a,...i}=e,d=t?n.DX:"a";return(0,r.jsx)(d,{ref:s,className:(0,l.cn)("transition-colors hover:text-foreground",a),...i})});m.displayName="BreadcrumbLink";let u=a.forwardRef((e,s)=>{let{className:t,...a}=e;return(0,r.jsx)("span",{ref:s,role:"link","aria-disabled":"true","aria-current":"page",className:(0,l.cn)("font-normal text-foreground",t),...a})});u.displayName="BreadcrumbPage";let p=e=>{let{children:s,className:t,...a}=e;return(0,r.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,l.cn)("[&>svg]:size-3.5",t),...a,children:null!=s?s:(0,r.jsx)(i.A,{})})};p.displayName="BreadcrumbSeparator"},92215:(e,s,t)=>{"use strict";t.d(s,{U:()=>l,Y:()=>d});var r=t(95155),a=t(12115),n=t(65409);let i=(0,a.createContext)(void 0);function l(e){let{children:s}=e,[t,l]=(0,a.useState)(null),[d,o]=(0,a.useState)(!1),c=async(e,s,t)=>{if(!e.trim())return{valid:!1,message:"Please enter a coupon code",discount:0};o(!0);try{let a=(null==t?void 0:t.map(e=>({ProductId:e.id,ProductName:e.name,Price:e.adjustedPrice||e.price,Quantity:e.quantity,IsDiscountAllowed:!0})))||[],i=JSON.stringify(a),d={requestParameters:{CouponCode:e.toUpperCase(),cartJsonData:i}},o=await (0,n.MakeApiCallAsync)(n.Config.END_POINT_NAMES.GET_COUPON_CODE_DISCOUNT,n.Config.DYNAMIC_METHOD_SUB_URL,d,{"Content-Type":"application/json",Accept:"application/json"},"POST");if(o&&o.data&&!o.data.errorMessage){let t;if((t="string"==typeof o.data.data?JSON.parse(o.data.data):o.data.data)&&t.DiscountValueAfterCouponAppliedWithQuantity>0){let s=t.DiscountValueAfterCouponAppliedWithQuantity,r=1===t.DiscountValueType?"percentage":"fixed",a={code:e.toUpperCase(),discount:s,type:r,discountTypeId:t.DiscountTypeId||1};return l(a),{valid:!0,message:"Coupon applied successfully!",discount:s}}if(t&&Array.isArray(t)&&t.length>0){let r=t[0];if(r&&r.DiscountValue>0&&r.IsActive){let t=0,a=r.DiscountTypeId||1;switch(a){case 1:case 2:1===r.DiscountValueType?t=r.DiscountValue*s/100:2===r.DiscountValueType&&(t=r.DiscountValue);break;case 3:case 4:case 5:case 6:case 7:t=1===r.DiscountValueType?r.DiscountValue*s/100:r.DiscountValue;break;default:t=0}if(t>0){let s={code:e.toUpperCase(),discount:t,type:1===r.DiscountValueType?"percentage":"fixed",discountTypeId:a,title:r.Title,discountId:r.DiscountId,maxQuantity:r.MaxQuantity,productId:r.ProductId,categoryId:r.CategoryID};return l(s),{valid:!0,message:'Coupon "'.concat(r.Title,'" applied successfully! Discount applied on ').concat({1:"order total",2:"order subtotal",3:"products",4:"categories",5:"manufacturers",6:"cities",7:"shipping"}[a]||"order","."),discount:t}}}}return{valid:!1,message:"Invalid coupon code or coupon not applicable to your cart",discount:0}}else{var r;return{valid:!1,message:(null==(r=o.data)?void 0:r.errorMessage)||"Failed to validate coupon",discount:0}}}catch(e){return console.error("Coupon validation error:",e),{valid:!1,message:"Error validating coupon. Please try again.",discount:0}}finally{o(!1)}};return(0,r.jsx)(i.Provider,{value:{appliedCoupon:t,validateCoupon:c,clearCoupon:()=>{l(null)},isLoading:d},children:s})}function d(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useCoupon must be used within a CouponProvider");return e}}},e=>{e.O(0,[8320,4277,3464,4706,6774,8816,9321,4792,8441,5964,7358],()=>e(e.s=7597)),_N_E=e.O()}]);