{"pages": {"/layout": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/css/app/layout.css", "static/chunks/app/layout.js"], "/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/page.js"], "/api/auth/get-token/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/auth/get-token/route.js"], "/wishlist/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/wishlist/page.js"], "/product/[id]/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/product/[id]/page.js"], "/products/layout": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/products/layout.js"], "/products/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/products/page.js"], "/api/products/get-products/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/products/get-products/route.js"], "/api/categories/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/categories/route.js"], "/api/product-types/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/product-types/route.js"], "/api/reviews/get-product-reviews/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/reviews/get-product-reviews/route.js"], "/api/video-proxy/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/video-proxy/route.js"]}}