"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5371],{5623:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},13052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},28905:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(12115),o=n(6101),a=n(52712),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),u=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(u.current);c.current="mounted"===d?e:"none"},[d]),(0,a.N)(()=>{let t=u.current,n=s.current;if(n!==e){let r=c.current,o=l(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=l(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(c.current=l(u.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,i(e)},[])}}(t),u="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),s=(0,o.s)(i.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||i.isPresent?r.cloneElement(u,{ref:s}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},30064:(e,t,n)=>{n.d(t,{UC:()=>Q,B8:()=>q,bL:()=>H,l9:()=>J});var r=n(12115),o=n(85185),a=n(46081),i=n(37328),l=n(6101),u=n(61285),s=n(63655),c=n(39033),d=n(5845),f=n(94315),m=n(95155),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[y,w,h]=(0,i.N)(b),[g,N]=(0,a.A)(b,[h]),[A,T]=g(b),I=r.forwardRef((e,t)=>(0,m.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(R,{...e,ref:t})})}));I.displayName=b;var R=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:i=!1,dir:u,currentTabStopId:y,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:g,onEntryFocus:N,preventScrollOnEntryFocus:T=!1,...I}=e,R=r.useRef(null),E=(0,l.s)(t,R),x=(0,f.jH)(u),[C,F]=(0,d.i)({prop:y,defaultProp:null!=h?h:null,onChange:g,caller:b}),[M,j]=r.useState(!1),O=(0,c.c)(N),k=w(n),U=r.useRef(!1),[L,S]=r.useState(0);return r.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(p,O),()=>e.removeEventListener(p,O)},[O]),(0,m.jsx)(A,{scope:n,orientation:a,dir:x,loop:i,currentTabStopId:C,onItemFocus:r.useCallback(e=>F(e),[F]),onItemShiftTab:r.useCallback(()=>j(!0),[]),onFocusableItemAdd:r.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>S(e=>e-1),[]),children:(0,m.jsx)(s.sG.div,{tabIndex:M||0===L?-1:0,"data-orientation":a,...I,ref:E,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{U.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!U.current;if(e.target===e.currentTarget&&t&&!M){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=k().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),T)}}U.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>j(!1))})})}),E="RovingFocusGroupItem",x=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:i=!1,tabStopId:l,children:c,...d}=e,f=(0,u.B)(),p=l||f,v=T(E,n),b=v.currentTabStopId===p,h=w(n),{onFocusableItemAdd:g,onFocusableItemRemove:N,currentTabStopId:A}=v;return r.useEffect(()=>{if(a)return g(),()=>N()},[a,g,N]),(0,m.jsx)(y.ItemSlot,{scope:n,id:p,focusable:a,active:i,children:(0,m.jsx)(s.sG.span,{tabIndex:b?0:-1,"data-orientation":v.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return C[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=v.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>D(n))}}),children:"function"==typeof c?c({isCurrentTabStop:b,hasTabStop:null!=A}):c})})});x.displayName=E;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var F=n(28905),M="Tabs",[j,O]=(0,a.A)(M,[N]),k=N(),[U,L]=j(M),S=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:l,activationMode:c="automatic",...p}=e,v=(0,f.jH)(l),[b,y]=(0,d.i)({prop:r,onChange:o,defaultProp:null!=a?a:"",caller:M});return(0,m.jsx)(U,{scope:n,baseId:(0,u.B)(),value:b,onValueChange:y,orientation:i,dir:v,activationMode:c,children:(0,m.jsx)(s.sG.div,{dir:v,"data-orientation":i,...p,ref:t})})});S.displayName=M;var _="TabsList",P=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,a=L(_,n),i=k(n);return(0,m.jsx)(I,{asChild:!0,...i,orientation:a.orientation,dir:a.dir,loop:r,children:(0,m.jsx)(s.sG.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});P.displayName=_;var G="TabsTrigger",K=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:a=!1,...i}=e,l=L(G,n),u=k(n),c=W(l.baseId,r),d=z(l.baseId,r),f=r===l.value;return(0,m.jsx)(x,{asChild:!0,...u,focusable:!a,active:f,children:(0,m.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:c,...i,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(r)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(r)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;f||a||!e||l.onValueChange(r)})})})});K.displayName=G;var B="TabsContent",V=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:a,children:i,...l}=e,u=L(B,n),c=W(u.baseId,o),d=z(u.baseId,o),f=o===u.value,p=r.useRef(f);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(F.C,{present:a||f,children:n=>{let{present:r}=n;return(0,m.jsx)(s.sG.div,{"data-state":f?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:d,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:r&&i})}})});function W(e,t){return"".concat(e,"-trigger-").concat(t)}function z(e,t){return"".concat(e,"-content-").concat(t)}V.displayName=B;var H=S,q=P,J=K,Q=V}}]);