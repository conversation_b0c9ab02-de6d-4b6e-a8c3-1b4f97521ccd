(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6272],{5623:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},13052:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},53999:(e,t,s)=>{"use strict";s.d(t,{cn:()=>l});var a=s(52596),r=s(39688);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},63944:(e,t,s)=>{Promise.resolve().then(s.bind(s,97023))},79891:(e,t,s)=>{"use strict";s.d(t,{Z:()=>m,t:()=>c});var a=s(95155),r=s(12115);let l={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us",searchProducts:"Search products..."},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",searchProducts:"ابحث عن المنتجات...",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}};var n=s(94213);let d=(0,r.createContext)(void 0);function m(e){let{children:t}=e,[s,m]=(0,r.useState)("light"),[c,i]=(0,r.useState)("en"),[o,x]=(0,r.useState)("#0074b2"),[u,h]=(0,r.useState)("#ffffff");return(0,r.useEffect)(()=>{let e=(0,n.N)(o);h(e),document.documentElement.style.setProperty("--primary",o),document.documentElement.style.setProperty("--primary-foreground",e)},[o]),(0,a.jsx)(d.Provider,{value:{theme:s,language:c,primaryColor:o,primaryTextColor:u,toggleTheme:()=>{m("light"===s?"dark":"light")},setLanguage:e=>{i(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{x(e);let t=(0,n.N)(e);h(t),document.documentElement.style.setProperty("--primary",e),document.documentElement.style.setProperty("--primary-foreground",t)},t:e=>(function(e,t){let s=l[t];return e in s?s[e]:"en"!==t&&e in l.en?l.en[e]:e})(e,c)},children:t})}function c(){let e=(0,r.useContext)(d);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},84995:(e,t,s)=>{"use strict";s.d(t,{AB:()=>c,J5:()=>i,Qp:()=>m,tH:()=>u,tJ:()=>x,w1:()=>o});var a=s(95155),r=s(12115),l=s(99708),n=s(13052),d=(s(5623),s(53999));let m=r.forwardRef((e,t)=>{let{...s}=e;return(0,a.jsx)("nav",{ref:t,"aria-label":"breadcrumb",...s})});m.displayName="Breadcrumb";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("ol",{ref:t,className:(0,d.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",s),...r})});c.displayName="BreadcrumbList";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("li",{ref:t,className:(0,d.cn)("inline-flex items-center gap-1.5",s),...r})});i.displayName="BreadcrumbItem";let o=r.forwardRef((e,t)=>{let{asChild:s,className:r,...n}=e,m=s?l.DX:"a";return(0,a.jsx)(m,{ref:t,className:(0,d.cn)("transition-colors hover:text-foreground",r),...n})});o.displayName="BreadcrumbLink";let x=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,d.cn)("font-normal text-foreground",s),...r})});x.displayName="BreadcrumbPage";let u=e=>{let{children:t,className:s,...r}=e;return(0,a.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,d.cn)("[&>svg]:size-3.5",s),...r,children:null!=t?t:(0,a.jsx)(n.A,{})})};u.displayName="BreadcrumbSeparator"},88482:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>i,ZB:()=>m,Zp:()=>n,aR:()=>d,wL:()=>o});var a=s(95155),r=s(12115),l=s(53999);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...r})});n.displayName="Card";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...r})});d.displayName="CardHeader";let m=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});m.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})});c.displayName="CardDescription";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...r})});i.displayName="CardContent";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...r})});o.displayName="CardFooter"},94213:(e,t,s)=>{"use strict";function a(e,t){let s=e=>{let t=e.replace("#",""),s=parseInt(t.slice(0,2),16)/255,a=[s,parseInt(t.slice(2,4),16)/255,parseInt(t.slice(4,6),16)/255].map(e=>e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4));return .2126*a[0]+.7152*a[1]+.0722*a[2]},a=s(e),r=s(t);return(Math.max(a,r)+.05)/(Math.min(a,r)+.05)}function r(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"AA",s=a(e,"#ffffff"),r=a(e,"#000000"),l="AAA"===t?7:4.5;return s>=l&&r>=l?s>r?"#ffffff":"#000000":s>=l?"#ffffff":r>=l?"#000000":s>r?"#ffffff":"#000000"}s.d(t,{N:()=>r})},97023:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var a=s(95155),r=s(84995),l=s(88482),n=s(6874),d=s.n(n),m=s(79891);function c(){let{t:e,primaryColor:t}=(0,m.t)();return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,a.jsx)(r.Qp,{className:"mb-6",children:(0,a.jsxs)(r.AB,{children:[(0,a.jsx)(r.J5,{children:(0,a.jsx)(r.w1,{asChild:!0,children:(0,a.jsx)(d(),{href:"/",children:e("home")})})}),(0,a.jsx)(r.tH,{}),(0,a.jsx)(r.J5,{children:(0,a.jsx)(r.tJ,{children:e("paymentMethods")})})]})}),(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-2xl md:text-3xl font-bold mb-6 md:mb-8 text-center",children:"Payment Methods"}),(0,a.jsxs)("section",{className:"mb-12",children:[(0,a.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-4 md:mb-6",style:{color:t},children:"Inside Iraq"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[(0,a.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,a.jsx)("img",{src:"/Zaincash iraq.png",alt:"Zain Cash",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2 break-words",children:"Zain cash (Iraq)"}),(0,a.jsx)("p",{className:"text-base md:text-lg font-medium break-all",children:"***********"})]})]})}),(0,a.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,a.jsx)("img",{src:"/Qicard iraq.png",alt:"Rafidein Account",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2 break-words",children:" Rafidain Bank"}),(0,a.jsx)("p",{className:"text-base md:text-lg font-medium break-all",children:"**********"})]})]})}),(0,a.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,a.jsx)("img",{src:"/Asia pay.png",alt:"Asia Pay",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2",children:"Asia Pay"}),(0,a.jsx)("p",{className:"text-base md:text-lg font-medium break-all",children:"***********"})]})]})})]})]}),(0,a.jsxs)("section",{children:[(0,a.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-4 md:mb-6 mt-8 md:mt-12",style:{color:t},children:"Outside Iraq"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,a.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,a.jsx)("img",{src:"/Paypal.png",alt:"PayPal",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2",children:"PayPal"}),(0,a.jsx)("p",{className:"mb-1 md:mb-2 text-sm md:text-base",children:"You can pay through this link\uD83D\uDC47"}),(0,a.jsxs)("a",{href:"https://paypal.me/FatimahNaser?country.x=JO&locale.x=en_US",target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium text-sm md:text-base",children:[(0,a.jsx)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",fill:"currentColor",children:(0,a.jsx)("path",{d:"M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.4-.04c-.524-.03-1.05-.04-1.608-.04h-2.19c-.524 0-.968.382-1.05.9L14.85 14.6c-.082.518.302.937.826.937h2.19c3.73 0 6.607-1.518 7.397-5.897.4-2.22-.1-3.66-1.24-4.723z"})}),"Pay with PayPal"]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2",children:"Click to open PayPal payment page"})]})]})}),(0,a.jsx)(l.Zp,{className:"p-4 md:p-6",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left md:gap-4",children:[(0,a.jsx)("img",{src:"/Amazon gift card.png",alt:"Amazon Gift",className:"h-16 w-16 flex-shrink-0 mb-2 md:mb-0"}),(0,a.jsxs)("div",{className:"space-y-2 w-full",children:[(0,a.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2",children:"Amazon Gift"}),(0,a.jsx)("a",{href:"https://www.amazon.com/Amazon-eGift-Card-Logo-Animated/dp/B07PCMWTSG/ref=mp_s_a_1_1?adgrpid=160438626878&dib=eyJ2IjoiMSJ9.y343JC2nqCCCtAt_MaFdYdSEoDk1IL8C8OVn3MADfESEozTH6jWzFIJ4WqlXn7_W-n2IrnPR-rfE3Spk4QYVuOOL7cvbuK9Esy0CXQivH6v0c4KW6RfZeH8pYn15Gdj-s62p0V-fiHzAE12D4YOgeY2zQf3sUuAQF30eHiR7nSfSyvGj9P0M79Suz3VRAqqxS64beG-r2SJhB_Y_apq-6Q.gbfTjpxr2hWpO9dWg-U8dthgvZM21cwxR6PrsZBpG38&dib_tag=se&hvadid=692707382867&hvdev=m&hvlocphy=9211521&hvnetw=g&hvqmt=e&hvrand=15003258399388157606&hvtargid=kwd-2389411675177&hydadcr=22339_13333066&keywords=amazon%27+gift+card&mcid=90ecf431d9b83733b420d0f87065fc78&qid=1748814128&sr=8-1",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 underline block break-all",children:"Amazon eGift Card Link"}),(0,a.jsx)("p",{className:"text-gray-700 text-sm md:text-base",children:"Please choose the amount and then send it to this email\uD83D\uDC47"}),(0,a.jsx)("p",{className:"text-base md:text-lg font-medium break-all",children:"<EMAIL>"})]})]})})]})]}),(0,a.jsxs)("section",{className:"mt-8",children:[(0,a.jsx)("h2",{className:"text-xl md:text-2xl font-semibold mb-4 md:mb-6 mt-8 md:mt-12",style:{color:t},children:"Cash on Delivery"}),(0,a.jsx)(l.Zp,{className:"p-2 md:p-4",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center md:flex-row md:items-start md:text-left",children:[(0,a.jsx)("div",{className:"w-32 h-32 md:w-40 md:h-40 rounded-full flex items-center justify-center mb-4 md:mb-0 md:mr-4",style:{backgroundColor:"".concat(t,"20")},children:(0,a.jsx)("img",{src:"/Cash on delivery.png",alt:"Cash on Delivery",className:"w-full h-full rounded-full bg-white p-2 object-contain"})}),(0,a.jsxs)("div",{className:"md:flex-1",children:[(0,a.jsx)("h3",{className:"text-base md:text-xl font-semibold mb-1 md:mb-2",children:"Cash on Delivery"}),(0,a.jsx)("p",{className:"text-muted-foreground text-sm md:text-base",children:"Pay in cash upon delivery - we offer delivery to all provinces within Iraq. Additional fees may apply depending on your location."})]})]})})]}),(0,a.jsxs)("section",{className:"mt-12",children:[(0,a.jsx)("h2",{className:"text-xl md:text-2xl font-bold mb-4 md:mb-6 mt-8 md:mt-12",children:"Payment Process"}),(0,a.jsx)(l.Zp,{className:"p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-xl font-bold",children:"1"})}),(0,a.jsx)("h3",{className:"font-medium text-sm md:text-base mb-1 md:mb-2",children:"Select Products"}),(0,a.jsx)("p",{className:"text-xs md:text-sm text-muted-foreground",children:"Add items to your cart"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-xl font-bold",children:"2"})}),(0,a.jsx)("h3",{className:"font-medium text-sm md:text-base mb-1 md:mb-2",children:"Shipping Details"}),(0,a.jsx)("p",{className:"text-xs md:text-sm text-muted-foreground",children:"Enter your shipping information"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-xl font-bold",children:"3"})}),(0,a.jsx)("h3",{className:"font-medium text-sm md:text-base mb-1 md:mb-2",children:"Payment Method"}),(0,a.jsx)("p",{className:"text-xs md:text-sm text-muted-foreground",children:"Choose your payment method"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-xl font-bold",children:"4"})}),(0,a.jsx)("h3",{className:"font-medium text-sm md:text-base mb-1 md:mb-2",children:"Confirmation"}),(0,a.jsx)("p",{className:"text-xs md:text-sm text-muted-foreground",children:"Review and confirm your order"})]})]})})]}),(0,a.jsx)("div",{className:"mt-12 text-center",children:(0,a.jsx)("p",{className:"text-gray-700 text-sm md:text-base text-center px-4",children:"For payment support, please contact our customer service team."})})]})]})}}},e=>{e.O(0,[4277,4706,8441,5964,7358],()=>e(e.s=63944)),_N_E=e.O()}]);