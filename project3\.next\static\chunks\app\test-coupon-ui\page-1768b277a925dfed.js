(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9881],{17727:(e,s,t)=>{Promise.resolve().then(t.bind(t,65416))},65416:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(95155),n=t(12115);function r(){let[e,s]=(0,n.useState)("954B50"),[t,r]=(0,n.useState)(!1),[o,d]=(0,n.useState)(null),[l,i]=(0,n.useState)(""),[c,u]=(0,n.useState)("https://localhost:7149"),m=[{ProductId:1,ProductName:"Test Product",Price:100,Quantity:1,IsDiscountAllowed:!0}],p=async()=>{r(!0),i(""),d(null);try{let s=await fetch("".concat(c,"/api/v1/dynamic/dataoperation/get-coupon-code-data"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({requestParameters:{CouponCode:e,cartJsonData:JSON.stringify(m)}})}),t=await s.json();if(d(t),200===t.statusCode&&t.data)try{let e=JSON.parse(t.data);d({...t,parsedData:e})}catch(e){console.error("Error parsing coupon data:",e)}}catch(e){i(e.message||"An error occurred")}finally{r(!1)}},h=async()=>{r(!0),i(""),d(null);try{let s=await fetch("".concat(c,"/api/v1/dynamic/dataoperation/place-order"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({requestParameters:{FirstName:"Test",LastName:"User",Email:"<EMAIL>",Phone:"+1234567890",Address:"123 Test Street",City:"Test City",Country:"Test Country",ZipCode:"12345",OrderNote:"Test order with coupon",PaymentMethodId:1,CouponCode:e,cartJsonData:JSON.stringify(m)}})}),t=await s.json();d(t)}catch(e){i(e.message||"An error occurred")}finally{r(!1)}};return(0,a.jsxs)("div",{className:"container mx-auto p-4",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Test Coupon Functionality"}),(0,a.jsxs)("div",{className:"mb-6 p-4 border rounded",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Backend Configuration"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Backend URL:"}),(0,a.jsx)("input",{type:"text",value:c,onChange:e=>u(e.target.value),className:"w-full p-2 border rounded"})]})]}),(0,a.jsxs)("div",{className:"mb-6 p-4 border rounded",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Coupon Test"}),(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Coupon Code:"}),(0,a.jsx)("input",{type:"text",value:e,onChange:e=>s(e.target.value),className:"w-full p-2 border rounded"})]}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)("button",{onClick:p,disabled:t,className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50",children:t?"Testing...":"Test Coupon"}),(0,a.jsx)("button",{onClick:h,disabled:t,className:"px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50",children:t?"Processing...":"Place Order with Coupon"})]})]}),l&&(0,a.jsxs)("div",{className:"mb-6 p-4 border border-red-300 bg-red-50 rounded",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-2 text-red-700",children:"Error"}),(0,a.jsx)("p",{className:"text-red-700",children:l})]}),o&&(0,a.jsxs)("div",{className:"mb-6 p-4 border rounded",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Result"}),(0,a.jsx)("div",{className:"bg-gray-100 p-4 rounded overflow-auto max-h-96",children:(0,a.jsx)("pre",{children:JSON.stringify(o,null,2)})}),o.parsedData&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Coupon Details"}),(0,a.jsx)("ul",{className:"list-disc pl-5",children:o.parsedData.map((e,s)=>(0,a.jsxs)("li",{className:"mb-2",children:[(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Title:"})," ",e.Title]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Code:"})," ",e.CouponCode]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Discount Value:"})," ",e.DiscountValue]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Discount Type:"})," ",1===e.DiscountTypeId?"Order Total":2===e.DiscountTypeId?"Product":3===e.DiscountTypeId?"Category":"Unknown"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Value Type:"})," ",1===e.DiscountValueType?"Percentage":"Fixed Amount"]}),(0,a.jsxs)("p",{children:[(0,a.jsx)("strong",{children:"Valid Until:"})," ",new Date(e.EndDate).toLocaleDateString()]})]},s))})]})]})]})}}},e=>{e.O(0,[8441,5964,7358],()=>e(e.s=17727)),_N_E=e.O()}]);