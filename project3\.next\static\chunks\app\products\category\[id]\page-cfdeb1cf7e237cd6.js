(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4078],{5623:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},13052:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},31803:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var s=t(95155),a=t(12115),l=t(35695),n=t(6874),d=t.n(n),c=t(84995),i=t(88482),o=t(97168),u=t(35169),m=t(37108);function x(){let e=(0,l.useParams)().id,[r,t]=(0,a.useState)([]),[n,x]=(0,a.useState)(!0),[f,h]=(0,a.useState)(""),[p,g]=(0,a.useState)(null);(0,a.useEffect)(()=>{j()},[e]);let j=async()=>{x(!0),g(null);try{t([]),h("Category")}catch(e){console.error("Error fetching category products:",e),g("Failed to load category products")}finally{x(!1)}};return n?(0,s.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-lg text-muted-foreground",children:"Loading category..."})]})}):(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,s.jsx)(c.Qp,{className:"mb-6",children:(0,s.jsxs)(c.AB,{children:[(0,s.jsx)(c.J5,{children:(0,s.jsx)(c.w1,{asChild:!0,children:(0,s.jsx)(d(),{href:"/",children:"Home"})})}),(0,s.jsx)(c.tH,{}),(0,s.jsx)(c.J5,{children:(0,s.jsx)(c.w1,{asChild:!0,children:(0,s.jsx)(d(),{href:"/products",children:"Products"})})}),(0,s.jsx)(c.tH,{}),(0,s.jsx)(c.J5,{children:(0,s.jsx)(c.tJ,{children:f||"Category ".concat(e)})})]})}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-6",children:[(0,s.jsx)(o.$,{variant:"outline",size:"sm",asChild:!0,children:(0,s.jsxs)(d(),{href:"/products",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back to Products"]})}),(0,s.jsx)("h1",{className:"text-3xl font-bold",children:f||"Category ".concat(e)})]}),p?(0,s.jsx)(i.Zp,{className:"p-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(m.A,{className:"h-8 w-8 text-red-500"})}),(0,s.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Error Loading Category"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:p}),(0,s.jsx)(o.$,{onClick:j,children:"Try Again"})]})}):0===r.length?(0,s.jsx)(i.Zp,{className:"p-8",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(m.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,s.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Category Products Coming Soon"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"This category page is under development. Products will be displayed here once the category API is implemented."}),(0,s.jsx)(o.$,{asChild:!0,children:(0,s.jsx)(d(),{href:"/products",children:"View All Products"})})]})}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:r.map(e=>(0,s.jsxs)(i.Zp,{className:"overflow-hidden",children:[(0,s.jsx)("div",{className:"aspect-square bg-muted flex items-center justify-center",children:(0,s.jsx)(m.A,{className:"h-12 w-12 text-muted-foreground"})}),(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("h3",{className:"font-medium mb-2",children:e.ProductName}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-2 line-clamp-2",children:e.ShortDescription}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("span",{className:"font-bold text-primary",children:["$",(e.DiscountPrice||e.Price).toFixed(2)]}),e.DiscountPrice&&(0,s.jsxs)("span",{className:"text-sm text-muted-foreground line-through",children:["$",e.Price.toFixed(2)]})]}),(0,s.jsx)(o.$,{size:"sm",asChild:!0,children:(0,s.jsx)(d(),{href:"/product/".concat(e.ProductId),children:"View"})})]})]})]},e.ProductId))})]})]})}},35169:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,r,t)=>{"use strict";var s=t(18999);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},37108:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},41952:(e,r,t)=>{Promise.resolve().then(t.bind(t,31803))},53999:(e,r,t)=>{"use strict";t.d(r,{cn:()=>l});var s=t(52596),a=t(39688);function l(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>n});var s=t(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=s.$,n=(e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return l(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:n,defaultVariants:d}=r,c=Object.keys(n).map(e=>{let r=null==t?void 0:t[e],s=null==d?void 0:d[e];if(null===r)return null;let l=a(r)||a(s);return n[e][l]}),i=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return l(e,c,null==r||null==(s=r.compoundVariants)?void 0:s.reduce((e,r)=>{let{class:t,className:s,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...d,...i}[r]):({...d,...i})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},84995:(e,r,t)=>{"use strict";t.d(r,{AB:()=>i,J5:()=>o,Qp:()=>c,tH:()=>x,tJ:()=>m,w1:()=>u});var s=t(95155),a=t(12115),l=t(99708),n=t(13052),d=(t(5623),t(53999));let c=a.forwardRef((e,r)=>{let{...t}=e;return(0,s.jsx)("nav",{ref:r,"aria-label":"breadcrumb",...t})});c.displayName="Breadcrumb";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("ol",{ref:r,className:(0,d.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",t),...a})});i.displayName="BreadcrumbList";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("li",{ref:r,className:(0,d.cn)("inline-flex items-center gap-1.5",t),...a})});o.displayName="BreadcrumbItem";let u=a.forwardRef((e,r)=>{let{asChild:t,className:a,...n}=e,c=t?l.DX:"a";return(0,s.jsx)(c,{ref:r,className:(0,d.cn)("transition-colors hover:text-foreground",a),...n})});u.displayName="BreadcrumbLink";let m=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("span",{ref:r,role:"link","aria-disabled":"true","aria-current":"page",className:(0,d.cn)("font-normal text-foreground",t),...a})});m.displayName="BreadcrumbPage";let x=e=>{let{children:r,className:t,...a}=e;return(0,s.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,d.cn)("[&>svg]:size-3.5",t),...a,children:null!=r?r:(0,s.jsx)(n.A,{})})};x.displayName="BreadcrumbSeparator"},88482:(e,r,t)=>{"use strict";t.d(r,{BT:()=>i,Wu:()=>o,ZB:()=>c,Zp:()=>n,aR:()=>d,wL:()=>u});var s=t(95155),a=t(12115),l=t(53999);let n=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...a})});n.displayName="Card";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...a})});d.displayName="CardHeader";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});c.displayName="CardTitle";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",t),...a})});i.displayName="CardDescription";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",t),...a})});o.displayName="CardContent";let u=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",t),...a})});u.displayName="CardFooter"},97168:(e,r,t)=>{"use strict";t.d(r,{$:()=>i,r:()=>c});var s=t(95155),a=t(12115),l=t(99708),n=t(74466),d=t(53999);let c=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=a.forwardRef((e,r)=>{let{className:t,variant:a,size:n,asChild:i=!1,...o}=e,u=i?l.DX:"button";return(0,s.jsx)(u,{className:(0,d.cn)(c({variant:a,size:n,className:t})),ref:r,...o})});i.displayName="Button"}},e=>{e.O(0,[4277,4706,8441,5964,7358],()=>e(e.s=41952)),_N_E=e.O()}]);