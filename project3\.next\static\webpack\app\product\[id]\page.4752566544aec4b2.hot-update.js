"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/product-details-client.tsx":
/*!*****************************************************!*\
  !*** ./app/product/[id]/product-details-client.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Heart,Share2,ShoppingCart,Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_modern_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/modern-toast */ \"(app-pages-browser)/./components/ui/modern-toast.tsx\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _contexts_currency_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/currency-context */ \"(app-pages-browser)/./contexts/currency-context.tsx\");\n/* harmony import */ var _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/settings-context */ \"(app-pages-browser)/./contexts/settings-context.tsx\");\n/* harmony import */ var _components_products_product_specifications__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/products/product-specifications */ \"(app-pages-browser)/./components/products/product-specifications.tsx\");\n/* harmony import */ var _components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/products/product-media-gallery */ \"(app-pages-browser)/./components/products/product-media-gallery.tsx\");\n/* harmony import */ var _product_loading__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./product-loading */ \"(app-pages-browser)/./app/product/[id]/product-loading.tsx\");\n/* harmony import */ var _product_error__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./product-error */ \"(app-pages-browser)/./app/product/[id]/product-error.tsx\");\n/* harmony import */ var _components_ui_product_reviews_display__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/product-reviews-display */ \"(app-pages-browser)/./components/ui/product-reviews-display.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to construct image URL\nconst constructImageUrl = (attachmentUrl)=>{\n    if (!attachmentUrl) return \"/placeholder.svg?height=400&width=400\";\n    if (attachmentUrl.startsWith(\"http\")) {\n        return attachmentUrl;\n    }\n    const baseUrl = \"https://admin.codemedicalapps.com\";\n    // Normalize path (ensure it starts with exactly one slash)\n    let normalizedPath = attachmentUrl.startsWith(\"/\") ? attachmentUrl : \"/\".concat(attachmentUrl);\n    // Remove any double slashes in the path\n    normalizedPath = normalizedPath.replace(/\\/+/g, '/');\n    return \"\".concat(baseUrl).concat(normalizedPath);\n};\nfunction ProductDetailsClient(param) {\n    let { productId } = param;\n    var _product_Rating;\n    _s();\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__.useWishlist)();\n    const { rate } = (0,_contexts_currency_context__WEBPACK_IMPORTED_MODULE_11__.useCurrency)();\n    const { primaryColor, primaryTextColor } = (0,_contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings)();\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [activeImage, setActiveImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoLinks, setVideoLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideoIndex, setSelectedVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingToWishlist, setAddingToWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationType, setAnimationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ProductDetailsClient.useState\": ()=>{\n            // Initialize with first option selected for each attribute if none selected\n            const initial = {};\n            if (product === null || product === void 0 ? void 0 : product.AttributesJson) {\n                product.AttributesJson.forEach({\n                    \"ProductDetailsClient.useState\": (attr)=>{\n                        const key = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        initial[key] = true; // Select first option by default\n                    }\n                }[\"ProductDetailsClient.useState\"]);\n            }\n            return initial;\n        }\n    }[\"ProductDetailsClient.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetailsClient.useEffect\": ()=>{\n            fetchProduct();\n        }\n    }[\"ProductDetailsClient.useEffect\"], [\n        productId\n    ]);\n    const fetchProduct = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Try direct API call first, then fallback to proxy if CORS issues\n            const requestBody = {\n                requestParameters: {\n                    ProductId: Number.parseInt(productId, 10),\n                    recordValueJson: \"[]\"\n                }\n            };\n            console.log(\"Fetching product with ID:\", productId, \"Request body:\", requestBody);\n            let response;\n            try {\n                // Import MakeApiCallAsync for JWT token handling\n                const { MakeApiCallAsync } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/api-helper */ \"(app-pages-browser)/./lib/api-helper.ts\"));\n                // Use API helper which automatically handles JWT tokens and removes UserID\n                response = await MakeApiCallAsync(\"get-product_detail\", null, requestBody, {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                }, \"POST\", true);\n                console.log(\"API helper response:\", response.data);\n            } catch (apiHelperError) {\n                console.log(\"API helper failed, trying proxy route:\", apiHelperError);\n                // Fallback to proxy route\n                response = await axios__WEBPACK_IMPORTED_MODULE_18__[\"default\"].post(\"/api/product-detail\", requestBody, {\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                console.log(\"Proxy API response:\", response.data);\n            }\n            if (response.data) {\n                // Check if response.data has a nested data property (from API proxy)\n                const responseData = response.data.data ? response.data : response.data;\n                if (responseData && responseData.data) {\n                    try {\n                        // Parse the response data\n                        const parsedData = JSON.parse(responseData.data);\n                        console.log(\"Parsed product data:\", parsedData);\n                        if (parsedData) {\n                            // The API might return an array with one item or a single object\n                            const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;\n                            if (productData) {\n                                // Ensure AttributesJson is properly parsed if it's a string\n                                if (productData.AttributesJson && typeof productData.AttributesJson === \"string\") {\n                                    try {\n                                        productData.AttributesJson = JSON.parse(productData.AttributesJson);\n                                    } catch (e) {\n                                        console.error(\"Error parsing AttributesJson:\", e);\n                                        productData.AttributesJson = [];\n                                    }\n                                } else if (!productData.AttributesJson) {\n                                    productData.AttributesJson = [];\n                                }\n                                console.log(\"Product data with attributes:\", productData);\n                                console.log(\"CategoryName in product data:\", productData.CategoryName);\n                                console.log(\"CategoryID in product data:\", productData.CategoryID);\n                                setProduct(productData);\n                                // Set active image\n                                if (productData.ProductImagesJson && productData.ProductImagesJson.length > 0) {\n                                    const primaryImage = productData.ProductImagesJson.find((img)=>img.IsPrimary) || productData.ProductImagesJson[0];\n                                    setActiveImage(constructImageUrl(primaryImage.AttachmentURL));\n                                }\n                                // Handle comma-separated video links\n                                if (productData.VideoLink) {\n                                    console.log(\"Video links found:\", productData.VideoLink);\n                                    const links = productData.VideoLink.split(\",\").map((link)=>link.trim());\n                                    const processedLinks = links.map((link)=>constructVideoUrl(link));\n                                    setVideoLinks(processedLinks);\n                                    setSelectedVideoIndex(0);\n                                }\n                                // Set initial quantity based on product minimum order quantity\n                                if (productData.OrderMinimumQuantity > 0) {\n                                    setQuantity(productData.OrderMinimumQuantity);\n                                }\n                            } else {\n                                console.error(\"No product data found in parsed response\");\n                                setError(\"Product with ID \".concat(productId, \" not found. Please check if this product exists.\"));\n                            }\n                        } else {\n                            console.error(\"Invalid product data format - parsedData is null/undefined\");\n                            setError(\"Invalid product data format\");\n                        }\n                    } catch (parseError) {\n                        console.error(\"Error parsing product data:\", parseError, \"Raw data:\", responseData.data);\n                        setError(\"Error parsing product data\");\n                    }\n                } else {\n                    console.error(\"No data property in API response:\", response.data);\n                    setError(\"No data in API response\");\n                }\n            } else {\n                console.error(\"Empty response from API\");\n                setError(\"Empty response from server\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching product:\", error);\n            // More detailed error handling\n            if (error.response) {\n                var _error_response_data;\n                // Server responded with error status\n                console.error(\"Server error:\", error.response.status, error.response.data);\n                setError(\"Server error: \".concat(error.response.status, \" - \").concat(((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Unknown error\"));\n            } else if (error.request) {\n                // Request was made but no response received\n                console.error(\"Network error:\", error.request);\n                setError(\"Network error - please check your connection\");\n            } else {\n                // Something else happened\n                console.error(\"Request setup error:\", error.message);\n                setError(\"Error: \".concat(error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const constructVideoUrl = (videoLink)=>{\n        if (!videoLink) return \"\";\n        if (videoLink.includes(\"youtube.com\") || videoLink.includes(\"youtu.be\")) {\n            return videoLink;\n        }\n        // For MP4 videos, use a proxy URL to handle CORS\n        if (videoLink.startsWith(\"http\")) {\n            return \"/api/video-proxy?url=\".concat(encodeURIComponent(videoLink));\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedVideoLink = videoLink.startsWith(\"/\") ? videoLink : \"/\".concat(videoLink);\n        return \"/api/video-proxy?url=\".concat(encodeURIComponent(\"\".concat(baseUrl).concat(normalizedVideoLink)));\n    };\n    // Group attributes by ProductAttributeID\n    const groupedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[groupedAttributes]\": ()=>{\n            if (!(product === null || product === void 0 ? void 0 : product.AttributesJson)) return {};\n            return product.AttributesJson.reduce({\n                \"ProductDetailsClient.useMemo[groupedAttributes]\": (groups, attr)=>{\n                    const groupId = attr.ProductAttributeID;\n                    if (!groups[groupId]) {\n                        groups[groupId] = [];\n                    }\n                    groups[groupId].push(attr);\n                    return groups;\n                }\n            }[\"ProductDetailsClient.useMemo[groupedAttributes]\"], {});\n        }\n    }[\"ProductDetailsClient.useMemo[groupedAttributes]\"], [\n        product === null || product === void 0 ? void 0 : product.AttributesJson\n    ]);\n    // Handle attribute selection with conditional behavior\n    const handleAttributeChange = (attr, isChecked, isRadioGroup)=>{\n        setSelectedAttributes((prev)=>{\n            const newState = {\n                ...prev\n            };\n            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n            // For radio groups, uncheck all other attributes in the same group\n            if (isRadioGroup && isChecked) {\n                Object.keys(prev).forEach((key)=>{\n                    if (key.startsWith(\"\".concat(attr.ProductAttributeID, \"_\")) && key !== attrKey) {\n                        newState[key] = false;\n                    }\n                });\n            }\n            // Set the selected attribute\n            // For checkboxes, toggle the state\n            // For radio buttons, always set to true (since we already unset others if needed)\n            newState[attrKey] = isRadioGroup ? true : !prev[attrKey];\n            return newState;\n        });\n    };\n    // Render price with all price-related information\n    const renderPrice = ()=>{\n        if (!product) return null;\n        const showDiscount = product.DiscountPrice && product.DiscountPrice < product.Price;\n        const adjustedPrice = calculateAdjustedPrice();\n        const showAdjustedPrice = adjustedPrice !== product.Price && adjustedPrice !== (product.DiscountPrice || product.Price);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-baseline gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-3xl font-bold text-primary\",\n                            children: [\n                                \"$\",\n                                showDiscount ? (product.DiscountPrice || 0).toFixed(2) : adjustedPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 13\n                        }, this),\n                        showAdjustedPrice && !showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 13\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded\",\n                            children: [\n                                Math.round((product.Price - (product.DiscountPrice || 0)) / product.Price * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 433,\n                    columnNumber: 9\n                }, this),\n                product.PriceIQD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-lg font-medium text-gray-600\",\n                    children: [\n                        product.PriceIQD.toLocaleString(),\n                        \" IQD\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 470,\n                    columnNumber: 11\n                }, this),\n                product.PointNo && product.PointNo > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                        children: [\n                            \"Buy & Earn \",\n                            product.PointNo,\n                            \" $ credit\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 478,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 11\n                }, this),\n                product.OldPrice && product.OldPrice > product.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"line-through\",\n                            children: [\n                                \"$\",\n                                product.OldPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-green-600\",\n                            children: [\n                                Math.round((product.OldPrice - (product.DiscountPrice || product.Price)) / product.OldPrice * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 486,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 431,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate adjusted price based on selected attributes\n    const calculateAdjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProductDetailsClient.useCallback[calculateAdjustedPrice]\": ()=>{\n            if (!product) return 0;\n            let adjustedPrice = product.Price;\n            if (product.AttributesJson && product.AttributesJson.length > 0) {\n                product.AttributesJson.forEach({\n                    \"ProductDetailsClient.useCallback[calculateAdjustedPrice]\": (attr)=>{\n                        const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        if (selectedAttributes[attrKey] && typeof attr.PriceAdjustment === \"number\" && typeof attr.PriceAdjustmentType === \"number\") {\n                            switch(attr.PriceAdjustmentType){\n                                case 1:\n                                    adjustedPrice += attr.PriceAdjustment;\n                                    break;\n                                case 2:\n                                    adjustedPrice += product.Price * attr.PriceAdjustment / 100;\n                                    break;\n                            }\n                        }\n                    }\n                }[\"ProductDetailsClient.useCallback[calculateAdjustedPrice]\"]);\n            }\n            return Math.max(0, adjustedPrice); // Ensure price doesn't go below 0\n        }\n    }[\"ProductDetailsClient.useCallback[calculateAdjustedPrice]\"], [\n        product,\n        selectedAttributes\n    ]);\n    const adjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[adjustedPrice]\": ()=>calculateAdjustedPrice()\n    }[\"ProductDetailsClient.useMemo[adjustedPrice]\"], [\n        calculateAdjustedPrice\n    ]);\n    // Render product badges\n    const renderBadges = ()=>{\n        if (!product) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute top-4 left-4 z-10 flex flex-col gap-2\",\n            children: [\n                product.IsDiscountAllowed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-red-500 hover:bg-red-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"SALE\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 543,\n                    columnNumber: 11\n                }, this),\n                product.MarkAsNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-green-500 hover:bg-green-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"NEW\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 548,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 541,\n            columnNumber: 7\n        }, this);\n    };\n    // Combine images and videos into a single media array for the gallery\n    const mediaItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[mediaItems]\": ()=>{\n            var _product_ProductImagesJson;\n            const items = [];\n            // Add product images\n            if (product === null || product === void 0 ? void 0 : (_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : _product_ProductImagesJson.length) {\n                product.ProductImagesJson.forEach({\n                    \"ProductDetailsClient.useMemo[mediaItems]\": (img)=>{\n                        items.push({\n                            type: \"image\",\n                            url: constructImageUrl(img.AttachmentURL),\n                            alt: (product === null || product === void 0 ? void 0 : product.ProductName) || \"Product image\",\n                            thumbnail: constructImageUrl(img.AttachmentURL)\n                        });\n                    }\n                }[\"ProductDetailsClient.useMemo[mediaItems]\"]);\n            }\n            // Add videos\n            videoLinks.forEach({\n                \"ProductDetailsClient.useMemo[mediaItems]\": (videoUrl, index)=>{\n                    items.push({\n                        type: \"video\",\n                        url: videoUrl,\n                        alt: \"\".concat((product === null || product === void 0 ? void 0 : product.ProductName) || \"Product\", \" - Video \").concat(index + 1),\n                        thumbnail: activeImage || \"\"\n                    });\n                }\n            }[\"ProductDetailsClient.useMemo[mediaItems]\"]);\n            return items;\n        }\n    }[\"ProductDetailsClient.useMemo[mediaItems]\"], [\n        product,\n        videoLinks,\n        activeImage\n    ]);\n    const animateCounter = (type)=>{\n        setAnimationType(type);\n        setIsAnimating(true);\n        setTimeout(()=>setIsAnimating(false), 300);\n    };\n    const incrementQuantity = ()=>{\n        if (product) {\n            const maxQuantity = product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity;\n            if (quantity < maxQuantity) {\n                setQuantity((prev)=>prev + 1);\n                animateCounter(\"increment\");\n            } else {\n                // Visual feedback when max quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Maximum quantity of \".concat(maxQuantity, \" reached\"));\n            }\n        }\n    };\n    const decrementQuantity = ()=>{\n        if (product) {\n            const minQuantity = product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1;\n            if (quantity > minQuantity) {\n                setQuantity((prev)=>prev - 1);\n                animateCounter(\"decrement\");\n            } else {\n                // Visual feedback when min quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Minimum quantity is \".concat(minQuantity));\n            }\n        }\n    };\n    // Dynamic button styles based on state\n    const getButtonStyles = (type)=>{\n        const baseStyles = \"flex items-center justify-center w-6 h-6 sm:w-7 sm:h-7 rounded-full transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-offset-1\";\n        const disabledStyles = \"bg-gray-100 text-gray-400 cursor-not-allowed\";\n        if (type === \"increment\") {\n            const isMax = product && quantity >= (product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity);\n            return \"\".concat(baseStyles, \" \").concat(isMax ? disabledStyles : \"bg-primary text-white hover:bg-primary/90 focus:ring-primary/50\");\n        } else {\n            const isMin = product && quantity <= (product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1);\n            return \"\".concat(baseStyles, \" \").concat(isMin ? disabledStyles : \"bg-primary text-white hover:bg-primary/90 focus:ring-primary/50\");\n        }\n    };\n    // Counter display with animation\n    const CounterDisplay = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center justify-center w-10 sm:w-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm sm:text-base font-medium transition-all duration-200 \".concat(isAnimating ? \"scale-125 text-primary\" : \"scale-100\"),\n                    children: quantity\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 655,\n                    columnNumber: 7\n                }, this),\n                isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"absolute text-xs font-bold text-primary transition-all duration-200 \".concat(animationType === \"increment\" ? \"-top-4 sm:-top-5\" : \"top-4 sm:top-5\"),\n                    children: animationType === \"increment\" ? \"+1\" : \"-1\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 663,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 654,\n            columnNumber: 5\n        }, this);\n    // Early return if product is not loaded yet\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_loading__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 678,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            error: error,\n            retry: fetchProduct\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 682,\n            columnNumber: 12\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Product Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 688,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: [\n                        'The product with ID \"',\n                        productId,\n                        '\" could not be found. It may not exist in the database.'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 689,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/products\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"View All Products\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 694,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Check the products list to find available product IDs\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 700,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 693,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 687,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.Breadcrumb, {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbList, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 714,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 713,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 718,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products?category=\".concat(product.CategoryID || \"all\"),\n                                    children: product.CategoryName || \"Medical Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 720,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 719,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 726,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 729,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 728,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 727,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 732,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbPage, {\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 733,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 712,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 711,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_14__.ProductMediaGallery, {\n                            media: mediaItems,\n                            className: \"w-full rounded-lg overflow-hidden\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 742,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 741,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:w-1/2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-2\",\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 750,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            ...Array(5)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(i < Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 756,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 ml-2\",\n                                        children: [\n                                            \"(\",\n                                            product.Rating || 0,\n                                            \") \",\n                                            product.TotalReviews || 0,\n                                            \" reviews\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 766,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 753,\n                                columnNumber: 11\n                            }, this),\n                            renderPrice(),\n                            product.ShortDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-sm max-w-none mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: product.ShortDescription\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 777,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 776,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 border-t border-gray-200 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Choose your preferences from the options below.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 788,\n                                        columnNumber: 13\n                                    }, this),\n                                    Object.entries(groupedAttributes).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: Object.entries(groupedAttributes).map((param)=>{\n                                            let [groupId, attributes] = param;\n                                            var _attributes_, _attributes_1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            ((_attributes_ = attributes[0]) === null || _attributes_ === void 0 ? void 0 : _attributes_.DisplayName) || ((_attributes_1 = attributes[0]) === null || _attributes_1 === void 0 ? void 0 : _attributes_1.AttributeName),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 796,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 pl-4\",\n                                                        children: attributes.map((attr)=>{\n                                                            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                                                            const isSelected = !!selectedAttributes[attrKey];\n                                                            const isRadioGroup = attributes.length > 1;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center h-5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: isRadioGroup ? \"radio\" : \"checkbox\",\n                                                                            id: \"attr-\".concat(attrKey),\n                                                                            name: \"attr-group-\".concat(groupId),\n                                                                            className: \"h-4 w-4 \".concat(isRadioGroup ? \"rounded-full\" : \"rounded\", \" border-gray-300 text-primary focus:ring-primary\"),\n                                                                            checked: isSelected,\n                                                                            onChange: (e)=>handleAttributeChange(attr, e.target.checked, isRadioGroup)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                            lineNumber: 810,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 809,\n                                                                        columnNumber: 31\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-3 text-sm\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"attr-\".concat(attrKey),\n                                                                            className: \"font-medium \".concat(isSelected ? \"text-primary\" : \"text-gray-700\"),\n                                                                            children: [\n                                                                                attr.AttributeValueText,\n                                                                                (attr.PriceAdjustment || attr.PriceAdjustment === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-2 text-sm font-normal text-green-600\",\n                                                                                    children: [\n                                                                                        \"(\",\n                                                                                        attr.PriceAdjustmentType === 1 ? \"+\" : \"\",\n                                                                                        \"$\",\n                                                                                        attr.PriceAdjustment,\n                                                                                        \" \",\n                                                                                        attr.PriceAdjustmentType === 2 ? \"%\" : \"\",\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                                    lineNumber: 839,\n                                                                                    columnNumber: 37\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                            lineNumber: 828,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 827,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                ]\n                                                            }, attrKey, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 808,\n                                                                columnNumber: 29\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 801,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, \"attr-group-\".concat(groupId), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 795,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No additional product details available.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 862,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 784,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row sm:items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Quantity:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 871,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between sm:justify-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: decrementQuantity,\n                                                            className: getButtonStyles(\"decrement\"),\n                                                            disabled: quantity <= (product.OrderMinimumQuantity || 1),\n                                                            \"aria-label\": \"Decrease quantity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"h-4 w-4 sm:h-5 sm:w-5\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                fill: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                    lineNumber: 886,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 880,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 874,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CounterDisplay, {}, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 894,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: incrementQuantity,\n                                                            className: getButtonStyles(\"increment\"),\n                                                            disabled: product.OrderMaximumQuantity > 0 ? quantity >= Math.min(product.OrderMaximumQuantity, product.StockQuantity) : quantity >= product.StockQuantity,\n                                                            \"aria-label\": \"Increase quantity\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"h-4 w-4 sm:h-5 sm:w-5\",\n                                                                viewBox: \"0 0 20 20\",\n                                                                fill: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    fillRule: \"evenodd\",\n                                                                    d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                    clipRule: \"evenodd\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                    lineNumber: 916,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 910,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 873,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row sm:items-center gap-2 sm:ml-4\",\n                                                    children: [\n                                                        product.OrderMinimumQuantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Min: \",\n                                                                product.OrderMinimumQuantity\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 927,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.OrderMaximumQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500\",\n                                                            children: [\n                                                                \"Max:\",\n                                                                \" \",\n                                                                Math.min(product.OrderMaximumQuantity, product.StockQuantity)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 933,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 925,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 872,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 870,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 869,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-3 sm:gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none text-sm sm:text-base\",\n                                        disabled: product.StockQuantity <= 0 || addingToCart,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToCart(true);\n                                            try {\n                                                var _product_ProductImagesJson_, _product_ProductImagesJson;\n                                                // Get the first product image or use a placeholder\n                                                const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0 ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL) : \"/placeholder.jpg\";\n                                                // Get selected attributes\n                                                const selectedAttrs = (product.AttributesJson || []).filter((attr)=>selectedAttributes[\"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID)]);\n                                                // Add to cart using the cart context with attributes and adjusted price\n                                                cart.addToCart({\n                                                    id: product.ProductId,\n                                                    name: product.ProductName,\n                                                    price: product.DiscountPrice || product.Price,\n                                                    discountPrice: product.DiscountPrice,\n                                                    image: productImage,\n                                                    originalPrice: product.Price\n                                                }, quantity, selectedAttrs, product.PriceIQD, rate // Pass currency rate as the fifth parameter\n                                                );\n                                                // Show modern toast notification\n                                                (0,_components_ui_modern_toast__WEBPACK_IMPORTED_MODULE_8__.showModernAddToCartToast)({\n                                                    productName: product.ProductName,\n                                                    quantity,\n                                                    productImage: ((_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : (_product_ProductImagesJson_ = _product_ProductImagesJson[0]) === null || _product_ProductImagesJson_ === void 0 ? void 0 : _product_ProductImagesJson_.AttachmentURL) || \"/placeholder.svg\",\n                                                    onViewCart: ()=>{\n                                                        window.location.href = \"/cart\";\n                                                    }\n                                                });\n                                            } catch (error) {\n                                                console.error(\"Error adding to cart:\", error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Failed to add product to cart. Please try again.\");\n                                            } finally{\n                                                setAddingToCart(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1013,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1015,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: addingToCart ? \"Adding...\" : \"Add to Cart\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1017,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 949,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none text-sm sm:text-base sm:flex-initial sm:min-w-[120px]\",\n                                        disabled: addingToWishlist,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToWishlist(true);\n                                            try {\n                                                // Check if product is already in wishlist\n                                                const isAlreadyInWishlist = wishlist.isInWishlist(product.ProductId);\n                                                if (isAlreadyInWishlist) {\n                                                    // Remove from wishlist if already there\n                                                    wishlist.removeFromWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(product.ProductName, \" removed from wishlist\"));\n                                                } else {\n                                                    var _product_ProductImagesJson_, _product_ProductImagesJson;\n                                                    // Add to wishlist\n                                                    const productUrl = \"/product/\".concat(product.ProductId);\n                                                    const imageUrl = ((_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : (_product_ProductImagesJson_ = _product_ProductImagesJson[0]) === null || _product_ProductImagesJson_ === void 0 ? void 0 : _product_ProductImagesJson_.AttachmentURL) || \"/placeholder.svg\";\n                                                    const price = product.DiscountPrice || product.Price;\n                                                    wishlist.addToWishlist(product.ProductId, product.ProductName, productUrl, imageUrl, price);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(product.ProductName, \" added to wishlist\"));\n                                                }\n                                            } catch (error) {\n                                                console.error(\"Error updating wishlist:\", error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Failed to update wishlist. Please try again.\");\n                                            } finally{\n                                                setAddingToWishlist(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1065,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5\",\n                                                fill: product && wishlist.isInWishlist(product.ProductId) ? \"currentColor\" : \"none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1067,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: addingToWishlist ? \"Updating...\" : product && wishlist.isInWishlist(product.ProductId) ? \"Remove\" : \"Wishlist\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1076,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1021,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground text-sm sm:text-base sm:flex-initial sm:min-w-[100px]\",\n                                        onClick: ()=>{\n                                            if (navigator.share) {\n                                                navigator.share({\n                                                    title: (product === null || product === void 0 ? void 0 : product.MetaTitle) || (product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    text: (product === null || product === void 0 ? void 0 : product.MetaDescription) || \"Check out this product: \".concat(product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    url: window.location.href\n                                                }).catch((err)=>console.error(\"Error sharing:\", err));\n                                            } else {\n                                                // Fallback - copy to clipboard\n                                                navigator.clipboard.writeText(window.location.href);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Product link copied to clipboard\");\n                                            }\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1107,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: \"Share\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1108,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1086,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 947,\n                                columnNumber: 11\n                            }, this),\n                            product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-3\",\n                                        children: \"Product Tags\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1115,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs bg-white/70 hover:bg-white transition-colors\",\n                                                children: keyword.trim()\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1118,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1116,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1114,\n                                columnNumber: 13\n                            }, this),\n                            product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1134,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"About This Product\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 leading-relaxed\",\n                                        children: product.MetaDescription\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1137,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1132,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 749,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 739,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                    defaultValue: \"description\",\n                    className: \"w-full\",\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                            className: \"grid w-full grid-cols-3 mb-6 gap-2 bg-transparent p-0 h-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"description\",\n                                    className: \"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === \"description\" ? primaryColor : \"rgb(209 213 219)\",\n                                        color: activeTab === \"description\" ? primaryTextColor : \"rgb(55 65 81)\",\n                                        transform: activeTab === \"description\" ? \"scale(1.05)\" : \"scale(1)\",\n                                        boxShadow: activeTab === \"description\" ? \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\" : \"none\",\n                                        borderColor: activeTab === \"description\" ? primaryColor : \"transparent\"\n                                    },\n                                    children: \"Overview\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"reviews\",\n                                    className: \"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === \"reviews\" ? primaryColor : \"rgb(209 213 219)\",\n                                        color: activeTab === \"reviews\" ? primaryTextColor : \"rgb(55 65 81)\",\n                                        transform: activeTab === \"reviews\" ? \"scale(1.05)\" : \"scale(1)\",\n                                        boxShadow: activeTab === \"reviews\" ? \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\" : \"none\",\n                                        borderColor: activeTab === \"reviews\" ? primaryColor : \"transparent\"\n                                    },\n                                    children: \"Specifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"shipping\",\n                                    className: \"rounded-lg px-8 py-4 text-lg font-semibold transition-all duration-300 border-2 border-transparent data-[state=inactive]:bg-gray-300 data-[state=inactive]:text-gray-700 data-[state=inactive]:scale-100 hover:bg-gray-400 hover:text-white hover:scale-102\",\n                                    style: {\n                                        backgroundColor: activeTab === \"shipping\" ? primaryColor : \"rgb(209 213 219)\",\n                                        color: activeTab === \"shipping\" ? primaryTextColor : \"rgb(55 65 81)\",\n                                        transform: activeTab === \"shipping\" ? \"scale(1.05)\" : \"scale(1)\",\n                                        boxShadow: activeTab === \"shipping\" ? \"0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)\" : \"none\",\n                                        borderColor: activeTab === \"shipping\" ? primaryColor : \"transparent\"\n                                    },\n                                    children: \"Reviews\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1197,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1153,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"description\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Product Overview\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1224,\n                                        columnNumber: 15\n                                    }, this),\n                                    product.FullDescription ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none\",\n                                        dangerouslySetInnerHTML: {\n                                            __html: product.FullDescription\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1226,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: product.ShortDescription || \"This is a high-quality medical product designed to meet professional standards.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1232,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900 mb-2\",\n                                                                children: \"Key Features\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1238,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-sm text-gray-700 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Professional grade quality\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1242,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Durable construction\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1243,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Easy to use\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1244,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Reliable performance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1245,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1241,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1237,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900 mb-2\",\n                                                                children: \"Benefits\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1249,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-sm text-gray-700 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Enhanced efficiency\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1253,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Cost-effective solution\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1254,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Long-lasting durability\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1255,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Professional results\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 1256,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1252,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1248,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1236,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1231,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1223,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"reviews\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Product Specifications\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_specifications__WEBPACK_IMPORTED_MODULE_13__.ProductSpecifications, {\n                                        attributes: product.AttributesJson || []\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1273,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1269,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1265,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"shipping\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Customer Reviews\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            1,\n                                                            2,\n                                                            3,\n                                                            4,\n                                                            5\n                                                        ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Heart_Share2_ShoppingCart_Star_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"w-6 h-6 \".concat(star <= Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                            }, star, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1289,\n                                                                columnNumber: 23\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1287,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: ((_product_Rating = product.Rating) === null || _product_Rating === void 0 ? void 0 : _product_Rating.toFixed(1)) || \"0.0\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1300,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \" \",\n                                                            \"out of 5\",\n                                                            product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \" \",\n                                                                    \"• \",\n                                                                    product.TotalReviews,\n                                                                    \" review\",\n                                                                    product.TotalReviews !== 1 ? \"s\" : \"\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 1305,\n                                                                columnNumber: 23\n                                                            }, this) : null\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1299,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_product_reviews_display__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    productId: product.ProductId,\n                                                    showTitle: false\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1315,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 1314,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 1285,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1283,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1279,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 1147,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 1146,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n        lineNumber: 709,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDetailsClient, \"fhF87DY2zm3u0DvUJErcoKedGAA=\", false, function() {\n    return [\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__.useWishlist,\n        _contexts_currency_context__WEBPACK_IMPORTED_MODULE_11__.useCurrency,\n        _contexts_settings_context__WEBPACK_IMPORTED_MODULE_12__.useSettings\n    ];\n});\n_c = ProductDetailsClient;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductDetailsClient);\nvar _c;\n$RefreshReg$(_c, \"ProductDetailsClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/product-details-client.tsx\n"));

/***/ })

});