(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2963],{6101:(e,t,s)=>{"use strict";s.d(t,{s:()=>n,t:()=>a});var r=s(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let s=!1,r=e.map(e=>{let r=l(e,t);return s||"function"!=typeof r||(s=!0),r});if(s)return()=>{for(let t=0;t<r.length;t++){let s=r[t];"function"==typeof s?s():l(e[t],null)}}}}function n(...e){return r.useCallback(a(...e),e)}},19946:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(12115);let l=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&s.indexOf(e)===t).join(" ")};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:n=24,strokeWidth:i=2,absoluteStrokeWidth:c,className:o="",children:d,iconNode:u,...m}=e;return(0,r.createElement)("svg",{ref:t,...a,width:n,height:n,stroke:s,strokeWidth:c?24*Number(i)/Number(n):i,className:l("lucide",o),...m},[...u.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(d)?d:[d]])}),i=(e,t)=>{let s=(0,r.forwardRef)((s,a)=>{let{className:i,...c}=s;return(0,r.createElement)(n,{ref:a,iconNode:t,className:l("lucide-".concat(e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),i),...c})});return s.displayName="".concat(e),s}},22097:(e,t,s)=>{Promise.resolve().then(s.bind(s,33834))},33834:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(95155),l=s(12115),a=s(97168),n=s(89852),i=s(88482),c=s(49504),o=s(69026);function d(){let[e,t]=(0,l.useState)("39"),[s,d]=(0,l.useState)(!1);return(0,r.jsx)("div",{className:"container mx-auto p-8",children:(0,r.jsxs)(i.Zp,{className:"max-w-4xl mx-auto p-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Product Reviews Test"}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)("label",{className:"block text-sm font-medium",children:"Product ID:"}),(0,r.jsx)(n.p,{type:"number",value:e,onChange:e=>t(e.target.value),placeholder:"Enter product ID",className:"w-32"}),(0,r.jsx)(a.$,{onClick:()=>d(!s),variant:"outline",children:s?"Hide Review Form":"Write Review"})]}),s&&(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Write a Review"}),(0,r.jsx)(o.A,{productId:parseInt(e),productName:"Test Product",onReviewSubmitted:()=>{d(!1),window.location.reload()}})]}),(0,r.jsxs)("div",{className:"border-t pt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Product Reviews"}),(0,r.jsx)(c.A,{productId:parseInt(e),showTitle:!0})]})]}),(0,r.jsxs)("div",{className:"mt-8 p-4 bg-blue-50 rounded",children:[(0,r.jsx)("h3",{className:"font-semibold mb-2",children:"API Endpoints Used:"}),(0,r.jsxs)("ul",{className:"text-sm space-y-1",children:[(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Get Reviews:"})," POST /api/v1/dynamic/dataoperation/get-product-reviews"]}),(0,r.jsxs)("li",{children:[(0,r.jsx)("strong",{children:"Insert Review:"})," POST /api/v1/dynamic/dataoperation/Insert-Product-Review"]})]}),(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Test Instructions:"}),(0,r.jsxs)("ol",{className:"text-sm list-decimal list-inside space-y-1",children:[(0,r.jsx)("li",{children:"Enter a product ID (default: 39)"}),(0,r.jsx)("li",{children:"View existing reviews for that product"}),(0,r.jsx)("li",{children:'Click "Write Review" to add a new review'}),(0,r.jsx)("li",{children:"Fill out the review form and submit"}),(0,r.jsx)("li",{children:"Check if the review appears (may need approval)"})]})]})]})]})})}},49504:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(95155),l=s(12115),a=s(88482),n=s(97168),i=s(27737),c=s(38564),o=s(71366),d=s(71007),u=s(70333);function m(e){let{productId:t,showTitle:s=!0}=e,[m,h]=(0,l.useState)([]),[p,x]=(0,l.useState)(!0),[f,v]=(0,l.useState)(null),[j,w]=(0,l.useState)(!1);(0,l.useEffect)(()=>{t&&g()},[t]);let g=async()=>{x(!0),v(null);try{console.log("\uD83D\uDD0D Fetching product reviews for ProductId:",t);let e=await fetch("/api/reviews/get-product-reviews",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json"},body:JSON.stringify({requestParameters:{ProductId:t,recordValueJson:"[]"}})}),s=await e.json();if(console.log("\uD83D\uDD0D Product reviews API response:",s),e.ok&&s&&!s.errorMessage){let e=s.data||s;if(console.log("\uD83D\uDD0D Raw reviews data:",e),"string"==typeof e)try{e=JSON.parse(e),console.log("\uD83D\uDD0D Parsed reviews data from string:",e)}catch(t){console.error("❌ Failed to parse reviews data string:",t),e=[]}if(Array.isArray(e)&&e.length>0&&e[0].DATA)try{let t=JSON.parse(e[0].DATA);console.log("\uD83D\uDD0D Parsed inner DATA:",t),Array.isArray(t)?h(t.filter(e=>!1!==e.IsApproved)):h([])}catch(e){console.error("❌ Failed to parse inner DATA:",e),h([])}else Array.isArray(e)?(console.log("\uD83D\uDD0D Using reviews data as array:",e),e.length>0&&console.log("\uD83D\uDD0D Sample review date fields:",{ReviewDate:e[0].ReviewDate,CreatedOn:e[0].CreatedOn,availableFields:Object.keys(e[0])}),h(e.filter(e=>!1!==e.IsApproved))):(console.log("ℹ️ No valid reviews data found"),h([]))}else console.error("❌ API error:",s),v((null==s?void 0:s.errorMessage)||(null==s?void 0:s.message)||"Failed to load reviews")}catch(e){console.error("❌ Error fetching product reviews:",e),v("Failed to load reviews")}finally{x(!1)}},y=e=>(0,r.jsx)("div",{className:"flex items-center gap-1",children:[1,2,3,4,5].map(t=>(0,r.jsx)(c.A,{className:"h-4 w-4 ".concat(t<=e?"fill-yellow-400 text-yellow-400":"text-gray-300")},t))}),N=j?m:m.slice(0,3),b=m.length>0?m.reduce((e,t)=>e+t.Rating,0)/m.length:0;return p?(0,r.jsxs)("div",{className:"space-y-4",children:[s&&(0,r.jsx)(i.E,{className:"h-6 w-48"}),[1,2,3].map(e=>(0,r.jsx)(a.Zp,{className:"p-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(i.E,{className:"h-4 w-20"}),(0,r.jsx)(i.E,{className:"h-4 w-24"})]}),(0,r.jsx)(i.E,{className:"h-4 w-full"}),(0,r.jsx)(i.E,{className:"h-16 w-full"})]})},e))]}):f?(0,r.jsxs)(a.Zp,{className:"p-6 text-center",children:[(0,r.jsx)(o.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-gray-500",children:f})]}):0===m.length?(0,r.jsxs)(a.Zp,{className:"p-6 text-center",children:[(0,r.jsx)(o.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,r.jsx)("h3",{className:"font-medium mb-1",children:"No Reviews Yet"}),(0,r.jsx)("p",{className:"text-gray-500 text-sm",children:"Be the first to review this product!"})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[s&&(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Customer Reviews"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[y(Math.round(b)),(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[b.toFixed(1)," (",m.length," review",1!==m.length?"s":"",")"]})]})]}),(0,r.jsx)("div",{className:"space-y-4",children:N.map(e=>(0,r.jsx)(a.Zp,{className:"p-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:(0,r.jsx)(d.A,{className:"h-4 w-4 text-primary"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:e.ReviewerName}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[y(e.Rating),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{if(!e)return"N/A";try{return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}catch(t){return e}})(e.ReviewDate||e.CreatedOn||"")})]})]})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium mb-1",children:e.Title}),(0,r.jsx)("p",{className:"text-gray-700 text-sm leading-relaxed",children:e.Body})]}),e.HelpfulCount&&e.HelpfulCount>0&&(0,r.jsxs)("div",{className:"flex items-center gap-1 text-xs text-gray-500",children:[(0,r.jsx)(u.A,{className:"h-3 w-3"}),(0,r.jsxs)("span",{children:[e.HelpfulCount," people found this helpful"]})]})]})},e.ReviewID))}),m.length>3&&(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)(n.$,{variant:"outline",onClick:()=>w(!j),children:j?"Show Less":"Show All ".concat(m.length," Reviews")})})]})}},70333:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("ThumbsUp",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]])},71366:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},74466:(e,t,s)=>{"use strict";s.d(t,{F:()=>n});var r=s(52596);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=r.$,n=(e,t)=>s=>{var r;if((null==t?void 0:t.variants)==null)return a(e,null==s?void 0:s.class,null==s?void 0:s.className);let{variants:n,defaultVariants:i}=t,c=Object.keys(n).map(e=>{let t=null==s?void 0:s[e],r=null==i?void 0:i[e];if(null===t)return null;let a=l(t)||l(r);return n[e][a]}),o=s&&Object.entries(s).reduce((e,t)=>{let[s,r]=t;return void 0===r||(e[s]=r),e},{});return a(e,c,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:s,className:r,...l}=t;return Object.entries(l).every(e=>{let[t,s]=e;return Array.isArray(s)?s.includes({...i,...o}[t]):({...i,...o})[t]===s})?[...e,s,r]:e},[]),null==s?void 0:s.class,null==s?void 0:s.className)}},99708:(e,t,s)=>{"use strict";s.d(t,{DX:()=>i,Dc:()=>o,TL:()=>n});var r=s(12115),l=s(6101),a=s(95155);function n(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:s,...a}=e;if(r.isValidElement(s)){var n;let e,i,c=(n=s,(i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(i=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),o=function(e,t){let s={...t};for(let r in t){let l=e[r],a=t[r];/^on[A-Z]/.test(r)?l&&a?s[r]=(...e)=>{let t=a(...e);return l(...e),t}:l&&(s[r]=l):"style"===r?s[r]={...l,...a}:"className"===r&&(s[r]=[l,a].filter(Boolean).join(" "))}return{...e,...s}}(a,s.props);return s.type!==r.Fragment&&(o.ref=t?(0,l.t)(t,c):c),r.cloneElement(s,o)}return r.Children.count(s)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=r.forwardRef((e,s)=>{let{children:l,...n}=e,i=r.Children.toArray(l),c=i.find(d);if(c){let e=c.props.children,l=i.map(t=>t!==c?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...n,ref:s,children:r.isValidElement(e)?r.cloneElement(e,void 0,l):null})}return(0,a.jsx)(t,{...n,ref:s,children:l})});return s.displayName=`${e}.Slot`,s}var i=n("Slot"),c=Symbol("radix.slottable");function o(e){let t=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=c,t}function d(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}}},e=>{e.O(0,[8320,4277,3464,8816,9972,8441,5964,7358],()=>e(e.s=22097)),_N_E=e.O()}]);