"use strict";exports.id=8026,exports.ids=[8026],exports.modules={15616:(a,b,c)=>{c.d(b,{T:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,f.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...b}));g.displayName="Textarea"},27900:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},39956:(a,b,c)=>{c.d(b,{A:()=>n});var d=c(60687),e=c(43210),f=c(24934),g=c(68988),h=c(15616),i=c(55192),j=c(64398),k=c(27900),l=c(832),m=c(77567);function n({productId:a,productName:b,onReviewSubmitted:c}){let{user:n,token:o}=(0,l.J)(),[p,q]=(0,e.useState)(0),[r,s]=(0,e.useState)(0),[t,u]=(0,e.useState)(""),[v,w]=(0,e.useState)(""),[x,y]=(0,e.useState)(!1),[z,A]=(0,e.useState)(null),[B,C]=(0,e.useState)(!0),[D,E]=(0,e.useState)(!1),F=()=>{s(0)},G=async()=>{if(!p||!t.trim()||!v.trim())return void m.default.fire({icon:"warning",title:"Missing Information",text:"Please provide a rating, title, and review text.",confirmButtonColor:"#3085d6"});if(!n?.UserID&&!n?.UserId||!o)return void m.default.fire({icon:"error",title:"Authentication Required",text:"Please log in to submit a review.",confirmButtonColor:"#3085d6"});y(!0);try{let b={requestParameters:{ProductId:a,ReviewTitle:t.trim(),ReviewBody:v.trim(),ReviewRating:p,ReviewerName:`${n.FirstName||""} ${n.LastName||""}`.trim()||n.Email||"Anonymous",ReviewerEmail:n.Email||n.EmailAddress||""}};console.log("\uD83D\uDD0D Review submission: Sending review data:",b),console.log("\uD83D\uDD0D Review submission: User data:",{userId:n.UserID||n.UserId,email:n.Email||n.EmailAddress,name:`${n.FirstName||""} ${n.LastName||""}`.trim()});let d=await fetch("/api/reviews/insert",{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json",Authorization:`Bearer ${o}`},body:JSON.stringify(b)}),e=await d.json();if(console.log("\uD83D\uDD0D Review submission: API response status:",d.status),console.log("\uD83D\uDD0D Review submission: Response data:",e),d.ok&&e&&!e.errorMessage)await m.default.fire({icon:"success",title:"Review Submitted!",text:"Thank you for your review. It will be visible after approval.",confirmButtonColor:"#10b981"}),q(0),u(""),w(""),c&&c();else throw Error(e?.errorMessage||e?.message||"Failed to submit review")}catch(a){console.error("Error submitting review:",a),m.default.fire({icon:"error",title:"Submission Failed",text:a instanceof Error?a.message:"Failed to submit review. Please try again.",confirmButtonColor:"#ef4444"})}finally{y(!1)}};return B?(0,d.jsx)(i.Zp,{className:"p-6",children:(0,d.jsx)("div",{className:"space-y-4",children:(0,d.jsxs)("div",{className:"animate-pulse",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/3 mb-2"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-2/3"})]})})}):D&&z?(0,d.jsx)(i.Zp,{className:"p-6",children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Your Review"}),(0,d.jsxs)("p",{className:"text-sm text-muted-foreground",children:["You have already reviewed ",b]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)("div",{className:"flex items-center",children:[1,2,3,4,5].map(a=>(0,d.jsx)(j.A,{className:`h-4 w-4 ${a<=(z.ReviewRating||z.Rating||0)?"fill-yellow-400 text-yellow-400":"text-gray-300"}`},a))}),(0,d.jsxs)("span",{className:"text-sm text-gray-600",children:[z.ReviewRating||z.Rating||0,"/5"]})]}),z.ReviewTitle&&(0,d.jsx)("h4",{className:"font-medium mb-2",children:z.ReviewTitle}),z.ReviewBody&&(0,d.jsx)("p",{className:"text-gray-700 text-sm",children:z.ReviewBody}),(0,d.jsxs)("div",{className:"mt-3 text-xs text-gray-500",children:["Reviewed by ",z.ReviewerName||"You",(z.ReviewDate||z.CreatedOn)&&(0,d.jsxs)("span",{children:[" on ",new Date(z.ReviewDate||z.CreatedOn).toLocaleDateString()]})]})]})]})}):(0,d.jsx)(i.Zp,{className:"p-6",children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Write a Review"}),(0,d.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Share your experience with ",b]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Rating *"}),(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[[1,2,3,4,5].map(a=>(0,d.jsx)("button",{type:"button",onClick:()=>{q(a)},onMouseEnter:()=>{s(a)},onMouseLeave:F,className:"p-1 transition-colors",disabled:x,children:(0,d.jsx)(j.A,{className:`h-6 w-6 ${a<=(r||p)?"fill-yellow-400 text-yellow-400":"text-gray-300"}`})},a)),(0,d.jsx)("span",{className:"ml-2 text-sm text-muted-foreground",children:p>0&&`${p} star${1!==p?"s":""}`})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Review Title *"}),(0,d.jsx)(g.p,{type:"text",value:t,onChange:a=>u(a.target.value),placeholder:"Summarize your experience",disabled:x,maxLength:100}),(0,d.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:[t.length,"/100 characters"]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{className:"block text-sm font-medium mb-2",children:"Your Review *"}),(0,d.jsx)(h.T,{value:v,onChange:a=>w(a.target.value),placeholder:"Tell others about your experience with this product...",disabled:x,rows:4,maxLength:500}),(0,d.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:[v.length,"/500 characters"]})]}),(0,d.jsx)(f.$,{onClick:G,disabled:x||!p||!t.trim()||!v.trim(),className:"w-full",children:x?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Submitting..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(k.A,{className:"h-4 w-4 mr-2"}),"Submit Review"]})}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground text-center",children:"Reviews are moderated and will be published after approval."})]})})}},55192:(a,b,c)=>{c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h,wL:()=>l});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b}));l.displayName="CardFooter"},64398:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},68988:(a,b,c)=>{c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"},71463:(a,b,c)=>{c.d(b,{E:()=>f});var d=c(60687),e=c(96241);function f({className:a,...b}){return(0,d.jsx)("div",{className:(0,e.cn)("animate-pulse rounded-md bg-muted",a),...b})}}};