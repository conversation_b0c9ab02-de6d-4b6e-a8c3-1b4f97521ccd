(()=>{var a={};a.id=8279,a.ids=[8279],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5466:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>y});var d=c(60687),e=c(43210),f=c(16189),g=c(93283),h=c(832),i=c(15991),j=c(79936),k=c(77080),l=c(24934),m=c(55192),n=c(39390),o=c(21812),p=c(34993),q=c(85814),r=c.n(q),s=c(98712),t=c(58869),u=c(85778),v=c(23928),w=c(64398),x=c(77567);function y(){let{t:a,primaryColor:b}=(0,k.t)(),{user:c,isLoggedIn:q,token:y,isLoading:z}=(0,h.J)(),{items:A,total:B,subtotal:C,totalIQD:D,subtotalIQD:E,clearCart:F}=(0,g._)(),{appliedCoupon:G}=(0,i.Y)(),{formatIQD:H,formatUSD:I}=(0,j.H)(),J=(0,f.useRouter)(),[K,L]=(0,e.useState)(null),[M,N]=(0,e.useState)([]),[O,P]=(0,e.useState)(!0),[Q,R]=(0,e.useState)(!1),[S,T]=(0,e.useState)(!1),[U,V]=(0,e.useState)({address:"",cityId:"",countryId:"107",zipCode:""}),[W,X]=(0,e.useState)([]),[Y,Z]=(0,e.useState)([]),[$,_]=(0,e.useState)(!1),[aa,ab]=(0,e.useState)(!1),[ac,ad]=(0,e.useState)([]),[ae,af]=(0,e.useState)(null),[ag,ah]=(0,e.useState)(!1),[ai,aj]=(0,e.useState)(!1),ak=c?.Pointno||0,al=S?ak:0,am=Math.round(1500*al),an=Math.max(0,B-(G?G.discount:0)-al),ao=Math.max(0,D-(G?Math.round(1500*G.discount):0)-am),ap=async a=>{if(a.preventDefault(),!q)return void x.default.fire({title:"Login Required",text:"Please login to complete your purchase",icon:"info",showCancelButton:!0,confirmButtonText:"Login",cancelButtonText:"Cancel"}).then(a=>{a.isConfirmed&&J.push("/login?redirect=checkout")});if(!K)return void x.default.fire({title:"Error",text:"Please select a payment method",icon:"error"});if(!ae)return void x.default.fire({title:"Address Required",text:"Please select a delivery address to continue",icon:"error"});if(!U.address)return void x.default.fire({title:"Error",text:"Please provide a shipping address",icon:"error"});if(!U.countryId)return void x.default.fire({title:"Error",text:"Please select a country",icon:"error"});if(!U.cityId)return void x.default.fire({title:"Error",text:"Please select a city",icon:"error"});try{x.default.fire({title:"Processing",text:"Please wait while we process your order...",allowOutsideClick:!1,didOpen:()=>{x.default.showLoading()}});let a=A.map(a=>({ProductId:a.id,Quantity:a.quantity,Price:parseFloat((a.discountPrice||a.price).toFixed(2)),ItemPriceTotal:parseFloat(((a.discountPrice||a.price)*a.quantity).toFixed(2)),ItemSubTotal:parseFloat(((a.discountPrice||a.price)*a.quantity).toFixed(2)),IsShippingFree:!0,ShippingChargesTotal:0,OrderItemAttributeChargesTotal:0,DiscountId:G?.discountId||null,CouponCode:G?.code||"",DiscountedPrice:a.discountPrice?parseFloat(a.discountPrice.toFixed(2)):null,OrderItemDiscountTotal:a.discountPrice?parseFloat(((a.price-a.discountPrice)*a.quantity).toFixed(2)):0,IsDiscountCalculated:!1,ProductAllSelectedAttributes:JSON.stringify(a.attributes||[])})),d=M.find(a=>a.PaymentMethodID===K),e=d?.PaymentMethodName||"Unknown Payment Method";if(!c)throw Error("User not found. Please login again.");let f=W.find(a=>a.CountryID.toString()===U.countryId),g=Y.find(a=>a.CityID.toString()===U.cityId),h=f?.CountryName||f?.Name||"Unknown Country",i=g?.CityName||g?.Name||"Unknown City",j={OrderNote:`Order from WEB app${G?` - Coupon: ${G.code}`:""}${S?` - Credit used: ${al}`:""} - Payment: ${e} - Address: ${U.address}, ${h}, ${i}`,cartJsonData:JSON.stringify(a),OrderTotal:parseFloat(an.toFixed(2)),CouponCode:G?.code||"",Description:`Order placed via WEB checkout - Payment Method: ${e}`,StripeStatus:"",StripeResponseJson:"",StripeBalanceTransactionId:"",StripeChargeId:"",PayPalResponseJson:"",CurrencyCode:"USD",PaymentMethod:K,Point:S&&al>0?al:null,addressid:ae||1};console.log("Order data being sent:",j);let k={"Content-Type":"application/json",Accept:"application/json"};y&&(k.Authorization=`Bearer ${y}`,console.log("\uD83D\uDD10 Added JWT token to order placement request"));let l=await fetch("/api/orders/post-order",{method:"POST",headers:k,body:JSON.stringify(j)}),m=await l.json();if(console.log("Order response:",m),l.ok&&m.data){let a=m.data;if(a&&("Order Placed Successfully"===a.message||a.orderID))x.default.fire({title:"\uD83C\uDF89 Order Placed Successfully!",html:`
              <div style="text-align: center; padding: 20px;">
                <div style="font-size: 48px; margin-bottom: 20px;">✅</div>
                <h3 style="color: #10B981; margin-bottom: 15px;">Thank you for your order!</h3>
                ${a.orderID?`<p><strong>Order ID:</strong> #${a.orderID}</p>`:""}
                ${a.orderNumber?`<p><strong>Order Number:</strong> ${a.orderNumber}</p>`:""}
                <p style="margin-top: 15px; color: #6B7280;">
                  <strong>Total:</strong> $${an.toFixed(2)}
                </p>
                <p style="color: #6B7280;">
                  We will contact you soon.
                </p>
              </div>
            `,icon:"success",confirmButtonText:"View My Orders",confirmButtonColor:b,showCancelButton:!0,cancelButtonText:"Continue Shopping",allowOutsideClick:!1,showCloseButton:!1,customClass:{popup:"swal-wide",actions:"swal-actions-visible"},didOpen:()=>{let a=document.createElement("style");a.textContent=`
                .swal-wide {
                  width: 600px !important;
                  max-width: 90vw !important;
                }
                .swal2-html-container {
                  font-family: inherit !important;
                }
                .swal-actions-visible .swal2-actions {
                  display: flex !important;
                  justify-content: center !important;
                  gap: 10px !important;
                  margin-top: 20px !important;
                  visibility: visible !important;
                  opacity: 1 !important;
                }
                .swal2-confirm, .swal2-cancel {
                  display: inline-block !important;
                  margin: 0 5px !important;
                  visibility: visible !important;
                  opacity: 1 !important;
                  background-color: #6B7280 !important;
                  color: white !important;
                  border: none !important;
                  padding: 10px 20px !important;
                  border-radius: 6px !important;
                  cursor: pointer !important;
                }
                .swal2-confirm {
                  background-color: ${b} !important;
                }
                .swal2-cancel:hover, .swal2-confirm:hover {
                  opacity: 0.9 !important;
                  transform: translateY(-1px) !important;
                }
              `,document.head.appendChild(a)}}).then(a=>{F(),localStorage.removeItem("usePoints"),a.isConfirmed?J.push("/orders"):J.push("/")});else throw Error(a?.message||"Order placement failed")}else throw console.error("Order placement failed. Response:",m),Error(m?.error||m?.details?.ErrorMessage||"Order placement failed")}catch(b){console.error("Error placing order:",b);let a=b instanceof Error?b.message:"There was an error processing your order. Please try again.";x.default.fire({title:"Order Failed",text:a,icon:"error",confirmButtonText:"Try Again",footer:"If the problem persists, please contact support."})}};return z?(0,d.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):0===A.length?(0,d.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("h1",{className:"text-2xl font-bold mb-4",children:"Your cart is empty"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:"Add items to your cart to proceed to checkout"}),(0,d.jsx)(l.$,{asChild:!0,children:(0,d.jsx)(r(),{href:"/",children:"Continue Shopping"})})]})}):q?(0,d.jsxs)("div",{className:"container mx-auto py-4 sm:py-6 lg:py-8 px-3 sm:px-4",children:[(0,d.jsx)(p.Qp,{className:"mb-3 sm:mb-4 lg:mb-6",children:(0,d.jsxs)(p.AB,{children:[(0,d.jsx)(p.J5,{children:(0,d.jsx)(p.w1,{asChild:!0,children:(0,d.jsx)(r(),{href:"/",children:"Home"})})}),(0,d.jsx)(p.tH,{}),(0,d.jsx)(p.J5,{children:(0,d.jsx)(p.w1,{asChild:!0,children:(0,d.jsx)(r(),{href:"/cart",children:"Cart"})})}),(0,d.jsx)(p.tH,{}),(0,d.jsx)(p.J5,{children:(0,d.jsx)(p.tJ,{children:"Checkout"})})]})}),(0,d.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-3 gap-3 lg:gap-8",children:[(0,d.jsx)("div",{className:"lg:col-span-2 order-1 lg:order-none",children:(0,d.jsxs)(m.Zp,{children:[(0,d.jsxs)("form",{id:"checkout-form",onSubmit:ap,className:"p-3 sm:p-4 lg:p-6 space-y-3 sm:space-y-4 lg:space-y-6",children:[(0,d.jsxs)("div",{className:"space-y-2 sm:space-y-3",children:[(0,d.jsxs)("h2",{className:"text-base sm:text-lg font-bold flex items-center gap-2",children:[(0,d.jsx)(t.A,{className:"h-4 w-4"}),"Account Information"]}),(0,d.jsx)("div",{className:"bg-gray-50 rounded-lg p-2 sm:p-3",children:(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-2 sm:gap-3",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Name"}),(0,d.jsxs)("p",{className:"font-medium break-words",children:[c?.FirstName," ",c?.LastName]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Email"}),(0,d.jsx)("p",{className:"font-medium break-all",children:c?.Email||c?.EmailAddress})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"Phone"}),(0,d.jsx)("p",{className:"font-medium break-words",children:c?.PhoneNumber||c?.PhoneNo||c?.MobileNo})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("p",{className:"text-sm text-muted-foreground",children:"User ID"}),(0,d.jsxs)("p",{className:"font-medium",children:["#",c?.UserID||c?.UserId]})]})]})})]}),(0,d.jsxs)("div",{className:"space-y-2 sm:space-y-3",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0",children:[(0,d.jsx)("h2",{className:"text-base sm:text-lg font-bold",children:"Shipping Address"}),(0,d.jsx)(l.$,{type:"button",variant:"outline",size:"sm",onClick:()=>J.push("/addresses"),className:"text-xs sm:text-sm w-full sm:w-auto",children:"Manage Addresses"})]}),ag&&(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(n.J,{children:"Loading saved addresses..."}),(0,d.jsx)("div",{className:"space-y-2",children:[1,2,3].map(a=>(0,d.jsx)("div",{className:"p-3 border rounded-lg animate-pulse",children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16"}),(0,d.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,d.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]}),(0,d.jsx)("div",{className:"w-4 h-4 bg-gray-200 rounded-full"})]})},a))})]}),!ag&&ac.length>0&&(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsx)(n.J,{className:"text-sm sm:text-base font-semibold",children:"Select Delivery Address"}),(0,d.jsxs)("span",{className:"text-xs sm:text-sm text-gray-500",children:[ac.length," saved address",1!==ac.length?"es":""]})]}),(0,d.jsx)("div",{className:"space-y-2",children:ac.map(a=>(0,d.jsxs)("div",{className:`relative p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${ae===a.AddressID?"border-primary bg-primary/5 shadow-sm":"border-gray-200 hover:border-gray-300"}`,onClick:()=>(a=>{let b=ac.find(b=>b.AddressID===a);b&&(af(a),V({address:`${b.AddressLineOne}${b.AddressLineTwo?", "+b.AddressLineTwo:""}`,cityId:b.CityID?.toString()||"",countryId:b.CountryID.toString(),zipCode:b.PostalCode||""}))})(a.AddressID),children:[ae===a.AddressID&&(0,d.jsx)("div",{className:"absolute top-3 right-3",children:(0,d.jsx)("div",{className:"w-6 h-6 bg-primary rounded-full flex items-center justify-center",children:(0,d.jsx)("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})}),(0,d.jsxs)("div",{className:"flex items-start gap-3",children:[(0,d.jsx)("div",{className:"flex-shrink-0 mt-1",children:(0,d.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:1===a.AddressTypeID?(0,d.jsx)("svg",{className:"w-4 h-4 text-primary",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{d:"M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"})}):2===a.AddressTypeID?(0,d.jsx)("svg",{className:"w-4 h-4 text-primary",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{d:"M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"})}):3===a.AddressTypeID?(0,d.jsx)("svg",{className:"w-4 h-4 text-primary",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"})}):4===a.AddressTypeID?(0,d.jsxs)("svg",{className:"w-4 h-4 text-primary",fill:"currentColor",viewBox:"0 0 20 20",children:[(0,d.jsx)("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),(0,d.jsx)("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})]}):(0,d.jsx)("svg",{className:"w-4 h-4 text-primary",fill:"currentColor",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fillRule:"evenodd",d:"M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z",clipRule:"evenodd"})})})}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary break-words",children:a.AddressTypeName||(1===a.AddressTypeID?"Home":2===a.AddressTypeID?"Billing":3===a.AddressTypeID?"Shipping":4===a.AddressTypeID?"Mailing":"Other")}),a.IsDefault&&(0,d.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"Default"})]}),(0,d.jsx)("p",{className:"font-medium text-gray-900 mb-1 break-words",children:a.AddressLineOne}),a.AddressLineTwo&&(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-1 break-words",children:a.AddressLineTwo}),(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,d.jsx)("span",{children:a.CityName||"City"}),(0,d.jsx)("span",{children:"•"}),(0,d.jsx)("span",{children:a.CountryName}),a.PostalCode&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("span",{children:"•"}),(0,d.jsx)("span",{children:a.PostalCode})]})]})]})]})]},a.AddressID))})]}),!ag&&0===ac.length&&(0,d.jsxs)("div",{className:"text-center py-8 border-2 border-dashed border-gray-200 rounded-lg",children:[(0,d.jsx)("div",{className:"w-12 h-12 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center",children:(0,d.jsxs)("svg",{className:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]})}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No saved addresses"}),(0,d.jsx)("p",{className:"text-gray-500 mb-4",children:"Please add a delivery address in your account settings to continue with checkout."}),(0,d.jsx)(l.$,{asChild:!0,variant:"outline",children:(0,d.jsx)(r(),{href:"/addresses",children:"Manage Addresses"})})]})]}),(0,d.jsxs)("div",{className:"space-y-2 sm:space-y-3",children:[(0,d.jsx)("h2",{className:"text-base sm:text-lg font-bold",children:"Payment Method"}),O?(0,d.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-3",children:[...Array(6)].map((a,b)=>(0,d.jsxs)("div",{className:"flex flex-col items-center p-2 sm:p-4 border rounded-md",children:[(0,d.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-200 animate-pulse mb-3"}),(0,d.jsx)("div",{className:"h-4 w-20 bg-gray-200 rounded animate-pulse mb-2"}),(0,d.jsx)("div",{className:"w-4 h-4 rounded-full border bg-gray-200 animate-pulse"})]},b))}):(0,d.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 gap-2 sm:gap-3",children:M.map(a=>(0,d.jsxs)("div",{className:"flex flex-col items-center p-2 sm:p-4 border rounded-md cursor-pointer hover:bg-muted/50 transition-colors relative",onClick:()=>L(a.PaymentMethodID),style:{borderColor:K===a.PaymentMethodID?b:""},children:[(0,d.jsx)("div",{className:"absolute top-3 right-3 w-5 h-5 rounded-full border flex items-center justify-center",style:{borderColor:b},children:K===a.PaymentMethodID&&(0,d.jsx)("div",{className:"w-3 h-3 rounded-full",style:{backgroundColor:b}})}),(0,d.jsxs)("div",{className:"w-12 h-12 rounded-full flex items-center justify-center overflow-hidden mb-3",style:{backgroundColor:`${b}20`},children:[a.ImageUrl?(0,d.jsx)("img",{src:a.ImageUrl,alt:a.PaymentMethodName,className:"w-full h-full object-cover",onError:a=>{a.currentTarget.style.display="none";let b=a.currentTarget.nextElementSibling;b&&(b.style.display="block")}}):null,(0,d.jsx)(u.A,{className:"h-6 w-6",style:{color:b,display:a.ImageUrl?"none":"block"}})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("p",{className:"font-medium text-sm break-words text-center",children:a.PaymentMethodName}),a.Description&&(0,d.jsx)("p",{className:"text-xs text-muted-foreground mt-1 break-words text-center",children:a.Description})]})]},a.PaymentMethodID))}),K&&(()=>{let a=M.find(a=>a.PaymentMethodID===K);return a?(0,d.jsx)(o.Y,{paymentMethodName:a.PaymentMethodName,paymentMethodId:a.PaymentMethodID}):null})(),(0,d.jsx)("div",{className:"mt-4",children:(0,d.jsx)(r(),{href:"/payment-methods",className:"text-sm text-primary hover:underline",children:"View detailed payment instructions"})})]})]}),(0,d.jsx)("div",{className:"p-3 sm:p-4 lg:p-6 pt-0",children:(0,d.jsx)(l.$,{type:"submit",className:"w-full hidden lg:block",style:{backgroundColor:b,color:"white"},disabled:!K||!ae&&!U.address,children:"Place Order"})})]})}),(0,d.jsx)("div",{className:"lg:col-span-1 order-2 lg:order-none",children:(0,d.jsx)(m.Zp,{className:"lg:sticky lg:top-4",children:(0,d.jsxs)("div",{className:"p-3 sm:p-4 lg:p-6",children:[(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0 mb-3 sm:mb-4",children:[(0,d.jsx)("h2",{className:"text-base sm:text-lg font-bold",children:"Order Summary"}),(0,d.jsxs)("div",{className:"flex items-center gap-1 w-full sm:w-auto",children:[(0,d.jsx)("a",{href:"#",onClick:a=>{a.preventDefault(),a.stopPropagation();let b=`Order Summary:
Subtotal: ${Q?I(C):H(E)}${G?`
Coupon (${G.code}): -${Q?I(G.discount):H(Math.round(1500*G.discount))}`:""}${S&&al>0?`
Credit Discount: -$${al.toFixed(2)}`:""}
Total: ${Q?I(an):H(ao)}`;navigator.clipboard.writeText(b);let c=a.target,d=c.textContent;c.textContent="Copied!",setTimeout(()=>{c.textContent=d},2e3)},className:"inline-block px-3 py-1.5 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 border border-gray-200 rounded-md text-xs font-medium transition-colors duration-200 mr-2 no-underline cursor-pointer",children:"\uD83D\uDCCB Copy Summary"}),(0,d.jsx)(l.$,{variant:Q?"outline":"default",size:"sm",onClick:()=>R(!1),className:"text-xs px-2 flex-1 sm:flex-none",children:"IQD"}),(0,d.jsxs)(l.$,{variant:Q?"default":"outline",size:"sm",onClick:()=>R(!0),className:"text-xs px-2 flex-1 sm:flex-none",children:[(0,d.jsx)(v.A,{className:"h-3 w-3 mr-1"}),"USD"]})]})]}),(0,d.jsx)("div",{className:"space-y-2 sm:space-y-3 mb-4 sm:mb-6",children:A.map(a=>(0,d.jsxs)("div",{className:"flex justify-between items-start",children:[(0,d.jsxs)("div",{className:"flex items-start gap-2 sm:gap-3",children:[(0,d.jsx)("div",{className:"w-12 h-12 sm:w-16 sm:h-16 bg-muted rounded-md overflow-hidden flex-shrink-0",children:(0,d.jsx)("img",{src:a.image||`/products/book${a.id}.jpg`,alt:a.name,className:"w-full h-full object-cover"})}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("p",{className:"font-medium text-xs sm:text-sm break-words",children:a.name}),(0,d.jsxs)("p",{className:"text-xs text-muted-foreground break-words",children:["Qty: ",a.quantity]}),a.attributes&&a.attributes.length>0&&(0,d.jsxs)("div",{className:"mt-1",children:[(0,d.jsx)("p",{className:"text-xs font-medium text-gray-600 mb-1",children:"Selected Options:"}),(0,d.jsx)("div",{className:"space-y-1",children:a.attributes.map((a,b)=>(0,d.jsxs)("div",{className:"text-xs text-gray-500 break-words",children:[(0,d.jsxs)("span",{className:"font-medium",children:[a.DisplayName||a.AttributeName,":"]})," ",(0,d.jsx)("span",{children:a.AttributeValueText}),a.PriceAdjustment&&0!==a.PriceAdjustment&&(0,d.jsxs)("span",{className:"text-green-600 ml-1",children:["(",1===a.PriceAdjustmentType?"+":"",1===a.PriceAdjustmentType?I(a.PriceAdjustment):`+${a.PriceAdjustment}%`,")"]})]},b))})]})]})]}),(0,d.jsx)("p",{className:"font-medium text-sm",children:Q?I(a.adjustedPrice||a.discountPrice||a.price):H(a.adjustedIqdPrice||a.iqdPrice||Math.round(1500*(a.discountPrice||a.price)))})]},a.id))}),(ae||U.address)&&(0,d.jsxs)("div",{className:"border-t pt-4 mb-4",children:[(0,d.jsxs)("h3",{className:"font-medium mb-2 flex items-center gap-2",children:[(0,d.jsxs)("svg",{className:"w-4 h-4 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),(0,d.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),"Delivery Address"]}),(0,d.jsx)("div",{className:"bg-gray-50 rounded-lg p-3",children:ae?(()=>{let a=ac.find(a=>a.AddressID===ae);return a?(0,d.jsxs)("div",{className:"text-sm",children:[(0,d.jsx)("div",{className:"flex items-center gap-2 mb-1",children:(0,d.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary break-words",children:a.AddressTypeName||(1===a.AddressTypeID?"Home":2===a.AddressTypeID?"Billing":3===a.AddressTypeID?"Shipping":4===a.AddressTypeID?"Mailing":"Other")})}),(0,d.jsx)("p",{className:"font-medium text-gray-900 break-words",children:a.AddressLineOne}),a.AddressLineTwo&&(0,d.jsx)("p",{className:"text-gray-600 break-words",children:a.AddressLineTwo}),(0,d.jsxs)("p",{className:"text-gray-600 break-words",children:[a.CityName||"City"," •"," ",a.CountryName,a.PostalCode&&` • ${a.PostalCode}`]})]}):null})():(0,d.jsxs)("div",{className:"text-sm",children:[(0,d.jsx)("p",{className:"font-medium text-gray-900",children:U.address}),(0,d.jsxs)("p",{className:"text-gray-600",children:[Y.find(a=>a.CityID.toString()===U.cityId)?.CityName||"City"," ","•"," ",W.find(a=>a.CountryID.toString()===U.countryId)?.CountryName||"Country",U.zipCode&&` • ${U.zipCode}`]})]})})]}),(0,d.jsxs)("div",{className:"space-y-3 border-t pt-4",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsx)("span",{className:"text-muted-foreground",children:"Subtotal"}),(0,d.jsx)("span",{className:"font-medium",children:Q?I(C):H(E)})]}),G&&(0,d.jsxs)("div",{className:"flex justify-between text-green-600",children:[(0,d.jsxs)("span",{children:["Coupon (",G.code,")"]}),(0,d.jsxs)("span",{children:["-",Q?I(G.discount):H(Math.round(1500*G.discount))]})]}),S&&al>0&&(0,d.jsxs)("div",{className:"flex justify-between text-yellow-600",children:[(0,d.jsxs)("span",{className:"flex items-center gap-1",children:[(0,d.jsx)(w.A,{className:"h-3 w-3"}),"Credit Discount"]}),(0,d.jsxs)("span",{children:["-$",al.toFixed(2)]})]}),(0,d.jsx)("div",{className:"border-t pt-3 mt-3",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-lg font-bold",children:"Total"}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("div",{className:"text-xl font-bold",style:{color:b},children:Q?I(an):H(ao)}),(0,d.jsx)("a",{href:"#",onClick:a=>{a.preventDefault(),a.stopPropagation();let b=Q?I(an):H(ao);navigator.clipboard.writeText(b);let c=a.target,d=c.textContent;c.textContent="Copied!",setTimeout(()=>{c.textContent=d},2e3)},className:"inline-block px-3 py-1.5 bg-gray-50 hover:bg-gray-100 text-gray-600 hover:text-gray-800 border border-gray-200 rounded-md text-xs font-medium transition-colors duration-200 no-underline cursor-pointer",children:"\uD83D\uDCCB Copy"})]}),(0,d.jsxs)("div",{className:"text-xs text-muted-foreground",children:["≈"," ",Q?H(ao):I(an)]})]})]})}),(G||S&&al>0)&&(0,d.jsx)("div",{className:"bg-green-50 rounded-lg p-3 border border-green-200",children:(0,d.jsxs)("div",{className:"text-sm text-green-700",children:[(0,d.jsx)("div",{className:"font-medium mb-1",children:"\uD83D\uDCB0 Total Savings:"}),(0,d.jsx)("div",{className:"font-bold",children:Q?I((G?G.discount:0)+al):H((G?Math.round(1500*G.discount):0)+am)})]})})]})]})})}),(0,d.jsx)("div",{className:"lg:hidden order-3",children:(0,d.jsx)(m.Zp,{children:(0,d.jsx)("div",{className:"p-3",children:(0,d.jsx)(l.$,{type:"submit",form:"checkout-form",className:"w-full",style:{backgroundColor:b,color:"white"},disabled:!K||!ae&&!U.address,children:"Place Order"})})})})]})]}):(0,d.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,d.jsx)(m.Zp,{className:"max-w-md mx-auto",children:(0,d.jsxs)("div",{className:"p-8 text-center",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(s.A,{className:"h-8 w-8 text-primary"})}),(0,d.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Login Required"}),(0,d.jsx)("p",{className:"text-muted-foreground mb-4",children:"Please log in to continue with checkout"}),(0,d.jsx)(l.$,{asChild:!0,className:"w-full",children:(0,d.jsx)(r(),{href:"/login",children:"Login to Continue"})})]})})})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},14952:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17669:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,61528)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\checkout\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\checkout\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/checkout/page",pathname:"/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/checkout/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},18195:(a,b,c)=>{Promise.resolve().then(c.bind(c,5466))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:a=>{"use strict";a.exports=require("os")},23928:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},34993:(a,b,c)=>{"use strict";c.d(b,{AB:()=>j,J5:()=>k,Qp:()=>i,tH:()=>n,tJ:()=>m,w1:()=>l});var d=c(60687),e=c(43210),f=c(8730),g=c(14952),h=(c(93661),c(96241));let i=e.forwardRef(({...a},b)=>(0,d.jsx)("nav",{ref:b,"aria-label":"breadcrumb",...a}));i.displayName="Breadcrumb";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("ol",{ref:c,className:(0,h.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",a),...b}));j.displayName="BreadcrumbList";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("li",{ref:c,className:(0,h.cn)("inline-flex items-center gap-1.5",a),...b}));k.displayName="BreadcrumbItem";let l=e.forwardRef(({asChild:a,className:b,...c},e)=>{let g=a?f.DX:"a";return(0,d.jsx)(g,{ref:e,className:(0,h.cn)("transition-colors hover:text-foreground",b),...c})});l.displayName="BreadcrumbLink";let m=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("span",{ref:c,role:"link","aria-disabled":"true","aria-current":"page",className:(0,h.cn)("font-normal text-foreground",a),...b}));m.displayName="BreadcrumbPage";let n=({children:a,className:b,...c})=>(0,d.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,h.cn)("[&>svg]:size-3.5",b),...c,children:a??(0,d.jsx)(g.A,{})});n.displayName="BreadcrumbSeparator"},39390:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(96241);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},61528:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\checkout\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\checkout\\page.tsx","default")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},65051:(a,b,c)=>{Promise.resolve().then(c.bind(c,61528))},74075:a=>{"use strict";a.exports=require("zlib")},78148:(a,b,c)=>{"use strict";c.d(b,{b:()=>h});var d=c(43210),e=c(14163),f=c(60687),g=d.forwardRef((a,b)=>(0,f.jsx)(e.sG.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));g.displayName="Label";var h=g},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},93661:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},94735:a=>{"use strict";a.exports=require("events")},98712:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,1697,7567,9822,1812],()=>b(b.s=17669));module.exports=c})();