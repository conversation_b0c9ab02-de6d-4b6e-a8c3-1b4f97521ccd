{"version": 1, "files": ["../../webpack-runtime.js", "../../chunks/4985.js", "../../chunks/1697.js", "../../chunks/5361.js", "../../chunks/2978.js", "../../chunks/9822.js", "../../chunks/5861.js", "page_client-reference-manifest.js", "../../../../components/product-card.tsx", "../../../../package.json", "../../../../contexts/wishlist-context.tsx", "../../../../components/ui/button.tsx", "../../../../components/ui/select.tsx", "../../../../components/ui/skeleton.tsx", "../../../../components/ui/pagination.tsx", "../../../../components/ui/card.tsx", "../../../../contexts/settings-context.tsx", "../../../../hooks/use-toast.ts", "../../../../components/ui/modern-toast.tsx", "../../../../lib/utils.ts", "../../../../components/ui/badge.tsx", "../../../../contexts/cart-context.tsx", "../../../../lib/translations.ts", "../../../../lib/color-utils.ts"]}