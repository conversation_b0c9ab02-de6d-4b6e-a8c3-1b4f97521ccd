(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{4516:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},5623:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},12486:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},13052:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},18949:(e,t,r)=>{Promise.resolve().then(r.bind(r,48966))},19420:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},28883:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},30242:(e,t,r)=>{"use strict";r.d(t,{G3:()=>m,_Y:()=>f});var s,a=r(12115),n=r(49509),o=function(){return(o=Object.assign||function(e){for(var t,r=1,s=arguments.length;r<s;r++)for(var a in t=arguments[r])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e}).apply(this,arguments)},i=function(e){var t;e?function(e){if(e)for(;e.lastChild;)e.lastChild.remove()}("string"==typeof e?document.getElementById(e):e):(t=document.querySelector(".grecaptcha-badge"))&&t.parentNode&&document.body.removeChild(t.parentNode)},l=function(e,t){i(t),window.___grecaptcha_cfg=void 0;var r,s=document.querySelector("#"+e);s&&s.remove(),(r=document.querySelector('script[src^="https://www.gstatic.com/recaptcha/releases"]'))&&r.remove()},c=function(e){var t=e.render,r=e.onLoadCallbackName,s=e.language,a=e.onLoad,n=e.useRecaptchaNet,o=e.useEnterprise,i=e.scriptProps,l=void 0===i?{}:i,c=l.nonce,d=void 0===c?"":c,u=l.defer,m=l.async,f=l.id,p=l.appendTo,h=(void 0===f?"":f)||"google-recaptcha-v3";if(document.querySelector("#"+h))a();else{var g,x="https://www."+((g={useEnterprise:o,useRecaptchaNet:n}).useRecaptchaNet?"recaptcha.net":"google.com")+"/recaptcha/"+(g.useEnterprise?"enterprise.js":"api.js"),y=document.createElement("script");y.id=h,y.src=x+"?render="+t+("explicit"===t?"&onload="+r:"")+(s?"&hl="+s:""),d&&(y.nonce=d),y.defer=!!(void 0!==u&&u),y.async=!!(void 0!==m&&m),y.onload=a,("body"===p?document.body:document.getElementsByTagName("head")[0]).appendChild(y)}},d=function(e){void 0===n||n.env,console.warn(e)};(s||(s={})).SCRIPT_NOT_AVAILABLE="Recaptcha script is not available";var u=(0,a.createContext)({executeRecaptcha:function(){throw Error("GoogleReCaptcha Context has not yet been implemented, if you are using useGoogleReCaptcha hook, make sure the hook is called inside component wrapped by GoogleRecaptchaProvider")}});function m(e){var t=e.reCaptchaKey,r=e.useEnterprise,n=void 0!==r&&r,i=e.useRecaptchaNet,m=void 0!==i&&i,f=e.scriptProps,p=e.language,h=e.container,g=e.children,x=(0,a.useState)(null),y=x[0],b=x[1],v=(0,a.useRef)(t),j=JSON.stringify(f),N=JSON.stringify(null==h?void 0:h.parameters);(0,a.useEffect)(function(){if(t){var e=(null==f?void 0:f.id)||"google-recaptcha-v3",r=(null==f?void 0:f.onLoadCallbackName)||"onRecaptchaLoadCallback";return window[r]=function(){var e=n?window.grecaptcha.enterprise:window.grecaptcha,r=o({badge:"inline",size:"invisible",sitekey:t},(null==h?void 0:h.parameters)||{});v.current=e.render(null==h?void 0:h.element,r)},c({render:(null==h?void 0:h.element)?"explicit":t,onLoadCallbackName:r,useEnterprise:n,useRecaptchaNet:m,scriptProps:f,language:p,onLoad:function(){if(window&&window.grecaptcha){var e=n?window.grecaptcha.enterprise:window.grecaptcha;e.ready(function(){b(e)})}else d("<GoogleRecaptchaProvider /> "+s.SCRIPT_NOT_AVAILABLE)},onError:function(){d("Error loading google recaptcha script")}}),function(){l(e,null==h?void 0:h.element)}}d("<GoogleReCaptchaProvider /> recaptcha key not provided")},[n,m,j,N,p,t,null==h?void 0:h.element]);var w=(0,a.useCallback)(function(e){if(!y||!y.execute)throw Error("<GoogleReCaptchaProvider /> Google Recaptcha has not been loaded");return y.execute(v.current,{action:e})},[y,v]),C=(0,a.useMemo)(function(){return{executeRecaptcha:y?w:void 0,container:null==h?void 0:h.element}},[w,y,null==h?void 0:h.element]);return a.createElement(u.Provider,{value:C},g)}u.Consumer;var f=function(){return(0,a.useContext)(u)};function p(e,t){return e(t={exports:{}},t.exports),t.exports}var h="function"==typeof Symbol&&Symbol.for,g=h?Symbol.for("react.element"):60103,x=h?Symbol.for("react.portal"):60106,y=h?Symbol.for("react.fragment"):60107,b=h?Symbol.for("react.strict_mode"):60108,v=h?Symbol.for("react.profiler"):60114,j=h?Symbol.for("react.provider"):60109,N=h?Symbol.for("react.context"):60110,w=h?Symbol.for("react.async_mode"):60111,C=h?Symbol.for("react.concurrent_mode"):60111,P=h?Symbol.for("react.forward_ref"):60112,S=h?Symbol.for("react.suspense"):60113,A=h?Symbol.for("react.suspense_list"):60120,k=h?Symbol.for("react.memo"):60115,E=h?Symbol.for("react.lazy"):60116,M=h?Symbol.for("react.block"):60121,R=h?Symbol.for("react.fundamental"):60117,L=h?Symbol.for("react.responder"):60118,U=h?Symbol.for("react.scope"):60119;function F(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case g:switch(e=e.type){case w:case C:case y:case v:case b:case S:return e;default:switch(e=e&&e.$$typeof){case N:case P:case E:case k:case j:return e;default:return t}}case x:return t}}}function $(e){return F(e)===C}var _={AsyncMode:w,ConcurrentMode:C,ContextConsumer:N,ContextProvider:j,Element:g,ForwardRef:P,Fragment:y,Lazy:E,Memo:k,Portal:x,Profiler:v,StrictMode:b,Suspense:S,isAsyncMode:function(e){return $(e)||F(e)===w},isConcurrentMode:$,isContextConsumer:function(e){return F(e)===N},isContextProvider:function(e){return F(e)===j},isElement:function(e){return"object"==typeof e&&null!==e&&e.$$typeof===g},isForwardRef:function(e){return F(e)===P},isFragment:function(e){return F(e)===y},isLazy:function(e){return F(e)===E},isMemo:function(e){return F(e)===k},isPortal:function(e){return F(e)===x},isProfiler:function(e){return F(e)===v},isStrictMode:function(e){return F(e)===b},isSuspense:function(e){return F(e)===S},isValidElementType:function(e){return"string"==typeof e||"function"==typeof e||e===y||e===C||e===v||e===b||e===S||e===A||"object"==typeof e&&null!==e&&(e.$$typeof===E||e.$$typeof===k||e.$$typeof===j||e.$$typeof===N||e.$$typeof===P||e.$$typeof===R||e.$$typeof===L||e.$$typeof===U||e.$$typeof===M)},typeOf:F},T=p(function(e,t){}),I=(T.AsyncMode,T.ConcurrentMode,T.ContextConsumer,T.ContextProvider,T.Element,T.ForwardRef,T.Fragment,T.Lazy,T.Memo,T.Portal,T.Profiler,T.StrictMode,T.Suspense,T.isAsyncMode,T.isConcurrentMode,T.isContextConsumer,T.isContextProvider,T.isElement,T.isForwardRef,T.isFragment,T.isLazy,T.isMemo,T.isPortal,T.isProfiler,T.isStrictMode,T.isSuspense,T.isValidElementType,T.typeOf,p(function(e){e.exports=_})),O={};O[I.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},O[I.Memo]={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Object.getOwnPropertyNames,Object.getOwnPropertySymbols,Object.getOwnPropertyDescriptor,Object.getPrototypeOf,Object.prototype},40646:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},48966:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var s=r(95155),a=r(12115),n=r(84995),o=r(88482),i=r(97168),l=r(89852),c=r(82714),d=r(99474),u=r(6874),m=r.n(u),f=r(79891),p=r(98816),h=r(71007),g=r(40646),x=r(12486),y=r(4516),b=r(19420),v=r(28883),j=r(39365),N=r.n(j);r(30133);var w=r(30242);function C(){let{t:e,primaryColor:t}=(0,f.t)(),{user:r,isLoggedIn:u}=(0,p.J)(),[j,C]=(0,a.useState)(!1),[P,S]=(0,a.useState)(!1),[A,k]=(0,a.useState)(""),{executeRecaptcha:E}=(0,w._Y)(),[M,R]=(0,a.useState)(""),[L,U]=(0,a.useState)("iq"),[F,$]=(0,a.useState)({name:"",email:"",phoneNumber:"",subject:"",message:""});(0,a.useEffect)(()=>{if(u&&r){let e="".concat(r.FirstName||""," ").concat(r.LastName||"").trim()||r.UserName||r.Name||"",t=r.Email||r.EmailAddress||r.email||"",s=r.PhoneNumber||r.PhoneNo||r.MobileNo||r.phone||"";$(r=>({...r,name:e,email:t,phoneNumber:s})),s&&R(s)}},[u,r]),(0,a.useEffect)(()=>{fetch("https://ipapi.co/json/").then(e=>e.json()).then(e=>{e.country_code&&(U(e.country_code.toLowerCase()),u&&(null==r?void 0:r.PhoneNumber)||R(e.country_calling_code.replace("+","")))}).catch(()=>{U("iq"),u&&(null==r?void 0:r.PhoneNumber)||R("964")})},[u,r]),(0,a.useEffect)(()=>{fetch("https://ipapi.co/json/").then(e=>e.json()).then(e=>{e.country_code&&(U(e.country_code.toLowerCase()),R(e.country_calling_code.replace("+","")))}).catch(()=>{U("iq"),R("964")})},[]),(0,a.useEffect)(()=>{if(u&&r){let e=r.PhoneNumber||r.PhoneNo||r.MobileNo||"";$(t=>({...t,name:"".concat(r.FirstName||""," ").concat(r.LastName||"").trim()||r.UserName||"",email:r.Email||r.EmailAddress||"",phoneNumber:e})),e&&R(e)}},[u,r]);let _=e=>{let{name:t,value:r}=e.target;$(e=>({...e,[t]:r}))},T=async e=>{if(e.preventDefault(),C(!0),k(""),!E){k("reCAPTCHA not ready"),C(!1);return}M.startsWith("+");try{let e=await E("contact_us"),t={FullName:F.name,Email:F.email,Subject:F.subject,Message:F.message,RecaptchaToken:e};if(u&&r){let e=r.PhoneNumber||r.PhoneNo||r.MobileNo||r.phone||"";e&&(t.PhoneNumber=e.startsWith("+")?e:"+".concat(e))}else t.PhoneNumber=M.startsWith("+")?M:"+".concat(M);u&&r?(t.UserId=r.UserId||r.UserID||r.Id,t.UserType=r.UserType||"Customer",t.IsLoggedIn=!0):t.IsLoggedIn=!1;let s=await fetch("".concat("https://admin.codemedicalapps.com/","api/v1/dynamic/dataoperation/contact-us"),{method:"POST",headers:{"Content-Type":"application/json",...u&&r?{Authorization:"Bearer ".concat(r.token)}:{}},body:JSON.stringify({requestParameters:t})}),a=await s.json();if(s.ok){if(S(!0),u&&r){let e="".concat(r.FirstName||""," ").concat(r.LastName||"").trim()||r.UserName||r.Name||"",t=r.Email||r.EmailAddress||r.email||"",s=r.PhoneNumber||r.PhoneNo||r.MobileNo||"";$({name:e,email:t,phoneNumber:s,subject:"",message:""})}else $({name:"",email:"",phoneNumber:"",subject:"",message:""});setTimeout(()=>S(!1),5e3)}else k(a.message||"Failed to send message.")}catch(e){k("An error occurred. Please try again.")}finally{C(!1)}};return(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,s.jsx)(n.Qp,{className:"mb-6",children:(0,s.jsxs)(n.AB,{children:[(0,s.jsx)(n.J5,{children:(0,s.jsx)(n.w1,{asChild:!0,children:(0,s.jsx)(m(),{href:"/",children:e("home")})})}),(0,s.jsx)(n.tH,{}),(0,s.jsx)(n.J5,{children:(0,s.jsx)(n.tJ,{children:e("contact")})})]})}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-6",children:e("contact")}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-[1fr_400px] gap-8 mb-12",children:[(0,s.jsx)(o.Zp,{children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold",children:"Get in Touch"}),u&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),(0,s.jsxs)("span",{children:["Logged in as ",null==r?void 0:r.FirstName," ",null==r?void 0:r.LastName]})]})]}),P?(0,s.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-6 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)(g.A,{className:"h-8 w-8 text-green-600"})}),(0,s.jsx)("h3",{className:"text-xl font-medium text-green-800 mb-2",children:"Message Sent!"}),(0,s.jsx)("p",{className:"text-green-700 mb-4",children:"Thank you for contacting us. We'll get back to you as soon as possible."}),(0,s.jsx)(i.$,{onClick:()=>S(!1),children:"Send Another Message"})]}):(0,s.jsxs)("form",{onSubmit:T,children:[u&&(0,s.jsxs)("div",{className:"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 text-green-600"}),(0,s.jsx)("span",{className:"text-sm font-medium text-green-800",children:"Your Information"})]}),(0,s.jsxs)("div",{className:"text-sm text-green-700",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Name:"})," ",null==r?void 0:r.FirstName," ",null==r?void 0:r.LastName]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Email:"})," ",(null==r?void 0:r.Email)||(null==r?void 0:r.EmailAddress)]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Phone:"})," ",(null==r?void 0:r.PhoneNumber)||(null==r?void 0:r.PhoneNo)||(null==r?void 0:r.MobileNo)]})]})]}),!u&&(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"name",children:"Your Name"}),(0,s.jsx)(l.p,{id:"name",name:"name",value:F.name,onChange:_,required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(c.J,{htmlFor:"email",children:"Email Address"}),(0,s.jsx)(l.p,{id:"email",name:"email",type:"email",value:F.email,onChange:_,required:!0})]}),(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)(c.J,{children:e("phonenumber")}),(0,s.jsx)(N(),{country:L,value:M,onChange:(e,t)=>{R(e);let r=e.startsWith("+")?e:"+".concat(e);$(e=>({...e,phoneNumber:r})),t&&t.countryCode&&U(t.countryCode.toLowerCase())},enableSearch:!0,searchPlaceholder:"Search country...",containerClass:"w-full",inputClass:"w-full p-2 border rounded-md focus:ring-2 focus:ring-primary/50",buttonClass:"!border-input !bg-background hover:!bg-accent",dropdownClass:"!bg-background !border-input",countryCodeEditable:!1,inputProps:{name:"phoneNumber",required:!0,id:"phoneNumber"}})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)(c.J,{htmlFor:"subject",children:"Subject"}),(0,s.jsx)(l.p,{id:"subject",name:"subject",value:F.subject,onChange:_,required:!0})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)(c.J,{htmlFor:"message",children:"Message"}),(0,s.jsx)(d.T,{id:"message",name:"message",value:F.message,onChange:_,rows:6,required:!0})]}),A&&(0,s.jsx)("p",{className:"text-red-500 text-sm mb-4",children:A}),(0,s.jsx)(i.$,{type:"submit",disabled:j,className:"w-full",children:j?(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Sending..."]}):(0,s.jsxs)("span",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4"}),"Send Message"]})})]})]})}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(o.Zp,{children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Contact Information"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-4",style:{backgroundColor:"".concat(t,"20")},children:(0,s.jsx)(y.A,{className:"h-5 w-5",style:{color:t}})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Address"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Iraq"})]})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-4",style:{backgroundColor:"".concat(t,"20")},children:(0,s.jsx)(b.A,{className:"h-5 w-5",style:{color:t}})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Phone"}),(0,s.jsx)(m(),{href:"tel:".concat(e("phone")),className:"text-muted-foreground hover:text-primary transition-colors",children:e("phone")})]})]}),(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full flex items-center justify-center mr-4",style:{backgroundColor:"".concat(t,"20")},children:(0,s.jsx)(v.A,{className:"h-5 w-5",style:{color:t}})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Email"}),(0,s.jsx)(m(),{href:"mailto:".concat(e("email")),className:"text-muted-foreground hover:text-primary transition-colors",children:e("email")})]})]})]})]})}),(0,s.jsx)(o.Zp,{children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-4",children:"Business Hours"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Monday - Friday"}),(0,s.jsx)("span",{children:"9:00 AM - 11:00 PM"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Saturday"}),(0,s.jsx)("span",{children:"10:00 AM - 10:00 PM"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)("span",{children:"Sunday"}),(0,s.jsx)("span",{children:"Closed"})," "]}),(0,s.jsx)("div",{className:"flex justify-between",children:(0,s.jsx)("p",{children:"We’re available 24/7 for orders and inquiries. Our team will get back to you during our regular working hours."})})]})]})})]})]}),(0,s.jsxs)("div",{className:"mb-12",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-6",children:"Frequently Asked Questions"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(o.Zp,{children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"How can I track my order?"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"You can track your order by logging into your account and visiting the Orders section. Alternatively, you can use the tracking number provided in your order confirmation email."})]})}),(0,s.jsx)(o.Zp,{children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"What payment methods do you accept?"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"We accept various payment methods including credit/debit cards, PayPal, and bank transfers. For more information, please visit our Payment Methods page."})]})}),(0,s.jsx)(o.Zp,{children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"How long does shipping take?"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Shipping times vary depending on your location. Domestic orders typically take 3-5 business days, while international orders may take 7-14 business days."})]})}),(0,s.jsx)(o.Zp,{children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"What is your return policy?"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"We offer a 30-day return policy for most products. Please visit our Returns page for detailed information on our return process and eligibility criteria."})]})})]})]})]})]})}},53999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(52596),a=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},71007:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},79891:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l,t:()=>c});var s=r(95155),a=r(12115);let n={en:{menu:"Menu",newsletter:"Newsletter",enterEmail:"Enter your email",newsletterDisclaimer:"Subscribe to our newsletter to receive updates and exclusive offers",popularCategories:"Popular Categories!",settings:"Settings",language:"Language",theme:"Theme",color:"Color",save:"Save",cancel:"Cancel",close:"Close",phone:"***************",phonenumber:"Phone Number",email:"<EMAIL>",liveChat:"Live Chat",welcome:"Welcome",logout:"Logout",login:"Login",signup:"Sign Up",signUp:"Sign Up",category:"Category",categories:"Categories",filters:"Filters",clearAll:"Clear All",products:"Products",loadingCategories:"Loading categories...",home:"Home",todayDeals:"Today's Deals",followUs:"Follow Us",aboutUs:"About Us",contactUs:"Contact Us",tryAgain:"Try Again",noProductsFound:"No products found",allProducts:"All Products",quickLinks:"Quick Links",about:"About Us",contact:"Contact Us",hotDeals:"Hot Deals",customerArea:"Customer Area",myAccount:"My Account",orders:"Orders",cart:"Cart",wishlist:"Wishlist",paymentMethods:"Payment Methods",location:"Location",callUs:"Call Us",emailUs:"Email Us",subscribe:"Subscribe",name:"Name",subject:"Subject",message:"Message",sendMessage:"Send Message",sending:"Sending...",messageSent:"Message sent successfully!",messageError:"Failed to send message",contactInfo:"Contact Information",address:"Address",findUs:"Find Us",searchProducts:"Search products..."},ar:{menu:"القائمة",popularCategories:"الفئات الشائعة!",settings:"الإعدادات",language:"اللغة",theme:"المظهر",color:"اللون",save:"حفظ",cancel:"إلغاء",close:"إغلاق",phone:"***************",phonenumber:"رقم الهاتف",email:"<EMAIL>",liveChat:"محادثة مباشرة",welcome:"مرحباً",logout:"تسجيل الخروج",login:"تسجيل الدخول",signup:"تسجيل جديد",signUp:"تسجيل جديد",category:"الفئة",categories:"الفئات",products:"المنتجات",loadingCategories:"جاري تحميل الفئات...",filters:"المرشحات",clearAll:"مسح الكل",home:"الرئيسية",todayDeals:"عروض اليوم",followUs:"تابعنا",aboutUs:"من نحن",contactUs:"اتصل بنا",tryAgain:"حاول مرة أخرى",noProductsFound:"لم يتم العثور على منتجات",allProducts:"جميع المنتجات",quickLinks:"روابط سريعة",about:"من نحن",contact:"اتصل بنا",hotDeals:"عروض ساخنة",customerArea:"منطقة العملاء",myAccount:"حسابي",searchProducts:"ابحث عن المنتجات...",orders:"الطلبات",cart:"السلة",wishlist:"المفضلة",paymentMethods:"طرق الدفع",location:"الموقع",callUs:"اتصل بنا",emailUs:"راسلنا",subscribe:"اشترك",name:"الاسم",subject:"الموضوع",message:"الرسالة",sendMessage:"إرسال الرسالة",sending:"جاري الإرسال...",messageSent:"تم إرسال الرسالة بنجاح!",messageError:"فشل في إرسال الرسالة",contactInfo:"معلومات الاتصال",address:"العنوان",findUs:"موقعنا"}};var o=r(94213);let i=(0,a.createContext)(void 0);function l(e){let{children:t}=e,[r,l]=(0,a.useState)("light"),[c,d]=(0,a.useState)("en"),[u,m]=(0,a.useState)("#0074b2"),[f,p]=(0,a.useState)("#ffffff");return(0,a.useEffect)(()=>{let e=(0,o.N)(u);p(e),document.documentElement.style.setProperty("--primary",u),document.documentElement.style.setProperty("--primary-foreground",e)},[u]),(0,s.jsx)(i.Provider,{value:{theme:r,language:c,primaryColor:u,primaryTextColor:f,toggleTheme:()=>{l("light"===r?"dark":"light")},setLanguage:e=>{d(e),document.documentElement.dir="ar"===e?"rtl":"ltr"},setPrimaryColor:e=>{m(e);let t=(0,o.N)(e);p(t),document.documentElement.style.setProperty("--primary",e),document.documentElement.style.setProperty("--primary-foreground",t)},t:e=>(function(e,t){let r=n[t];return e in r?r[e]:"en"!==t&&e in n.en?n.en[e]:e})(e,c)},children:t})}function c(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useSettings must be used within a SettingsProvider");return e}},82714:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var s=r(95155),a=r(12115),n=r(40968),o=r(74466),i=r(53999);let l=(0,o.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)(n.b,{ref:t,className:(0,i.cn)(l(),r),...a})});c.displayName=n.b.displayName},84995:(e,t,r)=>{"use strict";r.d(t,{AB:()=>c,J5:()=>d,Qp:()=>l,tH:()=>f,tJ:()=>m,w1:()=>u});var s=r(95155),a=r(12115),n=r(99708),o=r(13052),i=(r(5623),r(53999));let l=a.forwardRef((e,t)=>{let{...r}=e;return(0,s.jsx)("nav",{ref:t,"aria-label":"breadcrumb",...r})});l.displayName="Breadcrumb";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("ol",{ref:t,className:(0,i.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",r),...a})});c.displayName="BreadcrumbList";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("li",{ref:t,className:(0,i.cn)("inline-flex items-center gap-1.5",r),...a})});d.displayName="BreadcrumbItem";let u=a.forwardRef((e,t)=>{let{asChild:r,className:a,...o}=e,l=r?n.DX:"a";return(0,s.jsx)(l,{ref:t,className:(0,i.cn)("transition-colors hover:text-foreground",a),...o})});u.displayName="BreadcrumbLink";let m=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,i.cn)("font-normal text-foreground",r),...a})});m.displayName="BreadcrumbPage";let f=e=>{let{children:t,className:r,...a}=e;return(0,s.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,i.cn)("[&>svg]:size-3.5",r),...a,children:null!=t?t:(0,s.jsx)(o.A,{})})};f.displayName="BreadcrumbSeparator"},88482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>o,aR:()=>i,wL:()=>u});var s=r(95155),a=r(12115),n=r(53999);let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});o.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});l.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})});u.displayName="CardFooter"},89852:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var s=r(95155),a=r(12115),n=r(53999);let o=a.forwardRef((e,t)=>{let{className:r,type:a,...o}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...o})});o.displayName="Input"},94213:(e,t,r)=>{"use strict";function s(e,t){let r=e=>{let t=e.replace("#",""),r=parseInt(t.slice(0,2),16)/255,s=[r,parseInt(t.slice(2,4),16)/255,parseInt(t.slice(4,6),16)/255].map(e=>e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4));return .2126*s[0]+.7152*s[1]+.0722*s[2]},s=r(e),a=r(t);return(Math.max(s,a)+.05)/(Math.min(s,a)+.05)}function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"AA",r=s(e,"#ffffff"),a=s(e,"#000000"),n="AAA"===t?7:4.5;return r>=n&&a>=n?r>a?"#ffffff":"#000000":r>=n?"#ffffff":a>=n?"#000000":r>a?"#ffffff":"#000000"}r.d(t,{N:()=>a})},97168:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>l});var s=r(95155),a=r(12115),n=r(99708),o=r(74466),i=r(53999);let l=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:r,variant:a,size:o,asChild:c=!1,...d}=e,u=c?n.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(l({variant:a,size:o,className:r})),ref:t,...d})});c.displayName="Button"},99474:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});var s=r(95155),a=r(12115),n=r(53999);let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...a})});o.displayName="Textarea"}},e=>{e.O(0,[7540,4277,3464,4706,3165,8816,8441,5964,7358],()=>e(e.s=18949)),_N_E=e.O()}]);