(()=>{var a={};a.id=2146,a.ids=[2146],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3018:(a,b,c)=>{"use strict";c.d(b,{Fc:()=>i,TN:()=>j});var d=c(60687),e=c(43210),f=c(24224),g=c(96241);let h=(0,f.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-red-500 dark:border-destructive [&>svg]:text-red-500"}},defaultVariants:{variant:"default"}}),i=e.forwardRef(({className:a,variant:b,...c},e)=>(0,d.jsx)("div",{ref:e,role:"alert",className:(0,g.cn)(h({variant:b}),a),...c}));i.displayName="Alert",e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h5",{ref:c,className:(0,g.cn)("mb-1 font-medium leading-none tracking-tight",a),...b})).displayName="AlertTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,g.cn)("text-sm [&_p]:leading-relaxed",a),...b}));j.displayName="AlertDescription"},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:a=>{"use strict";a.exports=require("assert")},12597:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},14719:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20690:(a,b,c)=>{Promise.resolve().then(c.bind(c,81977))},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},39390:(a,b,c)=>{"use strict";c.d(b,{J:()=>j});var d=c(60687),e=c(43210),f=c(78148),g=c(24224),h=c(96241);let i=(0,g.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)(f.b,{ref:c,className:(0,h.cn)(i(),a),...b}));j.displayName=f.b.displayName},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41862:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44111:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["signup-twilio",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,81977)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\signup-twilio\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,58014)),"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\signup-twilio\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/signup-twilio/page",pathname:"/signup-twilio",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/signup-twilio/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},55192:(a,b,c)=>{"use strict";c.d(b,{BT:()=>j,Wu:()=>k,ZB:()=>i,Zp:()=>g,aR:()=>h,wL:()=>l});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...b}));g.displayName="Card";let h=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex flex-col space-y-1.5 p-6",a),...b}));h.displayName="CardHeader";let i=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("h3",{ref:c,className:(0,f.cn)("text-2xl font-semibold leading-none tracking-tight",a),...b}));i.displayName="CardTitle";let j=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("p",{ref:c,className:(0,f.cn)("text-sm text-muted-foreground",a),...b}));j.displayName="CardDescription";let k=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("p-6 pt-0",a),...b}));k.displayName="CardContent";let l=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("div",{ref:c,className:(0,f.cn)("flex items-center p-6 pt-0",a),...b}));l.displayName="CardFooter"},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66235:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>z});var d=c(60687),e=c(43210),f=c(55192),g=c(24934),h=c(68988),i=c(39390),j=c(3018),k=c(97905),l=c(85814),m=c.n(l),n=c(32192),o=c(58869),p=c(12597),q=c(13861),r=c(70334),s=c(14719),t=c(48340),u=c(99891),v=c(41862);async function w(a){try{let b=await fetch("/api/sms/send-verification",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:a})}),c=await b.json();if(!b.ok)return{success:!1,message:c.error||"Failed to send verification code",error:c.error};return{success:!0,message:c.message,messageId:c.messageId}}catch(a){return console.error("Error sending SMS verification:",a),{success:!1,message:"Network error occurred",error:"Network error"}}}async function x(a,b){try{let c=await fetch("/api/sms/verify-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({phoneNumber:a,code:b})}),d=await c.json();if(!c.ok)return{success:!1,message:d.error||"Verification failed",error:d.error};return{success:!0,message:d.message}}catch(a){return console.error("Error verifying SMS code:",a),{success:!1,message:"Network error occurred",error:"Network error"}}}function y({onVerificationSuccess:a,onError:b}){let[c,i]=(0,e.useState)("phone"),[k,l]=(0,e.useState)(""),[m,n]=(0,e.useState)(""),[o,p]=(0,e.useState)(!1),[q,r]=(0,e.useState)(""),[s,y]=(0,e.useState)(""),z=async()=>{if(!k.trim())return void r("Please enter a phone number");let a=function(a,b="+1"){let c=a.replace(/\D/g,"");return c.startsWith(b.replace("+",""))?`+${c}`:`${b}${c}`}(k);if(!/^\+[1-9]\d{1,14}$/.test(a))return void r("Please enter a valid phone number");p(!0),r(""),y("");try{let c=await w(a);c.success?(y("Verification code sent successfully!"),i("code"),l(a)):(r(c.message),b?.(c.message))}catch(c){let a="Failed to send verification code";r(a),b?.(a)}finally{p(!1)}},A=async()=>{if(!m.trim())return void r("Please enter the verification code");if(6!==m.length)return void r("Verification code must be 6 digits");p(!0),r("");try{let c=await x(k,m);c.success?(y("Phone number verified successfully!"),a(k)):(r(c.message),b?.(c.message))}catch(c){let a="Failed to verify code";r(a),b?.(a)}finally{p(!1)}},B=async()=>{n(""),await z()};return(0,d.jsxs)(f.Zp,{className:"w-full max-w-md mx-auto",children:[(0,d.jsxs)(f.aR,{className:"text-center",children:[(0,d.jsx)("div",{className:"mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4",children:"phone"===c?(0,d.jsx)(t.A,{className:"w-6 h-6 text-primary"}):(0,d.jsx)(u.A,{className:"w-6 h-6 text-primary"})}),(0,d.jsx)(f.ZB,{children:"phone"===c?"Verify Phone Number":"Enter Verification Code"}),(0,d.jsx)(f.BT,{children:"phone"===c?"We'll send you a verification code via SMS":`Enter the 6-digit code sent to ${k}`})]}),(0,d.jsxs)(f.Wu,{className:"space-y-4",children:[q&&(0,d.jsx)(j.Fc,{variant:"destructive",children:(0,d.jsx)(j.TN,{children:q})}),s&&(0,d.jsx)(j.Fc,{children:(0,d.jsx)(j.TN,{children:s})}),"phone"===c?(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium mb-2",children:"Phone Number"}),(0,d.jsx)(h.p,{id:"phone",type:"tel",placeholder:"+****************",value:k,onChange:a=>l(a.target.value),disabled:o,autoComplete:"tel",inputMode:"tel",className:"h-12 md:h-10 text-lg md:text-base"}),(0,d.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Include country code (e.g., +1 for US)"})]}),(0,d.jsx)(g.$,{onClick:z,disabled:o,className:"w-full",children:o?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(v.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Sending Code..."]}):"Send Verification Code"})]}):(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("label",{htmlFor:"code",className:"block text-sm font-medium mb-2",children:"Verification Code"}),(0,d.jsx)(h.p,{id:"code",type:"text",placeholder:"123456",value:m,onChange:a=>n(a.target.value.replace(/\D/g,"").slice(0,6)),disabled:o,maxLength:6,autoComplete:"one-time-code",inputMode:"numeric",className:"text-center text-xl md:text-base h-16 md:h-10 tracking-widest font-mono px-4"})]}),(0,d.jsx)(g.$,{onClick:A,disabled:o||6!==m.length,className:"w-full",children:o?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(v.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Verifying..."]}):"Verify Code"}),(0,d.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,d.jsx)(g.$,{variant:"ghost",onClick:()=>{i("phone"),n(""),r(""),y("")},disabled:o,children:"Change Number"}),(0,d.jsx)(g.$,{variant:"ghost",onClick:B,disabled:o,children:"Resend Code"})]})]})]})]})}function z(){let[a,b]=(0,e.useState)("phone"),[c,l]=(0,e.useState)(""),[t,u]=(0,e.useState)(!1),[v,w]=(0,e.useState)(""),[x,z]=(0,e.useState)(!1),[A,B]=(0,e.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:""}),C=async a=>{if(a.preventDefault(),u(!0),w(""),!A.firstName.trim()){w("First name is required"),u(!1);return}if(!A.lastName.trim()){w("Last name is required"),u(!1);return}if(!A.email.trim()){w("Email is required"),u(!1);return}if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(A.email)){w("Please enter a valid email address"),u(!1);return}if(!A.password){w("Password is required"),u(!1);return}if(A.password.length<8){w("Password must be at least 8 characters long"),u(!1);return}if(A.password!==A.confirmPassword){w("Passwords do not match"),u(!1);return}try{let a={...A,phoneNumber:c,phoneVerified:!0};console.log("Registration data:",a),await new Promise(a=>setTimeout(a,2e3)),b("success")}catch(a){w("Registration failed. Please try again."),console.error("Registration error:",a)}finally{u(!1)}},D=(a,b)=>{B(c=>({...c,[a]:b}))};return(0,d.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,d.jsxs)("div",{className:"w-full max-w-md",children:[(0,d.jsxs)("div",{className:"text-center mb-8",children:[(0,d.jsxs)(m(),{href:"/",className:"inline-flex items-center text-primary hover:text-primary/80 mb-4",children:[(0,d.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]}),(0,d.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Create Account"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Join CodeMedical today"})]}),(0,d.jsx)("div",{className:"flex justify-center mb-8",children:(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${"phone"===a?"bg-primary text-white":"details"===a||"success"===a?"bg-green-500 text-white":"bg-gray-200 text-gray-600"}`,children:"1"}),(0,d.jsx)("div",{className:`w-16 h-1 ${"details"===a||"success"===a?"bg-green-500":"bg-gray-200"}`}),(0,d.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${"details"===a?"bg-primary text-white":"success"===a?"bg-green-500 text-white":"bg-gray-200 text-gray-600"}`,children:"2"}),(0,d.jsx)("div",{className:`w-16 h-1 ${"success"===a?"bg-green-500":"bg-gray-200"}`}),(0,d.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${"success"===a?"bg-green-500 text-white":"bg-gray-200 text-gray-600"}`,children:"3"})]})}),(0,d.jsxs)(k.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:["phone"===a&&(0,d.jsx)(y,{onVerificationSuccess:a=>{l(a),b("details"),w("")},onError:a=>{w(a)}}),"details"===a&&(0,d.jsxs)(f.Zp,{children:[(0,d.jsxs)(f.aR,{className:"text-center",children:[(0,d.jsx)("div",{className:"mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4",children:(0,d.jsx)(o.A,{className:"w-6 h-6 text-primary"})}),(0,d.jsx)(f.ZB,{children:"Personal Details"}),(0,d.jsx)(f.BT,{children:"Complete your profile information"})]}),(0,d.jsx)(f.Wu,{children:(0,d.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[v&&(0,d.jsx)(j.Fc,{variant:"destructive",children:(0,d.jsx)(j.TN,{children:v})}),(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{htmlFor:"firstName",children:"First Name"}),(0,d.jsx)(h.p,{id:"firstName",type:"text",value:A.firstName,onChange:a=>D("firstName",a.target.value),disabled:t,required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{htmlFor:"lastName",children:"Last Name"}),(0,d.jsx)(h.p,{id:"lastName",type:"text",value:A.lastName,onChange:a=>D("lastName",a.target.value),disabled:t,required:!0})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{htmlFor:"email",children:"Email Address"}),(0,d.jsx)(h.p,{id:"email",type:"email",value:A.email,onChange:a=>D("email",a.target.value),disabled:t,required:!0})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{htmlFor:"password",children:"Password"}),(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(h.p,{id:"password",type:x?"text":"password",value:A.password,onChange:a=>D("password",a.target.value),disabled:t,required:!0}),(0,d.jsx)(g.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>z(!x),children:x?(0,d.jsx)(p.A,{className:"h-4 w-4"}):(0,d.jsx)(q.A,{className:"h-4 w-4"})})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)(i.J,{htmlFor:"confirmPassword",children:"Confirm Password"}),(0,d.jsx)(h.p,{id:"confirmPassword",type:"password",value:A.confirmPassword,onChange:a=>D("confirmPassword",a.target.value),disabled:t,required:!0})]}),(0,d.jsxs)("div",{className:"text-sm text-gray-600 bg-gray-50 p-3 rounded-md",children:[(0,d.jsx)("strong",{children:"Phone:"})," ",c," ✓ Verified"]}),(0,d.jsxs)(g.$,{type:"submit",disabled:t,className:"w-full",children:[t?"Creating Account...":"Create Account",(0,d.jsx)(r.A,{className:"w-4 h-4 ml-2"})]})]})})]}),"success"===a&&(0,d.jsx)(f.Zp,{children:(0,d.jsxs)(f.Wu,{className:"text-center py-8",children:[(0,d.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6",children:(0,d.jsx)(s.A,{className:"w-8 h-8 text-green-600"})}),(0,d.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Account Created!"}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:"Your account has been successfully created. You can now sign in with your credentials."}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(g.$,{asChild:!0,className:"w-full",children:(0,d.jsxs)(m(),{href:"/login",children:["Sign In Now",(0,d.jsx)(r.A,{className:"w-4 h-4 ml-2"})]})}),(0,d.jsx)(g.$,{variant:"outline",asChild:!0,className:"w-full",children:(0,d.jsxs)(m(),{href:"/",children:[(0,d.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Back to Home"]})})]})]})})]},a),(0,d.jsx)("div",{className:"text-center mt-8",children:(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,d.jsx)(m(),{href:"/login",className:"text-primary hover:text-primary/80 font-medium",children:"Sign in here"})]})})]})})}},67546:(a,b,c)=>{Promise.resolve().then(c.bind(c,66235))},68988:(a,b,c)=>{"use strict";c.d(b,{p:()=>g});var d=c(60687),e=c(43210),f=c(96241);let g=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));g.displayName="Input"},70334:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},74075:a=>{"use strict";a.exports=require("zlib")},78148:(a,b,c)=>{"use strict";c.d(b,{b:()=>h});var d=c(43210),e=c(14163),f=c(60687),g=d.forwardRef((a,b)=>(0,f.jsx)(e.sG.label,{...a,ref:b,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));g.displayName="Label";var h=g},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},81977:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\signup-twilio\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Downloads\\ec\\.NET 8 Version - Latest\\project\\codemedical\\project3\\app\\signup-twilio\\page.tsx","default")},83997:a=>{"use strict";a.exports=require("tty")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94735:a=>{"use strict";a.exports=require("events")},99891:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[4985,1697,4773,9822],()=>b(b.s=44111));module.exports=c})();