(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4483],{6101:(e,r,n)=>{"use strict";n.d(r,{s:()=>o,t:()=>l});var t=n(12115);function s(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function l(...e){return r=>{let n=!1,t=e.map(e=>{let t=s(e,r);return n||"function"!=typeof t||(n=!0),t});if(n)return()=>{for(let r=0;r<t.length;r++){let n=t[r];"function"==typeof n?n():s(e[r],null)}}}}function o(...e){return t.useCallback(l(...e),e)}},53999:(e,r,n)=>{"use strict";n.d(r,{cn:()=>l});var t=n(52596),s=n(39688);function l(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return(0,s.QP)((0,t.$)(r))}},70297:(e,r,n)=>{Promise.resolve().then(n.bind(n,84398))},74466:(e,r,n)=>{"use strict";n.d(r,{F:()=>o});var t=n(52596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=t.$,o=(e,r)=>n=>{var t;if((null==r?void 0:r.variants)==null)return l(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:o,defaultVariants:i}=r,a=Object.keys(o).map(e=>{let r=null==n?void 0:n[e],t=null==i?void 0:i[e];if(null===r)return null;let l=s(r)||s(t);return o[e][l]}),d=n&&Object.entries(n).reduce((e,r)=>{let[n,t]=r;return void 0===t||(e[n]=t),e},{});return l(e,a,null==r||null==(t=r.compoundVariants)?void 0:t.reduce((e,r)=>{let{class:n,className:t,...s}=r;return Object.entries(s).every(e=>{let[r,n]=e;return Array.isArray(n)?n.includes({...i,...d}[r]):({...i,...d})[r]===n})?[...e,n,t]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},84398:(e,r,n)=>{"use strict";n.r(r),n.d(r,{default:()=>a});var t=n(95155),s=n(98816),l=n(88482),o=n(97168),i=n(12115);function a(){let{user:e,token:r,isLoggedIn:n}=(0,s.J)(),[a,d]=(0,i.useState)(null),[c,u]=(0,i.useState)(!1),f=async()=>{try{let e=await fetch("/api/auth/get-token",{method:"GET",credentials:"include"}),r=await e.json();console.log("Token API Response:",r),d({tokenAPI:r})}catch(e){console.error("Token API error:",e),d({tokenAPIError:e instanceof Error?e.message:"Unknown error"})}},p=async()=>{u(!0);try{let e={"Content-Type":"application/json",Accept:"application/json"};r&&(e.Authorization="Bearer ".concat(r)),console.log("Testing with token:",r),console.log("Headers:",e);let n=await fetch("/api/orders/history",{method:"POST",headers:e,body:JSON.stringify({requestParameters:{recordValueJson:"[]"}})}),t=await n.json();console.log("API Response:",t),d({ordersAPI:t})}catch(e){console.error("Test error:",e),d({ordersAPIError:e instanceof Error?e.message:"Unknown error"})}finally{u(!1)}};return(0,t.jsx)("div",{className:"container mx-auto p-8",children:(0,t.jsxs)(l.Zp,{className:"max-w-4xl mx-auto p-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Token & API Test"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Authentication Status"}),(0,t.jsxs)("div",{className:"bg-gray-100 p-4 rounded",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Is Logged In:"})," ",n?"Yes":"No"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"User ID:"})," ",(null==e?void 0:e.UserID)||(null==e?void 0:e.UserId)||"N/A"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"User Email:"})," ",(null==e?void 0:e.Email)||(null==e?void 0:e.EmailAddress)||"N/A"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Token Available:"})," ",r?"Yes":"No"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Token Length:"})," ",r?r.length:0]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Token Preview:"})," ",r?"".concat(r.substring(0,20),"..."):"N/A"]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Environment Variables"}),(0,t.jsx)("div",{className:"bg-gray-100 p-4 rounded",children:(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Base URL:"})," ","https://admin.codemedicalapps.com/"]})})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Cookie Information"}),(0,t.jsxs)("div",{className:"bg-gray-100 p-4 rounded text-xs",children:[(0,t.jsx)("p",{children:(0,t.jsx)("strong",{children:"All Cookies:"})}),(0,t.jsx)("pre",{children:document.cookie})]})]}),(0,t.jsxs)("div",{className:"space-x-4",children:[(0,t.jsx)(o.$,{onClick:f,variant:"outline",children:"Test Token API"}),(0,t.jsx)(o.$,{onClick:p,disabled:c||!n,children:c?"Testing...":"Test Orders API"})]}),a&&(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"API Test Result"}),(0,t.jsx)("pre",{className:"bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96",children:JSON.stringify(a,null,2)})]})]})]})})}},88482:(e,r,n)=>{"use strict";n.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>a,Zp:()=>o,aR:()=>i,wL:()=>u});var t=n(95155),s=n(12115),l=n(53999);let o=s.forwardRef((e,r)=>{let{className:n,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",n),...s})});o.displayName="Card";let i=s.forwardRef((e,r)=>{let{className:n,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",n),...s})});i.displayName="CardHeader";let a=s.forwardRef((e,r)=>{let{className:n,...s}=e;return(0,t.jsx)("h3",{ref:r,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",n),...s})});a.displayName="CardTitle";let d=s.forwardRef((e,r)=>{let{className:n,...s}=e;return(0,t.jsx)("p",{ref:r,className:(0,l.cn)("text-sm text-muted-foreground",n),...s})});d.displayName="CardDescription";let c=s.forwardRef((e,r)=>{let{className:n,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("p-6 pt-0",n),...s})});c.displayName="CardContent";let u=s.forwardRef((e,r)=>{let{className:n,...s}=e;return(0,t.jsx)("div",{ref:r,className:(0,l.cn)("flex items-center p-6 pt-0",n),...s})});u.displayName="CardFooter"},97168:(e,r,n)=>{"use strict";n.d(r,{$:()=>d,r:()=>a});var t=n(95155),s=n(12115),l=n(99708),o=n(74466),i=n(53999);let a=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-red-500 text-destructive-foreground hover:bg-red-600",outline:"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=s.forwardRef((e,r)=>{let{className:n,variant:s,size:o,asChild:d=!1,...c}=e,u=d?l.DX:"button";return(0,t.jsx)(u,{className:(0,i.cn)(a({variant:s,size:o,className:n})),ref:r,...c})});d.displayName="Button"},99708:(e,r,n)=>{"use strict";n.d(r,{DX:()=>i,Dc:()=>d,TL:()=>o});var t=n(12115),s=n(6101),l=n(95155);function o(e){let r=function(e){let r=t.forwardRef((e,r)=>{let{children:n,...l}=e;if(t.isValidElement(n)){var o;let e,i,a=(o=n,(i=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(i=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),d=function(e,r){let n={...r};for(let t in r){let s=e[t],l=r[t];/^on[A-Z]/.test(t)?s&&l?n[t]=(...e)=>{let r=l(...e);return s(...e),r}:s&&(n[t]=s):"style"===t?n[t]={...s,...l}:"className"===t&&(n[t]=[s,l].filter(Boolean).join(" "))}return{...e,...n}}(l,n.props);return n.type!==t.Fragment&&(d.ref=r?(0,s.t)(r,a):a),t.cloneElement(n,d)}return t.Children.count(n)>1?t.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),n=t.forwardRef((e,n)=>{let{children:s,...o}=e,i=t.Children.toArray(s),a=i.find(c);if(a){let e=a.props.children,s=i.map(r=>r!==a?r:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,l.jsx)(r,{...o,ref:n,children:t.isValidElement(e)?t.cloneElement(e,void 0,s):null})}return(0,l.jsx)(r,{...o,ref:n,children:s})});return n.displayName=`${e}.Slot`,n}var i=o("Slot"),a=Symbol("radix.slottable");function d(e){let r=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=a,r}function c(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}}},e=>{e.O(0,[4277,3464,8816,8441,5964,7358],()=>e(e.s=70297)),_N_E=e.O()}]);